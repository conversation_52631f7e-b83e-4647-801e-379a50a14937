<!-- backend/templates/admin_tools/user_profile_management.html -->
{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}User Profile Management | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- Bootstrap CSS and JS for modal functionality -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Modal base styles -->
<link rel="stylesheet" type="text/css" href="{% static 'admin_tools/modals/modal_base_styles.css' %}">
{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
    .profile-container {
        max-width: 1600px;
        margin: 0 auto;
        padding: 20px;
    }

    .card {
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .filters {
        margin-bottom: 20px;
        background: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #eee;
    }

    .filter-form {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        align-items: flex-end;
    }

    .form-group {
        margin-bottom: 10px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .form-group select,
    .form-group input[type="text"] {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        min-width: 180px;
    }

    .profile-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }

    .profile-table th,
    .profile-table td {
        padding: 12px;
        border-bottom: 1px solid #ddd;
        text-align: left;
        vertical-align: top;
    }

    .profile-table th {
        background-color: #f8f8f8;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .profile-table tr:hover {
        background-color: #f5f5f5;
    }

    .profile-type-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .profile-type-real {
        background: #d4edda;
        color: #155724;
    }

    .profile-type-test {
        background: #fff3cd;
        color: #856404;
    }

    .completeness-bar {
        width: 100px;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }

    .completeness-fill {
        height: 100%;
        background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
        transition: width 0.3s ease;
    }

    .completeness-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 11px;
        font-weight: bold;
        color: #333;
    }

    .btn {
        padding: 8px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        margin-right: 5px;
    }

    .btn-primary {
        background: #007bff;
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
    }

    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
        display: block;
    }

    .stat-label {
        font-size: 14px;
        color: #6c757d;
        margin-top: 5px;
    }

    .search-box {
        width: 300px;
    }

    .table-container {
        max-height: 70vh;
        overflow-y: auto;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .no-data {
        text-align: center;
        padding: 40px;
        color: #6c757d;
        font-style: italic;
    }

    /* Batch Actions */
    .batch-actions-card {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        margin-bottom: 15px;
    }

    .batch-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
    }

    .batch-info {
        font-weight: 600;
        color: #495057;
    }

    .batch-buttons {
        display: flex;
        gap: 10px;
    }

    .batch-buttons .btn {
        padding: 6px 12px;
        font-size: 14px;
    }

    /* Checkbox styling */
    .profile-checkbox, #select-all-checkbox {
        transform: scale(1.2);
        margin: 0;
    }

    .profile-table th:first-child,
    .profile-table td:first-child {
        width: 40px;
        text-align: center;
    }

    /* Selected row highlighting */
    .profile-table tr.selected {
        background-color: #e7f3ff;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-buttons .btn {
        padding: 4px 8px;
        font-size: 12px;
        margin-right: 0;
    }

    /* Loading states */
    .loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
    }

    .loader {
        border: 3px solid #f3f3f3;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Import Profile Styles */
    .import-profile-card {
        margin-bottom: 20px;
    }
    
    .import-method-tabs {
        display: flex;
        border-bottom: 2px solid #dee2e6;
        margin-bottom: 20px;
    }
    
    .tab-btn {
        background: none;
        border: none;
        padding: 12px 20px;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .tab-btn:hover {
        background-color: #f8f9fa;
    }
    
    .tab-btn.active {
        border-bottom-color: #007bff;
        color: #007bff;
        background-color: #f8f9fa;
    }
    
    .tab-content {
        display: none;
        padding: 20px 0;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .upload-area {
        border: 2px dashed #ced4da;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
    }
    
    .upload-area:hover {
        border-color: #007bff;
        background-color: #e7f3ff;
    }
    
    .upload-area.dragover {
        border-color: #28a745;
        background-color: #d4edda;
    }
    
    .upload-icon {
        font-size: 48px;
        margin-bottom: 10px;
    }
    
    .upload-text {
        color: #6c757d;
    }
    
    .json-input-section label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
    }
    
    #json-textarea, #questionnaire-data {
        width: 100%;
        min-height: 200px;
        padding: 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        resize: vertical;
    }
    
    .json-validation {
        margin-top: 10px;
        padding: 8px;
        border-radius: 4px;
        font-size: 14px;
    }
    
    .json-validation.valid {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .json-validation.invalid {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .ai-options {
        margin: 15px 0;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .ai-options label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: normal;
    }
    
    .profile-preview {
        margin-top: 20px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .preview-summary {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .preview-stat {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        text-align: center;
    }
    
    .preview-stat-number {
        font-size: 18px;
        font-weight: bold;
        color: #007bff;
        display: block;
    }
    
    .preview-stat-label {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
    }
    
    .preview-details {
        background: white;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .import-controls {
        margin-top: 20px;
        padding: 20px;
        background-color: #fff3cd;
        border-radius: 8px;
        border: 1px solid #ffeaa7;
    }
    
    .import-options {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
    }
    
    .import-options label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: normal;
    }
    
    .import-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }
    
    .import-status {
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
    }
    
    .import-status.success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    
    .import-status.error {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    
    .import-status.warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
    
    .import-status.info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
    
    .status-message {
        font-weight: 600;
        margin-bottom: 8px;
    }
    
    .status-details {
        font-size: 14px;
        max-height: 200px;
        overflow-y: auto;
    }
    
    /* Progress indicator */
    .progress-indicator {
        width: 100%;
        height: 4px;
        background-color: #e9ecef;
        border-radius: 2px;
        overflow: hidden;
        margin: 10px 0;
    }
    
    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #007bff, #28a745);
        width: 0%;
        transition: width 0.3s ease;
        border-radius: 2px;
    }
    
    /* Responsive design */
    @media (max-width: 768px) {
        .filter-form {
            flex-direction: column;
            align-items: stretch;
        }
        
        .form-group select,
        .form-group input[type="text"] {
            min-width: auto;
            width: 100%;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .search-box {
            width: 100%;
        }
        
        .import-method-tabs {
            flex-direction: column;
        }
        
        .tab-btn {
            border-bottom: none;
            border-left: 3px solid transparent;
            text-align: left;
        }
        
        .tab-btn.active {
            border-left-color: #007bff;
            border-bottom-color: transparent;
        }
        
        .import-buttons {
            flex-direction: column;
        }
        
        .preview-summary {
            grid-template-columns: repeat(2, 1fr);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="card">
        <h1>👤 User Profile Management</h1>
        <p>Comprehensive user profile management with detailed information display and editing capabilities.</p>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">{{ total_profiles }}</span>
                <div class="stat-label">Total Profiles</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ real_profiles }}</span>
                <div class="stat-label">Real Users</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ test_profiles }}</span>
                <div class="stat-label">Test Profiles</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ profiles_with_demographics }}</span>
                <div class="stat-label">With Demographics</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ profiles_with_environment }}</span>
                <div class="stat-label">With Environment</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card">
        <div class="filters">
            <form method="get" class="filter-form">
                <div class="form-group">
                    <label for="search">Search:</label>
                    <input type="text" id="search" name="search" value="{{ search_query }}" 
                           placeholder="Name, username, email..." class="search-box">
                </div>
                <div class="form-group">
                    <label for="profile_type">Profile Type:</label>
                    <select id="profile_type" name="profile_type">
                        <option value="">All Types</option>
                        <option value="real" {% if profile_type == 'real' %}selected{% endif %}>Real Users</option>
                        <option value="test" {% if profile_type == 'test' %}selected{% endif %}>Test Profiles</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="has_demographics">Demographics:</label>
                    <select id="has_demographics" name="has_demographics">
                        <option value="">All</option>
                        <option value="yes" {% if has_demographics == 'yes' %}selected{% endif %}>Has Demographics</option>
                        <option value="no" {% if has_demographics == 'no' %}selected{% endif %}>No Demographics</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="has_environment">Environment:</label>
                    <select id="has_environment" name="has_environment">
                        <option value="">All</option>
                        <option value="yes" {% if has_environment == 'yes' %}selected{% endif %}>Has Environment</option>
                        <option value="no" {% if has_environment == 'no' %}selected{% endif %}>No Environment</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="completeness">Completeness:</label>
                    <select id="completeness" name="completeness">
                        <option value="">All Levels</option>
                        <option value="low" {% if completeness == 'low' %}selected{% endif %}>Low (&lt; 30%)</option>
                        <option value="medium" {% if completeness == 'medium' %}selected{% endif %}>Medium (30-70%)</option>
                        <option value="high" {% if completeness == 'high' %}selected{% endif %}>High (&gt; 70%)</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Filter</button>
                    <a href="{% url 'admin:user_profile_management' %}" class="btn btn-secondary">Clear</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Import User Profile -->    
    <div class="card import-profile-card">
        <h3>📤 Import User Profile</h3>
        <p>Import a complete user profile from JSON data (supports AI-generated profiles and manual exports).</p>
        
        <div class="import-section">
            <div class="import-method-tabs">
                <button class="tab-btn active" data-tab="json-upload">📄 JSON Upload</button>
                <button class="tab-btn" data-tab="json-paste">📝 JSON Paste</button>
                <button class="tab-btn" data-tab="ai-generate">🤖 AI Generate</button>
            </div>
            
            <!-- JSON Upload Tab -->
            <div class="tab-content active" data-tab="json-upload">
                <div class="upload-area" id="json-upload-area">
                    <div class="upload-placeholder">
                        <div class="upload-icon">📁</div>
                        <div class="upload-text">
                            <strong>Drop JSON file here or click to browse</strong>
                            <br><small>Supports .json files with complete user profile data</small>
                        </div>
                    </div>
                    <input type="file" id="json-file-input" accept=".json" style="display: none;">
                </div>
            </div>
            
            <!-- JSON Paste Tab -->
            <div class="tab-content" data-tab="json-paste">
                <div class="json-input-section">
                    <label for="json-textarea">Paste JSON Profile Data:</label>
                    <textarea id="json-textarea" placeholder="Paste your JSON user profile data here..."></textarea>
                    <div class="json-validation" id="json-validation"></div>
                </div>
            </div>
            
            <!-- AI Generate Tab -->
            <div class="tab-content" data-tab="ai-generate">
                <div class="ai-generate-section">
                    <label for="questionnaire-data">Questionnaire Responses:</label>
                    <textarea id="questionnaire-data" placeholder="Paste questionnaire responses or interview transcript here..."></textarea>
                    <div class="ai-options">
                        <label>
                            <input type="checkbox" id="include-archetype-analysis" checked>
                            Include Archetype Analysis
                        </label>
                        <label>
                            <input type="checkbox" id="include-environment-inference" checked>
                            Infer Environment Details
                        </label>
                        <label>
                            <input type="checkbox" id="include-goals-extraction" checked>
                            Extract Goals and Aspirations
                        </label>
                    </div>
                    <button class="btn btn-primary" id="generate-profile-btn">🤖 Generate Profile</button>
                </div>
            </div>
        </div>
        
        <!-- Profile Preview -->
        <div class="profile-preview" id="profile-preview" style="display: none;">
            <h4>📋 Profile Preview</h4>
            <div class="preview-summary" id="preview-summary"></div>
            <div class="preview-details" id="preview-details"></div>
        </div>
        
        <!-- Import Controls -->
        <div class="import-controls" id="import-controls" style="display: none;">
            <div class="import-options">
                <label>
                    <input type="checkbox" id="overwrite-existing" checked>
                    Overwrite existing profile if username/email exists
                </label>
                <label>
                    <input type="checkbox" id="validate-before-import" checked>
                    Validate data before importing
                </label>
                <label>
                    <input type="checkbox" id="create-backup" checked>
                    Create backup of existing data
                </label>
            </div>
            
            <div class="import-buttons">
                <button class="btn btn-success" id="import-profile-btn">✅ Import Profile</button>
                <button class="btn btn-secondary" id="clear-import-btn">🗑️ Clear</button>
                <button class="btn btn-info" id="download-schema-btn">📊 Download Schema</button>
            </div>
        </div>
        
        <!-- Import Status -->
        <div class="import-status" id="import-status" style="display: none;">
            <div class="status-message" id="status-message"></div>
            <div class="status-details" id="status-details"></div>
        </div>
    </div>

    <!-- Batch Actions -->
    <div class="card batch-actions-card" style="display: none;">
        <div class="batch-actions">
            <div class="batch-info">
                <span id="selected-count">0 profiles selected</span>
            </div>
            <div class="batch-buttons">
                <button id="select-all-btn" class="btn btn-outline-primary">Select All</button>
                <button id="deselect-all-btn" class="btn btn-outline-secondary">Deselect All</button>
                <button id="batch-delete-btn" class="btn btn-danger" disabled>🗑️ Delete Selected</button>
                <button id="batch-export-btn" class="btn btn-success" disabled>📊 Export Selected</button>
            </div>
        </div>
    </div>

    <!-- Profile Table -->
    <div class="card">
        <div class="table-container">
            {% if profiles %}
            <table class="profile-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all-checkbox" title="Select all profiles">
                        </th>
                        <th>Profile</th>
                        <th>Type</th>
                        <th>User Account</th>
                        <th>Demographics</th>
                        <th>Environment</th>
                        <th>Data Counts</th>
                        <th>Completeness</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for profile in profiles %}
                    <tr data-profile-id="{{ profile.id }}">
                        <td>
                            <input type="checkbox" class="profile-checkbox" value="{{ profile.id }}" title="Select this profile">
                        </td>
                        <td>
                            <strong>{{ profile.profile_name }}</strong>
                            <br>
                            <small>ID: {{ profile.id }}</small>
                        </td>
                        <td>
                            <span class="profile-type-badge {% if profile.is_real %}profile-type-real{% else %}profile-type-test{% endif %}">
                                {{ profile.profile_type }}
                            </span>
                        </td>
                        <td>
                            <strong>{{ profile.user.username }}</strong>
                            {% if profile.user.email %}
                            <br><small>{{ profile.user.email }}</small>
                            {% endif %}
                            <br><small>Joined: {{ profile.user.date_joined|date:"Y-m-d" }}</small>
                        </td>
                        <td>
                            {% if profile.demographics %}
                            <strong>{{ profile.demographics.full_name }}</strong>
                            <br><small>{{ profile.demographics.age }} years, {{ profile.demographics.gender }}</small>
                            <br><small>{{ profile.demographics.location }}</small>
                            {% else %}
                            <em>No demographics</em>
                            {% endif %}
                        </td>
                        <td>
                            {% if profile.current_environment %}
                            <strong>{{ profile.current_environment.environment_name }}</strong>
                            <br><small>{{ profile.current_environment.environment_description|truncatechars:50 }}</small>
                            {% else %}
                            <em>No environment</em>
                            {% endif %}
                        </td>
                        <td>
                            <small>
                                Envs: {{ profile.environments.count }}<br>
                                Skills: {{ profile.skills.count }}<br>
                                Resources: {{ profile.resources.count }}<br>
                                Prefs: {{ profile.preferences.count }}
                            </small>
                        </td>
                        <td>
                            <div class="completeness-bar">
                                <div class="completeness-fill" style="width: 75%"></div>
                                <div class="completeness-text">75%</div>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary view-profile-btn" data-profile-id="{{ profile.id }}">
                                    👁️ View
                                </button>
                                <button class="btn btn-secondary edit-profile-btn" data-profile-id="{{ profile.id }}">
                                    ✏️ Edit
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
            <div class="no-data">
                <h3>No profiles found</h3>
                <p>Try adjusting your search criteria or filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Include the user profile detail modal -->
{% include 'admin_tools/modals/user_profile_detail_modal.html' %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle view profile buttons
    document.querySelectorAll('.view-profile-btn').forEach(button => {
        button.addEventListener('click', function() {
            const profileId = this.getAttribute('data-profile-id');
            openProfileModal(profileId, 'view');
        });
    });

    // Handle edit profile buttons
    document.querySelectorAll('.edit-profile-btn').forEach(button => {
        button.addEventListener('click', function() {
            const profileId = this.getAttribute('data-profile-id');
            openProfileModal(profileId, 'edit');
        });
    });

    // Calculate and update completeness bars
    updateCompletenessDisplay();

    // Initialize batch actions
    initializeBatchActions();
    
    // Initialize import functionality
    initializeImportFunctionality();
});

// openProfileModal function is defined in the modal template

function updateCompletenessDisplay() {
    // This will be enhanced when we have the API data
    // For now, just update the visual display
    document.querySelectorAll('.completeness-bar').forEach(bar => {
        const fill = bar.querySelector('.completeness-fill');
        const text = bar.querySelector('.completeness-text');
        // This will be calculated from actual data via API
        const percentage = Math.floor(Math.random() * 100); // Placeholder
        fill.style.width = percentage + '%';
        text.textContent = percentage + '%';
    });
}

function initializeBatchActions() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const profileCheckboxes = document.querySelectorAll('.profile-checkbox');
    const batchActionsCard = document.querySelector('.batch-actions-card');
    const selectedCountSpan = document.getElementById('selected-count');
    const selectAllBtn = document.getElementById('select-all-btn');
    const deselectAllBtn = document.getElementById('deselect-all-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const batchExportBtn = document.getElementById('batch-export-btn');

    let selectedProfiles = new Set();

    function updateBatchUI() {
        const count = selectedProfiles.size;
        selectedCountSpan.textContent = `${count} profile${count !== 1 ? 's' : ''} selected`;

        // Show/hide batch actions
        if (count > 0) {
            batchActionsCard.style.display = 'block';
        } else {
            batchActionsCard.style.display = 'none';
        }

        // Enable/disable buttons
        batchDeleteBtn.disabled = count === 0;
        batchExportBtn.disabled = count === 0;

        // Update select all checkbox state
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === profileCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }

        // Update row highlighting
        profileCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('tr');
            if (selectedProfiles.has(checkbox.value)) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });
    }

    // Handle individual checkbox changes
    profileCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedProfiles.add(this.value);
            } else {
                selectedProfiles.delete(this.value);
            }
            updateBatchUI();
        });
    });

    // Handle select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        if (this.checked) {
            profileCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                selectedProfiles.add(checkbox.value);
            });
        } else {
            profileCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                selectedProfiles.delete(checkbox.value);
            });
        }
        updateBatchUI();
    });

    // Handle select all button
    selectAllBtn.addEventListener('click', function() {
        profileCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedProfiles.add(checkbox.value);
        });
        updateBatchUI();
    });

    // Handle deselect all button
    deselectAllBtn.addEventListener('click', function() {
        profileCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            selectedProfiles.delete(checkbox.value);
        });
        updateBatchUI();
    });

    // Handle batch delete
    batchDeleteBtn.addEventListener('click', function() {
        if (selectedProfiles.size === 0) return;

        const count = selectedProfiles.size;
        const confirmMessage = `Are you sure you want to delete ${count} profile${count !== 1 ? 's' : ''}? This action cannot be undone.`;

        if (confirm(confirmMessage)) {
            performBatchDelete(Array.from(selectedProfiles));
        }
    });

    // Handle batch export
    batchExportBtn.addEventListener('click', function() {
        if (selectedProfiles.size === 0) return;
        performBatchExport(Array.from(selectedProfiles));
    });

    // Initial UI update
    updateBatchUI();
}

async function performBatchDelete(profileIds) {
    try {
        const response = await fetch('/admin/user-profiles/batch-delete/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({ profile_ids: profileIds })
        });

        if (response.ok) {
            const result = await response.json();
            alert(`Successfully deleted ${result.deleted_count} profiles.`);
            location.reload(); // Refresh the page
        } else {
            const error = await response.json();
            alert(`Error deleting profiles: ${error.error}`);
        }
    } catch (error) {
        console.error('Batch delete error:', error);
        alert('An error occurred while deleting profiles.');
    }
}

async function performBatchExport(profileIds) {
    try {
        const response = await fetch('/admin/user-profiles/batch-export/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({ profile_ids: profileIds })
        });

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `user_profiles_export_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            const error = await response.json();
            alert(`Error exporting profiles: ${error.error}`);
        }
    } catch (error) {
        console.error('Batch export error:', error);
        alert('An error occurred while exporting profiles.');
    }
}

function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value ||
           document.querySelector('meta[name=csrf-token]').getAttribute('content') ||
           '';
}

// Import Functionality
function initializeImportFunctionality() {
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            switchImportTab(targetTab);
        });
    });
    
    // File upload handling
    const uploadArea = document.getElementById('json-upload-area');
    const fileInput = document.getElementById('json-file-input');
    
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    
    fileInput.addEventListener('change', handleFileSelect);
    
    // JSON textarea validation
    const jsonTextarea = document.getElementById('json-textarea');
    jsonTextarea.addEventListener('input', validateJsonInput);
    
    // AI generation
    document.getElementById('generate-profile-btn').addEventListener('click', generateProfileFromQuestionnaire);
    
    // Import controls
    document.getElementById('import-profile-btn').addEventListener('click', performImport);
    document.getElementById('clear-import-btn').addEventListener('click', clearImportData);
    document.getElementById('download-schema-btn').addEventListener('click', downloadSchema);
}

function switchImportTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Clear previous data when switching tabs
    clearImportData();
}

function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    if (!file.name.endsWith('.json')) {
        showImportStatus('error', 'Invalid file type', 'Please upload a .json file.');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const jsonData = JSON.parse(e.target.result);
            displayProfilePreview(jsonData);
            showImportControls();
        } catch (error) {
            showImportStatus('error', 'Invalid JSON', `Error parsing JSON file: ${error.message}`);
        }
    };
    reader.readAsText(file);
}

function validateJsonInput() {
    const textarea = document.getElementById('json-textarea');
    const validation = document.getElementById('json-validation');
    
    if (!textarea.value.trim()) {
        validation.style.display = 'none';
        return;
    }
    
    try {
        const jsonData = JSON.parse(textarea.value);
        validation.className = 'json-validation valid';
        validation.textContent = '✅ Valid JSON format';
        validation.style.display = 'block';
        
        displayProfilePreview(jsonData);
        showImportControls();
    } catch (error) {
        validation.className = 'json-validation invalid';
        validation.textContent = `❌ Invalid JSON: ${error.message}`;
        validation.style.display = 'block';
        
        hideProfilePreview();
        hideImportControls();
    }
}

async function generateProfileFromQuestionnaire() {
    const questionnaireData = document.getElementById('questionnaire-data').value.trim();
    
    if (!questionnaireData) {
        showImportStatus('warning', 'No questionnaire data', 'Please paste questionnaire responses or interview transcript.');
        return;
    }
    
    const options = {
        includeArchetypeAnalysis: document.getElementById('include-archetype-analysis').checked,
        includeEnvironmentInference: document.getElementById('include-environment-inference').checked,
        includeGoalsExtraction: document.getElementById('include-goals-extraction').checked
    };
    
    showImportStatus('info', 'Generating profile...', 'AI is analyzing the questionnaire responses and creating a comprehensive user profile.');
    
    try {
        const response = await fetch('/admin/user-profiles/ai-generate/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({
                questionnaire_data: questionnaireData,
                options: options
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            displayProfilePreview(result.profile_data);
            showImportControls();
            showImportStatus('success', 'Profile generated successfully', 'Review the generated profile and adjust options if needed.');
        } else {
            const error = await response.json();
            showImportStatus('error', 'Generation failed', error.error || 'Failed to generate profile from questionnaire data.');
        }
    } catch (error) {
        console.error('Profile generation error:', error);
        showImportStatus('error', 'Generation error', 'An error occurred while generating the profile.');
    }
}

function displayProfilePreview(profileData) {
    const preview = document.getElementById('profile-preview');
    const summary = document.getElementById('preview-summary');
    const details = document.getElementById('preview-details');
    
    // Create summary statistics
    const stats = calculateProfileStats(profileData);
    summary.innerHTML = Object.entries(stats).map(([key, value]) => `
        <div class="preview-stat">
            <span class="preview-stat-number">${value}</span>
            <div class="preview-stat-label">${key}</div>
        </div>
    `).join('');
    
    // Create detailed view
    details.innerHTML = `<pre>${JSON.stringify(profileData, null, 2)}</pre>`;
    
    preview.style.display = 'block';
}

function calculateProfileStats(profileData) {
    const stats = {};
    
    // Count different profile components
    stats['Demographics'] = profileData.demographics ? 1 : 0;
    stats['Environments'] = profileData.environments ? profileData.environments.length : 0;
    stats['Skills'] = profileData.skills ? profileData.skills.length : 0;
    stats['Resources'] = profileData.resources ? profileData.resources.length : 0;
    stats['Preferences'] = profileData.preferences ? profileData.preferences.length : 0;
    stats['Goals'] = (profileData.aspirations ? profileData.aspirations.length : 0) + 
                     (profileData.intentions ? profileData.intentions.length : 0);
    stats['Beliefs'] = profileData.beliefs ? profileData.beliefs.length : 0;
    stats['Traits'] = profileData.traits ? profileData.traits.length : 0;
    stats['Limitations'] = profileData.limitations ? profileData.limitations.length : 0;
    
    return stats;
}

function hideProfilePreview() {
    document.getElementById('profile-preview').style.display = 'none';
}

function showImportControls() {
    document.getElementById('import-controls').style.display = 'block';
}

function hideImportControls() {
    document.getElementById('import-controls').style.display = 'none';
}

async function performImport() {
    let profileData;
    
    // Get profile data based on active tab
    const activeTab = document.querySelector('.tab-content.active');
    const tabType = activeTab.getAttribute('data-tab');
    
    if (tabType === 'json-paste') {
        try {
            profileData = JSON.parse(document.getElementById('json-textarea').value);
        } catch (error) {
            showImportStatus('error', 'Invalid JSON', 'Please fix the JSON format before importing.');
            return;
        }
    } else if (tabType === 'json-upload') {
        // Profile data should already be set from file processing
        try {
            profileData = JSON.parse(document.getElementById('json-textarea').value || '{}');
        } catch (error) {
            showImportStatus('error', 'No valid data', 'Please upload a valid JSON file first.');
            return;
        }
    } else if (tabType === 'ai-generate') {
        // Profile data should be set from AI generation
        const previewDetails = document.getElementById('preview-details');
        if (!previewDetails.textContent) {
            showImportStatus('error', 'No generated data', 'Please generate a profile first.');
            return;
        }
        try {
            profileData = JSON.parse(previewDetails.textContent);
        } catch (error) {
            showImportStatus('error', 'Invalid generated data', 'Generated profile data is invalid.');
            return;
        }
    }
    
    const options = {
        overwriteExisting: document.getElementById('overwrite-existing').checked,
        validateBeforeImport: document.getElementById('validate-before-import').checked,
        createBackup: document.getElementById('create-backup').checked
    };
    
    showImportStatus('info', 'Importing profile...', 'Processing user profile data and creating database records.');
    
    try {
        const response = await fetch('/admin/user-profiles/import/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken(),
            },
            body: JSON.stringify({
                profile_data: profileData,
                options: options
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            showImportStatus('success', 'Import successful', 
                `Successfully imported user profile: ${result.profile_name}. ` +
                `Created ${result.created_records} new records.`);
            
            // Refresh the page after a delay
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            const error = await response.json();
            showImportStatus('error', 'Import failed', error.error || 'Failed to import user profile.');
        }
    } catch (error) {
        console.error('Import error:', error);
        showImportStatus('error', 'Import error', 'An error occurred while importing the profile.');
    }
}

function clearImportData() {
    // Clear all inputs
    document.getElementById('json-textarea').value = '';
    document.getElementById('questionnaire-data').value = '';
    document.getElementById('json-file-input').value = '';
    
    // Clear validation
    document.getElementById('json-validation').style.display = 'none';
    
    // Hide preview and controls
    hideProfilePreview();
    hideImportControls();
    
    // Hide status
    document.getElementById('import-status').style.display = 'none';
}

function showImportStatus(type, message, details) {
    const status = document.getElementById('import-status');
    const messageEl = document.getElementById('status-message');
    const detailsEl = document.getElementById('status-details');
    
    status.className = `import-status ${type}`;
    messageEl.textContent = message;
    detailsEl.textContent = details;
    status.style.display = 'block';
}

async function downloadSchema() {
    try {
        const response = await fetch('/admin/user-profiles/schema/', {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });
        
        if (response.ok) {
            const schema = await response.json();
            const blob = new Blob([JSON.stringify(schema, null, 2)], { type: 'application/json' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'user_profile_schema.json';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            showImportStatus('error', 'Schema download failed', 'Could not retrieve the profile schema.');
        }
    } catch (error) {
        console.error('Schema download error:', error);
        showImportStatus('error', 'Download error', 'An error occurred while downloading the schema.');
    }
}
</script>
{% endblock %}
