"""
Django management command to test database connection from within the container.
This helps debug database connectivity issues in production environments.
"""
import os
import sys
from django.core.management.base import BaseCommand
from django.db import connection
from django.conf import settings
import psycopg2


class Command(BaseCommand):
    help = 'Test database connection and display environment variables'

    def handle(self, *args, **options):
        self.stdout.write("🔍 DATABASE CONNECTION TEST")
        self.stdout.write("=" * 50)
        
        # Display all database-related environment variables
        self.stdout.write("\n📋 ENVIRONMENT VARIABLES:")
        for key, value in sorted(os.environ.items()):
            if any(keyword in key.upper() for keyword in ['DATABASE', 'DB', 'POSTGRES', 'CUSTOM']):
                # Mask passwords for security
                if 'PASSWORD' in key.upper() or 'PASS' in key.upper():
                    masked_value = value[:4] + '*' * (len(value) - 8) + value[-4:] if len(value) > 8 else '*' * len(value)
                    self.stdout.write(f"  {key} = {masked_value}")
                else:
                    self.stdout.write(f"  {key} = {value}")
        
        # Display Django database configuration
        self.stdout.write(f"\n⚙️  DJANGO DATABASE CONFIG:")
        db_config = settings.DATABASES['default']
        self.stdout.write(f"  ENGINE: {db_config.get('ENGINE', 'Not set')}")
        self.stdout.write(f"  NAME: {db_config.get('NAME', 'Not set')}")
        self.stdout.write(f"  USER: {db_config.get('USER', 'Not set')}")
        self.stdout.write(f"  HOST: {db_config.get('HOST', 'Not set')}")
        self.stdout.write(f"  PORT: {db_config.get('PORT', 'Not set')}")
        self.stdout.write(f"  OPTIONS: {db_config.get('OPTIONS', {})}")
        
        # Test Django database connection
        self.stdout.write(f"\n🔌 TESTING DJANGO CONNECTION:")
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                self.stdout.write(self.style.SUCCESS(f"✅ Django connection successful!"))
                self.stdout.write(f"   PostgreSQL version: {version}")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Django connection failed: {e}"))
        
        # Test direct psycopg2 connection with correct credentials
        self.stdout.write(f"\n🔌 TESTING DIRECT CONNECTION (CORRECT CREDENTIALS):")
        correct_url = "postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=disable"
        try:
            conn = psycopg2.connect(correct_url)
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            self.stdout.write(self.style.SUCCESS(f"✅ Direct connection (correct creds) successful!"))
            self.stdout.write(f"   PostgreSQL version: {version}")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Direct connection (correct creds) failed: {e}"))
        
        # Test direct psycopg2 connection with auto-injected credentials
        self.stdout.write(f"\n🔌 TESTING DIRECT CONNECTION (AUTO-INJECTED CREDENTIALS):")
        auto_url = os.environ.get('DATABASE_URL', 'Not set')
        if auto_url != 'Not set':
            try:
                conn = psycopg2.connect(auto_url)
                cursor = conn.cursor()
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                cursor.close()
                conn.close()
                self.stdout.write(self.style.SUCCESS(f"✅ Direct connection (auto creds) successful!"))
                self.stdout.write(f"   PostgreSQL version: {version}")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Direct connection (auto creds) failed: {e}"))
        else:
            self.stdout.write(self.style.WARNING("⚠️  DATABASE_URL not found in environment"))
        
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("🏁 DATABASE CONNECTION TEST COMPLETE")
