import json
import logging
from datetime import datetime
from typing import Dict, Any
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View

from apps.user.serializers.import_serializers import (
    UserProfileImportRequestSerializer, AIGenerateRequestSerializer,
    ImportResponseSerializer, AIGenerateResponseSerializer, ErrorResponseSerializer
)
from apps.user.services.profile_import_service import ProfileImportService, ProfileImportError
from apps.user.services.ai_profile_generator import AIProfileGenerator, AIProfileGenerationError

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileImportView(View):
    """Handle user profile import requests"""
    
    def post(self, request):
        """Import a complete user profile from JSON data"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            
            # Validate request
            serializer = UserProfileImportRequestSerializer(data=data)
            if not serializer.is_valid():
                return JsonResponse({
                    'success': False,
                    'error': 'Validation failed',
                    'validation_errors': serializer.errors
                }, status=400)
            
            # Extract validated data
            profile_data = serializer.validated_data['profile_data']
            options = serializer.validated_data.get('options', {})
            
            # Import the profile
            import_service = ProfileImportService()
            result = import_service.import_profile(profile_data, options)
            
            return JsonResponse(result)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            }, status=400)
        except ProfileImportError as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in profile import: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Internal server error'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class AIProfileGenerateView(View):
    """Handle AI-powered profile generation from questionnaire data"""
    
    def post(self, request):
        """Generate a user profile from questionnaire responses using AI"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            
            # Validate request
            serializer = AIGenerateRequestSerializer(data=data)
            if not serializer.is_valid():
                return JsonResponse({
                    'success': False,
                    'error': 'Validation failed',
                    'validation_errors': serializer.errors
                }, status=400)
            
            # Extract validated data
            questionnaire_data = serializer.validated_data['questionnaire_data']
            options = serializer.validated_data.get('options', {})
            
            # Generate the profile
            ai_generator = AIProfileGenerator()
            result = ai_generator.generate_profile(questionnaire_data, options)
            
            return JsonResponse(result)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            }, status=400)
        except AIProfileGenerationError as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in AI profile generation: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Internal server error'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class ProfileSchemaView(View):
    """Provide the user profile import schema"""
    
    def get(self, request):
        """Return the OpenAPI schema for user profile import"""
        try:
            # Load the schema file
            with open('/projects/goali/backend/schemas/user_profile_import_schema.json', 'r') as f:
                schema = json.load(f)
            
            return JsonResponse(schema)
            
        except FileNotFoundError:
            return JsonResponse({
                'error': 'Schema file not found'
            }, status=404)
        except Exception as e:
            logger.error(f"Error serving schema: {e}")
            return JsonResponse({
                'error': 'Error loading schema'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class BatchProfileExportView(View):
    """Handle batch export of user profiles"""
    
    def post(self, request):
        """Export selected user profiles as JSON"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            profile_ids = data.get('profile_ids', [])
            
            if not profile_ids:
                return JsonResponse({
                    'error': 'No profile IDs provided'
                }, status=400)
            
            # Export profiles (this would be implemented based on your needs)
            # For now, return a placeholder response
            exported_data = self._export_profiles(profile_ids)
            
            response = HttpResponse(
                json.dumps(exported_data, indent=2),
                content_type='application/json'
            )
            response['Content-Disposition'] = f'attachment; filename=\"user_profiles_export.json\"'
            
            return response
            
        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            logger.error(f"Error in batch export: {e}")
            return JsonResponse({
                'error': 'Export failed'
            }, status=500)
    
    def _export_profiles(self, profile_ids):
        """Export profiles to structured data (placeholder implementation)"""
        from apps.user.models import UserProfile
        
        profiles = UserProfile.objects.filter(id__in=profile_ids)
        exported_profiles = []
        
        for profile in profiles:
            # This would be a full export implementation
            # For now, just return basic info
            exported_profiles.append({
                'profile_id': profile.id,
                'profile_name': profile.profile_name,
                'username': profile.user.username,
                'is_real': profile.is_real,
                # Add full profile export logic here
                'note': 'Full export implementation needed'
            })
        
        return {
            'exported_at': datetime.now().isoformat(),
            'profile_count': len(exported_profiles),
            'profiles': exported_profiles
        }


@method_decorator(csrf_exempt, name='dispatch')
class BatchProfileDeleteView(View):
    """Handle batch deletion of user profiles"""
    
    def post(self, request):
        """Delete selected user profiles"""
        if not request.user.is_authenticated or not request.user.is_staff:
            return JsonResponse({'error': 'Authentication required'}, status=401)
        
        try:
            # Parse request data
            data = json.loads(request.body)
            profile_ids = data.get('profile_ids', [])
            
            if not profile_ids:
                return JsonResponse({
                    'error': 'No profile IDs provided'
                }, status=400)
            
            # Delete profiles
            from apps.user.models import UserProfile
            deleted_count = UserProfile.objects.filter(id__in=profile_ids).delete()[0]
            
            return JsonResponse({
                'success': True,
                'deleted_count': deleted_count
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'error': 'Invalid JSON data'
            }, status=400)
        except Exception as e:
            logger.error(f"Error in batch delete: {e}")
            return JsonResponse({
                'error': 'Delete failed'
            }, status=500)


# Function-based views for compatibility
@api_view(['POST'])
@permission_classes([IsAdminUser])
def import_user_profile(request):
    """Function-based view wrapper for profile import"""
    view = ProfileImportView()
    return view.post(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def ai_generate_profile(request):
    """Function-based view wrapper for AI profile generation"""
    view = AIProfileGenerateView()
    return view.post(request)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_profile_schema(request):
    """Function-based view wrapper for schema retrieval"""
    view = ProfileSchemaView()
    return view.get(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def batch_export_profiles(request):
    """Function-based view wrapper for batch export"""
    view = BatchProfileExportView()
    return view.post(request)


@api_view(['POST'])
@permission_classes([IsAdminUser])
def batch_delete_profiles(request):
    """Function-based view wrapper for batch delete"""
    view = BatchProfileDeleteView()
    return view.post(request)
