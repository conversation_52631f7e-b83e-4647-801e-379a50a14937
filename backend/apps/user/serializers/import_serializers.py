from rest_framework import serializers
from django.contrib.auth.models import User
from django.db import transaction
from datetime import datetime, date
import json

from apps.user.models import (
    UserProfile, Demographics, UserEnvironment, UserTraitInclination,
    Belief, BeliefEvidence, Aspiration, Intention, Inspiration,
    Skill, UserResource, UserLimitation, Preference, CurrentMood,
    TrustLevel, GenericTrait, GenericSkill, GenericResource,
    GenericUserLimitation, UserEnvironmentPhysicalProperties,
    UserEnvironmentSocialContext, UserEnvironmentActivitySupport,
    UserEnvironmentPsychologicalQualities
)


class UserAccountSerializer(serializers.Serializer):
    """Serializer for user account creation during import"""
    username = serializers.CharField(max_length=150)
    email = serializers.EmailField()
    first_name = serializers.CharField(max_length=150, required=False, default='')
    last_name = serializers.Char<PERSON><PERSON>(max_length=150, required=False, default='')
    password = serializers.CharField(write_only=True, required=False)
    is_staff = serializers.BooleanField(default=False)
    is_superuser = serializers.BooleanField(default=False)


class DemographicsSerializer(serializers.Serializer):
    """Serializer for demographics data"""
    full_name = serializers.CharField(max_length=255)
    age = serializers.IntegerField(min_value=13, max_value=120)
    gender = serializers.CharField(max_length=50)
    location = serializers.CharField(max_length=255)
    language = serializers.CharField(max_length=50)
    occupation = serializers.CharField(max_length=255)


class PhysicalPropertiesSerializer(serializers.Serializer):
    """Serializer for environment physical properties"""
    rurality = serializers.IntegerField(min_value=0, max_value=100, required=False)
    noise_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    light_quality = serializers.IntegerField(min_value=0, max_value=100, required=False)
    temperature_range = serializers.CharField(required=False, default='')
    accessibility = serializers.IntegerField(min_value=0, max_value=100, required=False)
    air_quality = serializers.IntegerField(min_value=0, max_value=100, required=False)
    has_natural_elements = serializers.BooleanField(required=False, default=False)
    surface_type = serializers.CharField(required=False, default='')
    water_proximity = serializers.IntegerField(min_value=0, max_value=100, required=False)
    space_size = serializers.CharField(required=False, default='')


class SocialContextSerializer(serializers.Serializer):
    """Serializer for environment social context"""
    privacy_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    typical_occupancy = serializers.IntegerField(min_value=0, max_value=100, required=False)
    social_interaction_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    formality_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    safety_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    supervision_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    cultural_diversity = serializers.IntegerField(min_value=0, max_value=100, required=False)


class ActivitySupportSerializer(serializers.Serializer):
    """Serializer for environment activity support"""
    digital_connectivity = serializers.IntegerField(min_value=0, max_value=100, required=False)
    resource_availability = serializers.IntegerField(min_value=0, max_value=100, required=False)
    domain_specific_support = serializers.DictField(required=False, default=dict)
    time_availability = serializers.DictField(required=False, default=dict)


class PsychologicalQualitiesSerializer(serializers.Serializer):
    """Serializer for environment psychological qualities"""
    restorative_quality = serializers.IntegerField(min_value=0, max_value=100, required=False)
    stimulation_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    aesthetic_appeal = serializers.IntegerField(min_value=0, max_value=100, required=False)
    novelty_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    comfort_level = serializers.IntegerField(min_value=0, max_value=100, required=False)
    personal_significance = serializers.IntegerField(min_value=0, max_value=100, required=False)
    emotional_associations = serializers.DictField(required=False, default=dict)


class UserEnvironmentSerializer(serializers.Serializer):
    """Serializer for user environment data"""
    environment_name = serializers.CharField(max_length=255)
    environment_description = serializers.CharField()
    is_current = serializers.BooleanField(default=False)
    effective_start = serializers.DateField(required=False)
    effective_end = serializers.DateField(required=False, allow_null=True)
    generic_environment_code = serializers.CharField(required=False, default='')
    physical_properties = PhysicalPropertiesSerializer(required=False)
    social_context = SocialContextSerializer(required=False)
    activity_support = ActivitySupportSerializer(required=False)
    psychological_qualities = PsychologicalQualitiesSerializer(required=False)


class TraitInclinationSerializer(serializers.Serializer):
    """Serializer for trait inclination data"""
    trait_code = serializers.CharField()
    strength = serializers.FloatField(min_value=0, max_value=100)
    awareness = serializers.IntegerField(min_value=0, max_value=100)


class BeliefEvidenceSerializer(serializers.Serializer):
    """Serializer for belief evidence"""
    evidence_type = serializers.ChoiceField(choices=[
        'EXPERIENCE', 'EXTERNAL_FEEDBACK', 'PATTERN', 'OBSERVATION', 'RESEARCH'
    ])
    description = serializers.CharField()
    credibility_score = serializers.FloatField(min_value=0, max_value=1, required=False)
    source = serializers.CharField(required=False, default='')


class BeliefSerializer(serializers.Serializer):
    """Serializer for belief data"""
    content = serializers.CharField()
    user_confidence = serializers.IntegerField(min_value=0, max_value=100, required=False)
    system_confidence = serializers.IntegerField(min_value=0, max_value=100, required=False)
    emotionality = serializers.IntegerField(min_value=0, max_value=100, required=False)
    stability = serializers.IntegerField(min_value=0, max_value=100, required=False)
    user_awareness = serializers.IntegerField(min_value=0, max_value=100, required=False)
    evidence = BeliefEvidenceSerializer(many=True, required=False)


class AspirationSerializer(serializers.Serializer):
    """Serializer for aspiration data"""
    title = serializers.CharField(max_length=255)
    description = serializers.CharField()
    importance_according_user = serializers.IntegerField(min_value=0, max_value=100, required=False)
    importance_according_system = serializers.IntegerField(min_value=0, max_value=100, required=False)
    strength = serializers.IntegerField(min_value=0, max_value=100, required=False)
    domain = serializers.CharField(required=False, default='')
    horizon = serializers.CharField(required=False, default='')
    level_of_ambition = serializers.CharField(required=False, default='')
    effective_start = serializers.DateField(required=False)
    duration_estimate = serializers.CharField(required=False, default='')
    effective_end = serializers.DateField(required=False, allow_null=True)


class IntentionSerializer(serializers.Serializer):
    """Serializer for intention data"""
    title = serializers.CharField(max_length=255)
    description = serializers.CharField()
    importance_according_user = serializers.IntegerField(min_value=0, max_value=100, required=False)
    importance_according_system = serializers.IntegerField(min_value=0, max_value=100, required=False)
    strength = serializers.IntegerField(min_value=0, max_value=100, required=False)
    start_date = serializers.DateField(required=False)
    due_date = serializers.DateField(required=False, allow_null=True)
    is_completed = serializers.BooleanField(default=False)
    progress_notes = serializers.CharField(required=False, default='')
    effective_start = serializers.DateField(required=False)
    duration_estimate = serializers.CharField(required=False, default='')
    effective_end = serializers.DateField(required=False, allow_null=True)


class InspirationSerializer(serializers.Serializer):
    """Serializer for inspiration data"""
    source = serializers.CharField(max_length=255)
    description = serializers.CharField()
    strength = serializers.IntegerField(min_value=0, max_value=100, required=False)
    reference_url = serializers.URLField(required=False, default='')


class SkillSerializer(serializers.Serializer):
    """Serializer for skill data"""
    skill_code = serializers.CharField()
    description = serializers.CharField()
    level = serializers.IntegerField(min_value=0, max_value=100)
    user_awareness = serializers.IntegerField(min_value=0, max_value=100, required=False)
    user_enjoyment = serializers.IntegerField(min_value=0, max_value=100, required=False)
    note = serializers.CharField(required=False, default='')
    last_practiced = serializers.DateField(required=False, allow_null=True)
    acquisition_date = serializers.DateField(required=False, allow_null=True)


class ResourceSerializer(serializers.Serializer):
    """Serializer for resource data"""
    specific_name = serializers.CharField(max_length=255)
    resource_code = serializers.CharField()
    location_details = serializers.CharField(required=False, default='')
    ownership_details = serializers.CharField(required=False, default='')
    contact_info = serializers.CharField(required=False, default='')
    notes = serializers.CharField(required=False, default='')


class LimitationSerializer(serializers.Serializer):
    """Serializer for limitation data"""
    limitation_code = serializers.CharField()
    severity = serializers.IntegerField(min_value=0, max_value=100)
    is_unlimited = serializers.BooleanField(default=False)
    user_awareness = serializers.IntegerField(min_value=0, max_value=100, required=False)
    valid_until = serializers.DateField(required=False, allow_null=True)
    effective_start = serializers.DateField(required=False)
    duration_estimate = serializers.CharField(required=False, default='')
    effective_end = serializers.DateField(required=False, allow_null=True)


class PreferenceSerializer(serializers.Serializer):
    """Serializer for preference data"""
    pref_name = serializers.CharField(max_length=255)
    pref_description = serializers.CharField()
    pref_strength = serializers.IntegerField(min_value=0, max_value=100)
    user_awareness = serializers.IntegerField(min_value=0, max_value=100, required=False)
    environment_specific = serializers.BooleanField(default=False)
    effective_start = serializers.DateField(required=False)
    duration_estimate = serializers.CharField(required=False, default='')
    effective_end = serializers.DateField(required=False, allow_null=True)


class CurrentMoodSerializer(serializers.Serializer):
    """Serializer for current mood data"""
    description = serializers.CharField()
    height = serializers.IntegerField(min_value=0, max_value=100)
    user_awareness = serializers.IntegerField(min_value=0, max_value=100, required=False)
    effective_start = serializers.DateField(required=False)
    duration_estimate = serializers.CharField(required=False, default='')
    effective_end = serializers.DateField(required=False, allow_null=True)


class TrustLevelSerializer(serializers.Serializer):
    """Serializer for trust level data"""
    value = serializers.IntegerField(min_value=0, max_value=100)
    aggregate_type = serializers.ChoiceField(
        choices=['Foundation', 'Expansion', 'Integration', 'Mastery'],
        required=False,
        default='Foundation'
    )
    aggregate_id = serializers.CharField(required=False, default='')
    notes = serializers.CharField(required=False, default='')


class CompleteUserProfileSerializer(serializers.Serializer):
    """Main serializer for complete user profile import"""
    user_account = UserAccountSerializer()
    profile_name = serializers.CharField(max_length=255)
    is_real = serializers.BooleanField(default=True)
    demographics = DemographicsSerializer(required=False)
    environments = UserEnvironmentSerializer(many=True, required=False)
    traits = TraitInclinationSerializer(many=True, required=False)
    beliefs = BeliefSerializer(many=True, required=False)
    aspirations = AspirationSerializer(many=True, required=False)
    intentions = IntentionSerializer(many=True, required=False)
    inspirations = InspirationSerializer(many=True, required=False)
    skills = SkillSerializer(many=True, required=False)
    resources = ResourceSerializer(many=True, required=False)
    limitations = LimitationSerializer(many=True, required=False)
    preferences = PreferenceSerializer(many=True, required=False)
    current_mood = CurrentMoodSerializer(required=False)
    trust_level = TrustLevelSerializer(required=False)

    def validate(self, data):
        """Validate the complete profile data"""
        # Check for at least one current environment if environments provided
        environments = data.get('environments', [])
        if environments:
            current_envs = [env for env in environments if env.get('is_current', False)]
            if len(current_envs) == 0:
                raise serializers.ValidationError("At least one environment must be marked as current")
            if len(current_envs) > 1:
                raise serializers.ValidationError("Only one environment can be marked as current")
        
        # Validate trait codes exist
        traits = data.get('traits', [])
        if traits:
            valid_trait_codes = set(GenericTrait.objects.values_list('code', flat=True))
            for trait in traits:
                if trait['trait_code'] not in valid_trait_codes:
                    raise serializers.ValidationError(f"Invalid trait code: {trait['trait_code']}")
        
        # Validate skill codes (if strict validation is needed)
        skills = data.get('skills', [])
        skill_codes = [skill['skill_code'] for skill in skills]
        # Note: We'll create generic skills if they don't exist to allow flexibility
        
        return data


class ImportOptionsSerializer(serializers.Serializer):
    """Serializer for import options"""
    overwriteExisting = serializers.BooleanField(default=False)
    validateBeforeImport = serializers.BooleanField(default=True)
    createBackup = serializers.BooleanField(default=True)


class UserProfileImportRequestSerializer(serializers.Serializer):
    """Serializer for the complete import request"""
    profile_data = CompleteUserProfileSerializer()
    options = ImportOptionsSerializer(required=False)


class AIGenerationOptionsSerializer(serializers.Serializer):
    """Serializer for AI generation options"""
    includeArchetypeAnalysis = serializers.BooleanField(default=True)
    includeEnvironmentInference = serializers.BooleanField(default=True)
    includeGoalsExtraction = serializers.BooleanField(default=True)


class AIGenerateRequestSerializer(serializers.Serializer):
    """Serializer for AI profile generation request"""
    questionnaire_data = serializers.CharField(min_length=100, max_length=50000)
    options = AIGenerationOptionsSerializer(required=False)


class ImportResponseSerializer(serializers.Serializer):
    """Serializer for import response"""
    success = serializers.BooleanField()
    profile_id = serializers.IntegerField(required=False)
    profile_name = serializers.CharField(required=False)
    created_records = serializers.IntegerField(required=False)
    updated_records = serializers.IntegerField(required=False)
    warnings = serializers.ListField(child=serializers.CharField(), required=False)


class AIGenerateResponseSerializer(serializers.Serializer):
    """Serializer for AI generation response"""
    success = serializers.BooleanField()
    profile_data = CompleteUserProfileSerializer(required=False)
    generation_metadata = serializers.DictField(required=False)


class ErrorResponseSerializer(serializers.Serializer):
    """Serializer for error responses"""
    success = serializers.BooleanField(default=False)
    error = serializers.CharField()
    details = serializers.DictField(required=False)
    validation_errors = serializers.ListField(required=False)
