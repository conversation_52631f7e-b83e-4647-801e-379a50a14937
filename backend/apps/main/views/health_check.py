"""
Health check endpoint for DigitalOcean App Platform monitoring.
"""
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.cache import never_cache
from django.conf import settings
from django.db import connection
import logging
import traceback
import os
import psycopg2

# Set up logging
logger = logging.getLogger(__name__)

@csrf_exempt
@never_cache
def health_check(request):
    """
    Ultra-minimal health check that bypasses Django security for debugging.
    """
    try:
        # Log everything for debugging
        logger.info("🏥 Health check started")
        logger.info(f"   Request method: {request.method}")
        logger.info(f"   Request path: {request.path}")
        logger.info(f"   Request host: {request.get_host()}")
        logger.info(f"   Request headers: {dict(request.headers)}")
        logger.info(f"   ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        logger.info(f"   DEBUG: {settings.DEBUG}")
        logger.info(f"   Environment ALLOWED_HOSTS: {os.environ.get('ALLOWED_HOSTS', 'NOT_SET')}")

        # Return the most basic possible response to avoid any Django processing issues
        return HttpResponse('OK', content_type='text/plain', status=200)

    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")

        # Return error with details
        try:
            error_details = f"ERROR: {str(e)} | Host: {request.get_host() if hasattr(request, 'get_host') else 'unknown'}"
            return HttpResponse(error_details, content_type='text/plain', status=500)
        except:
            return HttpResponse('CRITICAL ERROR', content_type='text/plain', status=500)


@csrf_exempt
@never_cache
def debug_db_health_check(request):
    """
    Detailed database health check for debugging deployment issues.
    """
    try:
        logger.info("🔍 Database health check started")

        # Collect environment variables
        env_vars = {}
        for key, value in os.environ.items():
            if any(keyword in key.upper() for keyword in ['DATABASE', 'DB', 'POSTGRES', 'CUSTOM']):
                # Mask passwords for security
                if 'PASSWORD' in key.upper() or 'PASS' in key.upper():
                    masked_value = value[:4] + '*' * (len(value) - 8) + value[-4:] if len(value) > 8 else '*' * len(value)
                    env_vars[key] = masked_value
                else:
                    env_vars[key] = value

        # Get Django database configuration
        db_config = settings.DATABASES['default']
        django_config = {
            'ENGINE': db_config.get('ENGINE', 'Not set'),
            'NAME': db_config.get('NAME', 'Not set'),
            'USER': db_config.get('USER', 'Not set'),
            'HOST': db_config.get('HOST', 'Not set'),
            'PORT': db_config.get('PORT', 'Not set'),
            'OPTIONS': db_config.get('OPTIONS', {}),
        }

        # Test Django database connection
        django_db_status = "unknown"
        django_db_error = None
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                django_db_status = "connected"
                django_config['version'] = version
        except Exception as e:
            django_db_status = "failed"
            django_db_error = str(e)

        # Test direct connection with correct credentials
        correct_db_status = "unknown"
        correct_db_error = None
        correct_url = "postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=disable"
        try:
            conn = psycopg2.connect(correct_url)
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            correct_db_status = "connected"
        except Exception as e:
            correct_db_status = "failed"
            correct_db_error = str(e)

        # Test direct connection with auto-injected credentials
        auto_db_status = "unknown"
        auto_db_error = None
        auto_url = os.environ.get('DATABASE_URL', 'Not set')
        if auto_url != 'Not set':
            try:
                conn = psycopg2.connect(auto_url)
                cursor = conn.cursor()
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                cursor.close()
                conn.close()
                auto_db_status = "connected"
            except Exception as e:
                auto_db_status = "failed"
                auto_db_error = str(e)
        else:
            auto_db_status = "no_url"

        response_data = {
            'status': 'debug_info',
            'environment_variables': env_vars,
            'django_config': django_config,
            'connection_tests': {
                'django_connection': {
                    'status': django_db_status,
                    'error': django_db_error
                },
                'correct_credentials': {
                    'status': correct_db_status,
                    'error': correct_db_error
                },
                'auto_injected': {
                    'status': auto_db_status,
                    'error': auto_db_error
                }
            }
        }

        logger.info(f"🔍 Database health check completed: {response_data}")
        return JsonResponse(response_data, status=200)

    except Exception as e:
        logger.error(f"❌ Database health check failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")

        return JsonResponse({
            'status': 'error',
            'error': str(e),
            'traceback': traceback.format_exc()
        }, status=500)