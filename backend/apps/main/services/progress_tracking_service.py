"""
Progress Tracking Service

Centralized service for managing real-time progress updates across all workflows
and system operations. Provides clean architecture for performance monitoring
and user feedback.

This service implements a comprehensive progress tracking system that:
1. Tracks workflow execution stages with timing data
2. Provides real-time WebSocket updates to frontend
3. Centralizes performance data collection
4. Supports hierarchical progress tracking (workflow > stage > substage)
5. Integrates with existing benchmarking and monitoring systems
"""

import asyncio
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import logging

from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

logger = logging.getLogger(__name__)


class ProgressStage(Enum):
    """Enumeration of progress stages for consistent tracking"""
    INITIALIZING = "initializing"
    PROCESSING = "processing"
    EXECUTING = "executing"
    COMPLETING = "completing"
    COMPLETED = "completed"
    ERROR = "error"


class ProgressPriority(Enum):
    """Priority levels for progress updates"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ProgressMetrics:
    """Performance metrics for a progress stage"""
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    tokens_used: Optional[int] = None
    cost_estimate: Optional[float] = None
    custom_metrics: Dict[str, Any] = field(default_factory=dict)

    def finalize(self):
        """Finalize metrics when stage completes"""
        if self.end_time is None:
            self.end_time = time.time()
        if self.duration_ms is None:
            self.duration_ms = (self.end_time - self.start_time) * 1000


@dataclass
class ProgressUpdate:
    """Individual progress update with comprehensive metadata"""
    stage_id: str
    stage_name: str
    stage: ProgressStage
    progress_percent: float
    message: str
    timestamp: datetime
    metrics: Optional[ProgressMetrics] = None
    priority: ProgressPriority = ProgressPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)
    substages: List['ProgressUpdate'] = field(default_factory=list)


class ProgressTracker:
    """Individual progress tracker for a specific workflow or operation"""
    
    def __init__(self, tracker_id: str, name: str, user_id: Optional[str] = None,
                 session_id: Optional[str] = None, workflow_type: Optional[str] = None):
        self.tracker_id = tracker_id
        self.name = name
        self.user_id = user_id
        self.session_id = session_id
        self.workflow_type = workflow_type
        
        self.start_time = time.time()
        self.end_time: Optional[float] = None
        self.current_stage: Optional[str] = None
        self.progress_updates: List[ProgressUpdate] = []
        self.stage_metrics: Dict[str, ProgressMetrics] = {}
        self.total_progress: float = 0.0
        self.is_completed = False
        self.has_error = False
        self.error_details: Optional[Dict[str, Any]] = None
        
        # Callbacks for real-time updates
        self.update_callbacks: List[Callable] = []
    
    def add_callback(self, callback: Callable):
        """Add callback for real-time updates"""
        self.update_callbacks.append(callback)
    
    def start_stage(self, stage_id: str, stage_name: str, message: str = "",
                   priority: ProgressPriority = ProgressPriority.NORMAL) -> str:
        """Start a new progress stage"""
        self.current_stage = stage_id
        
        # Initialize metrics for this stage
        self.stage_metrics[stage_id] = ProgressMetrics(start_time=time.time())
        
        # Create progress update
        update = ProgressUpdate(
            stage_id=stage_id,
            stage_name=stage_name,
            stage=ProgressStage.PROCESSING,
            progress_percent=self.total_progress,
            message=message or f"Starting {stage_name}",
            timestamp=datetime.now(timezone.utc),
            priority=priority,
            metadata={
                'tracker_id': self.tracker_id,
                'workflow_type': self.workflow_type,
                'stage_start_time': time.time()
            }
        )
        
        self.progress_updates.append(update)
        self._notify_callbacks(update)
        
        logger.debug(f"Started stage '{stage_name}' for tracker {self.tracker_id}")
        return stage_id
    
    def update_stage(self, stage_id: str, progress_percent: float, message: str = "",
                    custom_metrics: Optional[Dict[str, Any]] = None):
        """Update progress for current stage"""
        if stage_id not in self.stage_metrics:
            logger.warning(f"Stage {stage_id} not found in tracker {self.tracker_id}")
            return
        
        self.total_progress = progress_percent
        
        # Update metrics
        if custom_metrics:
            self.stage_metrics[stage_id].custom_metrics.update(custom_metrics)
        
        # Create progress update
        update = ProgressUpdate(
            stage_id=stage_id,
            stage_name=self._get_stage_name(stage_id),
            stage=ProgressStage.PROCESSING,
            progress_percent=progress_percent,
            message=message,
            timestamp=datetime.now(timezone.utc),
            metrics=self.stage_metrics[stage_id],
            metadata={
                'tracker_id': self.tracker_id,
                'workflow_type': self.workflow_type
            }
        )
        
        self.progress_updates.append(update)
        self._notify_callbacks(update)
    
    def complete_stage(self, stage_id: str, message: str = "", 
                      final_metrics: Optional[Dict[str, Any]] = None):
        """Complete a progress stage"""
        if stage_id not in self.stage_metrics:
            logger.warning(f"Stage {stage_id} not found in tracker {self.tracker_id}")
            return
        
        # Finalize metrics
        metrics = self.stage_metrics[stage_id]
        if final_metrics:
            metrics.custom_metrics.update(final_metrics)
        metrics.finalize()
        
        # Create completion update
        update = ProgressUpdate(
            stage_id=stage_id,
            stage_name=self._get_stage_name(stage_id),
            stage=ProgressStage.COMPLETED,
            progress_percent=self.total_progress,
            message=message or f"Completed {self._get_stage_name(stage_id)}",
            timestamp=datetime.now(timezone.utc),
            metrics=metrics,
            metadata={
                'tracker_id': self.tracker_id,
                'workflow_type': self.workflow_type,
                'stage_duration_ms': metrics.duration_ms
            }
        )
        
        self.progress_updates.append(update)
        self._notify_callbacks(update)
        
        logger.debug(f"Completed stage '{self._get_stage_name(stage_id)}' in {metrics.duration_ms:.2f}ms")
    
    def complete_tracker(self, final_message: str = ""):
        """Complete the entire progress tracker"""
        self.end_time = time.time()
        self.is_completed = True
        self.total_progress = 100.0
        
        total_duration = (self.end_time - self.start_time) * 1000
        
        # Create final completion update
        update = ProgressUpdate(
            stage_id="completion",
            stage_name="Workflow Complete",
            stage=ProgressStage.COMPLETED,
            progress_percent=100.0,
            message=final_message or f"Completed {self.name}",
            timestamp=datetime.now(timezone.utc),
            metadata={
                'tracker_id': self.tracker_id,
                'workflow_type': self.workflow_type,
                'total_duration_ms': total_duration,
                'total_stages': len(self.stage_metrics)
            }
        )
        
        self.progress_updates.append(update)
        self._notify_callbacks(update)
        
        logger.info(f"Completed tracker '{self.name}' in {total_duration:.2f}ms")
    
    def error_stage(self, stage_id: str, error_message: str, error_details: Optional[Dict[str, Any]] = None):
        """Mark a stage as errored"""
        self.has_error = True
        self.error_details = error_details or {}
        
        # Finalize current stage metrics if exists
        if stage_id in self.stage_metrics:
            self.stage_metrics[stage_id].finalize()
        
        # Create error update
        update = ProgressUpdate(
            stage_id=stage_id,
            stage_name=self._get_stage_name(stage_id),
            stage=ProgressStage.ERROR,
            progress_percent=self.total_progress,
            message=error_message,
            timestamp=datetime.now(timezone.utc),
            priority=ProgressPriority.CRITICAL,
            metadata={
                'tracker_id': self.tracker_id,
                'workflow_type': self.workflow_type,
                'error_details': error_details
            }
        )
        
        self.progress_updates.append(update)
        self._notify_callbacks(update)
        
        logger.error(f"Error in stage '{self._get_stage_name(stage_id)}': {error_message}")
    
    def _get_stage_name(self, stage_id: str) -> str:
        """Get human-readable stage name"""
        # Try to find the stage name from previous updates
        for update in reversed(self.progress_updates):
            if update.stage_id == stage_id:
                return update.stage_name
        return stage_id.replace('_', ' ').title()
    
    def _notify_callbacks(self, update: ProgressUpdate):
        """Notify all registered callbacks of progress update"""
        for callback in self.update_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    # Handle async callbacks
                    asyncio.create_task(callback(update))
                else:
                    callback(update)
            except Exception as e:
                logger.warning(f"Error in progress callback: {e}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive summary of progress tracker"""
        total_duration = None
        if self.end_time:
            total_duration = (self.end_time - self.start_time) * 1000
        
        return {
            'tracker_id': self.tracker_id,
            'name': self.name,
            'workflow_type': self.workflow_type,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'total_duration_ms': total_duration,
            'total_progress': self.total_progress,
            'is_completed': self.is_completed,
            'has_error': self.has_error,
            'error_details': self.error_details,
            'total_stages': len(self.stage_metrics),
            'total_updates': len(self.progress_updates),
            'stage_summaries': {
                stage_id: {
                    'duration_ms': metrics.duration_ms,
                    'tokens_used': metrics.tokens_used,
                    'cost_estimate': metrics.cost_estimate,
                    'custom_metrics': metrics.custom_metrics
                }
                for stage_id, metrics in self.stage_metrics.items()
            }
        }


class ProgressTrackingService:
    """
    Centralized service for managing progress tracking across the application.
    
    This service provides:
    - Centralized progress tracker management
    - Real-time WebSocket updates
    - Performance data aggregation
    - Integration with existing monitoring systems
    """
    
    _instance: Optional['ProgressTrackingService'] = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        self.active_trackers: Dict[str, ProgressTracker] = {}
        self.completed_trackers: Dict[str, ProgressTracker] = {}
        self.channel_layer = get_channel_layer()
        
        # Performance aggregation
        self.performance_stats = {
            'total_workflows': 0,
            'average_duration_ms': 0.0,
            'success_rate': 0.0,
            'error_rate': 0.0,
            'stage_performance': {}
        }
    
    @classmethod
    async def get_instance(cls) -> 'ProgressTrackingService':
        """Get singleton instance of ProgressTrackingService"""
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def create_tracker(self, name: str, user_id: Optional[str] = None,
                      session_id: Optional[str] = None, workflow_type: Optional[str] = None,
                      tracker_id: Optional[str] = None) -> ProgressTracker:
        """Create a new progress tracker"""
        if tracker_id is None:
            tracker_id = str(uuid.uuid4())
        
        tracker = ProgressTracker(
            tracker_id=tracker_id,
            name=name,
            user_id=user_id,
            session_id=session_id,
            workflow_type=workflow_type
        )
        
        # Add WebSocket callback for real-time updates
        tracker.add_callback(self._websocket_callback)
        
        self.active_trackers[tracker_id] = tracker
        
        logger.info(f"Created progress tracker '{name}' with ID {tracker_id}")
        return tracker
    
    async def _websocket_callback(self, update: ProgressUpdate):
        """Send progress updates via WebSocket"""
        if not self.channel_layer:
            return
        
        try:
            # Send to user-specific group if user_id is available
            tracker = self.active_trackers.get(update.metadata.get('tracker_id'))
            if tracker and tracker.user_id:
                group_name = f"user_session_group_{tracker.user_id}"
                await self.channel_layer.group_send(
                    group_name,
                    {
                        'type': 'progress_update',
                        'data': {
                            'type': 'progress_update',
                            'tracker_id': tracker.tracker_id,
                            'stage_id': update.stage_id,
                            'stage_name': update.stage_name,
                            'stage': update.stage.value,
                            'progress_percent': update.progress_percent,
                            'message': update.message,
                            'timestamp': update.timestamp.isoformat(),
                            'priority': update.priority.value,
                            'workflow_type': tracker.workflow_type,
                            'metrics': {
                                'duration_ms': update.metrics.duration_ms if update.metrics else None,
                                'tokens_used': update.metrics.tokens_used if update.metrics else None,
                                'cost_estimate': update.metrics.cost_estimate if update.metrics else None,
                                'custom_metrics': update.metrics.custom_metrics if update.metrics else {}
                            } if update.metrics else None
                        }
                    }
                )
            
            # Also send to monitoring dashboard
            await self.channel_layer.group_send(
                "progress_monitoring",
                {
                    'type': 'progress_monitoring_update',
                    'data': {
                        'type': 'progress_monitoring_update',
                        'tracker_summary': tracker.get_summary() if tracker else None,
                        'latest_update': {
                            'stage_id': update.stage_id,
                            'stage_name': update.stage_name,
                            'stage': update.stage.value,
                            'progress_percent': update.progress_percent,
                            'message': update.message,
                            'timestamp': update.timestamp.isoformat(),
                            'priority': update.priority.value
                        }
                    }
                }
            )
            
        except Exception as e:
            logger.warning(f"Error sending progress update via WebSocket: {e}")
    
    def get_tracker(self, tracker_id: str) -> Optional[ProgressTracker]:
        """Get active or completed tracker by ID"""
        return self.active_trackers.get(tracker_id) or self.completed_trackers.get(tracker_id)
    
    def complete_tracker(self, tracker_id: str, final_message: str = ""):
        """Complete a tracker and move it to completed trackers"""
        if tracker_id in self.active_trackers:
            tracker = self.active_trackers[tracker_id]
            tracker.complete_tracker(final_message)
            
            # Move to completed trackers
            self.completed_trackers[tracker_id] = tracker
            del self.active_trackers[tracker_id]
            
            # Update performance stats
            self._update_performance_stats(tracker)
            
            logger.info(f"Completed and archived tracker {tracker_id}")
    
    def _update_performance_stats(self, tracker: ProgressTracker):
        """Update aggregated performance statistics"""
        self.performance_stats['total_workflows'] += 1
        
        if tracker.end_time and tracker.start_time:
            duration = (tracker.end_time - tracker.start_time) * 1000
            
            # Update average duration
            total = self.performance_stats['total_workflows']
            current_avg = self.performance_stats['average_duration_ms']
            self.performance_stats['average_duration_ms'] = (
                (current_avg * (total - 1) + duration) / total
            )
        
        # Update success/error rates
        if tracker.has_error:
            error_count = self.performance_stats['total_workflows'] * self.performance_stats['error_rate'] + 1
        else:
            error_count = self.performance_stats['total_workflows'] * self.performance_stats['error_rate']
        
        self.performance_stats['error_rate'] = error_count / self.performance_stats['total_workflows']
        self.performance_stats['success_rate'] = 1.0 - self.performance_stats['error_rate']
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        return {
            'active_trackers': len(self.active_trackers),
            'completed_trackers': len(self.completed_trackers),
            'performance_stats': self.performance_stats,
            'active_tracker_summaries': [
                tracker.get_summary() for tracker in self.active_trackers.values()
            ]
        }
