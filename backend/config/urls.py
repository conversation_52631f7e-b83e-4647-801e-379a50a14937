"""config URL Configuration"""
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.staticfiles.urls import staticfiles_urlpatterns

# Import the custom admin site INSTANCE
from config.admin import admin_site # Import custom admin instance

# Import health check views
from apps.main.views.health_check import health_check, debug_db_health_check
import logging

# Set up logging for URL debugging
logger = logging.getLogger(__name__)

def debug_health_check(request):
    """Debug wrapper for health check to see if URLs are working"""
    logger.info(f"🔗 URL ROUTING: Health check URL accessed - {request.method} {request.path}")
    logger.info(f"🔗 URL ROUTING: Request headers: {dict(request.headers)}")
    return health_check(request)

urlpatterns = [
    # Health check endpoints for DigitalOcean App Platform (both with and without trailing slash)
    path('health/', debug_health_check, name='health_check'),
    path('health', debug_health_check, name='health_check_no_slash'),

    # Debug database health check endpoint
    path('debug/db-health/', debug_db_health_check, name='debug_db_health_check'),
    
    # Admin and main app URLs
    path('admin/', admin_site.urls),  # Use the imported custom admin site instance (includes admin_tools URLs now)
    path('', include('apps.main.urls')),  # Main app URLs (already have /api/ prefixes)
    path('coverage/', include('apps.coverage_dashboard.urls')),
    # path('admin-tools/', include('apps.admin_tools.urls')), # Removed - Integrated into admin_site
    # Add other URL patterns here
]

# Explicitly add staticfiles patterns for development
urlpatterns += staticfiles_urlpatterns()

# Serve static files in development
if settings.DEBUG:
    # Add static files from STATIC_ROOT (for admin)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # Also serve from STATICFILES_DIRS if specified
    for staticfiles_dir in settings.STATICFILES_DIRS:
        urlpatterns += static(settings.STATIC_URL, document_root=staticfiles_dir)
    
    # Media files (if applicable)
    if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_ROOT:
        urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
