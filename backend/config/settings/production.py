"""
Production settings for DigitalOcean App Platform deployment.
Optimized for managed PostgreSQL and Redis services.
"""
import os
from .base import *

# Security settings
DEBUG = False
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'

# Hosts configuration - DigitalOcean App Platform
ALLOWED_HOSTS = [
    '.ondigitalocean.app',
    'localhost',
    '127.0.0.1',
]

# Add custom domain if provided
CUSTOM_DOMAIN = os.environ.get('CUSTOM_DOMAIN')
if CUSTOM_DOMAIN:
    ALLOWED_HOSTS.append(CUSTOM_DOMAIN)
    ALLOWED_HOSTS.append(f'www.{CUSTOM_DOMAIN}')

# Secret key from environment (required)
SECRET_KEY = os.environ.get('SECRET_KEY')
if not SECRET_KEY:
    # For build-time operations, allow a temporary secret key
    if os.environ.get('BUILD_TIME_SECRET_KEY'):
        SECRET_KEY = os.environ.get('BUILD_TIME_SECRET_KEY')
    else:
        raise ValueError("SECRET_KEY environment variable is required in production")

# Database configuration - Secure production setup with auto-injection
DATABASE_URL = os.environ.get('DATABASE_URL')
if not DATABASE_URL:
    raise ValueError("DATABASE_URL environment variable is required (should be auto-injected by Digital Ocean)")

print(f"🔍 DEBUG: Using DATABASE_URL: {DATABASE_URL[:50]}...") # Only show first 50 chars for security

# Parse DATABASE_URL and configure with working SSL settings
# Following Digital Ocean tutorial approach exactly
# Use direct database parameters with existing managed database

# Debug: Check if environment variables are available
DB_HOST = os.environ.get('DB_HOST')
DB_PORT = os.environ.get('DB_PORT')
DB_PASSWORD = os.environ.get('DB_PASSWORD')

print(f"🔍 DEBUG Database Environment Variables:")
print(f"   DB_HOST: {DB_HOST}")
print(f"   DB_PORT: {DB_PORT}")
print(f"   DB_PASSWORD: {'***' if DB_PASSWORD else 'None'}")

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql_psycopg2',
        'NAME': 'defaultdb',  # Use existing default database
        'USER': 'doadmin',    # Use existing admin user
        'PASSWORD': DB_PASSWORD,
        'HOST': DB_HOST,
        'PORT': DB_PORT,
    }
}

# STANDARD DJANGO CONFIGURATION: Follow Digital Ocean tutorial approach
# Use standard Django database configuration without explicit SSL options
# Let Django and PostgreSQL handle SSL automatically with managed database
DATABASES['default']['OPTIONS'] = {
    'connect_timeout': 30,
}

# Redis configuration - Managed Redis
REDIS_URL = os.environ.get('REDIS_URL')
if not REDIS_URL:
    # Fallback to local Redis for development/testing
    REDIS_URL = 'redis://127.0.0.1:6379/1'
    print("⚠️  Using local Redis fallback - ensure Redis is available")

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 20,
                'retry_on_timeout': True,
            },
        },
        'KEY_PREFIX': 'goali_prod',
        'TIMEOUT': 300,
    }
}

# Session configuration
SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'default'
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_HTTPONLY = True
SESSION_COOKIE_SAMESITE = 'Lax'
SESSION_COOKIE_AGE = 3600

# CSRF security
CSRF_COOKIE_SECURE = True
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'
CSRF_TRUSTED_ORIGINS = [
    'https://*.ondigitalocean.app',
]

# Add custom domain to CSRF trusted origins
if CUSTOM_DOMAIN:
    CSRF_TRUSTED_ORIGINS.extend([
        f'https://{CUSTOM_DOMAIN}',
        f'https://www.{CUSTOM_DOMAIN}',
    ])

# Security headers
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Celery configuration - Managed Redis
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', REDIS_URL)
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', REDIS_URL)
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TIMEZONE = 'UTC'
CELERY_ENABLE_UTC = True
CELERY_TASK_TRACK_STARTED = True
CELERY_TASK_TIME_LIMIT = 300
CELERY_TASK_SOFT_TIME_LIMIT = 240
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 100

# Channels configuration - Managed Redis
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [REDIS_URL],
            'capacity': 1500,
            'expiry': 10,
        },
    },
}

# CORS configuration
CORS_ALLOWED_ORIGINS = [
    'https://*.ondigitalocean.app',
]

# Add custom domain to CORS
if CUSTOM_DOMAIN:
    CORS_ALLOWED_ORIGINS.extend([
        f'https://{CUSTOM_DOMAIN}',
        f'https://www.{CUSTOM_DOMAIN}',
    ])

CORS_ALLOW_CREDENTIALS = True

# Static files configuration
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Use DigitalOcean Spaces for static files if configured
SPACES_ACCESS_KEY = os.environ.get('SPACES_ACCESS_KEY')
SPACES_SECRET_KEY = os.environ.get('SPACES_SECRET_KEY')
SPACES_BUCKET_NAME = os.environ.get('SPACES_BUCKET_NAME')
SPACES_REGION = os.environ.get('SPACES_REGION')
SPACES_CDN_ENDPOINT = os.environ.get('SPACES_CDN_ENDPOINT')

if all([SPACES_ACCESS_KEY, SPACES_SECRET_KEY, SPACES_BUCKET_NAME, SPACES_REGION]):
    # Configure for DigitalOcean Spaces
    STATIC_URL = f"https://{SPACES_CDN_ENDPOINT}/" if SPACES_CDN_ENDPOINT else f"https://{SPACES_BUCKET_NAME}.{SPACES_REGION}.digitaloceanspaces.com/"
    
    # Use django-storages for Spaces
    DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
    STATICFILES_STORAGE = 'storages.backends.s3boto3.S3StaticStorage'
    
    # AWS S3 settings for DigitalOcean Spaces compatibility
    AWS_ACCESS_KEY_ID = SPACES_ACCESS_KEY
    AWS_SECRET_ACCESS_KEY = SPACES_SECRET_KEY
    AWS_STORAGE_BUCKET_NAME = SPACES_BUCKET_NAME
    AWS_S3_ENDPOINT_URL = f"https://{SPACES_REGION}.digitaloceanspaces.com"
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
    AWS_LOCATION = 'static'
    AWS_DEFAULT_ACL = 'public-read'
    AWS_S3_CUSTOM_DOMAIN = SPACES_CDN_ENDPOINT

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'apps': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# LLM configuration
MISTRAL_API_KEY = os.environ.get('MISTRAL_API_KEY')
DEFAULT_LLM_MODEL_NAME = os.environ.get('DEFAULT_LLM_MODEL_NAME', 'mistral-small-latest')
DEFAULT_LLM_TEMPERATURE = float(os.environ.get('DEFAULT_LLM_TEMPERATURE', '0.7'))

# Application execution mode
GOALI_DEFAULT_EXECUTION_MODE = os.environ.get('GOALI_DEFAULT_EXECUTION_MODE', 'production')

# Performance optimizations
DATA_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
FILE_UPLOAD_MAX_MEMORY_SIZE = 5 * 1024 * 1024  # 5MB
DATA_UPLOAD_MAX_NUMBER_FIELDS = 1000

# API throttling
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    }
}

# Email configuration
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
DEFAULT_FROM_EMAIL = '<EMAIL>'

# File upload settings
FILE_UPLOAD_PERMISSIONS = 0o644
FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o755

# Monitoring
ENABLE_PERFORMANCE_MONITORING = True
ENABLE_ERROR_TRACKING = True

# Validate required environment variables
required_env_vars = ['SECRET_KEY']
missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

if missing_vars:
    raise ValueError(f"Missing required environment variables: {missing_vars}")

print("✅ Production settings loaded successfully")
print(f"   Database: {'Managed PostgreSQL' if DATABASE_URL else 'Local PostgreSQL'}")
print(f"   Cache: {'Managed Redis' if 'digitalocean' in REDIS_URL else 'Local Redis'}")
print(f"   Static Files: {'DigitalOcean Spaces' if SPACES_ACCESS_KEY else 'Local Storage'}")
print(f"   Debug Mode: {DEBUG}")