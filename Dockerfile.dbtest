# Bare-bones Docker container for testing Digital Ocean managed database connection
FROM ubuntu:22.04

# Install minimal dependencies for PostgreSQL client
RUN apt-get update && apt-get install -y \
    postgresql-client \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create a simple test script
COPY test-db-connection.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/test-db-connection.sh

# Expose port for health checks
EXPOSE 8080

# Run the test script
CMD ["/usr/local/bin/test-db-connection.sh"]
