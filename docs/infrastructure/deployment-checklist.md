# DigitalOcean App Platform Deployment Checklist

## Pre-Deployment Setup

### 1. GitHub Repository Secrets
Configure the following secrets in your GitHub repository (Settings → Secrets and variables → Actions):

- [ ] `DIGITALOCEAN_ACCESS_TOKEN` - Your DigitalOcean API token
- [ ] `DJANGO_SECRET_KEY` - Generated Django secret key
- [ ] `MISTRAL_API_KEY` - Your Mistral AI API key
- [ ] `ADMIN_PASSWORD` - Strong password for Django admin
- [ ] `SPACES_ACCESS_KEY` - DigitalOcean Spaces access key (optional)
- [ ] `SPACES_SECRET_KEY` - DigitalOcean Spaces secret key (optional)

### 2. Update Configuration Files
- [ ] Update GitHub repository URL in `.do/app.yaml`
- [ ] Verify environment variables in `.do/app.yaml`
- [ ] Update Spaces configuration if using static file storage

### 3. Local Testing
- [ ] Test health check endpoint locally: `curl http://localhost:8000/health/`
- [ ] Verify production settings work: `DJANGO_SETTINGS_MODULE=config.settings.production python manage.py check`
- [ ] Test Docker builds: `docker build -f backend/Dockerfile.production .`

## Deployment Process

### 4. Initial Deployment
- [ ] Push code to main branch to trigger GitHub Actions
- [ ] Monitor deployment in GitHub Actions tab
- [ ] Verify app creation in DigitalOcean dashboard

### 5. Post-Deployment Verification
- [ ] Check app status: `doctl apps list`
- [ ] Verify health endpoint: `curl https://your-app-url.ondigitalocean.app/health/`
- [ ] Test admin panel: `https://your-app-url.ondigitalocean.app/admin/`
- [ ] Verify database connectivity
- [ ] Test Celery worker functionality

### 6. Database Setup
- [ ] Run migrations: `doctl apps run $APP_ID --component web -- python manage.py migrate`
- [ ] Create superuser: `doctl apps run $APP_ID --component web -- python manage.py createsuperuser`
- [ ] Load initial data if needed

## Production Monitoring

### 7. Health Checks
- [ ] Verify health endpoint returns 200 status
- [ ] Check database connectivity in health response
- [ ] Verify Redis/cache connectivity in health response

### 8. Performance Monitoring
- [ ] Monitor app metrics in DigitalOcean dashboard
- [ ] Check resource usage (CPU, memory)
- [ ] Verify autoscaling configuration

### 9. Security Verification
- [ ] Confirm HTTPS is working
- [ ] Verify CORS settings
- [ ] Check security headers
- [ ] Test authentication flows

## Troubleshooting

### Common Issues
- [ ] Build failures: Check GitHub Actions logs
- [ ] Database connection: Verify DATABASE_URL environment variable
- [ ] Static files: Check Spaces configuration
- [ ] Health check failures: Review application logs

### Debug Commands
```bash
# Get app ID
APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')

# Check logs
doctl apps logs $APP_ID --type=run --component web

# Run Django shell
doctl apps run $APP_ID --component web -- python manage.py shell

# Test database
doctl apps run $APP_ID --component web -- python manage.py dbshell
```

## Cost Optimization

### 10. Resource Monitoring
- [ ] Monitor monthly costs in DigitalOcean billing
- [ ] Review instance sizes based on actual usage
- [ ] Optimize database queries for better performance
- [ ] Implement caching strategies

### 11. Scaling Configuration
- [ ] Set appropriate autoscaling thresholds
- [ ] Monitor scaling events
- [ ] Adjust instance sizes as needed

## Maintenance

### 12. Regular Tasks
- [ ] Update dependencies regularly
- [ ] Monitor security advisories
- [ ] Review and rotate secrets
- [ ] Backup verification
- [ ] Performance optimization

### 13. Disaster Recovery
- [ ] Test database restore procedures
- [ ] Verify backup retention policies
- [ ] Document rollback procedures
- [ ] Test failover scenarios

---

## Quick Reference

### Essential Commands
```bash
# Deploy app
git push origin main

# Check app status
doctl apps list

# View logs
doctl apps logs $APP_ID --type=run --component web --follow

# Run migrations
doctl apps run $APP_ID --component web -- python manage.py migrate

# Access Django shell
doctl apps run $APP_ID --component web -- python manage.py shell
```

### Important URLs
- App Dashboard: https://cloud.digitalocean.com/apps
- Health Check: https://your-app-url.ondigitalocean.app/health/
- Admin Panel: https://your-app-url.ondigitalocean.app/admin/
- API Documentation: https://your-app-url.ondigitalocean.app/api/

---

**Deployment Status**: ✅ Ready for production deployment

All necessary files and configurations have been created. Follow this checklist to ensure a successful deployment to DigitalOcean App Platform.