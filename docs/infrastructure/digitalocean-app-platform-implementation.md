# DigitalOcean App Platform Implementation Plan for Goali

## Overview
This document provides a comprehensive implementation plan for deploying the Goali application to DigitalOcean App Platform using the recommended architecture: App Platform for compute services with managed PostgreSQL and Redis instances.

**Status**: ✅ **IMPLEMENTATION COMPLETE** - All required files and configurations have been created.

## Prerequisites
- DigitalOcean account with billing enabled
- GitHub repository with Goali codebase
- Docker knowledge for containerization
- Basic understanding of environment variables and secrets management

## ✅ Implementation Status

### Completed Components
- [x] Health check endpoint (`/health/`)
- [x] Production Django settings (`config.settings.production`)
- [x] Production Dockerfile (`Dockerfile.production`)
- [x] Celery worker Dockerfile (`Dockerfile.celery`)
- [x] App Platform specification (`.do/app.yaml`)
- [x] GitHub Actions deployment workflow (`.github/workflows/do-deploy.yml`)
- [x] Production requirements (`requirements.production.txt`)
- [x] Deployment guide documentation

### Ready for Deployment
The Goali application is now ready for deployment to DigitalOcean App Platform with:
- Managed PostgreSQL and Redis databases
- Automatic scaling capabilities
- Health monitoring
- CI/CD pipeline via GitHub Actions

## Phase 1: Infrastructure Setup

### 1.1 Create Managed Database Services

#### PostgreSQL Database Setup
```bash
# Create managed PostgreSQL cluster
doctl databases create goali-postgres \
  --engine postgres \
  --version 15 \
  --size db-s-1vcpu-1gb \
  --region nyc3 \
  --num-nodes 1

# Create application database
doctl databases db create <cluster-id> goali_production

# Create database user
doctl databases user create <cluster-id> goali_app_user
```

**Configuration Requirements:**
- Engine: PostgreSQL 15
- Size: db-s-1vcpu-1gb (1GB RAM, 1 vCPU, 10GB storage)
- Region: nyc3 (or closest to your users)
- Backup retention: 7 days (default)
- Connection pooling: Enable PgBouncer

#### Redis Database Setup
```bash
# Create managed Redis cluster
doctl databases create goali-redis \
  --engine redis \
  --version 7 \
  --size db-s-1vcpu-1gb \
  --region nyc3 \
  --num-nodes 1
```

**Configuration Requirements:**
- Engine: Redis 7
- Size: db-s-1vcpu-1gb (1GB RAM)
- Region: nyc3 (same as PostgreSQL)
- Eviction policy: allkeys-lru

### 1.2 Create Spaces for Static Assets
```bash
# Create Spaces bucket for static files
doctl compute cdn create goali-static-assets \
  --origin goali-static.nyc3.digitaloceanspaces.com
```

## Phase 2: Application Containerization

### 2.1 Production Dockerfile
Create `Dockerfile.production` in the backend directory:

```dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=config.settings.production

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project
COPY . .

# Collect static files
RUN python manage.py collectstatic --noinput

# Create non-root user
RUN adduser --disabled-password --gecos '' appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8000

# Run application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "2", "--worker-class", "uvicorn.workers.UvicornWorker", "config.asgi:application"]
```

### 2.2 Production Settings
Create `config/settings/production.py`:

```python
from .base import *
import os
import dj_database_url

# Security settings
DEBUG = False
ALLOWED_HOSTS = ['.ondigitalocean.app', 'your-domain.com']

# Database configuration
DATABASES = {
    'default': dj_database_url.parse(
        os.environ.get('DATABASE_URL'),
        conn_max_age=600,
        conn_health_checks=True,
    )
}

# Redis configuration
REDIS_URL = os.environ.get('REDIS_URL')
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': REDIS_URL,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Celery configuration
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL

# Static files configuration
STATIC_URL = f"https://{os.environ.get('SPACES_CDN_ENDPOINT')}/"
STATICFILES_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'

# AWS S3 settings for DigitalOcean Spaces
AWS_ACCESS_KEY_ID = os.environ.get('SPACES_ACCESS_KEY')
AWS_SECRET_ACCESS_KEY = os.environ.get('SPACES_SECRET_KEY')
AWS_STORAGE_BUCKET_NAME = os.environ.get('SPACES_BUCKET_NAME')
AWS_S3_ENDPOINT_URL = f"https://{os.environ.get('SPACES_REGION')}.digitaloceanspaces.com"
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',
}

# Security headers
SECURE_SSL_REDIRECT = True
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}
```

### 2.3 App Specification File
Create `.do/app.yaml`:

```yaml
name: goali-app
services:
- name: web
  source_dir: /backend
  github:
    repo: your-username/goali
    branch: main
    deploy_on_push: true
  dockerfile_path: Dockerfile.production
  http_port: 8000
  instance_count: 1
  instance_size_slug: professional-xs
  routes:
  - path: /
  health_check:
    http_path: /health/
    initial_delay_seconds: 30
    period_seconds: 10
    timeout_seconds: 5
    success_threshold: 1
    failure_threshold: 3
  envs:
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.production
  - key: DATABASE_URL
    value: ${goali-postgres.DATABASE_URL}
  - key: REDIS_URL
    value: ${goali-redis.DATABASE_URL}
  - key: SPACES_ACCESS_KEY
    value: ${SPACES_ACCESS_KEY}
    type: SECRET
  - key: SPACES_SECRET_KEY
    value: ${SPACES_SECRET_KEY}
    type: SECRET
  - key: SPACES_BUCKET_NAME
    value: goali-static-assets
  - key: SPACES_REGION
    value: nyc3
  - key: SPACES_CDN_ENDPOINT
    value: goali-static-assets.nyc3.cdn.digitaloceanspaces.com
  - key: SECRET_KEY
    value: ${DJANGO_SECRET_KEY}
    type: SECRET

- name: celery-worker
  source_dir: /backend
  github:
    repo: your-username/goali
    branch: main
    deploy_on_push: true
  dockerfile_path: Dockerfile.celery
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.production
  - key: DATABASE_URL
    value: ${goali-postgres.DATABASE_URL}
  - key: REDIS_URL
    value: ${goali-redis.DATABASE_URL}
  - key: SECRET_KEY
    value: ${DJANGO_SECRET_KEY}
    type: SECRET

databases:
- name: goali-postgres
  engine: PG
  version: "15"
  size: db-s-1vcpu-1gb
  num_nodes: 1
  
- name: goali-redis
  engine: REDIS
  version: "7"
  size: db-s-1vcpu-1gb
  num_nodes: 1

static_sites:
- name: frontend
  source_dir: /frontend
  github:
    repo: your-username/goali
    branch: main
    deploy_on_push: true
  build_command: npm run build
  output_dir: dist
  routes:
  - path: /
  envs:
  - key: VITE_API_URL
    value: https://goali-app-web.ondigitalocean.app
```

### 2.4 Celery Worker Dockerfile
Create `Dockerfile.celery`:

```dockerfile
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=config.settings.production

WORKDIR /app

RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

RUN adduser --disabled-password --gecos '' celeryuser
RUN chown -R celeryuser:celeryuser /app
USER celeryuser

CMD ["celery", "-A", "config", "worker", "--loglevel=info", "--concurrency=2"]
```

## Phase 3: Deployment Process

### 3.1 Pre-deployment Checklist
- [ ] Database migrations are ready
- [ ] Static files collection works
- [ ] Environment variables are configured
- [ ] Health check endpoint is implemented
- [ ] Logging is properly configured
- [ ] Error tracking is set up (Sentry recommended)

### 3.2 Health Check Endpoint
Add to `apps/main/views.py`:

```python
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
import redis

def health_check(request):
    """Health check endpoint for App Platform"""
    try:
        # Check database connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # Check Redis connection
        cache.set('health_check', 'ok', 30)
        cache.get('health_check')
        
        return JsonResponse({
            'status': 'healthy',
            'database': 'connected',
            'cache': 'connected'
        })
    except Exception as e:
        return JsonResponse({
            'status': 'unhealthy',
            'error': str(e)
        }, status=503)
```

### 3.3 Deployment Commands
```bash
# Deploy using doctl
doctl apps create --spec .do/app.yaml

# Or deploy via GitHub integration
# Push to main branch will trigger automatic deployment

# Monitor deployment
doctl apps list
doctl apps get <app-id>
doctl apps logs <app-id> --type=deploy
```

## Phase 4: Post-Deployment Configuration

### 4.1 Database Migration
```bash
# Run initial migrations
doctl apps run <app-id> --component web -- python manage.py migrate

# Create superuser
doctl apps run <app-id> --component web -- python manage.py createsuperuser
```

### 4.2 Domain Configuration
```bash
# Add custom domain
doctl apps update <app-id> --spec .do/app-with-domain.yaml

# Configure DNS
# Add CNAME record: www.yourdomain.com -> goali-app.ondigitalocean.app
```

### 4.3 SSL Certificate
App Platform automatically provisions SSL certificates for custom domains.

## Phase 5: Monitoring and Maintenance

### 5.1 Application Monitoring
- Enable App Platform built-in monitoring
- Set up log forwarding to external service (optional)
- Configure alerting for critical metrics

### 5.2 Database Monitoring
```bash
# Monitor database performance
doctl databases metrics <database-id>

# View connection stats
doctl databases connection <database-id>
```

### 5.3 Backup Strategy
- Database backups are automatic (7-day retention)
- Application code is backed up in Git
- Static assets are stored in Spaces with versioning

## Phase 6: Scaling Considerations

### 6.1 Horizontal Scaling
```yaml
# Update app.yaml for scaling
services:
- name: web
  instance_count: 2  # Scale to 2 instances
  autoscaling:
    min_instance_count: 1
    max_instance_count: 5
    metrics:
      cpu:
        percent: 70
```

### 6.2 Database Scaling
```bash
# Upgrade database size
doctl databases resize <database-id> --size db-s-2vcpu-2gb
```

## Cost Optimization Tips

1. **Start Small**: Begin with basic-xxs for Celery workers
2. **Monitor Usage**: Use App Platform metrics to optimize instance sizes
3. **Database Optimization**: Implement connection pooling and query optimization
4. **CDN Usage**: Leverage Spaces CDN for static assets
5. **Autoscaling**: Configure appropriate scaling thresholds

## Security Best Practices

1. **Environment Variables**: Use App Platform secrets for sensitive data
2. **Database Security**: Enable SSL connections and firewall rules
3. **Application Security**: Implement proper authentication and authorization
4. **Regular Updates**: Keep dependencies and base images updated
5. **Monitoring**: Set up security monitoring and alerting

## Troubleshooting Guide

### Common Issues:
1. **Database Connection Errors**: Check connection string and firewall rules
2. **Static Files Not Loading**: Verify Spaces configuration and CORS settings
3. **Celery Tasks Not Processing**: Check Redis connection and worker logs
4. **High Memory Usage**: Monitor and optimize Django queries
5. **Slow Response Times**: Implement caching and database indexing

### Debugging Commands:
```bash
# View application logs
doctl apps logs <app-id> --type=run --component web

# Access application shell
doctl apps run <app-id> --component web -- python manage.py shell

# Check database connectivity
doctl apps run <app-id> --component web -- python manage.py dbshell
```

## Migration from Development

### Data Migration:
1. Export development data: `python manage.py dumpdata > data.json`
2. Upload to production: `doctl apps run <app-id> --component web -- python manage.py loaddata data.json`

### DNS Cutover:
1. Test production environment thoroughly
2. Update DNS records to point to App Platform
3. Monitor for issues and have rollback plan ready

## Estimated Timeline

- **Phase 1-2**: 2-3 days (Infrastructure setup and containerization)
- **Phase 3**: 1 day (Initial deployment)
- **Phase 4-5**: 1-2 days (Configuration and monitoring setup)
- **Phase 6**: Ongoing (Optimization and scaling)

**Total Implementation Time: 5-7 days**

## Success Metrics

- Application successfully deployed and accessible
- Database connections stable and performant
- Static assets loading from CDN
- Celery tasks processing correctly
- Health checks passing consistently
- Response times under 500ms for 95th percentile
- Zero downtime during normal operations

This implementation plan provides a comprehensive roadmap for deploying Goali to DigitalOcean App Platform with managed services, ensuring scalability, reliability, and cost-effectiveness.