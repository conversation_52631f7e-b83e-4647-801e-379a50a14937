# DigitalOcean App Platform Deployment Guide for Goali

## ✅ Status: Ready for Deployment

The app specification has been **validated successfully** with DigitalOcean App Platform!

- **Estimated Monthly Cost**: $17/month
- **App Name**: goali-app (available)
- **Architecture**: Web service + Celery worker + Managed databases + Static frontend

## Quick Start

This guide provides step-by-step instructions for deploying the Goali application to DigitalOcean App Platform using the optimized configuration.

## Prerequisites

1. **DigitalOcean Account**: With billing enabled
2. **GitHub Repository**: Goali codebase with deployment files
3. **doctl CLI**: DigitalOcean command-line tool
4. **Environment Secrets**: Required API keys and configuration

## Step 1: Install and Configure doctl

```bash
# Install doctl (macOS)
brew install doctl

# Install doctl (Linux)
cd ~
wget https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz
tar xf ~/doctl-1.104.0-linux-amd64.tar.gz
sudo mv ~/doctl /usr/local/bin

# Authenticate with DigitalOcean
doctl auth init
```

## Step 2: Set Up Required Secrets

### GitHub Repository Secrets

Navigate to your GitHub repository → Settings → Secrets and variables → Actions, and add:

```
DIGITALOCEAN_ACCESS_TOKEN=your_do_token_here
DJANGO_SECRET_KEY=your_secret_key_here
MISTRAL_API_KEY=your_mistral_api_key_here
ADMIN_PASSWORD=your_admin_password_here
SPACES_ACCESS_KEY=your_spaces_access_key_here
SPACES_SECRET_KEY=your_spaces_secret_key_here
```

### Generate Required Keys

```bash
# Generate Django Secret Key
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"

# Generate strong admin password
openssl rand -base64 32
```

## Step 3: Create DigitalOcean Spaces (Optional)

For static file storage:

```bash
# Create Spaces bucket (via DigitalOcean web console)
# Go to: https://cloud.digitalocean.com/spaces
# Click "Create a Space"
# Name: goali-static-assets
# Region: NYC3
# CDN: Enable

# Alternative: Use doctl to list existing spaces
doctl compute space list

# Create CDN endpoint for existing space
doctl compute cdn create --origin goali-static-assets.nyc3.digitaloceanspaces.com

# Generate Spaces API keys (via web console)
# Go to: https://cloud.digitalocean.com/account/api/spaces
# Generate new key pair for your application
```

## Step 4: Update App Configuration

Edit `.do/app.yaml` and update:

```yaml
# Update GitHub repository
github:
  repo: your-username/goali  # Replace with your repo
  branch: main
  deploy_on_push: true

# Update Spaces configuration (if using)
- key: SPACES_BUCKET_NAME
  value: your-bucket-name
- key: SPACES_CDN_ENDPOINT
  value: your-cdn-endpoint
```

## Step 5: Deploy Using GitHub Actions

### Automatic Deployment

1. Push to `main` branch:
```bash
git add .
git commit -m "Deploy to DigitalOcean App Platform"
git push origin main
```

2. Monitor deployment in GitHub Actions tab

### Manual Deployment

```bash
# Validate app specification
doctl apps spec validate .do/app.yaml

# Create new app
doctl apps create --spec .do/app.yaml --wait

# Or update existing app
APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')
doctl apps update $APP_ID --spec .do/app.yaml --wait
```

## Step 6: Post-Deployment Setup

### Run Database Migrations

```bash
APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')
doctl apps run $APP_ID --component web -- python manage.py migrate
```

### Create Superuser

```bash
doctl apps run $APP_ID --component web -- python manage.py createsuperuser
```

### Load Initial Data (if needed)

```bash
doctl apps run $APP_ID --component web -- python ultimate_test_setup.py
```

## Step 7: Verify Deployment

### Check Application Status

```bash
# Get app information
doctl apps get $APP_ID

# Check logs
doctl apps logs $APP_ID --type=run --component web

# Check health endpoint
curl https://your-app-url.ondigitalocean.app/health/
```

### Test Key Functionality

1. **Frontend**: Visit the app URL
2. **Admin Panel**: Visit `/admin/` and login
3. **API**: Test API endpoints
4. **WebSockets**: Verify real-time features

## Configuration Details

### Environment Variables

The deployment automatically configures:

- `DATABASE_URL`: Managed PostgreSQL connection
- `REDIS_URL`: Managed Redis connection
- `DJANGO_SETTINGS_MODULE`: Production settings
- `SECRET_KEY`: Django secret key
- `MISTRAL_API_KEY`: LLM service key

### Resource Allocation

- **Web Service**: professional-xs (1 vCPU, 1GB RAM)
- **Celery Worker**: basic-xxs (0.5 vCPU, 0.5GB RAM)
- **PostgreSQL**: db-s-1vcpu-1gb (1 vCPU, 1GB RAM, 10GB storage)
- **Redis**: db-s-1vcpu-1gb (1 vCPU, 1GB RAM)

### Scaling Configuration

```yaml
# Add to web service in .do/app.yaml
autoscaling:
  min_instance_count: 1
  max_instance_count: 3
  metrics:
    cpu:
      percent: 70
```

## Monitoring and Maintenance

### View Logs

```bash
# Application logs
doctl apps logs $APP_ID --type=run --component web --follow

# Build logs
doctl apps logs $APP_ID --type=build --component web

# Deployment logs
doctl apps logs $APP_ID --type=deploy
```

### Database Management

```bash
# Connect to database
doctl apps run $APP_ID --component web -- python manage.py dbshell

# Create database backup
doctl databases backups list $DB_ID

# Restore from backup
doctl databases backups restore $DB_ID $BACKUP_ID
```

### Performance Monitoring

```bash
# Check app metrics
doctl apps get $APP_ID --format ID,Name,LiveURL,UpdatedAt

# Monitor resource usage
doctl monitoring alert-policy list
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Check build logs
   doctl apps logs $APP_ID --type=build --component web
   ```

2. **Database Connection Issues**
   ```bash
   # Verify database status
   doctl databases list
   doctl databases get $DB_ID
   ```

3. **Static Files Not Loading**
   ```bash
   # Check Spaces configuration
   doctl compute cdn list
   ```

### Debug Commands

```bash
# Access application shell
doctl apps run $APP_ID --component web -- python manage.py shell

# Run Django checks
doctl apps run $APP_ID --component web -- python manage.py check

# Test database connectivity
doctl apps run $APP_ID --component web -- python manage.py dbshell --command="SELECT 1;"
```

## Cost Optimization

### Current Monthly Costs (Estimated)

- **Web Service**: $12/month (professional-xs)
- **Celery Worker**: $5/month (basic-xxs)
- **PostgreSQL**: $15/month (db-s-1vcpu-1gb)
- **Redis**: $15/month (db-s-1vcpu-1gb)
- **Total**: ~$47/month

### Optimization Tips

1. **Start Small**: Use basic-xxs for all services initially
2. **Monitor Usage**: Scale up only when needed
3. **Database Optimization**: Use connection pooling
4. **Static Files**: Use Spaces CDN for better performance
5. **Caching**: Implement Redis caching effectively

## Security Best Practices

1. **Environment Variables**: Use App Platform secrets
2. **Database Access**: Restrict to app components only
3. **HTTPS**: Enabled by default on App Platform
4. **Regular Updates**: Keep dependencies updated
5. **Monitoring**: Set up alerts for unusual activity

## Backup Strategy

1. **Database**: Automatic daily backups (7-day retention)
2. **Code**: Git repository serves as backup
3. **Static Files**: Spaces provides redundancy
4. **Configuration**: App spec stored in repository

## Support and Resources

- **DigitalOcean Docs**: https://docs.digitalocean.com/products/app-platform/
- **Django Deployment**: https://docs.djangoproject.com/en/stable/howto/deployment/
- **Goali Documentation**: See `/docs` directory
- **Community Support**: GitHub Issues

## Next Steps

1. **Custom Domain**: Configure your domain name
2. **SSL Certificate**: Automatic with custom domains
3. **Monitoring**: Set up alerts and dashboards
4. **CI/CD**: Enhance deployment pipeline
5. **Performance**: Optimize based on usage patterns

---

**Deployment completed successfully! 🚀**

Your Goali application is now running on DigitalOcean App Platform with managed databases and automatic scaling capabilities.