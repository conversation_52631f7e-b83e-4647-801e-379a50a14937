# Bare-bones Digital Ocean App Platform configuration for database connection testing
# This configuration tests database connectivity without Django complexity

name: goali-dbtest
region: nyc1

services:
- name: dbtest
  # Use our custom Dockerfile for database testing
  dockerfile_path: Dockerfile.dbtest
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  
  # Basic service configuration
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-0.5gb
  
  # HTTP port for health checks
  http_port: 8080
  
  # Health check configuration
  health_check:
    http_path: /
    initial_delay_seconds: 30
    period_seconds: 10
    timeout_seconds: 5
    success_threshold: 1
    failure_threshold: 3
  
  # Environment variables - Digital Ocean will auto-inject DATABASE_URL
  # for the managed database connection
  envs:
  - key: APP_ENV
    value: dbtest
  - key: DEBUG_MODE
    value: "true"

# Connect to existing managed database
# Digital Ocean will automatically inject DATABASE_URL environment variable
# with the connection string for this database
databases:
- name: goali-db
  engine: PG
  production: true
  cluster_name: goali-db-cluster
