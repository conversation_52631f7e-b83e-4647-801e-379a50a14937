@keyframes wheelGlow{0%{box-shadow:0 4px 20px #0000004d}50%{box-shadow:0 4px 30px #ffd70066,0 0 40px #ffd70033}to{box-shadow:0 4px 20px #0000004d}}@keyframes spinPulse{0%{transform:scale(1)}50%{transform:scale(1.02)}to{transform:scale(1)}}@keyframes buttone{0%,20%,60%,to{transform:translate(-50%) translateY(0)}40%{transform:translate(-50%) translateY(-10px)}80%{transform:translate(-50%) translateY(-5px)}}@keyframes statusSlideIn{0%{opacity:0;transform:translate(-50%) translateY(-20px)}to{opacity:1;transform:translate(-50%) translateY(0)}}@keyframes errorShake{0%,to{transform:translate(-50%,-50%)}10%,30%,50%,70%,90%{transform:translate(-52%,-50%)}20%,40%,60%,80%{transform:translate(-48%,-50%)}}game-wheel{--wheel-primary-color: #ff6b6b;--wheel-secondary-color: #4ecdc4;--wheel-accent-color: #ffd700;--wheel-text-color: #ffffff;--wheel-background-color: #1a1a1a;--wheel-border-color: #333333;--wheel-shadow-color: rgba(0, 0, 0, .3);--wheel-glow-color: rgba(255, 215, 0, .4)}game-wheel[spinning] .wheel-container{animation:wheelGlow 2s ease-in-out infinite,spinPulse .5s ease-in-out infinite}game-wheel[spinning] .spin-button{animation:buttonBounce 1s ease-in-out infinite}game-wheel .wheel-status.visible{animation:statusSlideIn .3s ease-out}game-wheel .error-message{animation:errorShake .5s ease-in-out}@media (max-width: 1200px){game-wheel{--wheel-size: min(90vw, 90vh)}}@media (max-width: 768px){game-wheel{--wheel-size: min(95vw, 95vh)}game-wheel .wheel-container{box-shadow:0 2px 15px var(--wheel-shadow-color)}game-wheel .spin-button{padding:8px 16px;font-size:14px;bottom:5px}game-wheel .wheel-status{top:10px;font-size:12px;padding:6px 12px}}@media (max-width: 480px){game-wheel{--wheel-size: min(98vw, 98vh)}game-wheel .spin-button{padding:6px 12px;font-size:12px;border-radius:20px}game-wheel .wheel-status{font-size:11px;padding:4px 8px}game-wheel .error-message{max-width:250px;padding:12px;font-size:13px}}@media (-webkit-min-device-pixel-ratio: 2),(min-resolution: 192dpi){game-wheel .wheel-canvas{image-rendering:-webkit-optimize-contrast;image-rendering:crisp-edges}}@media (prefers-reduced-motion: reduce){game-wheel .wheel-container,game-wheel .spin-button,game-wheel .wheel-status,game-wheel .error-message{animation:none!important}game-wheel .wheel-container{transition:none}}game-wheel .spin-button:focus{outline:3px solid var(--wheel-accent-color);outline-offset:2px}game-wheel .wheel-canvas:focus{outline:2px solid var(--wheel-accent-color);outline-offset:-2px}@media (prefers-color-scheme: dark){game-wheel{--wheel-background-color: #0a0a0a;--wheel-border-color: #444444;--wheel-shadow-color: rgba(0, 0, 0, .5)}}@media (prefers-color-scheme: light){game-wheel{--wheel-background-color: #f5f5f5;--wheel-border-color: #cccccc;--wheel-shadow-color: rgba(0, 0, 0, .2);--wheel-text-color: #333333}game-wheel .wheel-status{background:#ffffffe6;color:var(--wheel-text-color)}}@media print{game-wheel{break-inside:avoid}game-wheel .spin-button,game-wheel .wheel-status{display:none}game-wheel .wheel-container{box-shadow:none;border:2px solid #000}}game-wheel[loading] .wheel-container{opacity:.7;pointer-events:none}game-wheel[loading] .wheel-container:after{content:"";position:absolute;top:50%;left:50%;width:40px;height:40px;margin:-20px 0 0 -20px;border:4px solid var(--wheel-border-color);border-top-color:var(--wheel-primary-color);border-radius:50%;animation:spin 1s linear infinite}game-wheel .winner-highlight{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:linear-gradient(45deg,var(--wheel-accent-color),#ffed4e);color:#333;padding:12px 24px;border-radius:25px;font-weight:700;font-size:18px;box-shadow:0 4px 20px #ffd70080;animation:winnerPulse 1s ease-in-out infinite;z-index:10}@keyframes winnerPulse{0%,to{transform:translate(-50%,-50%) scale(1)}50%{transform:translate(-50%,-50%) scale(1.05)}}@media (pointer: coarse){game-wheel .spin-button{min-height:44px;min-width:88px}game-wheel .wheel-canvas{touch-action:manipulation}}@media (orientation: landscape) and (max-height: 600px){game-wheel .spin-button{bottom:5px;padding:6px 12px;font-size:12px}game-wheel .wheel-status{top:5px;font-size:11px;padding:4px 8px}}@container (max-width: 400px){game-wheel .spin-button{font-size:11px;padding:5px 10px}}game-wheel ::-webkit-scrollbar{width:8px}game-wheel ::-webkit-scrollbar-track{background:var(--wheel-background-color)}game-wheel ::-webkit-scrollbar-thumb{background:var(--wheel-border-color);border-radius:4px}game-wheel ::-webkit-scrollbar-thumb:hover{background:var(--wheel-primary-color)}app-shell{display:block;width:100%;min-height:100vh;opacity:0;transition:opacity .5s ease-in-out}app-shell.loaded{opacity:1}game-wheel{display:block;width:100%;height:100%;min-height:400px;max-height:600px;aspect-ratio:1;margin:0 auto}chat-interface{display:block;width:100%;height:400px;min-height:300px;max-height:600px;border-radius:12px;overflow:hidden;box-shadow:0 4px 20px #0000001a}message-bubble{display:block;width:100%;margin:4px 0}@media (min-width: 1024px){.app-container{display:grid;grid-template-columns:1fr 400px;gap:2rem;max-width:1400px;margin:0 auto;padding:2rem}.wheel-section{grid-column:1}.chat-section{grid-column:2;height:100vh;max-height:800px;position:sticky;top:2rem}chat-interface{height:100%;max-height:none}game-wheel{max-height:800px}}@media (max-width: 1023px){.app-container{display:flex;flex-direction:column;gap:1.5rem;padding:1rem}.wheel-section{order:1}.chat-section{order:2}chat-interface{height:50vh;min-height:300px}}@media (max-width: 768px){.app-container{gap:1rem;padding:.5rem}game-wheel{min-height:300px;max-height:400px}chat-interface{height:40vh;min-height:250px}}.component-loading{display:flex;align-items:center;justify-content:center;min-height:200px;background:#ffffff0d;border-radius:12px;backdrop-filter:blur(10px)}.component-loading:after{content:"";width:32px;height:32px;border:3px solid rgba(255,255,255,.3);border-top-color:#667eea;border-radius:50%;animation:spin 1s linear infinite}.component-error{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:200px;padding:2rem;background:#ff00001a;border:2px dashed rgba(255,0,0,.3);border-radius:12px;color:#ff4757;text-align:center}.component-error-icon{font-size:2rem;margin-bottom:1rem}.component-error-message{font-weight:600;margin-bottom:.5rem}.component-error-details{font-size:.875rem;opacity:.8}@media (prefers-reduced-motion: reduce){app-shell,game-wheel,chat-interface,message-bubble{transition:none!important;animation:none!important}.component-loading:after{animation:none;border-top-color:transparent}}@media (prefers-contrast: high){game-wheel{border:2px solid currentColor}chat-interface{border:1px solid currentColor}message-bubble{outline:1px solid currentColor;outline-offset:2px}}@media print{app-shell{background:#fff!important;color:#000!important}.chat-section{display:none}game-wheel{max-height:none;page-break-inside:avoid}}app-shell:focus-within game-wheel{outline:2px solid #667eea;outline-offset:4px;border-radius:50%}chat-interface:focus-within{outline:2px solid #667eea;outline-offset:2px;border-radius:12px}game-wheel:hover{transform:scale(1.02);transition:transform .2s ease}game-wheel[spinning]{transform:scale(1.05);filter:drop-shadow(0 8px 32px rgba(102,126,234,.3))}chat-interface[processing]{opacity:.8;pointer-events:none}:root{--component-border-radius: 12px;--component-shadow: 0 4px 20px rgba(0, 0, 0, .1);--component-shadow-hover: 0 8px 32px rgba(0, 0, 0, .15);--component-transition: all .2s ease}@media (prefers-color-scheme: dark){:root{--component-shadow: 0 4px 20px rgba(0, 0, 0, .3);--component-shadow-hover: 0 8px 32px rgba(0, 0, 0, .4)}.component-loading{background:#ffffff05}.component-error{background:#ff00000d;border-color:#f003}}@keyframes fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes slideInFromRight{0%{opacity:0;transform:translate(100%)}to{opacity:1;transform:translate(0)}}@keyframes pulse{0%,to{opacity:1}50%{opacity:.7}}@keyframes spin{to{transform:rotate(360deg)}}game-wheel{animation:fadeIn .6s ease-out}chat-interface{animation:slideInFromRight .5s ease-out}message-bubble{animation:fadeIn .3s ease-out}.visually-hidden,.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.component-container{position:relative;border-radius:var(--component-border-radius);box-shadow:var(--component-shadow);transition:var(--component-transition)}.component-container:hover{box-shadow:var(--component-shadow-hover)}:root{--color-primary: #4A90E2;--color-primary-dark: #357ABD;--color-primary-light: #6BA3E8;--color-secondary: #50C878;--color-accent: #FF6B6B;--color-warning: #FFB347;--color-error: #FF4757;--color-success: #2ED573;--color-white: #FFFFFF;--color-gray-50: #F9FAFB;--color-gray-100: #F3F4F6;--color-gray-200: #E5E7EB;--color-gray-300: #D1D5DB;--color-gray-400: #9CA3AF;--color-gray-500: #6B7280;--color-gray-600: #4B5563;--color-gray-700: #374151;--color-gray-800: #1F2937;--color-gray-900: #111827;--gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);--gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);--gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);--font-family-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;--font-family-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;--font-size-xs: .75rem;--font-size-sm: .875rem;--font-size-base: 1rem;--font-size-lg: 1.125rem;--font-size-xl: 1.25rem;--font-size-2xl: 1.5rem;--font-size-3xl: 1.875rem;--font-size-4xl: 2.25rem;--font-weight-light: 300;--font-weight-normal: 400;--font-weight-medium: 500;--font-weight-semibold: 600;--font-weight-bold: 700;--line-height-tight: 1.25;--line-height-normal: 1.5;--line-height-relaxed: 1.75;--spacing-1: .25rem;--spacing-2: .5rem;--spacing-3: .75rem;--spacing-4: 1rem;--spacing-5: 1.25rem;--spacing-6: 1.5rem;--spacing-8: 2rem;--spacing-10: 2.5rem;--spacing-12: 3rem;--spacing-16: 4rem;--spacing-20: 5rem;--radius-sm: .25rem;--radius-md: .5rem;--radius-lg: .75rem;--radius-xl: 1rem;--radius-2xl: 1.5rem;--radius-full: 9999px;--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -2px rgba(0, 0, 0, .05);--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 10px 10px -5px rgba(0, 0, 0, .04);--z-dropdown: 1000;--z-sticky: 1020;--z-fixed: 1030;--z-modal-backdrop: 1040;--z-modal: 1050;--z-popover: 1060;--z-tooltip: 1070;--z-toast: 1080;--transition-fast: .15s ease-in-out;--transition-normal: .25s ease-in-out;--transition-slow: .35s ease-in-out;--wheel-size: 80vw;--wheel-border-width: 2px;--nail-color: #C0C0C0;--ball-color: #FFFFFF;--ball-outline-color: #000000;--spin-button-color: var(--color-primary)}*,*:before,*:after{box-sizing:border-box;margin:0;padding:0}html{font-family:var(--font-family-primary);font-size:var(--font-size-base);line-height:var(--line-height-normal);-webkit-text-size-adjust:100%;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;scroll-behavior:smooth}body{margin:0;padding:0;min-height:100vh;background:var(--gradient-primary);color:var(--color-gray-900);overflow-x:hidden;touch-action:manipulation;-webkit-overflow-scrolling:touch}h1,h2,h3,h4,h5,h6{font-weight:var(--font-weight-semibold);line-height:var(--line-height-tight);margin-bottom:var(--spacing-4)}h1{font-size:var(--font-size-4xl)}h2{font-size:var(--font-size-3xl)}h3{font-size:var(--font-size-2xl)}h4{font-size:var(--font-size-xl)}h5{font-size:var(--font-size-lg)}h6{font-size:var(--font-size-base)}p{margin-bottom:var(--spacing-4);line-height:var(--line-height-relaxed)}a{color:var(--color-primary);text-decoration:none;transition:color var(--transition-fast)}a:hover{color:var(--color-primary-dark);text-decoration:underline}button{font-family:inherit;font-size:inherit;line-height:inherit;margin:0;padding:var(--spacing-3) var(--spacing-6);border:none;border-radius:var(--radius-lg);background:var(--color-primary);color:var(--color-white);cursor:pointer;transition:all var(--transition-fast);touch-action:manipulation;user-select:none;-webkit-tap-highlight-color:transparent}button:hover:not(:disabled){background:var(--color-primary-dark);transform:translateY(-1px);box-shadow:var(--shadow-md)}button:active:not(:disabled){transform:translateY(0);box-shadow:var(--shadow-sm)}button:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:none}input,textarea{font-family:inherit;font-size:inherit;line-height:inherit;margin:0;padding:var(--spacing-3);border:1px solid var(--color-gray-300);border-radius:var(--radius-md);background:var(--color-white);color:var(--color-gray-900);transition:border-color var(--transition-fast);width:100%}input:focus,textarea:focus{outline:none;border-color:var(--color-primary);box-shadow:0 0 0 3px #4a90e21a}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.font-light{font-weight:var(--font-weight-light)}.font-normal{font-weight:var(--font-weight-normal)}.font-medium{font-weight:var(--font-weight-medium)}.font-semibold{font-weight:var(--font-weight-semibold)}.font-bold{font-weight:var(--font-weight-bold)}.text-xs{font-size:var(--font-size-xs)}.text-sm{font-size:var(--font-size-sm)}.text-base{font-size:var(--font-size-base)}.text-lg{font-size:var(--font-size-lg)}.text-xl{font-size:var(--font-size-xl)}.text-2xl{font-size:var(--font-size-2xl)}.hidden{display:none!important}.block{display:block}.inline{display:inline}.inline-block{display:inline-block}.flex{display:flex}.inline-flex{display:inline-flex}.grid{display:grid}@media (max-width: 640px){:root{--font-size-4xl: 2rem;--font-size-3xl: 1.75rem;--font-size-2xl: 1.375rem}body{font-size:var(--font-size-sm)}}@media (prefers-contrast: high){:root{--color-gray-400: #666666;--color-gray-500: #555555;--color-gray-600: #444444}}@media (prefers-reduced-motion: reduce){*,*:before,*:after{animation-duration:.01ms!important;animation-iteration-count:1!important;transition-duration:.01ms!important;scroll-behavior:auto!important}}
