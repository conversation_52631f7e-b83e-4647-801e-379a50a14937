{"version": 3, "file": "matter-4D3s4xTp.js", "sources": ["../../node_modules/matter-js/build/matter.js"], "sourcesContent": ["/*!\n * matter-js 0.19.0 by @liabru\n * http://brm.io/matter-js/\n * License MIT\n * \n * The MIT License (MIT)\n * \n * Copyright (c) <PERSON> and contributors.\n * \n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n * \n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n * \n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"Matter\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Matter\"] = factory();\n\telse\n\t\troot[\"Matter\"] = factory();\n})(this, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 20);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\n/**\n* The `Matter.Common` module contains utility functions that are common to all modules.\n*\n* @class Common\n*/\n\nvar Common = {};\n\nmodule.exports = Common;\n\n(function() {\n\n    Common._baseDelta = 1000 / 60;\n    Common._nextId = 0;\n    Common._seed = 0;\n    Common._nowStartTime = +(new Date());\n    Common._warnedOnce = {};\n    Common._decomp = null;\n    \n    /**\n     * Extends the object in the first argument using the object in the second argument.\n     * @method extend\n     * @param {} obj\n     * @param {boolean} deep\n     * @return {} obj extended\n     */\n    Common.extend = function(obj, deep) {\n        var argsStart,\n            args,\n            deepClone;\n\n        if (typeof deep === 'boolean') {\n            argsStart = 2;\n            deepClone = deep;\n        } else {\n            argsStart = 1;\n            deepClone = true;\n        }\n\n        for (var i = argsStart; i < arguments.length; i++) {\n            var source = arguments[i];\n\n            if (source) {\n                for (var prop in source) {\n                    if (deepClone && source[prop] && source[prop].constructor === Object) {\n                        if (!obj[prop] || obj[prop].constructor === Object) {\n                            obj[prop] = obj[prop] || {};\n                            Common.extend(obj[prop], deepClone, source[prop]);\n                        } else {\n                            obj[prop] = source[prop];\n                        }\n                    } else {\n                        obj[prop] = source[prop];\n                    }\n                }\n            }\n        }\n        \n        return obj;\n    };\n\n    /**\n     * Creates a new clone of the object, if deep is true references will also be cloned.\n     * @method clone\n     * @param {} obj\n     * @param {bool} deep\n     * @return {} obj cloned\n     */\n    Common.clone = function(obj, deep) {\n        return Common.extend({}, deep, obj);\n    };\n\n    /**\n     * Returns the list of keys for the given object.\n     * @method keys\n     * @param {} obj\n     * @return {string[]} keys\n     */\n    Common.keys = function(obj) {\n        if (Object.keys)\n            return Object.keys(obj);\n\n        // avoid hasOwnProperty for performance\n        var keys = [];\n        for (var key in obj)\n            keys.push(key);\n        return keys;\n    };\n\n    /**\n     * Returns the list of values for the given object.\n     * @method values\n     * @param {} obj\n     * @return {array} Array of the objects property values\n     */\n    Common.values = function(obj) {\n        var values = [];\n        \n        if (Object.keys) {\n            var keys = Object.keys(obj);\n            for (var i = 0; i < keys.length; i++) {\n                values.push(obj[keys[i]]);\n            }\n            return values;\n        }\n        \n        // avoid hasOwnProperty for performance\n        for (var key in obj)\n            values.push(obj[key]);\n        return values;\n    };\n\n    /**\n     * Gets a value from `base` relative to the `path` string.\n     * @method get\n     * @param {} obj The base object\n     * @param {string} path The path relative to `base`, e.g. 'Foo.Bar.baz'\n     * @param {number} [begin] Path slice begin\n     * @param {number} [end] Path slice end\n     * @return {} The object at the given path\n     */\n    Common.get = function(obj, path, begin, end) {\n        path = path.split('.').slice(begin, end);\n\n        for (var i = 0; i < path.length; i += 1) {\n            obj = obj[path[i]];\n        }\n\n        return obj;\n    };\n\n    /**\n     * Sets a value on `base` relative to the given `path` string.\n     * @method set\n     * @param {} obj The base object\n     * @param {string} path The path relative to `base`, e.g. 'Foo.Bar.baz'\n     * @param {} val The value to set\n     * @param {number} [begin] Path slice begin\n     * @param {number} [end] Path slice end\n     * @return {} Pass through `val` for chaining\n     */\n    Common.set = function(obj, path, val, begin, end) {\n        var parts = path.split('.').slice(begin, end);\n        Common.get(obj, path, 0, -1)[parts[parts.length - 1]] = val;\n        return val;\n    };\n\n    /**\n     * Shuffles the given array in-place.\n     * The function uses a seeded random generator.\n     * @method shuffle\n     * @param {array} array\n     * @return {array} array shuffled randomly\n     */\n    Common.shuffle = function(array) {\n        for (var i = array.length - 1; i > 0; i--) {\n            var j = Math.floor(Common.random() * (i + 1));\n            var temp = array[i];\n            array[i] = array[j];\n            array[j] = temp;\n        }\n        return array;\n    };\n\n    /**\n     * Randomly chooses a value from a list with equal probability.\n     * The function uses a seeded random generator.\n     * @method choose\n     * @param {array} choices\n     * @return {object} A random choice object from the array\n     */\n    Common.choose = function(choices) {\n        return choices[Math.floor(Common.random() * choices.length)];\n    };\n\n    /**\n     * Returns true if the object is a HTMLElement, otherwise false.\n     * @method isElement\n     * @param {object} obj\n     * @return {boolean} True if the object is a HTMLElement, otherwise false\n     */\n    Common.isElement = function(obj) {\n        if (typeof HTMLElement !== 'undefined') {\n            return obj instanceof HTMLElement;\n        }\n\n        return !!(obj && obj.nodeType && obj.nodeName);\n    };\n\n    /**\n     * Returns true if the object is an array.\n     * @method isArray\n     * @param {object} obj\n     * @return {boolean} True if the object is an array, otherwise false\n     */\n    Common.isArray = function(obj) {\n        return Object.prototype.toString.call(obj) === '[object Array]';\n    };\n\n    /**\n     * Returns true if the object is a function.\n     * @method isFunction\n     * @param {object} obj\n     * @return {boolean} True if the object is a function, otherwise false\n     */\n    Common.isFunction = function(obj) {\n        return typeof obj === \"function\";\n    };\n\n    /**\n     * Returns true if the object is a plain object.\n     * @method isPlainObject\n     * @param {object} obj\n     * @return {boolean} True if the object is a plain object, otherwise false\n     */\n    Common.isPlainObject = function(obj) {\n        return typeof obj === 'object' && obj.constructor === Object;\n    };\n\n    /**\n     * Returns true if the object is a string.\n     * @method isString\n     * @param {object} obj\n     * @return {boolean} True if the object is a string, otherwise false\n     */\n    Common.isString = function(obj) {\n        return toString.call(obj) === '[object String]';\n    };\n    \n    /**\n     * Returns the given value clamped between a minimum and maximum value.\n     * @method clamp\n     * @param {number} value\n     * @param {number} min\n     * @param {number} max\n     * @return {number} The value clamped between min and max inclusive\n     */\n    Common.clamp = function(value, min, max) {\n        if (value < min)\n            return min;\n        if (value > max)\n            return max;\n        return value;\n    };\n    \n    /**\n     * Returns the sign of the given value.\n     * @method sign\n     * @param {number} value\n     * @return {number} -1 if negative, +1 if 0 or positive\n     */\n    Common.sign = function(value) {\n        return value < 0 ? -1 : 1;\n    };\n    \n    /**\n     * Returns the current timestamp since the time origin (e.g. from page load).\n     * The result is in milliseconds and will use high-resolution timing if available.\n     * @method now\n     * @return {number} the current timestamp in milliseconds\n     */\n    Common.now = function() {\n        if (typeof window !== 'undefined' && window.performance) {\n            if (window.performance.now) {\n                return window.performance.now();\n            } else if (window.performance.webkitNow) {\n                return window.performance.webkitNow();\n            }\n        }\n\n        if (Date.now) {\n            return Date.now();\n        }\n\n        return (new Date()) - Common._nowStartTime;\n    };\n    \n    /**\n     * Returns a random value between a minimum and a maximum value inclusive.\n     * The function uses a seeded random generator.\n     * @method random\n     * @param {number} min\n     * @param {number} max\n     * @return {number} A random number between min and max inclusive\n     */\n    Common.random = function(min, max) {\n        min = (typeof min !== \"undefined\") ? min : 0;\n        max = (typeof max !== \"undefined\") ? max : 1;\n        return min + _seededRandom() * (max - min);\n    };\n\n    var _seededRandom = function() {\n        // https://en.wikipedia.org/wiki/Linear_congruential_generator\n        Common._seed = (Common._seed * 9301 + 49297) % 233280;\n        return Common._seed / 233280;\n    };\n\n    /**\n     * Converts a CSS hex colour string into an integer.\n     * @method colorToNumber\n     * @param {string} colorString\n     * @return {number} An integer representing the CSS hex string\n     */\n    Common.colorToNumber = function(colorString) {\n        colorString = colorString.replace('#','');\n\n        if (colorString.length == 3) {\n            colorString = colorString.charAt(0) + colorString.charAt(0)\n                        + colorString.charAt(1) + colorString.charAt(1)\n                        + colorString.charAt(2) + colorString.charAt(2);\n        }\n\n        return parseInt(colorString, 16);\n    };\n\n    /**\n     * The console logging level to use, where each level includes all levels above and excludes the levels below.\n     * The default level is 'debug' which shows all console messages.  \n     *\n     * Possible level values are:\n     * - 0 = None\n     * - 1 = Debug\n     * - 2 = Info\n     * - 3 = Warn\n     * - 4 = Error\n     * @static\n     * @property logLevel\n     * @type {Number}\n     * @default 1\n     */\n    Common.logLevel = 1;\n\n    /**\n     * Shows a `console.log` message only if the current `Common.logLevel` allows it.\n     * The message will be prefixed with 'matter-js' to make it easily identifiable.\n     * @method log\n     * @param ...objs {} The objects to log.\n     */\n    Common.log = function() {\n        if (console && Common.logLevel > 0 && Common.logLevel <= 3) {\n            console.log.apply(console, ['matter-js:'].concat(Array.prototype.slice.call(arguments)));\n        }\n    };\n\n    /**\n     * Shows a `console.info` message only if the current `Common.logLevel` allows it.\n     * The message will be prefixed with 'matter-js' to make it easily identifiable.\n     * @method info\n     * @param ...objs {} The objects to log.\n     */\n    Common.info = function() {\n        if (console && Common.logLevel > 0 && Common.logLevel <= 2) {\n            console.info.apply(console, ['matter-js:'].concat(Array.prototype.slice.call(arguments)));\n        }\n    };\n\n    /**\n     * Shows a `console.warn` message only if the current `Common.logLevel` allows it.\n     * The message will be prefixed with 'matter-js' to make it easily identifiable.\n     * @method warn\n     * @param ...objs {} The objects to log.\n     */\n    Common.warn = function() {\n        if (console && Common.logLevel > 0 && Common.logLevel <= 3) {\n            console.warn.apply(console, ['matter-js:'].concat(Array.prototype.slice.call(arguments)));\n        }\n    };\n\n    /**\n     * Uses `Common.warn` to log the given message one time only.\n     * @method warnOnce\n     * @param ...objs {} The objects to log.\n     */\n    Common.warnOnce = function() {\n        var message = Array.prototype.slice.call(arguments).join(' ');\n\n        if (!Common._warnedOnce[message]) {\n            Common.warn(message);\n            Common._warnedOnce[message] = true;\n        }\n    };\n\n    /**\n     * Shows a deprecated console warning when the function on the given object is called.\n     * The target function will be replaced with a new function that first shows the warning\n     * and then calls the original function.\n     * @method deprecated\n     * @param {object} obj The object or module\n     * @param {string} name The property name of the function on obj\n     * @param {string} warning The one-time message to show if the function is called\n     */\n    Common.deprecated = function(obj, prop, warning) {\n        obj[prop] = Common.chain(function() {\n            Common.warnOnce('🔅 deprecated 🔅', warning);\n        }, obj[prop]);\n    };\n\n    /**\n     * Returns the next unique sequential ID.\n     * @method nextId\n     * @return {Number} Unique sequential ID\n     */\n    Common.nextId = function() {\n        return Common._nextId++;\n    };\n\n    /**\n     * A cross browser compatible indexOf implementation.\n     * @method indexOf\n     * @param {array} haystack\n     * @param {object} needle\n     * @return {number} The position of needle in haystack, otherwise -1.\n     */\n    Common.indexOf = function(haystack, needle) {\n        if (haystack.indexOf)\n            return haystack.indexOf(needle);\n\n        for (var i = 0; i < haystack.length; i++) {\n            if (haystack[i] === needle)\n                return i;\n        }\n\n        return -1;\n    };\n\n    /**\n     * A cross browser compatible array map implementation.\n     * @method map\n     * @param {array} list\n     * @param {function} func\n     * @return {array} Values from list transformed by func.\n     */\n    Common.map = function(list, func) {\n        if (list.map) {\n            return list.map(func);\n        }\n\n        var mapped = [];\n\n        for (var i = 0; i < list.length; i += 1) {\n            mapped.push(func(list[i]));\n        }\n\n        return mapped;\n    };\n\n    /**\n     * Takes a directed graph and returns the partially ordered set of vertices in topological order.\n     * Circular dependencies are allowed.\n     * @method topologicalSort\n     * @param {object} graph\n     * @return {array} Partially ordered set of vertices in topological order.\n     */\n    Common.topologicalSort = function(graph) {\n        // https://github.com/mgechev/javascript-algorithms\n        // Copyright (c) Minko Gechev (MIT license)\n        // Modifications: tidy formatting and naming\n        var result = [],\n            visited = [],\n            temp = [];\n\n        for (var node in graph) {\n            if (!visited[node] && !temp[node]) {\n                Common._topologicalSort(node, visited, temp, graph, result);\n            }\n        }\n\n        return result;\n    };\n\n    Common._topologicalSort = function(node, visited, temp, graph, result) {\n        var neighbors = graph[node] || [];\n        temp[node] = true;\n\n        for (var i = 0; i < neighbors.length; i += 1) {\n            var neighbor = neighbors[i];\n\n            if (temp[neighbor]) {\n                // skip circular dependencies\n                continue;\n            }\n\n            if (!visited[neighbor]) {\n                Common._topologicalSort(neighbor, visited, temp, graph, result);\n            }\n        }\n\n        temp[node] = false;\n        visited[node] = true;\n\n        result.push(node);\n    };\n\n    /**\n     * Takes _n_ functions as arguments and returns a new function that calls them in order.\n     * The arguments applied when calling the new function will also be applied to every function passed.\n     * The value of `this` refers to the last value returned in the chain that was not `undefined`.\n     * Therefore if a passed function does not return a value, the previously returned value is maintained.\n     * After all passed functions have been called the new function returns the last returned value (if any).\n     * If any of the passed functions are a chain, then the chain will be flattened.\n     * @method chain\n     * @param ...funcs {function} The functions to chain.\n     * @return {function} A new function that calls the passed functions in order.\n     */\n    Common.chain = function() {\n        var funcs = [];\n\n        for (var i = 0; i < arguments.length; i += 1) {\n            var func = arguments[i];\n\n            if (func._chained) {\n                // flatten already chained functions\n                funcs.push.apply(funcs, func._chained);\n            } else {\n                funcs.push(func);\n            }\n        }\n\n        var chain = function() {\n            // https://github.com/GoogleChrome/devtools-docs/issues/53#issuecomment-51941358\n            var lastResult,\n                args = new Array(arguments.length);\n\n            for (var i = 0, l = arguments.length; i < l; i++) {\n                args[i] = arguments[i];\n            }\n\n            for (i = 0; i < funcs.length; i += 1) {\n                var result = funcs[i].apply(lastResult, args);\n\n                if (typeof result !== 'undefined') {\n                    lastResult = result;\n                }\n            }\n\n            return lastResult;\n        };\n\n        chain._chained = funcs;\n\n        return chain;\n    };\n\n    /**\n     * Chains a function to excute before the original function on the given `path` relative to `base`.\n     * See also docs for `Common.chain`.\n     * @method chainPathBefore\n     * @param {} base The base object\n     * @param {string} path The path relative to `base`\n     * @param {function} func The function to chain before the original\n     * @return {function} The chained function that replaced the original\n     */\n    Common.chainPathBefore = function(base, path, func) {\n        return Common.set(base, path, Common.chain(\n            func,\n            Common.get(base, path)\n        ));\n    };\n\n    /**\n     * Chains a function to excute after the original function on the given `path` relative to `base`.\n     * See also docs for `Common.chain`.\n     * @method chainPathAfter\n     * @param {} base The base object\n     * @param {string} path The path relative to `base`\n     * @param {function} func The function to chain after the original\n     * @return {function} The chained function that replaced the original\n     */\n    Common.chainPathAfter = function(base, path, func) {\n        return Common.set(base, path, Common.chain(\n            Common.get(base, path),\n            func\n        ));\n    };\n\n    /**\n     * Provide the [poly-decomp](https://github.com/schteppe/poly-decomp.js) library module to enable\n     * concave vertex decomposition support when using `Bodies.fromVertices` e.g. `Common.setDecomp(require('poly-decomp'))`.\n     * @method setDecomp\n     * @param {} decomp The [poly-decomp](https://github.com/schteppe/poly-decomp.js) library module.\n     */\n    Common.setDecomp = function(decomp) {\n        Common._decomp = decomp;\n    };\n\n    /**\n     * Returns the [poly-decomp](https://github.com/schteppe/poly-decomp.js) library module provided through `Common.setDecomp`,\n     * otherwise returns the global `decomp` if set.\n     * @method getDecomp\n     * @return {} The [poly-decomp](https://github.com/schteppe/poly-decomp.js) library module if provided.\n     */\n    Common.getDecomp = function() {\n        // get user provided decomp if set\n        var decomp = Common._decomp;\n\n        try {\n            // otherwise from window global\n            if (!decomp && typeof window !== 'undefined') {\n                decomp = window.decomp;\n            }\n    \n            // otherwise from node global\n            if (!decomp && typeof global !== 'undefined') {\n                decomp = global.decomp;\n            }\n        } catch (e) {\n            // decomp not available\n            decomp = null;\n        }\n\n        return decomp;\n    };\n})();\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports) {\n\n/**\n* The `Matter.Bounds` module contains methods for creating and manipulating axis-aligned bounding boxes (AABB).\n*\n* @class Bounds\n*/\n\nvar Bounds = {};\n\nmodule.exports = Bounds;\n\n(function() {\n\n    /**\n     * Creates a new axis-aligned bounding box (AABB) for the given vertices.\n     * @method create\n     * @param {vertices} vertices\n     * @return {bounds} A new bounds object\n     */\n    Bounds.create = function(vertices) {\n        var bounds = { \n            min: { x: 0, y: 0 }, \n            max: { x: 0, y: 0 }\n        };\n\n        if (vertices)\n            Bounds.update(bounds, vertices);\n        \n        return bounds;\n    };\n\n    /**\n     * Updates bounds using the given vertices and extends the bounds given a velocity.\n     * @method update\n     * @param {bounds} bounds\n     * @param {vertices} vertices\n     * @param {vector} velocity\n     */\n    Bounds.update = function(bounds, vertices, velocity) {\n        bounds.min.x = Infinity;\n        bounds.max.x = -Infinity;\n        bounds.min.y = Infinity;\n        bounds.max.y = -Infinity;\n\n        for (var i = 0; i < vertices.length; i++) {\n            var vertex = vertices[i];\n            if (vertex.x > bounds.max.x) bounds.max.x = vertex.x;\n            if (vertex.x < bounds.min.x) bounds.min.x = vertex.x;\n            if (vertex.y > bounds.max.y) bounds.max.y = vertex.y;\n            if (vertex.y < bounds.min.y) bounds.min.y = vertex.y;\n        }\n        \n        if (velocity) {\n            if (velocity.x > 0) {\n                bounds.max.x += velocity.x;\n            } else {\n                bounds.min.x += velocity.x;\n            }\n            \n            if (velocity.y > 0) {\n                bounds.max.y += velocity.y;\n            } else {\n                bounds.min.y += velocity.y;\n            }\n        }\n    };\n\n    /**\n     * Returns true if the bounds contains the given point.\n     * @method contains\n     * @param {bounds} bounds\n     * @param {vector} point\n     * @return {boolean} True if the bounds contain the point, otherwise false\n     */\n    Bounds.contains = function(bounds, point) {\n        return point.x >= bounds.min.x && point.x <= bounds.max.x \n               && point.y >= bounds.min.y && point.y <= bounds.max.y;\n    };\n\n    /**\n     * Returns true if the two bounds intersect.\n     * @method overlaps\n     * @param {bounds} boundsA\n     * @param {bounds} boundsB\n     * @return {boolean} True if the bounds overlap, otherwise false\n     */\n    Bounds.overlaps = function(boundsA, boundsB) {\n        return (boundsA.min.x <= boundsB.max.x && boundsA.max.x >= boundsB.min.x\n                && boundsA.max.y >= boundsB.min.y && boundsA.min.y <= boundsB.max.y);\n    };\n\n    /**\n     * Translates the bounds by the given vector.\n     * @method translate\n     * @param {bounds} bounds\n     * @param {vector} vector\n     */\n    Bounds.translate = function(bounds, vector) {\n        bounds.min.x += vector.x;\n        bounds.max.x += vector.x;\n        bounds.min.y += vector.y;\n        bounds.max.y += vector.y;\n    };\n\n    /**\n     * Shifts the bounds to the given position.\n     * @method shift\n     * @param {bounds} bounds\n     * @param {vector} position\n     */\n    Bounds.shift = function(bounds, position) {\n        var deltaX = bounds.max.x - bounds.min.x,\n            deltaY = bounds.max.y - bounds.min.y;\n            \n        bounds.min.x = position.x;\n        bounds.max.x = position.x + deltaX;\n        bounds.min.y = position.y;\n        bounds.max.y = position.y + deltaY;\n    };\n    \n})();\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports) {\n\n/**\n* The `Matter.Vector` module contains methods for creating and manipulating vectors.\n* Vectors are the basis of all the geometry related operations in the engine.\n* A `Matter.Vector` object is of the form `{ x: 0, y: 0 }`.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Vector\n*/\n\n// TODO: consider params for reusing vector objects\n\nvar Vector = {};\n\nmodule.exports = Vector;\n\n(function() {\n\n    /**\n     * Creates a new vector.\n     * @method create\n     * @param {number} x\n     * @param {number} y\n     * @return {vector} A new vector\n     */\n    Vector.create = function(x, y) {\n        return { x: x || 0, y: y || 0 };\n    };\n\n    /**\n     * Returns a new vector with `x` and `y` copied from the given `vector`.\n     * @method clone\n     * @param {vector} vector\n     * @return {vector} A new cloned vector\n     */\n    Vector.clone = function(vector) {\n        return { x: vector.x, y: vector.y };\n    };\n\n    /**\n     * Returns the magnitude (length) of a vector.\n     * @method magnitude\n     * @param {vector} vector\n     * @return {number} The magnitude of the vector\n     */\n    Vector.magnitude = function(vector) {\n        return Math.sqrt((vector.x * vector.x) + (vector.y * vector.y));\n    };\n\n    /**\n     * Returns the magnitude (length) of a vector (therefore saving a `sqrt` operation).\n     * @method magnitudeSquared\n     * @param {vector} vector\n     * @return {number} The squared magnitude of the vector\n     */\n    Vector.magnitudeSquared = function(vector) {\n        return (vector.x * vector.x) + (vector.y * vector.y);\n    };\n\n    /**\n     * Rotates the vector about (0, 0) by specified angle.\n     * @method rotate\n     * @param {vector} vector\n     * @param {number} angle\n     * @param {vector} [output]\n     * @return {vector} The vector rotated about (0, 0)\n     */\n    Vector.rotate = function(vector, angle, output) {\n        var cos = Math.cos(angle), sin = Math.sin(angle);\n        if (!output) output = {};\n        var x = vector.x * cos - vector.y * sin;\n        output.y = vector.x * sin + vector.y * cos;\n        output.x = x;\n        return output;\n    };\n\n    /**\n     * Rotates the vector about a specified point by specified angle.\n     * @method rotateAbout\n     * @param {vector} vector\n     * @param {number} angle\n     * @param {vector} point\n     * @param {vector} [output]\n     * @return {vector} A new vector rotated about the point\n     */\n    Vector.rotateAbout = function(vector, angle, point, output) {\n        var cos = Math.cos(angle), sin = Math.sin(angle);\n        if (!output) output = {};\n        var x = point.x + ((vector.x - point.x) * cos - (vector.y - point.y) * sin);\n        output.y = point.y + ((vector.x - point.x) * sin + (vector.y - point.y) * cos);\n        output.x = x;\n        return output;\n    };\n\n    /**\n     * Normalises a vector (such that its magnitude is `1`).\n     * @method normalise\n     * @param {vector} vector\n     * @return {vector} A new vector normalised\n     */\n    Vector.normalise = function(vector) {\n        var magnitude = Vector.magnitude(vector);\n        if (magnitude === 0)\n            return { x: 0, y: 0 };\n        return { x: vector.x / magnitude, y: vector.y / magnitude };\n    };\n\n    /**\n     * Returns the dot-product of two vectors.\n     * @method dot\n     * @param {vector} vectorA\n     * @param {vector} vectorB\n     * @return {number} The dot product of the two vectors\n     */\n    Vector.dot = function(vectorA, vectorB) {\n        return (vectorA.x * vectorB.x) + (vectorA.y * vectorB.y);\n    };\n\n    /**\n     * Returns the cross-product of two vectors.\n     * @method cross\n     * @param {vector} vectorA\n     * @param {vector} vectorB\n     * @return {number} The cross product of the two vectors\n     */\n    Vector.cross = function(vectorA, vectorB) {\n        return (vectorA.x * vectorB.y) - (vectorA.y * vectorB.x);\n    };\n\n    /**\n     * Returns the cross-product of three vectors.\n     * @method cross3\n     * @param {vector} vectorA\n     * @param {vector} vectorB\n     * @param {vector} vectorC\n     * @return {number} The cross product of the three vectors\n     */\n    Vector.cross3 = function(vectorA, vectorB, vectorC) {\n        return (vectorB.x - vectorA.x) * (vectorC.y - vectorA.y) - (vectorB.y - vectorA.y) * (vectorC.x - vectorA.x);\n    };\n\n    /**\n     * Adds the two vectors.\n     * @method add\n     * @param {vector} vectorA\n     * @param {vector} vectorB\n     * @param {vector} [output]\n     * @return {vector} A new vector of vectorA and vectorB added\n     */\n    Vector.add = function(vectorA, vectorB, output) {\n        if (!output) output = {};\n        output.x = vectorA.x + vectorB.x;\n        output.y = vectorA.y + vectorB.y;\n        return output;\n    };\n\n    /**\n     * Subtracts the two vectors.\n     * @method sub\n     * @param {vector} vectorA\n     * @param {vector} vectorB\n     * @param {vector} [output]\n     * @return {vector} A new vector of vectorA and vectorB subtracted\n     */\n    Vector.sub = function(vectorA, vectorB, output) {\n        if (!output) output = {};\n        output.x = vectorA.x - vectorB.x;\n        output.y = vectorA.y - vectorB.y;\n        return output;\n    };\n\n    /**\n     * Multiplies a vector and a scalar.\n     * @method mult\n     * @param {vector} vector\n     * @param {number} scalar\n     * @return {vector} A new vector multiplied by scalar\n     */\n    Vector.mult = function(vector, scalar) {\n        return { x: vector.x * scalar, y: vector.y * scalar };\n    };\n\n    /**\n     * Divides a vector and a scalar.\n     * @method div\n     * @param {vector} vector\n     * @param {number} scalar\n     * @return {vector} A new vector divided by scalar\n     */\n    Vector.div = function(vector, scalar) {\n        return { x: vector.x / scalar, y: vector.y / scalar };\n    };\n\n    /**\n     * Returns the perpendicular vector. Set `negate` to true for the perpendicular in the opposite direction.\n     * @method perp\n     * @param {vector} vector\n     * @param {bool} [negate=false]\n     * @return {vector} The perpendicular vector\n     */\n    Vector.perp = function(vector, negate) {\n        negate = negate === true ? -1 : 1;\n        return { x: negate * -vector.y, y: negate * vector.x };\n    };\n\n    /**\n     * Negates both components of a vector such that it points in the opposite direction.\n     * @method neg\n     * @param {vector} vector\n     * @return {vector} The negated vector\n     */\n    Vector.neg = function(vector) {\n        return { x: -vector.x, y: -vector.y };\n    };\n\n    /**\n     * Returns the angle between the vector `vectorB - vectorA` and the x-axis in radians.\n     * @method angle\n     * @param {vector} vectorA\n     * @param {vector} vectorB\n     * @return {number} The angle in radians\n     */\n    Vector.angle = function(vectorA, vectorB) {\n        return Math.atan2(vectorB.y - vectorA.y, vectorB.x - vectorA.x);\n    };\n\n    /**\n     * Temporary vector pool (not thread-safe).\n     * @property _temp\n     * @type {vector[]}\n     * @private\n     */\n    Vector._temp = [\n        Vector.create(), Vector.create(), \n        Vector.create(), Vector.create(), \n        Vector.create(), Vector.create()\n    ];\n\n})();\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Vertices` module contains methods for creating and manipulating sets of vertices.\n* A set of vertices is an array of `Matter.Vector` with additional indexing properties inserted by `Vertices.create`.\n* A `Matter.Body` maintains a set of vertices to represent the shape of the object (its convex hull).\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Vertices\n*/\n\nvar Vertices = {};\n\nmodule.exports = Vertices;\n\nvar Vector = __webpack_require__(2);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * Creates a new set of `Matter.Body` compatible vertices.\n     * The `points` argument accepts an array of `Matter.Vector` points orientated around the origin `(0, 0)`, for example:\n     *\n     *     [{ x: 0, y: 0 }, { x: 25, y: 50 }, { x: 50, y: 0 }]\n     *\n     * The `Vertices.create` method returns a new array of vertices, which are similar to Matter.Vector objects,\n     * but with some additional references required for efficient collision detection routines.\n     *\n     * Vertices must be specified in clockwise order.\n     *\n     * Note that the `body` argument is not optional, a `Matter.Body` reference must be provided.\n     *\n     * @method create\n     * @param {vector[]} points\n     * @param {body} body\n     */\n    Vertices.create = function(points, body) {\n        var vertices = [];\n\n        for (var i = 0; i < points.length; i++) {\n            var point = points[i],\n                vertex = {\n                    x: point.x,\n                    y: point.y,\n                    index: i,\n                    body: body,\n                    isInternal: false\n                };\n\n            vertices.push(vertex);\n        }\n\n        return vertices;\n    };\n\n    /**\n     * Parses a string containing ordered x y pairs separated by spaces (and optionally commas), \n     * into a `Matter.Vertices` object for the given `Matter.Body`.\n     * For parsing SVG paths, see `Svg.pathToVertices`.\n     * @method fromPath\n     * @param {string} path\n     * @param {body} body\n     * @return {vertices} vertices\n     */\n    Vertices.fromPath = function(path, body) {\n        var pathPattern = /L?\\s*([-\\d.e]+)[\\s,]*([-\\d.e]+)*/ig,\n            points = [];\n\n        path.replace(pathPattern, function(match, x, y) {\n            points.push({ x: parseFloat(x), y: parseFloat(y) });\n        });\n\n        return Vertices.create(points, body);\n    };\n\n    /**\n     * Returns the centre (centroid) of the set of vertices.\n     * @method centre\n     * @param {vertices} vertices\n     * @return {vector} The centre point\n     */\n    Vertices.centre = function(vertices) {\n        var area = Vertices.area(vertices, true),\n            centre = { x: 0, y: 0 },\n            cross,\n            temp,\n            j;\n\n        for (var i = 0; i < vertices.length; i++) {\n            j = (i + 1) % vertices.length;\n            cross = Vector.cross(vertices[i], vertices[j]);\n            temp = Vector.mult(Vector.add(vertices[i], vertices[j]), cross);\n            centre = Vector.add(centre, temp);\n        }\n\n        return Vector.div(centre, 6 * area);\n    };\n\n    /**\n     * Returns the average (mean) of the set of vertices.\n     * @method mean\n     * @param {vertices} vertices\n     * @return {vector} The average point\n     */\n    Vertices.mean = function(vertices) {\n        var average = { x: 0, y: 0 };\n\n        for (var i = 0; i < vertices.length; i++) {\n            average.x += vertices[i].x;\n            average.y += vertices[i].y;\n        }\n\n        return Vector.div(average, vertices.length);\n    };\n\n    /**\n     * Returns the area of the set of vertices.\n     * @method area\n     * @param {vertices} vertices\n     * @param {bool} signed\n     * @return {number} The area\n     */\n    Vertices.area = function(vertices, signed) {\n        var area = 0,\n            j = vertices.length - 1;\n\n        for (var i = 0; i < vertices.length; i++) {\n            area += (vertices[j].x - vertices[i].x) * (vertices[j].y + vertices[i].y);\n            j = i;\n        }\n\n        if (signed)\n            return area / 2;\n\n        return Math.abs(area) / 2;\n    };\n\n    /**\n     * Returns the moment of inertia (second moment of area) of the set of vertices given the total mass.\n     * @method inertia\n     * @param {vertices} vertices\n     * @param {number} mass\n     * @return {number} The polygon's moment of inertia\n     */\n    Vertices.inertia = function(vertices, mass) {\n        var numerator = 0,\n            denominator = 0,\n            v = vertices,\n            cross,\n            j;\n\n        // find the polygon's moment of inertia, using second moment of area\n        // from equations at http://www.physicsforums.com/showthread.php?t=25293\n        for (var n = 0; n < v.length; n++) {\n            j = (n + 1) % v.length;\n            cross = Math.abs(Vector.cross(v[j], v[n]));\n            numerator += cross * (Vector.dot(v[j], v[j]) + Vector.dot(v[j], v[n]) + Vector.dot(v[n], v[n]));\n            denominator += cross;\n        }\n\n        return (mass / 6) * (numerator / denominator);\n    };\n\n    /**\n     * Translates the set of vertices in-place.\n     * @method translate\n     * @param {vertices} vertices\n     * @param {vector} vector\n     * @param {number} scalar\n     */\n    Vertices.translate = function(vertices, vector, scalar) {\n        scalar = typeof scalar !== 'undefined' ? scalar : 1;\n\n        var verticesLength = vertices.length,\n            translateX = vector.x * scalar,\n            translateY = vector.y * scalar,\n            i;\n        \n        for (i = 0; i < verticesLength; i++) {\n            vertices[i].x += translateX;\n            vertices[i].y += translateY;\n        }\n\n        return vertices;\n    };\n\n    /**\n     * Rotates the set of vertices in-place.\n     * @method rotate\n     * @param {vertices} vertices\n     * @param {number} angle\n     * @param {vector} point\n     */\n    Vertices.rotate = function(vertices, angle, point) {\n        if (angle === 0)\n            return;\n\n        var cos = Math.cos(angle),\n            sin = Math.sin(angle),\n            pointX = point.x,\n            pointY = point.y,\n            verticesLength = vertices.length,\n            vertex,\n            dx,\n            dy,\n            i;\n\n        for (i = 0; i < verticesLength; i++) {\n            vertex = vertices[i];\n            dx = vertex.x - pointX;\n            dy = vertex.y - pointY;\n            vertex.x = pointX + (dx * cos - dy * sin);\n            vertex.y = pointY + (dx * sin + dy * cos);\n        }\n\n        return vertices;\n    };\n\n    /**\n     * Returns `true` if the `point` is inside the set of `vertices`.\n     * @method contains\n     * @param {vertices} vertices\n     * @param {vector} point\n     * @return {boolean} True if the vertices contains point, otherwise false\n     */\n    Vertices.contains = function(vertices, point) {\n        var pointX = point.x,\n            pointY = point.y,\n            verticesLength = vertices.length,\n            vertex = vertices[verticesLength - 1],\n            nextVertex;\n\n        for (var i = 0; i < verticesLength; i++) {\n            nextVertex = vertices[i];\n\n            if ((pointX - vertex.x) * (nextVertex.y - vertex.y) \n                + (pointY - vertex.y) * (vertex.x - nextVertex.x) > 0) {\n                return false;\n            }\n\n            vertex = nextVertex;\n        }\n\n        return true;\n    };\n\n    /**\n     * Scales the vertices from a point (default is centre) in-place.\n     * @method scale\n     * @param {vertices} vertices\n     * @param {number} scaleX\n     * @param {number} scaleY\n     * @param {vector} point\n     */\n    Vertices.scale = function(vertices, scaleX, scaleY, point) {\n        if (scaleX === 1 && scaleY === 1)\n            return vertices;\n\n        point = point || Vertices.centre(vertices);\n\n        var vertex,\n            delta;\n\n        for (var i = 0; i < vertices.length; i++) {\n            vertex = vertices[i];\n            delta = Vector.sub(vertex, point);\n            vertices[i].x = point.x + delta.x * scaleX;\n            vertices[i].y = point.y + delta.y * scaleY;\n        }\n\n        return vertices;\n    };\n\n    /**\n     * Chamfers a set of vertices by giving them rounded corners, returns a new set of vertices.\n     * The radius parameter is a single number or an array to specify the radius for each vertex.\n     * @method chamfer\n     * @param {vertices} vertices\n     * @param {number[]} radius\n     * @param {number} quality\n     * @param {number} qualityMin\n     * @param {number} qualityMax\n     */\n    Vertices.chamfer = function(vertices, radius, quality, qualityMin, qualityMax) {\n        if (typeof radius === 'number') {\n            radius = [radius];\n        } else {\n            radius = radius || [8];\n        }\n\n        // quality defaults to -1, which is auto\n        quality = (typeof quality !== 'undefined') ? quality : -1;\n        qualityMin = qualityMin || 2;\n        qualityMax = qualityMax || 14;\n\n        var newVertices = [];\n\n        for (var i = 0; i < vertices.length; i++) {\n            var prevVertex = vertices[i - 1 >= 0 ? i - 1 : vertices.length - 1],\n                vertex = vertices[i],\n                nextVertex = vertices[(i + 1) % vertices.length],\n                currentRadius = radius[i < radius.length ? i : radius.length - 1];\n\n            if (currentRadius === 0) {\n                newVertices.push(vertex);\n                continue;\n            }\n\n            var prevNormal = Vector.normalise({ \n                x: vertex.y - prevVertex.y, \n                y: prevVertex.x - vertex.x\n            });\n\n            var nextNormal = Vector.normalise({ \n                x: nextVertex.y - vertex.y, \n                y: vertex.x - nextVertex.x\n            });\n\n            var diagonalRadius = Math.sqrt(2 * Math.pow(currentRadius, 2)),\n                radiusVector = Vector.mult(Common.clone(prevNormal), currentRadius),\n                midNormal = Vector.normalise(Vector.mult(Vector.add(prevNormal, nextNormal), 0.5)),\n                scaledVertex = Vector.sub(vertex, Vector.mult(midNormal, diagonalRadius));\n\n            var precision = quality;\n\n            if (quality === -1) {\n                // automatically decide precision\n                precision = Math.pow(currentRadius, 0.32) * 1.75;\n            }\n\n            precision = Common.clamp(precision, qualityMin, qualityMax);\n\n            // use an even value for precision, more likely to reduce axes by using symmetry\n            if (precision % 2 === 1)\n                precision += 1;\n\n            var alpha = Math.acos(Vector.dot(prevNormal, nextNormal)),\n                theta = alpha / precision;\n\n            for (var j = 0; j < precision; j++) {\n                newVertices.push(Vector.add(Vector.rotate(radiusVector, theta * j), scaledVertex));\n            }\n        }\n\n        return newVertices;\n    };\n\n    /**\n     * Sorts the input vertices into clockwise order in place.\n     * @method clockwiseSort\n     * @param {vertices} vertices\n     * @return {vertices} vertices\n     */\n    Vertices.clockwiseSort = function(vertices) {\n        var centre = Vertices.mean(vertices);\n\n        vertices.sort(function(vertexA, vertexB) {\n            return Vector.angle(centre, vertexA) - Vector.angle(centre, vertexB);\n        });\n\n        return vertices;\n    };\n\n    /**\n     * Returns true if the vertices form a convex shape (vertices must be in clockwise order).\n     * @method isConvex\n     * @param {vertices} vertices\n     * @return {bool} `true` if the `vertices` are convex, `false` if not (or `null` if not computable).\n     */\n    Vertices.isConvex = function(vertices) {\n        // http://paulbourke.net/geometry/polygonmesh/\n        // Copyright (c) Paul Bourke (use permitted)\n\n        var flag = 0,\n            n = vertices.length,\n            i,\n            j,\n            k,\n            z;\n\n        if (n < 3)\n            return null;\n\n        for (i = 0; i < n; i++) {\n            j = (i + 1) % n;\n            k = (i + 2) % n;\n            z = (vertices[j].x - vertices[i].x) * (vertices[k].y - vertices[j].y);\n            z -= (vertices[j].y - vertices[i].y) * (vertices[k].x - vertices[j].x);\n\n            if (z < 0) {\n                flag |= 1;\n            } else if (z > 0) {\n                flag |= 2;\n            }\n\n            if (flag === 3) {\n                return false;\n            }\n        }\n\n        if (flag !== 0){\n            return true;\n        } else {\n            return null;\n        }\n    };\n\n    /**\n     * Returns the convex hull of the input vertices as a new array of points.\n     * @method hull\n     * @param {vertices} vertices\n     * @return [vertex] vertices\n     */\n    Vertices.hull = function(vertices) {\n        // http://geomalgorithms.com/a10-_hull-1.html\n\n        var upper = [],\n            lower = [], \n            vertex,\n            i;\n\n        // sort vertices on x-axis (y-axis for ties)\n        vertices = vertices.slice(0);\n        vertices.sort(function(vertexA, vertexB) {\n            var dx = vertexA.x - vertexB.x;\n            return dx !== 0 ? dx : vertexA.y - vertexB.y;\n        });\n\n        // build lower hull\n        for (i = 0; i < vertices.length; i += 1) {\n            vertex = vertices[i];\n\n            while (lower.length >= 2 \n                   && Vector.cross3(lower[lower.length - 2], lower[lower.length - 1], vertex) <= 0) {\n                lower.pop();\n            }\n\n            lower.push(vertex);\n        }\n\n        // build upper hull\n        for (i = vertices.length - 1; i >= 0; i -= 1) {\n            vertex = vertices[i];\n\n            while (upper.length >= 2 \n                   && Vector.cross3(upper[upper.length - 2], upper[upper.length - 1], vertex) <= 0) {\n                upper.pop();\n            }\n\n            upper.push(vertex);\n        }\n\n        // concatenation of the lower and upper hulls gives the convex hull\n        // omit last points because they are repeated at the beginning of the other list\n        upper.pop();\n        lower.pop();\n\n        return upper.concat(lower);\n    };\n\n})();\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Body` module contains methods for creating and manipulating rigid bodies.\n* For creating bodies with common configurations such as rectangles, circles and other polygons see the module `Matter.Bodies`.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n\n* @class Body\n*/\n\nvar Body = {};\n\nmodule.exports = Body;\n\nvar Vertices = __webpack_require__(3);\nvar Vector = __webpack_require__(2);\nvar Sleeping = __webpack_require__(7);\nvar Common = __webpack_require__(0);\nvar Bounds = __webpack_require__(1);\nvar Axes = __webpack_require__(11);\n\n(function() {\n\n    Body._timeCorrection = true;\n    Body._inertiaScale = 4;\n    Body._nextCollidingGroupId = 1;\n    Body._nextNonCollidingGroupId = -1;\n    Body._nextCategory = 0x0001;\n    Body._baseDelta = 1000 / 60;\n\n    /**\n     * Creates a new rigid body model. The options parameter is an object that specifies any properties you wish to override the defaults.\n     * All properties have default values, and many are pre-calculated automatically based on other properties.\n     * Vertices must be specified in clockwise order.\n     * See the properties section below for detailed information on what you can pass via the `options` object.\n     * @method create\n     * @param {} options\n     * @return {body} body\n     */\n    Body.create = function(options) {\n        var defaults = {\n            id: Common.nextId(),\n            type: 'body',\n            label: 'Body',\n            parts: [],\n            plugin: {},\n            angle: 0,\n            vertices: Vertices.fromPath('L 0 0 L 40 0 L 40 40 L 0 40'),\n            position: { x: 0, y: 0 },\n            force: { x: 0, y: 0 },\n            torque: 0,\n            positionImpulse: { x: 0, y: 0 },\n            constraintImpulse: { x: 0, y: 0, angle: 0 },\n            totalContacts: 0,\n            speed: 0,\n            angularSpeed: 0,\n            velocity: { x: 0, y: 0 },\n            angularVelocity: 0,\n            isSensor: false,\n            isStatic: false,\n            isSleeping: false,\n            motion: 0,\n            sleepThreshold: 60,\n            density: 0.001,\n            restitution: 0,\n            friction: 0.1,\n            frictionStatic: 0.5,\n            frictionAir: 0.01,\n            collisionFilter: {\n                category: 0x0001,\n                mask: 0xFFFFFFFF,\n                group: 0\n            },\n            slop: 0.05,\n            timeScale: 1,\n            render: {\n                visible: true,\n                opacity: 1,\n                strokeStyle: null,\n                fillStyle: null,\n                lineWidth: null,\n                sprite: {\n                    xScale: 1,\n                    yScale: 1,\n                    xOffset: 0,\n                    yOffset: 0\n                }\n            },\n            events: null,\n            bounds: null,\n            chamfer: null,\n            circleRadius: 0,\n            positionPrev: null,\n            anglePrev: 0,\n            parent: null,\n            axes: null,\n            area: 0,\n            mass: 0,\n            inertia: 0,\n            deltaTime: 1000 / 60,\n            _original: null\n        };\n\n        var body = Common.extend(defaults, options);\n\n        _initProperties(body, options);\n\n        return body;\n    };\n\n    /**\n     * Returns the next unique group index for which bodies will collide.\n     * If `isNonColliding` is `true`, returns the next unique group index for which bodies will _not_ collide.\n     * See `body.collisionFilter` for more information.\n     * @method nextGroup\n     * @param {bool} [isNonColliding=false]\n     * @return {Number} Unique group index\n     */\n    Body.nextGroup = function(isNonColliding) {\n        if (isNonColliding)\n            return Body._nextNonCollidingGroupId--;\n\n        return Body._nextCollidingGroupId++;\n    };\n\n    /**\n     * Returns the next unique category bitfield (starting after the initial default category `0x0001`).\n     * There are 32 available. See `body.collisionFilter` for more information.\n     * @method nextCategory\n     * @return {Number} Unique category bitfield\n     */\n    Body.nextCategory = function() {\n        Body._nextCategory = Body._nextCategory << 1;\n        return Body._nextCategory;\n    };\n\n    /**\n     * Initialises body properties.\n     * @method _initProperties\n     * @private\n     * @param {body} body\n     * @param {} [options]\n     */\n    var _initProperties = function(body, options) {\n        options = options || {};\n\n        // init required properties (order is important)\n        Body.set(body, {\n            bounds: body.bounds || Bounds.create(body.vertices),\n            positionPrev: body.positionPrev || Vector.clone(body.position),\n            anglePrev: body.anglePrev || body.angle,\n            vertices: body.vertices,\n            parts: body.parts || [body],\n            isStatic: body.isStatic,\n            isSleeping: body.isSleeping,\n            parent: body.parent || body\n        });\n\n        Vertices.rotate(body.vertices, body.angle, body.position);\n        Axes.rotate(body.axes, body.angle);\n        Bounds.update(body.bounds, body.vertices, body.velocity);\n\n        // allow options to override the automatically calculated properties\n        Body.set(body, {\n            axes: options.axes || body.axes,\n            area: options.area || body.area,\n            mass: options.mass || body.mass,\n            inertia: options.inertia || body.inertia\n        });\n\n        // render properties\n        var defaultFillStyle = (body.isStatic ? '#14151f' : Common.choose(['#f19648', '#f5d259', '#f55a3c', '#063e7b', '#ececd1'])),\n            defaultStrokeStyle = body.isStatic ? '#555' : '#ccc',\n            defaultLineWidth = body.isStatic && body.render.fillStyle === null ? 1 : 0;\n        body.render.fillStyle = body.render.fillStyle || defaultFillStyle;\n        body.render.strokeStyle = body.render.strokeStyle || defaultStrokeStyle;\n        body.render.lineWidth = body.render.lineWidth || defaultLineWidth;\n        body.render.sprite.xOffset += -(body.bounds.min.x - body.position.x) / (body.bounds.max.x - body.bounds.min.x);\n        body.render.sprite.yOffset += -(body.bounds.min.y - body.position.y) / (body.bounds.max.y - body.bounds.min.y);\n    };\n\n    /**\n     * Given a property and a value (or map of), sets the property(s) on the body, using the appropriate setter functions if they exist.\n     * Prefer to use the actual setter functions in performance critical situations.\n     * @method set\n     * @param {body} body\n     * @param {} settings A property name (or map of properties and values) to set on the body.\n     * @param {} value The value to set if `settings` is a single property name.\n     */\n    Body.set = function(body, settings, value) {\n        var property;\n\n        if (typeof settings === 'string') {\n            property = settings;\n            settings = {};\n            settings[property] = value;\n        }\n\n        for (property in settings) {\n            if (!Object.prototype.hasOwnProperty.call(settings, property))\n                continue;\n\n            value = settings[property];\n            switch (property) {\n\n            case 'isStatic':\n                Body.setStatic(body, value);\n                break;\n            case 'isSleeping':\n                Sleeping.set(body, value);\n                break;\n            case 'mass':\n                Body.setMass(body, value);\n                break;\n            case 'density':\n                Body.setDensity(body, value);\n                break;\n            case 'inertia':\n                Body.setInertia(body, value);\n                break;\n            case 'vertices':\n                Body.setVertices(body, value);\n                break;\n            case 'position':\n                Body.setPosition(body, value);\n                break;\n            case 'angle':\n                Body.setAngle(body, value);\n                break;\n            case 'velocity':\n                Body.setVelocity(body, value);\n                break;\n            case 'angularVelocity':\n                Body.setAngularVelocity(body, value);\n                break;\n            case 'speed':\n                Body.setSpeed(body, value);\n                break;\n            case 'angularSpeed':\n                Body.setAngularSpeed(body, value);\n                break;\n            case 'parts':\n                Body.setParts(body, value);\n                break;\n            case 'centre':\n                Body.setCentre(body, value);\n                break;\n            default:\n                body[property] = value;\n\n            }\n        }\n    };\n\n    /**\n     * Sets the body as static, including isStatic flag and setting mass and inertia to Infinity.\n     * @method setStatic\n     * @param {body} body\n     * @param {bool} isStatic\n     */\n    Body.setStatic = function(body, isStatic) {\n        for (var i = 0; i < body.parts.length; i++) {\n            var part = body.parts[i];\n            part.isStatic = isStatic;\n\n            if (isStatic) {\n                part._original = {\n                    restitution: part.restitution,\n                    friction: part.friction,\n                    mass: part.mass,\n                    inertia: part.inertia,\n                    density: part.density,\n                    inverseMass: part.inverseMass,\n                    inverseInertia: part.inverseInertia\n                };\n\n                part.restitution = 0;\n                part.friction = 1;\n                part.mass = part.inertia = part.density = Infinity;\n                part.inverseMass = part.inverseInertia = 0;\n\n                part.positionPrev.x = part.position.x;\n                part.positionPrev.y = part.position.y;\n                part.anglePrev = part.angle;\n                part.angularVelocity = 0;\n                part.speed = 0;\n                part.angularSpeed = 0;\n                part.motion = 0;\n            } else if (part._original) {\n                part.restitution = part._original.restitution;\n                part.friction = part._original.friction;\n                part.mass = part._original.mass;\n                part.inertia = part._original.inertia;\n                part.density = part._original.density;\n                part.inverseMass = part._original.inverseMass;\n                part.inverseInertia = part._original.inverseInertia;\n\n                part._original = null;\n            }\n        }\n    };\n\n    /**\n     * Sets the mass of the body. Inverse mass, density and inertia are automatically updated to reflect the change.\n     * @method setMass\n     * @param {body} body\n     * @param {number} mass\n     */\n    Body.setMass = function(body, mass) {\n        var moment = body.inertia / (body.mass / 6);\n        body.inertia = moment * (mass / 6);\n        body.inverseInertia = 1 / body.inertia;\n\n        body.mass = mass;\n        body.inverseMass = 1 / body.mass;\n        body.density = body.mass / body.area;\n    };\n\n    /**\n     * Sets the density of the body. Mass and inertia are automatically updated to reflect the change.\n     * @method setDensity\n     * @param {body} body\n     * @param {number} density\n     */\n    Body.setDensity = function(body, density) {\n        Body.setMass(body, density * body.area);\n        body.density = density;\n    };\n\n    /**\n     * Sets the moment of inertia of the body. This is the second moment of area in two dimensions.\n     * Inverse inertia is automatically updated to reflect the change. Mass is not changed.\n     * @method setInertia\n     * @param {body} body\n     * @param {number} inertia\n     */\n    Body.setInertia = function(body, inertia) {\n        body.inertia = inertia;\n        body.inverseInertia = 1 / body.inertia;\n    };\n\n    /**\n     * Sets the body's vertices and updates body properties accordingly, including inertia, area and mass (with respect to `body.density`).\n     * Vertices will be automatically transformed to be orientated around their centre of mass as the origin.\n     * They are then automatically translated to world space based on `body.position`.\n     *\n     * The `vertices` argument should be passed as an array of `Matter.Vector` points (or a `Matter.Vertices` array).\n     * Vertices must form a convex hull. Concave vertices must be decomposed into convex parts.\n     * \n     * @method setVertices\n     * @param {body} body\n     * @param {vector[]} vertices\n     */\n    Body.setVertices = function(body, vertices) {\n        // change vertices\n        if (vertices[0].body === body) {\n            body.vertices = vertices;\n        } else {\n            body.vertices = Vertices.create(vertices, body);\n        }\n\n        // update properties\n        body.axes = Axes.fromVertices(body.vertices);\n        body.area = Vertices.area(body.vertices);\n        Body.setMass(body, body.density * body.area);\n\n        // orient vertices around the centre of mass at origin (0, 0)\n        var centre = Vertices.centre(body.vertices);\n        Vertices.translate(body.vertices, centre, -1);\n\n        // update inertia while vertices are at origin (0, 0)\n        Body.setInertia(body, Body._inertiaScale * Vertices.inertia(body.vertices, body.mass));\n\n        // update geometry\n        Vertices.translate(body.vertices, body.position);\n        Bounds.update(body.bounds, body.vertices, body.velocity);\n    };\n\n    /**\n     * Sets the parts of the `body` and updates mass, inertia and centroid.\n     * Each part will have its parent set to `body`.\n     * By default the convex hull will be automatically computed and set on `body`, unless `autoHull` is set to `false.`\n     * Note that this method will ensure that the first part in `body.parts` will always be the `body`.\n     * @method setParts\n     * @param {body} body\n     * @param {body[]} parts\n     * @param {bool} [autoHull=true]\n     */\n    Body.setParts = function(body, parts, autoHull) {\n        var i;\n\n        // add all the parts, ensuring that the first part is always the parent body\n        parts = parts.slice(0);\n        body.parts.length = 0;\n        body.parts.push(body);\n        body.parent = body;\n\n        for (i = 0; i < parts.length; i++) {\n            var part = parts[i];\n            if (part !== body) {\n                part.parent = body;\n                body.parts.push(part);\n            }\n        }\n\n        if (body.parts.length === 1)\n            return;\n\n        autoHull = typeof autoHull !== 'undefined' ? autoHull : true;\n\n        // find the convex hull of all parts to set on the parent body\n        if (autoHull) {\n            var vertices = [];\n            for (i = 0; i < parts.length; i++) {\n                vertices = vertices.concat(parts[i].vertices);\n            }\n\n            Vertices.clockwiseSort(vertices);\n\n            var hull = Vertices.hull(vertices),\n                hullCentre = Vertices.centre(hull);\n\n            Body.setVertices(body, hull);\n            Vertices.translate(body.vertices, hullCentre);\n        }\n\n        // sum the properties of all compound parts of the parent body\n        var total = Body._totalProperties(body);\n\n        body.area = total.area;\n        body.parent = body;\n        body.position.x = total.centre.x;\n        body.position.y = total.centre.y;\n        body.positionPrev.x = total.centre.x;\n        body.positionPrev.y = total.centre.y;\n\n        Body.setMass(body, total.mass);\n        Body.setInertia(body, total.inertia);\n        Body.setPosition(body, total.centre);\n    };\n\n    /**\n     * Set the centre of mass of the body. \n     * The `centre` is a vector in world-space unless `relative` is set, in which case it is a translation.\n     * The centre of mass is the point the body rotates about and can be used to simulate non-uniform density.\n     * This is equal to moving `body.position` but not the `body.vertices`.\n     * Invalid if the `centre` falls outside the body's convex hull.\n     * @method setCentre\n     * @param {body} body\n     * @param {vector} centre\n     * @param {bool} relative\n     */\n    Body.setCentre = function(body, centre, relative) {\n        if (!relative) {\n            body.positionPrev.x = centre.x - (body.position.x - body.positionPrev.x);\n            body.positionPrev.y = centre.y - (body.position.y - body.positionPrev.y);\n            body.position.x = centre.x;\n            body.position.y = centre.y;\n        } else {\n            body.positionPrev.x += centre.x;\n            body.positionPrev.y += centre.y;\n            body.position.x += centre.x;\n            body.position.y += centre.y;\n        }\n    };\n\n    /**\n     * Sets the position of the body. By default velocity is unchanged.\n     * If `updateVelocity` is `true` then velocity is inferred from the change in position.\n     * @method setPosition\n     * @param {body} body\n     * @param {vector} position\n     * @param {boolean} [updateVelocity=false]\n     */\n    Body.setPosition = function(body, position, updateVelocity) {\n        var delta = Vector.sub(position, body.position);\n\n        if (updateVelocity) {\n            body.positionPrev.x = body.position.x;\n            body.positionPrev.y = body.position.y;\n            body.velocity.x = delta.x;\n            body.velocity.y = delta.y;\n            body.speed = Vector.magnitude(delta);\n        } else {\n            body.positionPrev.x += delta.x;\n            body.positionPrev.y += delta.y;\n        }\n\n        for (var i = 0; i < body.parts.length; i++) {\n            var part = body.parts[i];\n            part.position.x += delta.x;\n            part.position.y += delta.y;\n            Vertices.translate(part.vertices, delta);\n            Bounds.update(part.bounds, part.vertices, body.velocity);\n        }\n    };\n\n    /**\n     * Sets the angle of the body. By default angular velocity is unchanged.\n     * If `updateVelocity` is `true` then angular velocity is inferred from the change in angle.\n     * @method setAngle\n     * @param {body} body\n     * @param {number} angle\n     * @param {boolean} [updateVelocity=false]\n     */\n    Body.setAngle = function(body, angle, updateVelocity) {\n        var delta = angle - body.angle;\n        \n        if (updateVelocity) {\n            body.anglePrev = body.angle;\n            body.angularVelocity = delta;\n            body.angularSpeed = Math.abs(delta);\n        } else {\n            body.anglePrev += delta;\n        }\n\n        for (var i = 0; i < body.parts.length; i++) {\n            var part = body.parts[i];\n            part.angle += delta;\n            Vertices.rotate(part.vertices, delta, body.position);\n            Axes.rotate(part.axes, delta);\n            Bounds.update(part.bounds, part.vertices, body.velocity);\n            if (i > 0) {\n                Vector.rotateAbout(part.position, delta, body.position, part.position);\n            }\n        }\n    };\n\n    /**\n     * Sets the current linear velocity of the body.  \n     * Affects body speed.\n     * @method setVelocity\n     * @param {body} body\n     * @param {vector} velocity\n     */\n    Body.setVelocity = function(body, velocity) {\n        var timeScale = body.deltaTime / Body._baseDelta;\n        body.positionPrev.x = body.position.x - velocity.x * timeScale;\n        body.positionPrev.y = body.position.y - velocity.y * timeScale;\n        body.velocity.x = (body.position.x - body.positionPrev.x) / timeScale;\n        body.velocity.y = (body.position.y - body.positionPrev.y) / timeScale;\n        body.speed = Vector.magnitude(body.velocity);\n    };\n\n    /**\n     * Gets the current linear velocity of the body.\n     * @method getVelocity\n     * @param {body} body\n     * @return {vector} velocity\n     */\n    Body.getVelocity = function(body) {\n        var timeScale = Body._baseDelta / body.deltaTime;\n\n        return {\n            x: (body.position.x - body.positionPrev.x) * timeScale,\n            y: (body.position.y - body.positionPrev.y) * timeScale\n        };\n    };\n\n    /**\n     * Gets the current linear speed of the body.  \n     * Equivalent to the magnitude of its velocity.\n     * @method getSpeed\n     * @param {body} body\n     * @return {number} speed\n     */\n    Body.getSpeed = function(body) {\n        return Vector.magnitude(Body.getVelocity(body));\n    };\n\n    /**\n     * Sets the current linear speed of the body.  \n     * Direction is maintained. Affects body velocity.\n     * @method setSpeed\n     * @param {body} body\n     * @param {number} speed\n     */\n    Body.setSpeed = function(body, speed) {\n        Body.setVelocity(body, Vector.mult(Vector.normalise(Body.getVelocity(body)), speed));\n    };\n\n    /**\n     * Sets the current rotational velocity of the body.  \n     * Affects body angular speed.\n     * @method setAngularVelocity\n     * @param {body} body\n     * @param {number} velocity\n     */\n    Body.setAngularVelocity = function(body, velocity) {\n        var timeScale = body.deltaTime / Body._baseDelta;\n        body.anglePrev = body.angle - velocity * timeScale;\n        body.angularVelocity = (body.angle - body.anglePrev) / timeScale;\n        body.angularSpeed = Math.abs(body.angularVelocity);\n    };\n\n    /**\n     * Gets the current rotational velocity of the body.\n     * @method getAngularVelocity\n     * @param {body} body\n     * @return {number} angular velocity\n     */\n    Body.getAngularVelocity = function(body) {\n        return (body.angle - body.anglePrev) * Body._baseDelta / body.deltaTime;\n    };\n\n    /**\n     * Gets the current rotational speed of the body.  \n     * Equivalent to the magnitude of its angular velocity.\n     * @method getAngularSpeed\n     * @param {body} body\n     * @return {number} angular speed\n     */\n    Body.getAngularSpeed = function(body) {\n        return Math.abs(Body.getAngularVelocity(body));\n    };\n\n    /**\n     * Sets the current rotational speed of the body.  \n     * Direction is maintained. Affects body angular velocity.\n     * @method setAngularSpeed\n     * @param {body} body\n     * @param {number} speed\n     */\n    Body.setAngularSpeed = function(body, speed) {\n        Body.setAngularVelocity(body, Common.sign(Body.getAngularVelocity(body)) * speed);\n    };\n\n    /**\n     * Moves a body by a given vector relative to its current position. By default velocity is unchanged.\n     * If `updateVelocity` is `true` then velocity is inferred from the change in position.\n     * @method translate\n     * @param {body} body\n     * @param {vector} translation\n     * @param {boolean} [updateVelocity=false]\n     */\n    Body.translate = function(body, translation, updateVelocity) {\n        Body.setPosition(body, Vector.add(body.position, translation), updateVelocity);\n    };\n\n    /**\n     * Rotates a body by a given angle relative to its current angle. By default angular velocity is unchanged.\n     * If `updateVelocity` is `true` then angular velocity is inferred from the change in angle.\n     * @method rotate\n     * @param {body} body\n     * @param {number} rotation\n     * @param {vector} [point]\n     * @param {boolean} [updateVelocity=false]\n     */\n    Body.rotate = function(body, rotation, point, updateVelocity) {\n        if (!point) {\n            Body.setAngle(body, body.angle + rotation, updateVelocity);\n        } else {\n            var cos = Math.cos(rotation),\n                sin = Math.sin(rotation),\n                dx = body.position.x - point.x,\n                dy = body.position.y - point.y;\n                \n            Body.setPosition(body, {\n                x: point.x + (dx * cos - dy * sin),\n                y: point.y + (dx * sin + dy * cos)\n            }, updateVelocity);\n\n            Body.setAngle(body, body.angle + rotation, updateVelocity);\n        }\n    };\n\n    /**\n     * Scales the body, including updating physical properties (mass, area, axes, inertia), from a world-space point (default is body centre).\n     * @method scale\n     * @param {body} body\n     * @param {number} scaleX\n     * @param {number} scaleY\n     * @param {vector} [point]\n     */\n    Body.scale = function(body, scaleX, scaleY, point) {\n        var totalArea = 0,\n            totalInertia = 0;\n\n        point = point || body.position;\n\n        for (var i = 0; i < body.parts.length; i++) {\n            var part = body.parts[i];\n\n            // scale vertices\n            Vertices.scale(part.vertices, scaleX, scaleY, point);\n\n            // update properties\n            part.axes = Axes.fromVertices(part.vertices);\n            part.area = Vertices.area(part.vertices);\n            Body.setMass(part, body.density * part.area);\n\n            // update inertia (requires vertices to be at origin)\n            Vertices.translate(part.vertices, { x: -part.position.x, y: -part.position.y });\n            Body.setInertia(part, Body._inertiaScale * Vertices.inertia(part.vertices, part.mass));\n            Vertices.translate(part.vertices, { x: part.position.x, y: part.position.y });\n\n            if (i > 0) {\n                totalArea += part.area;\n                totalInertia += part.inertia;\n            }\n\n            // scale position\n            part.position.x = point.x + (part.position.x - point.x) * scaleX;\n            part.position.y = point.y + (part.position.y - point.y) * scaleY;\n\n            // update bounds\n            Bounds.update(part.bounds, part.vertices, body.velocity);\n        }\n\n        // handle parent body\n        if (body.parts.length > 1) {\n            body.area = totalArea;\n\n            if (!body.isStatic) {\n                Body.setMass(body, body.density * totalArea);\n                Body.setInertia(body, totalInertia);\n            }\n        }\n\n        // handle circles\n        if (body.circleRadius) { \n            if (scaleX === scaleY) {\n                body.circleRadius *= scaleX;\n            } else {\n                // body is no longer a circle\n                body.circleRadius = null;\n            }\n        }\n    };\n\n    /**\n     * Performs an update by integrating the equations of motion on the `body`.\n     * This is applied every update by `Matter.Engine` automatically.\n     * @method update\n     * @param {body} body\n     * @param {number} [deltaTime=16.666]\n     */\n    Body.update = function(body, deltaTime) {\n        deltaTime = (typeof deltaTime !== 'undefined' ? deltaTime : (1000 / 60)) * body.timeScale;\n\n        var deltaTimeSquared = deltaTime * deltaTime,\n            correction = Body._timeCorrection ? deltaTime / (body.deltaTime || deltaTime) : 1;\n\n        // from the previous step\n        var frictionAir = 1 - body.frictionAir * (deltaTime / Common._baseDelta),\n            velocityPrevX = (body.position.x - body.positionPrev.x) * correction,\n            velocityPrevY = (body.position.y - body.positionPrev.y) * correction;\n\n        // update velocity with Verlet integration\n        body.velocity.x = (velocityPrevX * frictionAir) + (body.force.x / body.mass) * deltaTimeSquared;\n        body.velocity.y = (velocityPrevY * frictionAir) + (body.force.y / body.mass) * deltaTimeSquared;\n\n        body.positionPrev.x = body.position.x;\n        body.positionPrev.y = body.position.y;\n        body.position.x += body.velocity.x;\n        body.position.y += body.velocity.y;\n        body.deltaTime = deltaTime;\n\n        // update angular velocity with Verlet integration\n        body.angularVelocity = ((body.angle - body.anglePrev) * frictionAir * correction) + (body.torque / body.inertia) * deltaTimeSquared;\n        body.anglePrev = body.angle;\n        body.angle += body.angularVelocity;\n\n        // transform the body geometry\n        for (var i = 0; i < body.parts.length; i++) {\n            var part = body.parts[i];\n\n            Vertices.translate(part.vertices, body.velocity);\n            \n            if (i > 0) {\n                part.position.x += body.velocity.x;\n                part.position.y += body.velocity.y;\n            }\n\n            if (body.angularVelocity !== 0) {\n                Vertices.rotate(part.vertices, body.angularVelocity, body.position);\n                Axes.rotate(part.axes, body.angularVelocity);\n                if (i > 0) {\n                    Vector.rotateAbout(part.position, body.angularVelocity, body.position, part.position);\n                }\n            }\n\n            Bounds.update(part.bounds, part.vertices, body.velocity);\n        }\n    };\n\n    /**\n     * Updates properties `body.velocity`, `body.speed`, `body.angularVelocity` and `body.angularSpeed` which are normalised in relation to `Body._baseDelta`.\n     * @method updateVelocities\n     * @param {body} body\n     */\n    Body.updateVelocities = function(body) {\n        var timeScale = Body._baseDelta / body.deltaTime,\n            bodyVelocity = body.velocity;\n\n        bodyVelocity.x = (body.position.x - body.positionPrev.x) * timeScale;\n        bodyVelocity.y = (body.position.y - body.positionPrev.y) * timeScale;\n        body.speed = Math.sqrt((bodyVelocity.x * bodyVelocity.x) + (bodyVelocity.y * bodyVelocity.y));\n\n        body.angularVelocity = (body.angle - body.anglePrev) * timeScale;\n        body.angularSpeed = Math.abs(body.angularVelocity);\n    };\n\n    /**\n     * Applies the `force` to the `body` from the force origin `position` in world-space, over a single timestep, including applying any resulting angular torque.\n     * \n     * Forces are useful for effects like gravity, wind or rocket thrust, but can be difficult in practice when precise control is needed. In these cases see `Body.setVelocity` and `Body.setPosition` as an alternative.\n     * \n     * The force from this function is only applied once for the duration of a single timestep, in other words the duration depends directly on the current engine update `delta` and the rate of calls to this function.\n     * \n     * Therefore to account for time, you should apply the force constantly over as many engine updates as equivalent to the intended duration.\n     * \n     * If all or part of the force duration is some fraction of a timestep, first multiply the force by `duration / timestep`.\n     * \n     * The force origin `position` in world-space must also be specified. Passing `body.position` will result in zero angular effect as the force origin would be at the centre of mass.\n     * \n     * The `body` will take time to accelerate under a force, the resulting effect depends on duration of the force, the body mass and other forces on the body including friction combined.\n     * @method applyForce\n     * @param {body} body\n     * @param {vector} position The force origin in world-space. Pass `body.position` to avoid angular torque.\n     * @param {vector} force\n     */\n    Body.applyForce = function(body, position, force) {\n        var offset = { x: position.x - body.position.x, y: position.y - body.position.y };\n        body.force.x += force.x;\n        body.force.y += force.y;\n        body.torque += offset.x * force.y - offset.y * force.x;\n    };\n\n    /**\n     * Returns the sums of the properties of all compound parts of the parent body.\n     * @method _totalProperties\n     * @private\n     * @param {body} body\n     * @return {}\n     */\n    Body._totalProperties = function(body) {\n        // from equations at:\n        // https://ecourses.ou.edu/cgi-bin/ebook.cgi?doc=&topic=st&chap_sec=07.2&page=theory\n        // http://output.to/sideway/default.asp?qno=121100087\n\n        var properties = {\n            mass: 0,\n            area: 0,\n            inertia: 0,\n            centre: { x: 0, y: 0 }\n        };\n\n        // sum the properties of all compound parts of the parent body\n        for (var i = body.parts.length === 1 ? 0 : 1; i < body.parts.length; i++) {\n            var part = body.parts[i],\n                mass = part.mass !== Infinity ? part.mass : 1;\n\n            properties.mass += mass;\n            properties.area += part.area;\n            properties.inertia += part.inertia;\n            properties.centre = Vector.add(properties.centre, Vector.mult(part.position, mass));\n        }\n\n        properties.centre = Vector.div(properties.centre, properties.mass);\n\n        return properties;\n    };\n\n    /*\n    *\n    *  Events Documentation\n    *\n    */\n\n    /**\n    * Fired when a body starts sleeping (where `this` is the body).\n    *\n    * @event sleepStart\n    * @this {body} The body that has started sleeping\n    * @param {} event An event object\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when a body ends sleeping (where `this` is the body).\n    *\n    * @event sleepEnd\n    * @this {body} The body that has ended sleeping\n    * @param {} event An event object\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * An integer `Number` uniquely identifying number generated in `Body.create` by `Common.nextId`.\n     *\n     * @property id\n     * @type number\n     */\n\n    /**\n     * _Read only_. Set by `Body.create`.\n     * \n     * A `String` denoting the type of object.\n     *\n     * @readOnly\n     * @property type\n     * @type string\n     * @default \"body\"\n     */\n\n    /**\n     * An arbitrary `String` name to help the user identify and manage bodies.\n     *\n     * @property label\n     * @type string\n     * @default \"Body\"\n     */\n\n    /**\n     * _Read only_. Use `Body.setParts` to set. \n     * \n     * An array of bodies that make up this body. \n     * The first body in the array must always be a self reference to the current body instance.\n     * All bodies in the `parts` array together form a single rigid compound body.\n     * Parts are allowed to overlap, have gaps or holes or even form concave bodies.\n     * Parts themselves should never be added to a `World`, only the parent body should be.\n     * Use `Body.setParts` when setting parts to ensure correct updates of all properties.\n     *\n     * @readOnly\n     * @property parts\n     * @type body[]\n     */\n\n    /**\n     * An object reserved for storing plugin-specific properties.\n     *\n     * @property plugin\n     * @type {}\n     */\n\n    /**\n     * _Read only_. Updated by `Body.setParts`.\n     * \n     * A reference to the body that this is a part of. See `body.parts`.\n     * This is a self reference if the body is not a part of another body.\n     *\n     * @readOnly\n     * @property parent\n     * @type body\n     */\n\n    /**\n     * A `Number` specifying the angle of the body, in radians.\n     *\n     * @property angle\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * _Read only_. Use `Body.setVertices` or `Body.setParts` to set. See also `Bodies.fromVertices`.\n     * \n     * An array of `Vector` objects that specify the convex hull of the rigid body.\n     * These should be provided about the origin `(0, 0)`. E.g.\n     *\n     * `[{ x: 0, y: 0 }, { x: 25, y: 50 }, { x: 50, y: 0 }]`\n     * \n     * Vertices must always be convex, in clockwise order and must not contain any duplicate points.\n     * \n     * Concave vertices should be decomposed into convex `parts`, see `Bodies.fromVertices` and `Body.setParts`.\n     *\n     * When set the vertices are translated such that `body.position` is at the centre of mass.\n     * Many other body properties are automatically calculated from these vertices when set including `density`, `area` and `inertia`.\n     * \n     * The module `Matter.Vertices` contains useful methods for working with vertices.\n     *\n     * @readOnly\n     * @property vertices\n     * @type vector[]\n     */\n\n    /**\n     * _Read only_. Use `Body.setPosition` to set. \n     * \n     * A `Vector` that specifies the current world-space position of the body.\n     * \n     * @readOnly\n     * @property position\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * A `Vector` that accumulates the total force applied to the body for a single update.\n     * Force is zeroed after every `Engine.update`, so constant forces should be applied for every update they are needed. See also `Body.applyForce`.\n     * \n     * @property force\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * A `Number` that accumulates the total torque (turning force) applied to the body for a single update. See also `Body.applyForce`.\n     * Torque is zeroed after every `Engine.update`, so constant torques should be applied for every update they are needed.\n     *\n     * Torques result in angular acceleration on every update, which depends on body inertia and the engine update delta.\n     * \n     * @property torque\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * _Read only_. Use `Body.setSpeed` to set. \n     * \n     * See `Body.getSpeed` for details.\n     * \n     * Equivalent to the magnitude of `body.velocity` (always positive).\n     * \n     * @readOnly\n     * @property speed\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * _Read only_. Use `Body.setVelocity` to set. \n     * \n     * See `Body.getVelocity` for details.\n     * \n     * Equivalent to the magnitude of `body.angularVelocity` (always positive).\n     * \n     * @readOnly\n     * @property velocity\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * _Read only_. Use `Body.setAngularSpeed` to set. \n     * \n     * See `Body.getAngularSpeed` for details.\n     * \n     * \n     * @readOnly\n     * @property angularSpeed\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * _Read only_. Use `Body.setAngularVelocity` to set. \n     * \n     * See `Body.getAngularVelocity` for details.\n     * \n     *\n     * @readOnly\n     * @property angularVelocity\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * _Read only_. Use `Body.setStatic` to set. \n     * \n     * A flag that indicates whether a body is considered static. A static body can never change position or angle and is completely fixed.\n     *\n     * @readOnly\n     * @property isStatic\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag that indicates whether a body is a sensor. Sensor triggers collision events, but doesn't react with colliding body physically.\n     *\n     * @property isSensor\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * _Read only_. Use `Sleeping.set` to set. \n     * \n     * A flag that indicates whether the body is considered sleeping. A sleeping body acts similar to a static body, except it is only temporary and can be awoken.\n     *\n     * @readOnly\n     * @property isSleeping\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * _Read only_. Calculated during engine update only when sleeping is enabled.\n     * \n     * A `Number` that loosely measures the amount of movement a body currently has.\n     *\n     * Derived from `body.speed^2 + body.angularSpeed^2`. See `Sleeping.update`.\n     * \n     * @readOnly\n     * @property motion\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `Number` that defines the length of time during which this body must have near-zero velocity before it is set as sleeping by the `Matter.Sleeping` module (if sleeping is enabled by the engine).\n     * \n     * @property sleepThreshold\n     * @type number\n     * @default 60\n     */\n\n    /**\n     * _Read only_. Use `Body.setDensity` to set. \n     * \n     * A `Number` that defines the density of the body (mass per unit area).\n     * \n     * Mass will also be updated when set.\n     *\n     * @readOnly\n     * @property density\n     * @type number\n     * @default 0.001\n     */\n\n    /**\n     * _Read only_. Use `Body.setMass` to set. \n     * \n     * A `Number` that defines the mass of the body.\n     * \n     * Density will also be updated when set.\n     * \n     * @readOnly\n     * @property mass\n     * @type number\n     */\n\n    /**\n     * _Read only_. Use `Body.setMass` to set. \n     * \n     * A `Number` that defines the inverse mass of the body (`1 / mass`).\n     *\n     * @readOnly\n     * @property inverseMass\n     * @type number\n     */\n\n    /**\n     * _Read only_. Automatically calculated when vertices, mass or density are set or set through `Body.setInertia`.\n     * \n     * A `Number` that defines the moment of inertia of the body. This is the second moment of area in two dimensions.\n     * \n     * Can be manually set to `Infinity` to prevent rotation of the body. See `Body.setInertia`.\n     * \n     * @readOnly\n     * @property inertia\n     * @type number\n     */\n\n    /**\n     * _Read only_. Automatically calculated when vertices, mass or density are set or calculated by `Body.setInertia`.\n     * \n     * A `Number` that defines the inverse moment of inertia of the body (`1 / inertia`).\n     * \n     * @readOnly\n     * @property inverseInertia\n     * @type number\n     */\n\n    /**\n     * A `Number` that defines the restitution (elasticity) of the body. The value is always positive and is in the range `(0, 1)`.\n     * A value of `0` means collisions may be perfectly inelastic and no bouncing may occur. \n     * A value of `0.8` means the body may bounce back with approximately 80% of its kinetic energy.\n     * Note that collision response is based on _pairs_ of bodies, and that `restitution` values are _combined_ with the following formula:\n     *\n     * `Math.max(bodyA.restitution, bodyB.restitution)`\n     *\n     * @property restitution\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `Number` that defines the friction of the body. The value is always positive and is in the range `(0, 1)`.\n     * A value of `0` means that the body may slide indefinitely.\n     * A value of `1` means the body may come to a stop almost instantly after a force is applied.\n     *\n     * The effects of the value may be non-linear. \n     * High values may be unstable depending on the body.\n     * The engine uses a Coulomb friction model including static and kinetic friction.\n     * Note that collision response is based on _pairs_ of bodies, and that `friction` values are _combined_ with the following formula:\n     *\n     * `Math.min(bodyA.friction, bodyB.friction)`\n     *\n     * @property friction\n     * @type number\n     * @default 0.1\n     */\n\n    /**\n     * A `Number` that defines the static friction of the body (in the Coulomb friction model). \n     * A value of `0` means the body will never 'stick' when it is nearly stationary and only dynamic `friction` is used.\n     * The higher the value (e.g. `10`), the more force it will take to initially get the body moving when nearly stationary.\n     * This value is multiplied with the `friction` property to make it easier to change `friction` and maintain an appropriate amount of static friction.\n     *\n     * @property frictionStatic\n     * @type number\n     * @default 0.5\n     */\n\n    /**\n     * A `Number` that defines the air friction of the body (air resistance). \n     * A value of `0` means the body will never slow as it moves through space.\n     * The higher the value, the faster a body slows when moving through space.\n     * The effects of the value are non-linear. \n     *\n     * @property frictionAir\n     * @type number\n     * @default 0.01\n     */\n\n    /**\n     * An `Object` that specifies the collision filtering properties of this body.\n     *\n     * Collisions between two bodies will obey the following rules:\n     * - If the two bodies have the same non-zero value of `collisionFilter.group`,\n     *   they will always collide if the value is positive, and they will never collide\n     *   if the value is negative.\n     * - If the two bodies have different values of `collisionFilter.group` or if one\n     *   (or both) of the bodies has a value of 0, then the category/mask rules apply as follows:\n     *\n     * Each body belongs to a collision category, given by `collisionFilter.category`. This\n     * value is used as a bit field and the category should have only one bit set, meaning that\n     * the value of this property is a power of two in the range [1, 2^31]. Thus, there are 32\n     * different collision categories available.\n     *\n     * Each body also defines a collision bitmask, given by `collisionFilter.mask` which specifies\n     * the categories it collides with (the value is the bitwise AND value of all these categories).\n     *\n     * Using the category/mask rules, two bodies `A` and `B` collide if each includes the other's\n     * category in its mask, i.e. `(categoryA & maskB) !== 0` and `(categoryB & maskA) !== 0`\n     * are both true.\n     *\n     * @property collisionFilter\n     * @type object\n     */\n\n    /**\n     * An Integer `Number`, that specifies the collision group this body belongs to.\n     * See `body.collisionFilter` for more information.\n     *\n     * @property collisionFilter.group\n     * @type object\n     * @default 0\n     */\n\n    /**\n     * A bit field that specifies the collision category this body belongs to.\n     * The category value should have only one bit set, for example `0x0001`.\n     * This means there are up to 32 unique collision categories available.\n     * See `body.collisionFilter` for more information.\n     *\n     * @property collisionFilter.category\n     * @type object\n     * @default 1\n     */\n\n    /**\n     * A bit mask that specifies the collision categories this body may collide with.\n     * See `body.collisionFilter` for more information.\n     *\n     * @property collisionFilter.mask\n     * @type object\n     * @default -1\n     */\n\n    /**\n     * A `Number` that specifies a thin boundary around the body where it is allowed to slightly sink into other bodies.\n     * \n     * This is required for proper collision response, including friction and restitution effects.\n     * \n     * The default should generally suffice in most cases. You may need to decrease this value for very small bodies that are nearing the default value in scale.\n     *\n     * @property slop\n     * @type number\n     * @default 0.05\n     */\n\n    /**\n     * A `Number` that specifies per-body time scaling.\n     *\n     * @property timeScale\n     * @type number\n     * @default 1\n     */\n\n    /**\n     * _Read only_. Updated during engine update.\n     * \n     * A `Number` that records the last delta time value used to update this body.\n     * Used to calculate speed and velocity.\n     *\n     * @readOnly\n     * @property deltaTime\n     * @type number\n     * @default 1000 / 60\n     */\n\n    /**\n     * An `Object` that defines the rendering properties to be consumed by the module `Matter.Render`.\n     *\n     * @property render\n     * @type object\n     */\n\n    /**\n     * A flag that indicates if the body should be rendered.\n     *\n     * @property render.visible\n     * @type boolean\n     * @default true\n     */\n\n    /**\n     * Sets the opacity to use when rendering.\n     *\n     * @property render.opacity\n     * @type number\n     * @default 1\n    */\n\n    /**\n     * An `Object` that defines the sprite properties to use when rendering, if any.\n     *\n     * @property render.sprite\n     * @type object\n     */\n\n    /**\n     * An `String` that defines the path to the image to use as the sprite texture, if any.\n     *\n     * @property render.sprite.texture\n     * @type string\n     */\n     \n    /**\n     * A `Number` that defines the scaling in the x-axis for the sprite, if any.\n     *\n     * @property render.sprite.xScale\n     * @type number\n     * @default 1\n     */\n\n    /**\n     * A `Number` that defines the scaling in the y-axis for the sprite, if any.\n     *\n     * @property render.sprite.yScale\n     * @type number\n     * @default 1\n     */\n\n    /**\n      * A `Number` that defines the offset in the x-axis for the sprite (normalised by texture width).\n      *\n      * @property render.sprite.xOffset\n      * @type number\n      * @default 0\n      */\n\n    /**\n      * A `Number` that defines the offset in the y-axis for the sprite (normalised by texture height).\n      *\n      * @property render.sprite.yOffset\n      * @type number\n      * @default 0\n      */\n\n    /**\n     * A `Number` that defines the line width to use when rendering the body outline (if a sprite is not defined).\n     * A value of `0` means no outline will be rendered.\n     *\n     * @property render.lineWidth\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `String` that defines the fill style to use when rendering the body (if a sprite is not defined).\n     * It is the same as when using a canvas, so it accepts CSS style property values.\n     *\n     * @property render.fillStyle\n     * @type string\n     * @default a random colour\n     */\n\n    /**\n     * A `String` that defines the stroke style to use when rendering the body outline (if a sprite is not defined).\n     * It is the same as when using a canvas, so it accepts CSS style property values.\n     *\n     * @property render.strokeStyle\n     * @type string\n     * @default a random colour\n     */\n\n    /**\n     * _Read only_. Calculated automatically when vertices are set.\n     * \n     * An array of unique axis vectors (edge normals) used for collision detection.\n     * These are automatically calculated when vertices are set.\n     * They are constantly updated by `Body.update` during the simulation.\n     *\n     * @readOnly\n     * @property axes\n     * @type vector[]\n     */\n     \n    /**\n     * _Read only_. Calculated automatically when vertices are set.\n     * \n     * A `Number` that measures the area of the body's convex hull.\n     * \n     * @readOnly\n     * @property area\n     * @type string\n     * @default \n     */\n\n    /**\n     * A `Bounds` object that defines the AABB region for the body.\n     * It is automatically calculated when vertices are set and constantly updated by `Body.update` during simulation.\n     * \n     * @property bounds\n     * @type bounds\n     */\n\n})();\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Events` module contains methods to fire and listen to events on other objects.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Events\n*/\n\nvar Events = {};\n\nmodule.exports = Events;\n\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * Subscribes a callback function to the given object's `eventName`.\n     * @method on\n     * @param {} object\n     * @param {string} eventNames\n     * @param {function} callback\n     */\n    Events.on = function(object, eventNames, callback) {\n        var names = eventNames.split(' '),\n            name;\n\n        for (var i = 0; i < names.length; i++) {\n            name = names[i];\n            object.events = object.events || {};\n            object.events[name] = object.events[name] || [];\n            object.events[name].push(callback);\n        }\n\n        return callback;\n    };\n\n    /**\n     * Removes the given event callback. If no callback, clears all callbacks in `eventNames`. If no `eventNames`, clears all events.\n     * @method off\n     * @param {} object\n     * @param {string} eventNames\n     * @param {function} callback\n     */\n    Events.off = function(object, eventNames, callback) {\n        if (!eventNames) {\n            object.events = {};\n            return;\n        }\n\n        // handle Events.off(object, callback)\n        if (typeof eventNames === 'function') {\n            callback = eventNames;\n            eventNames = Common.keys(object.events).join(' ');\n        }\n\n        var names = eventNames.split(' ');\n\n        for (var i = 0; i < names.length; i++) {\n            var callbacks = object.events[names[i]],\n                newCallbacks = [];\n\n            if (callback && callbacks) {\n                for (var j = 0; j < callbacks.length; j++) {\n                    if (callbacks[j] !== callback)\n                        newCallbacks.push(callbacks[j]);\n                }\n            }\n\n            object.events[names[i]] = newCallbacks;\n        }\n    };\n\n    /**\n     * Fires all the callbacks subscribed to the given object's `eventName`, in the order they subscribed, if any.\n     * @method trigger\n     * @param {} object\n     * @param {string} eventNames\n     * @param {} event\n     */\n    Events.trigger = function(object, eventNames, event) {\n        var names,\n            name,\n            callbacks,\n            eventClone;\n\n        var events = object.events;\n        \n        if (events && Common.keys(events).length > 0) {\n            if (!event)\n                event = {};\n\n            names = eventNames.split(' ');\n\n            for (var i = 0; i < names.length; i++) {\n                name = names[i];\n                callbacks = events[name];\n\n                if (callbacks) {\n                    eventClone = Common.clone(event, false);\n                    eventClone.name = name;\n                    eventClone.source = object;\n\n                    for (var j = 0; j < callbacks.length; j++) {\n                        callbacks[j].apply(object, [eventClone]);\n                    }\n                }\n            }\n        }\n    };\n\n})();\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* A composite is a collection of `Matter.Body`, `Matter.Constraint` and other `Matter.Composite` objects.\n*\n* They are a container that can represent complex objects made of multiple parts, even if they are not physically connected.\n* A composite could contain anything from a single body all the way up to a whole world.\n* \n* When making any changes to composites, use the included functions rather than changing their properties directly.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Composite\n*/\n\nvar Composite = {};\n\nmodule.exports = Composite;\n\nvar Events = __webpack_require__(5);\nvar Common = __webpack_require__(0);\nvar Bounds = __webpack_require__(1);\nvar Body = __webpack_require__(4);\n\n(function() {\n\n    /**\n     * Creates a new composite. The options parameter is an object that specifies any properties you wish to override the defaults.\n     * See the properites section below for detailed information on what you can pass via the `options` object.\n     * @method create\n     * @param {} [options]\n     * @return {composite} A new composite\n     */\n    Composite.create = function(options) {\n        return Common.extend({ \n            id: Common.nextId(),\n            type: 'composite',\n            parent: null,\n            isModified: false,\n            bodies: [], \n            constraints: [], \n            composites: [],\n            label: 'Composite',\n            plugin: {},\n            cache: {\n                allBodies: null,\n                allConstraints: null,\n                allComposites: null\n            }\n        }, options);\n    };\n\n    /**\n     * Sets the composite's `isModified` flag. \n     * If `updateParents` is true, all parents will be set (default: false).\n     * If `updateChildren` is true, all children will be set (default: false).\n     * @private\n     * @method setModified\n     * @param {composite} composite\n     * @param {boolean} isModified\n     * @param {boolean} [updateParents=false]\n     * @param {boolean} [updateChildren=false]\n     */\n    Composite.setModified = function(composite, isModified, updateParents, updateChildren) {\n        composite.isModified = isModified;\n\n        if (isModified && composite.cache) {\n            composite.cache.allBodies = null;\n            composite.cache.allConstraints = null;\n            composite.cache.allComposites = null;\n        }\n\n        if (updateParents && composite.parent) {\n            Composite.setModified(composite.parent, isModified, updateParents, updateChildren);\n        }\n\n        if (updateChildren) {\n            for (var i = 0; i < composite.composites.length; i++) {\n                var childComposite = composite.composites[i];\n                Composite.setModified(childComposite, isModified, updateParents, updateChildren);\n            }\n        }\n    };\n\n    /**\n     * Generic single or multi-add function. Adds a single or an array of body(s), constraint(s) or composite(s) to the given composite.\n     * Triggers `beforeAdd` and `afterAdd` events on the `composite`.\n     * @method add\n     * @param {composite} composite\n     * @param {object|array} object A single or an array of body(s), constraint(s) or composite(s)\n     * @return {composite} The original composite with the objects added\n     */\n    Composite.add = function(composite, object) {\n        var objects = [].concat(object);\n\n        Events.trigger(composite, 'beforeAdd', { object: object });\n\n        for (var i = 0; i < objects.length; i++) {\n            var obj = objects[i];\n\n            switch (obj.type) {\n\n            case 'body':\n                // skip adding compound parts\n                if (obj.parent !== obj) {\n                    Common.warn('Composite.add: skipped adding a compound body part (you must add its parent instead)');\n                    break;\n                }\n\n                Composite.addBody(composite, obj);\n                break;\n            case 'constraint':\n                Composite.addConstraint(composite, obj);\n                break;\n            case 'composite':\n                Composite.addComposite(composite, obj);\n                break;\n            case 'mouseConstraint':\n                Composite.addConstraint(composite, obj.constraint);\n                break;\n\n            }\n        }\n\n        Events.trigger(composite, 'afterAdd', { object: object });\n\n        return composite;\n    };\n\n    /**\n     * Generic remove function. Removes one or many body(s), constraint(s) or a composite(s) to the given composite.\n     * Optionally searching its children recursively.\n     * Triggers `beforeRemove` and `afterRemove` events on the `composite`.\n     * @method remove\n     * @param {composite} composite\n     * @param {object|array} object\n     * @param {boolean} [deep=false]\n     * @return {composite} The original composite with the objects removed\n     */\n    Composite.remove = function(composite, object, deep) {\n        var objects = [].concat(object);\n\n        Events.trigger(composite, 'beforeRemove', { object: object });\n\n        for (var i = 0; i < objects.length; i++) {\n            var obj = objects[i];\n\n            switch (obj.type) {\n\n            case 'body':\n                Composite.removeBody(composite, obj, deep);\n                break;\n            case 'constraint':\n                Composite.removeConstraint(composite, obj, deep);\n                break;\n            case 'composite':\n                Composite.removeComposite(composite, obj, deep);\n                break;\n            case 'mouseConstraint':\n                Composite.removeConstraint(composite, obj.constraint);\n                break;\n\n            }\n        }\n\n        Events.trigger(composite, 'afterRemove', { object: object });\n\n        return composite;\n    };\n\n    /**\n     * Adds a composite to the given composite.\n     * @private\n     * @method addComposite\n     * @param {composite} compositeA\n     * @param {composite} compositeB\n     * @return {composite} The original compositeA with the objects from compositeB added\n     */\n    Composite.addComposite = function(compositeA, compositeB) {\n        compositeA.composites.push(compositeB);\n        compositeB.parent = compositeA;\n        Composite.setModified(compositeA, true, true, false);\n        return compositeA;\n    };\n\n    /**\n     * Removes a composite from the given composite, and optionally searching its children recursively.\n     * @private\n     * @method removeComposite\n     * @param {composite} compositeA\n     * @param {composite} compositeB\n     * @param {boolean} [deep=false]\n     * @return {composite} The original compositeA with the composite removed\n     */\n    Composite.removeComposite = function(compositeA, compositeB, deep) {\n        var position = Common.indexOf(compositeA.composites, compositeB);\n        if (position !== -1) {\n            Composite.removeCompositeAt(compositeA, position);\n        }\n\n        if (deep) {\n            for (var i = 0; i < compositeA.composites.length; i++){\n                Composite.removeComposite(compositeA.composites[i], compositeB, true);\n            }\n        }\n\n        return compositeA;\n    };\n\n    /**\n     * Removes a composite from the given composite.\n     * @private\n     * @method removeCompositeAt\n     * @param {composite} composite\n     * @param {number} position\n     * @return {composite} The original composite with the composite removed\n     */\n    Composite.removeCompositeAt = function(composite, position) {\n        composite.composites.splice(position, 1);\n        Composite.setModified(composite, true, true, false);\n        return composite;\n    };\n\n    /**\n     * Adds a body to the given composite.\n     * @private\n     * @method addBody\n     * @param {composite} composite\n     * @param {body} body\n     * @return {composite} The original composite with the body added\n     */\n    Composite.addBody = function(composite, body) {\n        composite.bodies.push(body);\n        Composite.setModified(composite, true, true, false);\n        return composite;\n    };\n\n    /**\n     * Removes a body from the given composite, and optionally searching its children recursively.\n     * @private\n     * @method removeBody\n     * @param {composite} composite\n     * @param {body} body\n     * @param {boolean} [deep=false]\n     * @return {composite} The original composite with the body removed\n     */\n    Composite.removeBody = function(composite, body, deep) {\n        var position = Common.indexOf(composite.bodies, body);\n        if (position !== -1) {\n            Composite.removeBodyAt(composite, position);\n        }\n\n        if (deep) {\n            for (var i = 0; i < composite.composites.length; i++){\n                Composite.removeBody(composite.composites[i], body, true);\n            }\n        }\n\n        return composite;\n    };\n\n    /**\n     * Removes a body from the given composite.\n     * @private\n     * @method removeBodyAt\n     * @param {composite} composite\n     * @param {number} position\n     * @return {composite} The original composite with the body removed\n     */\n    Composite.removeBodyAt = function(composite, position) {\n        composite.bodies.splice(position, 1);\n        Composite.setModified(composite, true, true, false);\n        return composite;\n    };\n\n    /**\n     * Adds a constraint to the given composite.\n     * @private\n     * @method addConstraint\n     * @param {composite} composite\n     * @param {constraint} constraint\n     * @return {composite} The original composite with the constraint added\n     */\n    Composite.addConstraint = function(composite, constraint) {\n        composite.constraints.push(constraint);\n        Composite.setModified(composite, true, true, false);\n        return composite;\n    };\n\n    /**\n     * Removes a constraint from the given composite, and optionally searching its children recursively.\n     * @private\n     * @method removeConstraint\n     * @param {composite} composite\n     * @param {constraint} constraint\n     * @param {boolean} [deep=false]\n     * @return {composite} The original composite with the constraint removed\n     */\n    Composite.removeConstraint = function(composite, constraint, deep) {\n        var position = Common.indexOf(composite.constraints, constraint);\n        if (position !== -1) {\n            Composite.removeConstraintAt(composite, position);\n        }\n\n        if (deep) {\n            for (var i = 0; i < composite.composites.length; i++){\n                Composite.removeConstraint(composite.composites[i], constraint, true);\n            }\n        }\n\n        return composite;\n    };\n\n    /**\n     * Removes a body from the given composite.\n     * @private\n     * @method removeConstraintAt\n     * @param {composite} composite\n     * @param {number} position\n     * @return {composite} The original composite with the constraint removed\n     */\n    Composite.removeConstraintAt = function(composite, position) {\n        composite.constraints.splice(position, 1);\n        Composite.setModified(composite, true, true, false);\n        return composite;\n    };\n\n    /**\n     * Removes all bodies, constraints and composites from the given composite.\n     * Optionally clearing its children recursively.\n     * @method clear\n     * @param {composite} composite\n     * @param {boolean} keepStatic\n     * @param {boolean} [deep=false]\n     */\n    Composite.clear = function(composite, keepStatic, deep) {\n        if (deep) {\n            for (var i = 0; i < composite.composites.length; i++){\n                Composite.clear(composite.composites[i], keepStatic, true);\n            }\n        }\n        \n        if (keepStatic) {\n            composite.bodies = composite.bodies.filter(function(body) { return body.isStatic; });\n        } else {\n            composite.bodies.length = 0;\n        }\n\n        composite.constraints.length = 0;\n        composite.composites.length = 0;\n\n        Composite.setModified(composite, true, true, false);\n\n        return composite;\n    };\n\n    /**\n     * Returns all bodies in the given composite, including all bodies in its children, recursively.\n     * @method allBodies\n     * @param {composite} composite\n     * @return {body[]} All the bodies\n     */\n    Composite.allBodies = function(composite) {\n        if (composite.cache && composite.cache.allBodies) {\n            return composite.cache.allBodies;\n        }\n\n        var bodies = [].concat(composite.bodies);\n\n        for (var i = 0; i < composite.composites.length; i++)\n            bodies = bodies.concat(Composite.allBodies(composite.composites[i]));\n\n        if (composite.cache) {\n            composite.cache.allBodies = bodies;\n        }\n\n        return bodies;\n    };\n\n    /**\n     * Returns all constraints in the given composite, including all constraints in its children, recursively.\n     * @method allConstraints\n     * @param {composite} composite\n     * @return {constraint[]} All the constraints\n     */\n    Composite.allConstraints = function(composite) {\n        if (composite.cache && composite.cache.allConstraints) {\n            return composite.cache.allConstraints;\n        }\n\n        var constraints = [].concat(composite.constraints);\n\n        for (var i = 0; i < composite.composites.length; i++)\n            constraints = constraints.concat(Composite.allConstraints(composite.composites[i]));\n\n        if (composite.cache) {\n            composite.cache.allConstraints = constraints;\n        }\n\n        return constraints;\n    };\n\n    /**\n     * Returns all composites in the given composite, including all composites in its children, recursively.\n     * @method allComposites\n     * @param {composite} composite\n     * @return {composite[]} All the composites\n     */\n    Composite.allComposites = function(composite) {\n        if (composite.cache && composite.cache.allComposites) {\n            return composite.cache.allComposites;\n        }\n\n        var composites = [].concat(composite.composites);\n\n        for (var i = 0; i < composite.composites.length; i++)\n            composites = composites.concat(Composite.allComposites(composite.composites[i]));\n\n        if (composite.cache) {\n            composite.cache.allComposites = composites;\n        }\n\n        return composites;\n    };\n\n    /**\n     * Searches the composite recursively for an object matching the type and id supplied, null if not found.\n     * @method get\n     * @param {composite} composite\n     * @param {number} id\n     * @param {string} type\n     * @return {object} The requested object, if found\n     */\n    Composite.get = function(composite, id, type) {\n        var objects,\n            object;\n\n        switch (type) {\n        case 'body':\n            objects = Composite.allBodies(composite);\n            break;\n        case 'constraint':\n            objects = Composite.allConstraints(composite);\n            break;\n        case 'composite':\n            objects = Composite.allComposites(composite).concat(composite);\n            break;\n        }\n\n        if (!objects)\n            return null;\n\n        object = objects.filter(function(object) { \n            return object.id.toString() === id.toString(); \n        });\n\n        return object.length === 0 ? null : object[0];\n    };\n\n    /**\n     * Moves the given object(s) from compositeA to compositeB (equal to a remove followed by an add).\n     * @method move\n     * @param {compositeA} compositeA\n     * @param {object[]} objects\n     * @param {compositeB} compositeB\n     * @return {composite} Returns compositeA\n     */\n    Composite.move = function(compositeA, objects, compositeB) {\n        Composite.remove(compositeA, objects);\n        Composite.add(compositeB, objects);\n        return compositeA;\n    };\n\n    /**\n     * Assigns new ids for all objects in the composite, recursively.\n     * @method rebase\n     * @param {composite} composite\n     * @return {composite} Returns composite\n     */\n    Composite.rebase = function(composite) {\n        var objects = Composite.allBodies(composite)\n            .concat(Composite.allConstraints(composite))\n            .concat(Composite.allComposites(composite));\n\n        for (var i = 0; i < objects.length; i++) {\n            objects[i].id = Common.nextId();\n        }\n\n        return composite;\n    };\n\n    /**\n     * Translates all children in the composite by a given vector relative to their current positions, \n     * without imparting any velocity.\n     * @method translate\n     * @param {composite} composite\n     * @param {vector} translation\n     * @param {bool} [recursive=true]\n     */\n    Composite.translate = function(composite, translation, recursive) {\n        var bodies = recursive ? Composite.allBodies(composite) : composite.bodies;\n\n        for (var i = 0; i < bodies.length; i++) {\n            Body.translate(bodies[i], translation);\n        }\n\n        return composite;\n    };\n\n    /**\n     * Rotates all children in the composite by a given angle about the given point, without imparting any angular velocity.\n     * @method rotate\n     * @param {composite} composite\n     * @param {number} rotation\n     * @param {vector} point\n     * @param {bool} [recursive=true]\n     */\n    Composite.rotate = function(composite, rotation, point, recursive) {\n        var cos = Math.cos(rotation),\n            sin = Math.sin(rotation),\n            bodies = recursive ? Composite.allBodies(composite) : composite.bodies;\n\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i],\n                dx = body.position.x - point.x,\n                dy = body.position.y - point.y;\n                \n            Body.setPosition(body, {\n                x: point.x + (dx * cos - dy * sin),\n                y: point.y + (dx * sin + dy * cos)\n            });\n\n            Body.rotate(body, rotation);\n        }\n\n        return composite;\n    };\n\n    /**\n     * Scales all children in the composite, including updating physical properties (mass, area, axes, inertia), from a world-space point.\n     * @method scale\n     * @param {composite} composite\n     * @param {number} scaleX\n     * @param {number} scaleY\n     * @param {vector} point\n     * @param {bool} [recursive=true]\n     */\n    Composite.scale = function(composite, scaleX, scaleY, point, recursive) {\n        var bodies = recursive ? Composite.allBodies(composite) : composite.bodies;\n\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i],\n                dx = body.position.x - point.x,\n                dy = body.position.y - point.y;\n                \n            Body.setPosition(body, {\n                x: point.x + dx * scaleX,\n                y: point.y + dy * scaleY\n            });\n\n            Body.scale(body, scaleX, scaleY);\n        }\n\n        return composite;\n    };\n\n    /**\n     * Returns the union of the bounds of all of the composite's bodies.\n     * @method bounds\n     * @param {composite} composite The composite.\n     * @returns {bounds} The composite bounds.\n     */\n    Composite.bounds = function(composite) {\n        var bodies = Composite.allBodies(composite),\n            vertices = [];\n\n        for (var i = 0; i < bodies.length; i += 1) {\n            var body = bodies[i];\n            vertices.push(body.bounds.min, body.bounds.max);\n        }\n\n        return Bounds.create(vertices);\n    };\n\n    /*\n    *\n    *  Events Documentation\n    *\n    */\n\n    /**\n    * Fired when a call to `Composite.add` is made, before objects have been added.\n    *\n    * @event beforeAdd\n    * @param {} event An event object\n    * @param {} event.object The object(s) to be added (may be a single body, constraint, composite or a mixed array of these)\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when a call to `Composite.add` is made, after objects have been added.\n    *\n    * @event afterAdd\n    * @param {} event An event object\n    * @param {} event.object The object(s) that have been added (may be a single body, constraint, composite or a mixed array of these)\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when a call to `Composite.remove` is made, before objects have been removed.\n    *\n    * @event beforeRemove\n    * @param {} event An event object\n    * @param {} event.object The object(s) to be removed (may be a single body, constraint, composite or a mixed array of these)\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when a call to `Composite.remove` is made, after objects have been removed.\n    *\n    * @event afterRemove\n    * @param {} event An event object\n    * @param {} event.object The object(s) that have been removed (may be a single body, constraint, composite or a mixed array of these)\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * An integer `Number` uniquely identifying number generated in `Composite.create` by `Common.nextId`.\n     *\n     * @property id\n     * @type number\n     */\n\n    /**\n     * A `String` denoting the type of object.\n     *\n     * @property type\n     * @type string\n     * @default \"composite\"\n     * @readOnly\n     */\n\n    /**\n     * An arbitrary `String` name to help the user identify and manage composites.\n     *\n     * @property label\n     * @type string\n     * @default \"Composite\"\n     */\n\n    /**\n     * A flag that specifies whether the composite has been modified during the current step.\n     * This is automatically managed when bodies, constraints or composites are added or removed.\n     *\n     * @property isModified\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * The `Composite` that is the parent of this composite. It is automatically managed by the `Matter.Composite` methods.\n     *\n     * @property parent\n     * @type composite\n     * @default null\n     */\n\n    /**\n     * An array of `Body` that are _direct_ children of this composite.\n     * To add or remove bodies you should use `Composite.add` and `Composite.remove` methods rather than directly modifying this property.\n     * If you wish to recursively find all descendants, you should use the `Composite.allBodies` method.\n     *\n     * @property bodies\n     * @type body[]\n     * @default []\n     */\n\n    /**\n     * An array of `Constraint` that are _direct_ children of this composite.\n     * To add or remove constraints you should use `Composite.add` and `Composite.remove` methods rather than directly modifying this property.\n     * If you wish to recursively find all descendants, you should use the `Composite.allConstraints` method.\n     *\n     * @property constraints\n     * @type constraint[]\n     * @default []\n     */\n\n    /**\n     * An array of `Composite` that are _direct_ children of this composite.\n     * To add or remove composites you should use `Composite.add` and `Composite.remove` methods rather than directly modifying this property.\n     * If you wish to recursively find all descendants, you should use the `Composite.allComposites` method.\n     *\n     * @property composites\n     * @type composite[]\n     * @default []\n     */\n\n    /**\n     * An object reserved for storing plugin-specific properties.\n     *\n     * @property plugin\n     * @type {}\n     */\n\n    /**\n     * An object used for storing cached results for performance reasons.\n     * This is used internally only and is automatically managed.\n     *\n     * @private\n     * @property cache\n     * @type {}\n     */\n\n})();\n\n\n/***/ }),\n/* 7 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Sleeping` module contains methods to manage the sleeping state of bodies.\n*\n* @class Sleeping\n*/\n\nvar Sleeping = {};\n\nmodule.exports = Sleeping;\n\nvar Body = __webpack_require__(4);\nvar Events = __webpack_require__(5);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    Sleeping._motionWakeThreshold = 0.18;\n    Sleeping._motionSleepThreshold = 0.08;\n    Sleeping._minBias = 0.9;\n\n    /**\n     * Puts bodies to sleep or wakes them up depending on their motion.\n     * @method update\n     * @param {body[]} bodies\n     * @param {number} delta\n     */\n    Sleeping.update = function(bodies, delta) {\n        var timeScale = delta / Common._baseDelta,\n            motionSleepThreshold = Sleeping._motionSleepThreshold;\n        \n        // update bodies sleeping status\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i],\n                speed = Body.getSpeed(body),\n                angularSpeed = Body.getAngularSpeed(body),\n                motion = speed * speed + angularSpeed * angularSpeed;\n\n            // wake up bodies if they have a force applied\n            if (body.force.x !== 0 || body.force.y !== 0) {\n                Sleeping.set(body, false);\n                continue;\n            }\n\n            var minMotion = Math.min(body.motion, motion),\n                maxMotion = Math.max(body.motion, motion);\n        \n            // biased average motion estimation between frames\n            body.motion = Sleeping._minBias * minMotion + (1 - Sleeping._minBias) * maxMotion;\n\n            if (body.sleepThreshold > 0 && body.motion < motionSleepThreshold) {\n                body.sleepCounter += 1;\n                \n                if (body.sleepCounter >= body.sleepThreshold / timeScale) {\n                    Sleeping.set(body, true);\n                }\n            } else if (body.sleepCounter > 0) {\n                body.sleepCounter -= 1;\n            }\n        }\n    };\n\n    /**\n     * Given a set of colliding pairs, wakes the sleeping bodies involved.\n     * @method afterCollisions\n     * @param {pair[]} pairs\n     */\n    Sleeping.afterCollisions = function(pairs) {\n        var motionSleepThreshold = Sleeping._motionSleepThreshold;\n\n        // wake up bodies involved in collisions\n        for (var i = 0; i < pairs.length; i++) {\n            var pair = pairs[i];\n            \n            // don't wake inactive pairs\n            if (!pair.isActive)\n                continue;\n\n            var collision = pair.collision,\n                bodyA = collision.bodyA.parent, \n                bodyB = collision.bodyB.parent;\n        \n            // don't wake if at least one body is static\n            if ((bodyA.isSleeping && bodyB.isSleeping) || bodyA.isStatic || bodyB.isStatic)\n                continue;\n        \n            if (bodyA.isSleeping || bodyB.isSleeping) {\n                var sleepingBody = (bodyA.isSleeping && !bodyA.isStatic) ? bodyA : bodyB,\n                    movingBody = sleepingBody === bodyA ? bodyB : bodyA;\n\n                if (!sleepingBody.isStatic && movingBody.motion > motionSleepThreshold) {\n                    Sleeping.set(sleepingBody, false);\n                }\n            }\n        }\n    };\n  \n    /**\n     * Set a body as sleeping or awake.\n     * @method set\n     * @param {body} body\n     * @param {boolean} isSleeping\n     */\n    Sleeping.set = function(body, isSleeping) {\n        var wasSleeping = body.isSleeping;\n\n        if (isSleeping) {\n            body.isSleeping = true;\n            body.sleepCounter = body.sleepThreshold;\n\n            body.positionImpulse.x = 0;\n            body.positionImpulse.y = 0;\n\n            body.positionPrev.x = body.position.x;\n            body.positionPrev.y = body.position.y;\n\n            body.anglePrev = body.angle;\n            body.speed = 0;\n            body.angularSpeed = 0;\n            body.motion = 0;\n\n            if (!wasSleeping) {\n                Events.trigger(body, 'sleepStart');\n            }\n        } else {\n            body.isSleeping = false;\n            body.sleepCounter = 0;\n\n            if (wasSleeping) {\n                Events.trigger(body, 'sleepEnd');\n            }\n        }\n    };\n\n})();\n\n\n/***/ }),\n/* 8 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Collision` module contains methods for detecting collisions between a given pair of bodies.\n*\n* For efficient detection between a list of bodies, see `Matter.Detector` and `Matter.Query`.\n*\n* See `Matter.Engine` for collision events.\n*\n* @class Collision\n*/\n\nvar Collision = {};\n\nmodule.exports = Collision;\n\nvar Vertices = __webpack_require__(3);\nvar Pair = __webpack_require__(9);\n\n(function() {\n    var _supports = [];\n\n    var _overlapAB = {\n        overlap: 0,\n        axis: null\n    };\n\n    var _overlapBA = {\n        overlap: 0,\n        axis: null\n    };\n\n    /**\n     * Creates a new collision record.\n     * @method create\n     * @param {body} bodyA The first body part represented by the collision record\n     * @param {body} bodyB The second body part represented by the collision record\n     * @return {collision} A new collision record\n     */\n    Collision.create = function(bodyA, bodyB) {\n        return { \n            pair: null,\n            collided: false,\n            bodyA: bodyA,\n            bodyB: bodyB,\n            parentA: bodyA.parent,\n            parentB: bodyB.parent,\n            depth: 0,\n            normal: { x: 0, y: 0 },\n            tangent: { x: 0, y: 0 },\n            penetration: { x: 0, y: 0 },\n            supports: []\n        };\n    };\n\n    /**\n     * Detect collision between two bodies.\n     * @method collides\n     * @param {body} bodyA\n     * @param {body} bodyB\n     * @param {pairs} [pairs] Optionally reuse collision records from existing pairs.\n     * @return {collision|null} A collision record if detected, otherwise null\n     */\n    Collision.collides = function(bodyA, bodyB, pairs) {\n        Collision._overlapAxes(_overlapAB, bodyA.vertices, bodyB.vertices, bodyA.axes);\n\n        if (_overlapAB.overlap <= 0) {\n            return null;\n        }\n\n        Collision._overlapAxes(_overlapBA, bodyB.vertices, bodyA.vertices, bodyB.axes);\n\n        if (_overlapBA.overlap <= 0) {\n            return null;\n        }\n\n        // reuse collision records for gc efficiency\n        var pair = pairs && pairs.table[Pair.id(bodyA, bodyB)],\n            collision;\n\n        if (!pair) {\n            collision = Collision.create(bodyA, bodyB);\n            collision.collided = true;\n            collision.bodyA = bodyA.id < bodyB.id ? bodyA : bodyB;\n            collision.bodyB = bodyA.id < bodyB.id ? bodyB : bodyA;\n            collision.parentA = collision.bodyA.parent;\n            collision.parentB = collision.bodyB.parent;\n        } else {\n            collision = pair.collision;\n        }\n\n        bodyA = collision.bodyA;\n        bodyB = collision.bodyB;\n\n        var minOverlap;\n\n        if (_overlapAB.overlap < _overlapBA.overlap) {\n            minOverlap = _overlapAB;\n        } else {\n            minOverlap = _overlapBA;\n        }\n\n        var normal = collision.normal,\n            supports = collision.supports,\n            minAxis = minOverlap.axis,\n            minAxisX = minAxis.x,\n            minAxisY = minAxis.y;\n\n        // ensure normal is facing away from bodyA\n        if (minAxisX * (bodyB.position.x - bodyA.position.x) + minAxisY * (bodyB.position.y - bodyA.position.y) < 0) {\n            normal.x = minAxisX;\n            normal.y = minAxisY;\n        } else {\n            normal.x = -minAxisX;\n            normal.y = -minAxisY;\n        }\n        \n        collision.tangent.x = -normal.y;\n        collision.tangent.y = normal.x;\n\n        collision.depth = minOverlap.overlap;\n\n        collision.penetration.x = normal.x * collision.depth;\n        collision.penetration.y = normal.y * collision.depth;\n\n        // find support points, there is always either exactly one or two\n        var supportsB = Collision._findSupports(bodyA, bodyB, normal, 1),\n            supportCount = 0;\n\n        // find the supports from bodyB that are inside bodyA\n        if (Vertices.contains(bodyA.vertices, supportsB[0])) {\n            supports[supportCount++] = supportsB[0];\n        }\n\n        if (Vertices.contains(bodyA.vertices, supportsB[1])) {\n            supports[supportCount++] = supportsB[1];\n        }\n\n        // find the supports from bodyA that are inside bodyB\n        if (supportCount < 2) {\n            var supportsA = Collision._findSupports(bodyB, bodyA, normal, -1);\n\n            if (Vertices.contains(bodyB.vertices, supportsA[0])) {\n                supports[supportCount++] = supportsA[0];\n            }\n\n            if (supportCount < 2 && Vertices.contains(bodyB.vertices, supportsA[1])) {\n                supports[supportCount++] = supportsA[1];\n            }\n        }\n\n        // account for the edge case of overlapping but no vertex containment\n        if (supportCount === 0) {\n            supports[supportCount++] = supportsB[0];\n        }\n\n        // update supports array size\n        supports.length = supportCount;\n\n        return collision;\n    };\n\n    /**\n     * Find the overlap between two sets of vertices.\n     * @method _overlapAxes\n     * @private\n     * @param {object} result\n     * @param {vertices} verticesA\n     * @param {vertices} verticesB\n     * @param {axes} axes\n     */\n    Collision._overlapAxes = function(result, verticesA, verticesB, axes) {\n        var verticesALength = verticesA.length,\n            verticesBLength = verticesB.length,\n            verticesAX = verticesA[0].x,\n            verticesAY = verticesA[0].y,\n            verticesBX = verticesB[0].x,\n            verticesBY = verticesB[0].y,\n            axesLength = axes.length,\n            overlapMin = Number.MAX_VALUE,\n            overlapAxisNumber = 0,\n            overlap,\n            overlapAB,\n            overlapBA,\n            dot,\n            i,\n            j;\n\n        for (i = 0; i < axesLength; i++) {\n            var axis = axes[i],\n                axisX = axis.x,\n                axisY = axis.y,\n                minA = verticesAX * axisX + verticesAY * axisY,\n                minB = verticesBX * axisX + verticesBY * axisY,\n                maxA = minA,\n                maxB = minB;\n            \n            for (j = 1; j < verticesALength; j += 1) {\n                dot = verticesA[j].x * axisX + verticesA[j].y * axisY;\n\n                if (dot > maxA) { \n                    maxA = dot;\n                } else if (dot < minA) { \n                    minA = dot;\n                }\n            }\n\n            for (j = 1; j < verticesBLength; j += 1) {\n                dot = verticesB[j].x * axisX + verticesB[j].y * axisY;\n\n                if (dot > maxB) { \n                    maxB = dot;\n                } else if (dot < minB) { \n                    minB = dot;\n                }\n            }\n\n            overlapAB = maxA - minB;\n            overlapBA = maxB - minA;\n            overlap = overlapAB < overlapBA ? overlapAB : overlapBA;\n\n            if (overlap < overlapMin) {\n                overlapMin = overlap;\n                overlapAxisNumber = i;\n\n                if (overlap <= 0) {\n                    // can not be intersecting\n                    break;\n                }\n            } \n        }\n\n        result.axis = axes[overlapAxisNumber];\n        result.overlap = overlapMin;\n    };\n\n    /**\n     * Projects vertices on an axis and returns an interval.\n     * @method _projectToAxis\n     * @private\n     * @param {} projection\n     * @param {} vertices\n     * @param {} axis\n     */\n    Collision._projectToAxis = function(projection, vertices, axis) {\n        var min = vertices[0].x * axis.x + vertices[0].y * axis.y,\n            max = min;\n\n        for (var i = 1; i < vertices.length; i += 1) {\n            var dot = vertices[i].x * axis.x + vertices[i].y * axis.y;\n\n            if (dot > max) { \n                max = dot; \n            } else if (dot < min) { \n                min = dot; \n            }\n        }\n\n        projection.min = min;\n        projection.max = max;\n    };\n\n    /**\n     * Finds supporting vertices given two bodies along a given direction using hill-climbing.\n     * @method _findSupports\n     * @private\n     * @param {body} bodyA\n     * @param {body} bodyB\n     * @param {vector} normal\n     * @param {number} direction\n     * @return [vector]\n     */\n    Collision._findSupports = function(bodyA, bodyB, normal, direction) {\n        var vertices = bodyB.vertices,\n            verticesLength = vertices.length,\n            bodyAPositionX = bodyA.position.x,\n            bodyAPositionY = bodyA.position.y,\n            normalX = normal.x * direction,\n            normalY = normal.y * direction,\n            nearestDistance = Number.MAX_VALUE,\n            vertexA,\n            vertexB,\n            vertexC,\n            distance,\n            j;\n\n        // find deepest vertex relative to the axis\n        for (j = 0; j < verticesLength; j += 1) {\n            vertexB = vertices[j];\n            distance = normalX * (bodyAPositionX - vertexB.x) + normalY * (bodyAPositionY - vertexB.y);\n\n            // convex hill-climbing\n            if (distance < nearestDistance) {\n                nearestDistance = distance;\n                vertexA = vertexB;\n            }\n        }\n\n        // measure next vertex\n        vertexC = vertices[(verticesLength + vertexA.index - 1) % verticesLength];\n        nearestDistance = normalX * (bodyAPositionX - vertexC.x) + normalY * (bodyAPositionY - vertexC.y);\n\n        // compare with previous vertex\n        vertexB = vertices[(vertexA.index + 1) % verticesLength];\n        if (normalX * (bodyAPositionX - vertexB.x) + normalY * (bodyAPositionY - vertexB.y) < nearestDistance) {\n            _supports[0] = vertexA;\n            _supports[1] = vertexB;\n\n            return _supports;\n        }\n\n        _supports[0] = vertexA;\n        _supports[1] = vertexC;\n\n        return _supports;\n    };\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * A reference to the pair using this collision record, if there is one.\n     *\n     * @property pair\n     * @type {pair|null}\n     * @default null\n     */\n\n    /**\n     * A flag that indicates if the bodies were colliding when the collision was last updated.\n     * \n     * @property collided\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * The first body part represented by the collision (see also `collision.parentA`).\n     * \n     * @property bodyA\n     * @type body\n     */\n\n    /**\n     * The second body part represented by the collision (see also `collision.parentB`).\n     * \n     * @property bodyB\n     * @type body\n     */\n\n    /**\n     * The first body represented by the collision (i.e. `collision.bodyA.parent`).\n     * \n     * @property parentA\n     * @type body\n     */\n\n    /**\n     * The second body represented by the collision (i.e. `collision.bodyB.parent`).\n     * \n     * @property parentB\n     * @type body\n     */\n\n    /**\n     * A `Number` that represents the minimum separating distance between the bodies along the collision normal.\n     *\n     * @readOnly\n     * @property depth\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A normalised `Vector` that represents the direction between the bodies that provides the minimum separating distance.\n     *\n     * @property normal\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * A normalised `Vector` that is the tangent direction to the collision normal.\n     *\n     * @property tangent\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * A `Vector` that represents the direction and depth of the collision.\n     *\n     * @property penetration\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * An array of body vertices that represent the support points in the collision.\n     * These are the deepest vertices (along the collision normal) of each body that are contained by the other body's vertices.\n     *\n     * @property supports\n     * @type vector[]\n     * @default []\n     */\n\n})();\n\n\n/***/ }),\n/* 9 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Pair` module contains methods for creating and manipulating collision pairs.\n*\n* @class Pair\n*/\n\nvar Pair = {};\n\nmodule.exports = Pair;\n\nvar Contact = __webpack_require__(16);\n\n(function() {\n    \n    /**\n     * Creates a pair.\n     * @method create\n     * @param {collision} collision\n     * @param {number} timestamp\n     * @return {pair} A new pair\n     */\n    Pair.create = function(collision, timestamp) {\n        var bodyA = collision.bodyA,\n            bodyB = collision.bodyB;\n\n        var pair = {\n            id: Pair.id(bodyA, bodyB),\n            bodyA: bodyA,\n            bodyB: bodyB,\n            collision: collision,\n            contacts: [],\n            activeContacts: [],\n            separation: 0,\n            isActive: true,\n            confirmedActive: true,\n            isSensor: bodyA.isSensor || bodyB.isSensor,\n            timeCreated: timestamp,\n            timeUpdated: timestamp,\n            inverseMass: 0,\n            friction: 0,\n            frictionStatic: 0,\n            restitution: 0,\n            slop: 0\n        };\n\n        Pair.update(pair, collision, timestamp);\n\n        return pair;\n    };\n\n    /**\n     * Updates a pair given a collision.\n     * @method update\n     * @param {pair} pair\n     * @param {collision} collision\n     * @param {number} timestamp\n     */\n    Pair.update = function(pair, collision, timestamp) {\n        var contacts = pair.contacts,\n            supports = collision.supports,\n            activeContacts = pair.activeContacts,\n            parentA = collision.parentA,\n            parentB = collision.parentB,\n            parentAVerticesLength = parentA.vertices.length;\n        \n        pair.isActive = true;\n        pair.timeUpdated = timestamp;\n        pair.collision = collision;\n        pair.separation = collision.depth;\n        pair.inverseMass = parentA.inverseMass + parentB.inverseMass;\n        pair.friction = parentA.friction < parentB.friction ? parentA.friction : parentB.friction;\n        pair.frictionStatic = parentA.frictionStatic > parentB.frictionStatic ? parentA.frictionStatic : parentB.frictionStatic;\n        pair.restitution = parentA.restitution > parentB.restitution ? parentA.restitution : parentB.restitution;\n        pair.slop = parentA.slop > parentB.slop ? parentA.slop : parentB.slop;\n\n        collision.pair = pair;\n        activeContacts.length = 0;\n        \n        for (var i = 0; i < supports.length; i++) {\n            var support = supports[i],\n                contactId = support.body === parentA ? support.index : parentAVerticesLength + support.index,\n                contact = contacts[contactId];\n\n            if (contact) {\n                activeContacts.push(contact);\n            } else {\n                activeContacts.push(contacts[contactId] = Contact.create(support));\n            }\n        }\n    };\n    \n    /**\n     * Set a pair as active or inactive.\n     * @method setActive\n     * @param {pair} pair\n     * @param {bool} isActive\n     * @param {number} timestamp\n     */\n    Pair.setActive = function(pair, isActive, timestamp) {\n        if (isActive) {\n            pair.isActive = true;\n            pair.timeUpdated = timestamp;\n        } else {\n            pair.isActive = false;\n            pair.activeContacts.length = 0;\n        }\n    };\n\n    /**\n     * Get the id for the given pair.\n     * @method id\n     * @param {body} bodyA\n     * @param {body} bodyB\n     * @return {string} Unique pairId\n     */\n    Pair.id = function(bodyA, bodyB) {\n        if (bodyA.id < bodyB.id) {\n            return 'A' + bodyA.id + 'B' + bodyB.id;\n        } else {\n            return 'A' + bodyB.id + 'B' + bodyA.id;\n        }\n    };\n\n})();\n\n\n/***/ }),\n/* 10 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Constraint` module contains methods for creating and manipulating constraints.\n* Constraints are used for specifying that a fixed distance must be maintained between two bodies (or a body and a fixed world-space position).\n* The stiffness of constraints can be modified to create springs or elastic.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Constraint\n*/\n\nvar Constraint = {};\n\nmodule.exports = Constraint;\n\nvar Vertices = __webpack_require__(3);\nvar Vector = __webpack_require__(2);\nvar Sleeping = __webpack_require__(7);\nvar Bounds = __webpack_require__(1);\nvar Axes = __webpack_require__(11);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    Constraint._warming = 0.4;\n    Constraint._torqueDampen = 1;\n    Constraint._minLength = 0.000001;\n\n    /**\n     * Creates a new constraint.\n     * All properties have default values, and many are pre-calculated automatically based on other properties.\n     * To simulate a revolute constraint (or pin joint) set `length: 0` and a high `stiffness` value (e.g. `0.7` or above).\n     * If the constraint is unstable, try lowering the `stiffness` value and / or increasing `engine.constraintIterations`.\n     * For compound bodies, constraints must be applied to the parent body (not one of its parts).\n     * See the properties section below for detailed information on what you can pass via the `options` object.\n     * @method create\n     * @param {} options\n     * @return {constraint} constraint\n     */\n    Constraint.create = function(options) {\n        var constraint = options;\n\n        // if bodies defined but no points, use body centre\n        if (constraint.bodyA && !constraint.pointA)\n            constraint.pointA = { x: 0, y: 0 };\n        if (constraint.bodyB && !constraint.pointB)\n            constraint.pointB = { x: 0, y: 0 };\n\n        // calculate static length using initial world space points\n        var initialPointA = constraint.bodyA ? Vector.add(constraint.bodyA.position, constraint.pointA) : constraint.pointA,\n            initialPointB = constraint.bodyB ? Vector.add(constraint.bodyB.position, constraint.pointB) : constraint.pointB,\n            length = Vector.magnitude(Vector.sub(initialPointA, initialPointB));\n    \n        constraint.length = typeof constraint.length !== 'undefined' ? constraint.length : length;\n\n        // option defaults\n        constraint.id = constraint.id || Common.nextId();\n        constraint.label = constraint.label || 'Constraint';\n        constraint.type = 'constraint';\n        constraint.stiffness = constraint.stiffness || (constraint.length > 0 ? 1 : 0.7);\n        constraint.damping = constraint.damping || 0;\n        constraint.angularStiffness = constraint.angularStiffness || 0;\n        constraint.angleA = constraint.bodyA ? constraint.bodyA.angle : constraint.angleA;\n        constraint.angleB = constraint.bodyB ? constraint.bodyB.angle : constraint.angleB;\n        constraint.plugin = {};\n\n        // render\n        var render = {\n            visible: true,\n            lineWidth: 2,\n            strokeStyle: '#ffffff',\n            type: 'line',\n            anchors: true\n        };\n\n        if (constraint.length === 0 && constraint.stiffness > 0.1) {\n            render.type = 'pin';\n            render.anchors = false;\n        } else if (constraint.stiffness < 0.9) {\n            render.type = 'spring';\n        }\n\n        constraint.render = Common.extend(render, constraint.render);\n\n        return constraint;\n    };\n\n    /**\n     * Prepares for solving by constraint warming.\n     * @private\n     * @method preSolveAll\n     * @param {body[]} bodies\n     */\n    Constraint.preSolveAll = function(bodies) {\n        for (var i = 0; i < bodies.length; i += 1) {\n            var body = bodies[i],\n                impulse = body.constraintImpulse;\n\n            if (body.isStatic || (impulse.x === 0 && impulse.y === 0 && impulse.angle === 0)) {\n                continue;\n            }\n\n            body.position.x += impulse.x;\n            body.position.y += impulse.y;\n            body.angle += impulse.angle;\n        }\n    };\n\n    /**\n     * Solves all constraints in a list of collisions.\n     * @private\n     * @method solveAll\n     * @param {constraint[]} constraints\n     * @param {number} delta\n     */\n    Constraint.solveAll = function(constraints, delta) {\n        var timeScale = Common.clamp(delta / Common._baseDelta, 0, 1);\n\n        // Solve fixed constraints first.\n        for (var i = 0; i < constraints.length; i += 1) {\n            var constraint = constraints[i],\n                fixedA = !constraint.bodyA || (constraint.bodyA && constraint.bodyA.isStatic),\n                fixedB = !constraint.bodyB || (constraint.bodyB && constraint.bodyB.isStatic);\n\n            if (fixedA || fixedB) {\n                Constraint.solve(constraints[i], timeScale);\n            }\n        }\n\n        // Solve free constraints last.\n        for (i = 0; i < constraints.length; i += 1) {\n            constraint = constraints[i];\n            fixedA = !constraint.bodyA || (constraint.bodyA && constraint.bodyA.isStatic);\n            fixedB = !constraint.bodyB || (constraint.bodyB && constraint.bodyB.isStatic);\n\n            if (!fixedA && !fixedB) {\n                Constraint.solve(constraints[i], timeScale);\n            }\n        }\n    };\n\n    /**\n     * Solves a distance constraint with Gauss-Siedel method.\n     * @private\n     * @method solve\n     * @param {constraint} constraint\n     * @param {number} timeScale\n     */\n    Constraint.solve = function(constraint, timeScale) {\n        var bodyA = constraint.bodyA,\n            bodyB = constraint.bodyB,\n            pointA = constraint.pointA,\n            pointB = constraint.pointB;\n\n        if (!bodyA && !bodyB)\n            return;\n\n        // update reference angle\n        if (bodyA && !bodyA.isStatic) {\n            Vector.rotate(pointA, bodyA.angle - constraint.angleA, pointA);\n            constraint.angleA = bodyA.angle;\n        }\n        \n        // update reference angle\n        if (bodyB && !bodyB.isStatic) {\n            Vector.rotate(pointB, bodyB.angle - constraint.angleB, pointB);\n            constraint.angleB = bodyB.angle;\n        }\n\n        var pointAWorld = pointA,\n            pointBWorld = pointB;\n\n        if (bodyA) pointAWorld = Vector.add(bodyA.position, pointA);\n        if (bodyB) pointBWorld = Vector.add(bodyB.position, pointB);\n\n        if (!pointAWorld || !pointBWorld)\n            return;\n\n        var delta = Vector.sub(pointAWorld, pointBWorld),\n            currentLength = Vector.magnitude(delta);\n\n        // prevent singularity\n        if (currentLength < Constraint._minLength) {\n            currentLength = Constraint._minLength;\n        }\n\n        // solve distance constraint with Gauss-Siedel method\n        var difference = (currentLength - constraint.length) / currentLength,\n            isRigid = constraint.stiffness >= 1 || constraint.length === 0,\n            stiffness = isRigid ? constraint.stiffness * timeScale \n                : constraint.stiffness * timeScale * timeScale,\n            damping = constraint.damping * timeScale,\n            force = Vector.mult(delta, difference * stiffness),\n            massTotal = (bodyA ? bodyA.inverseMass : 0) + (bodyB ? bodyB.inverseMass : 0),\n            inertiaTotal = (bodyA ? bodyA.inverseInertia : 0) + (bodyB ? bodyB.inverseInertia : 0),\n            resistanceTotal = massTotal + inertiaTotal,\n            torque,\n            share,\n            normal,\n            normalVelocity,\n            relativeVelocity;\n    \n        if (damping > 0) {\n            var zero = Vector.create();\n            normal = Vector.div(delta, currentLength);\n\n            relativeVelocity = Vector.sub(\n                bodyB && Vector.sub(bodyB.position, bodyB.positionPrev) || zero,\n                bodyA && Vector.sub(bodyA.position, bodyA.positionPrev) || zero\n            );\n\n            normalVelocity = Vector.dot(normal, relativeVelocity);\n        }\n\n        if (bodyA && !bodyA.isStatic) {\n            share = bodyA.inverseMass / massTotal;\n\n            // keep track of applied impulses for post solving\n            bodyA.constraintImpulse.x -= force.x * share;\n            bodyA.constraintImpulse.y -= force.y * share;\n\n            // apply forces\n            bodyA.position.x -= force.x * share;\n            bodyA.position.y -= force.y * share;\n\n            // apply damping\n            if (damping > 0) {\n                bodyA.positionPrev.x -= damping * normal.x * normalVelocity * share;\n                bodyA.positionPrev.y -= damping * normal.y * normalVelocity * share;\n            }\n\n            // apply torque\n            torque = (Vector.cross(pointA, force) / resistanceTotal) * Constraint._torqueDampen * bodyA.inverseInertia * (1 - constraint.angularStiffness);\n            bodyA.constraintImpulse.angle -= torque;\n            bodyA.angle -= torque;\n        }\n\n        if (bodyB && !bodyB.isStatic) {\n            share = bodyB.inverseMass / massTotal;\n\n            // keep track of applied impulses for post solving\n            bodyB.constraintImpulse.x += force.x * share;\n            bodyB.constraintImpulse.y += force.y * share;\n            \n            // apply forces\n            bodyB.position.x += force.x * share;\n            bodyB.position.y += force.y * share;\n\n            // apply damping\n            if (damping > 0) {\n                bodyB.positionPrev.x += damping * normal.x * normalVelocity * share;\n                bodyB.positionPrev.y += damping * normal.y * normalVelocity * share;\n            }\n\n            // apply torque\n            torque = (Vector.cross(pointB, force) / resistanceTotal) * Constraint._torqueDampen * bodyB.inverseInertia * (1 - constraint.angularStiffness);\n            bodyB.constraintImpulse.angle += torque;\n            bodyB.angle += torque;\n        }\n\n    };\n\n    /**\n     * Performs body updates required after solving constraints.\n     * @private\n     * @method postSolveAll\n     * @param {body[]} bodies\n     */\n    Constraint.postSolveAll = function(bodies) {\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i],\n                impulse = body.constraintImpulse;\n\n            if (body.isStatic || (impulse.x === 0 && impulse.y === 0 && impulse.angle === 0)) {\n                continue;\n            }\n\n            Sleeping.set(body, false);\n\n            // update geometry and reset\n            for (var j = 0; j < body.parts.length; j++) {\n                var part = body.parts[j];\n                \n                Vertices.translate(part.vertices, impulse);\n\n                if (j > 0) {\n                    part.position.x += impulse.x;\n                    part.position.y += impulse.y;\n                }\n\n                if (impulse.angle !== 0) {\n                    Vertices.rotate(part.vertices, impulse.angle, body.position);\n                    Axes.rotate(part.axes, impulse.angle);\n                    if (j > 0) {\n                        Vector.rotateAbout(part.position, impulse.angle, body.position, part.position);\n                    }\n                }\n\n                Bounds.update(part.bounds, part.vertices, body.velocity);\n            }\n\n            // dampen the cached impulse for warming next step\n            impulse.angle *= Constraint._warming;\n            impulse.x *= Constraint._warming;\n            impulse.y *= Constraint._warming;\n        }\n    };\n\n    /**\n     * Returns the world-space position of `constraint.pointA`, accounting for `constraint.bodyA`.\n     * @method pointAWorld\n     * @param {constraint} constraint\n     * @returns {vector} the world-space position\n     */\n    Constraint.pointAWorld = function(constraint) {\n        return {\n            x: (constraint.bodyA ? constraint.bodyA.position.x : 0) \n                + (constraint.pointA ? constraint.pointA.x : 0),\n            y: (constraint.bodyA ? constraint.bodyA.position.y : 0) \n                + (constraint.pointA ? constraint.pointA.y : 0)\n        };\n    };\n\n    /**\n     * Returns the world-space position of `constraint.pointB`, accounting for `constraint.bodyB`.\n     * @method pointBWorld\n     * @param {constraint} constraint\n     * @returns {vector} the world-space position\n     */\n    Constraint.pointBWorld = function(constraint) {\n        return {\n            x: (constraint.bodyB ? constraint.bodyB.position.x : 0) \n                + (constraint.pointB ? constraint.pointB.x : 0),\n            y: (constraint.bodyB ? constraint.bodyB.position.y : 0) \n                + (constraint.pointB ? constraint.pointB.y : 0)\n        };\n    };\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * An integer `Number` uniquely identifying number generated in `Composite.create` by `Common.nextId`.\n     *\n     * @property id\n     * @type number\n     */\n\n    /**\n     * A `String` denoting the type of object.\n     *\n     * @property type\n     * @type string\n     * @default \"constraint\"\n     * @readOnly\n     */\n\n    /**\n     * An arbitrary `String` name to help the user identify and manage bodies.\n     *\n     * @property label\n     * @type string\n     * @default \"Constraint\"\n     */\n\n    /**\n     * An `Object` that defines the rendering properties to be consumed by the module `Matter.Render`.\n     *\n     * @property render\n     * @type object\n     */\n\n    /**\n     * A flag that indicates if the constraint should be rendered.\n     *\n     * @property render.visible\n     * @type boolean\n     * @default true\n     */\n\n    /**\n     * A `Number` that defines the line width to use when rendering the constraint outline.\n     * A value of `0` means no outline will be rendered.\n     *\n     * @property render.lineWidth\n     * @type number\n     * @default 2\n     */\n\n    /**\n     * A `String` that defines the stroke style to use when rendering the constraint outline.\n     * It is the same as when using a canvas, so it accepts CSS style property values.\n     *\n     * @property render.strokeStyle\n     * @type string\n     * @default a random colour\n     */\n\n    /**\n     * A `String` that defines the constraint rendering type. \n     * The possible values are 'line', 'pin', 'spring'.\n     * An appropriate render type will be automatically chosen unless one is given in options.\n     *\n     * @property render.type\n     * @type string\n     * @default 'line'\n     */\n\n    /**\n     * A `Boolean` that defines if the constraint's anchor points should be rendered.\n     *\n     * @property render.anchors\n     * @type boolean\n     * @default true\n     */\n\n    /**\n     * The first possible `Body` that this constraint is attached to.\n     *\n     * @property bodyA\n     * @type body\n     * @default null\n     */\n\n    /**\n     * The second possible `Body` that this constraint is attached to.\n     *\n     * @property bodyB\n     * @type body\n     * @default null\n     */\n\n    /**\n     * A `Vector` that specifies the offset of the constraint from center of the `constraint.bodyA` if defined, otherwise a world-space position.\n     *\n     * @property pointA\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * A `Vector` that specifies the offset of the constraint from center of the `constraint.bodyB` if defined, otherwise a world-space position.\n     *\n     * @property pointB\n     * @type vector\n     * @default { x: 0, y: 0 }\n     */\n\n    /**\n     * A `Number` that specifies the stiffness of the constraint, i.e. the rate at which it returns to its resting `constraint.length`.\n     * A value of `1` means the constraint should be very stiff.\n     * A value of `0.2` means the constraint acts like a soft spring.\n     *\n     * @property stiffness\n     * @type number\n     * @default 1\n     */\n\n    /**\n     * A `Number` that specifies the damping of the constraint, \n     * i.e. the amount of resistance applied to each body based on their velocities to limit the amount of oscillation.\n     * Damping will only be apparent when the constraint also has a very low `stiffness`.\n     * A value of `0.1` means the constraint will apply heavy damping, resulting in little to no oscillation.\n     * A value of `0` means the constraint will apply no damping.\n     *\n     * @property damping\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `Number` that specifies the target resting length of the constraint. \n     * It is calculated automatically in `Constraint.create` from initial positions of the `constraint.bodyA` and `constraint.bodyB`.\n     *\n     * @property length\n     * @type number\n     */\n\n    /**\n     * An object reserved for storing plugin-specific properties.\n     *\n     * @property plugin\n     * @type {}\n     */\n\n})();\n\n\n/***/ }),\n/* 11 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Axes` module contains methods for creating and manipulating sets of axes.\n*\n* @class Axes\n*/\n\nvar Axes = {};\n\nmodule.exports = Axes;\n\nvar Vector = __webpack_require__(2);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * Creates a new set of axes from the given vertices.\n     * @method fromVertices\n     * @param {vertices} vertices\n     * @return {axes} A new axes from the given vertices\n     */\n    Axes.fromVertices = function(vertices) {\n        var axes = {};\n\n        // find the unique axes, using edge normal gradients\n        for (var i = 0; i < vertices.length; i++) {\n            var j = (i + 1) % vertices.length, \n                normal = Vector.normalise({ \n                    x: vertices[j].y - vertices[i].y, \n                    y: vertices[i].x - vertices[j].x\n                }),\n                gradient = (normal.y === 0) ? Infinity : (normal.x / normal.y);\n            \n            // limit precision\n            gradient = gradient.toFixed(3).toString();\n            axes[gradient] = normal;\n        }\n\n        return Common.values(axes);\n    };\n\n    /**\n     * Rotates a set of axes by the given angle.\n     * @method rotate\n     * @param {axes} axes\n     * @param {number} angle\n     */\n    Axes.rotate = function(axes, angle) {\n        if (angle === 0)\n            return;\n        \n        var cos = Math.cos(angle),\n            sin = Math.sin(angle);\n\n        for (var i = 0; i < axes.length; i++) {\n            var axis = axes[i],\n                xx;\n            xx = axis.x * cos - axis.y * sin;\n            axis.y = axis.x * sin + axis.y * cos;\n            axis.x = xx;\n        }\n    };\n\n})();\n\n\n/***/ }),\n/* 12 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Bodies` module contains factory methods for creating rigid body models \n* with commonly used body configurations (such as rectangles, circles and other polygons).\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Bodies\n*/\n\n// TODO: true circle bodies\n\nvar Bodies = {};\n\nmodule.exports = Bodies;\n\nvar Vertices = __webpack_require__(3);\nvar Common = __webpack_require__(0);\nvar Body = __webpack_require__(4);\nvar Bounds = __webpack_require__(1);\nvar Vector = __webpack_require__(2);\n\n(function() {\n\n    /**\n     * Creates a new rigid body model with a rectangle hull. \n     * The options parameter is an object that specifies any properties you wish to override the defaults.\n     * See the properties section of the `Matter.Body` module for detailed information on what you can pass via the `options` object.\n     * @method rectangle\n     * @param {number} x\n     * @param {number} y\n     * @param {number} width\n     * @param {number} height\n     * @param {object} [options]\n     * @return {body} A new rectangle body\n     */\n    Bodies.rectangle = function(x, y, width, height, options) {\n        options = options || {};\n\n        var rectangle = { \n            label: 'Rectangle Body',\n            position: { x: x, y: y },\n            vertices: Vertices.fromPath('L 0 0 L ' + width + ' 0 L ' + width + ' ' + height + ' L 0 ' + height)\n        };\n\n        if (options.chamfer) {\n            var chamfer = options.chamfer;\n            rectangle.vertices = Vertices.chamfer(rectangle.vertices, chamfer.radius, \n                chamfer.quality, chamfer.qualityMin, chamfer.qualityMax);\n            delete options.chamfer;\n        }\n\n        return Body.create(Common.extend({}, rectangle, options));\n    };\n    \n    /**\n     * Creates a new rigid body model with a trapezoid hull. \n     * The options parameter is an object that specifies any properties you wish to override the defaults.\n     * See the properties section of the `Matter.Body` module for detailed information on what you can pass via the `options` object.\n     * @method trapezoid\n     * @param {number} x\n     * @param {number} y\n     * @param {number} width\n     * @param {number} height\n     * @param {number} slope\n     * @param {object} [options]\n     * @return {body} A new trapezoid body\n     */\n    Bodies.trapezoid = function(x, y, width, height, slope, options) {\n        options = options || {};\n\n        slope *= 0.5;\n        var roof = (1 - (slope * 2)) * width;\n        \n        var x1 = width * slope,\n            x2 = x1 + roof,\n            x3 = x2 + x1,\n            verticesPath;\n\n        if (slope < 0.5) {\n            verticesPath = 'L 0 0 L ' + x1 + ' ' + (-height) + ' L ' + x2 + ' ' + (-height) + ' L ' + x3 + ' 0';\n        } else {\n            verticesPath = 'L 0 0 L ' + x2 + ' ' + (-height) + ' L ' + x3 + ' 0';\n        }\n\n        var trapezoid = { \n            label: 'Trapezoid Body',\n            position: { x: x, y: y },\n            vertices: Vertices.fromPath(verticesPath)\n        };\n\n        if (options.chamfer) {\n            var chamfer = options.chamfer;\n            trapezoid.vertices = Vertices.chamfer(trapezoid.vertices, chamfer.radius, \n                chamfer.quality, chamfer.qualityMin, chamfer.qualityMax);\n            delete options.chamfer;\n        }\n\n        return Body.create(Common.extend({}, trapezoid, options));\n    };\n\n    /**\n     * Creates a new rigid body model with a circle hull. \n     * The options parameter is an object that specifies any properties you wish to override the defaults.\n     * See the properties section of the `Matter.Body` module for detailed information on what you can pass via the `options` object.\n     * @method circle\n     * @param {number} x\n     * @param {number} y\n     * @param {number} radius\n     * @param {object} [options]\n     * @param {number} [maxSides]\n     * @return {body} A new circle body\n     */\n    Bodies.circle = function(x, y, radius, options, maxSides) {\n        options = options || {};\n\n        var circle = {\n            label: 'Circle Body',\n            circleRadius: radius\n        };\n        \n        // approximate circles with polygons until true circles implemented in SAT\n        maxSides = maxSides || 25;\n        var sides = Math.ceil(Math.max(10, Math.min(maxSides, radius)));\n\n        // optimisation: always use even number of sides (half the number of unique axes)\n        if (sides % 2 === 1)\n            sides += 1;\n\n        return Bodies.polygon(x, y, sides, radius, Common.extend({}, circle, options));\n    };\n\n    /**\n     * Creates a new rigid body model with a regular polygon hull with the given number of sides. \n     * The options parameter is an object that specifies any properties you wish to override the defaults.\n     * See the properties section of the `Matter.Body` module for detailed information on what you can pass via the `options` object.\n     * @method polygon\n     * @param {number} x\n     * @param {number} y\n     * @param {number} sides\n     * @param {number} radius\n     * @param {object} [options]\n     * @return {body} A new regular polygon body\n     */\n    Bodies.polygon = function(x, y, sides, radius, options) {\n        options = options || {};\n\n        if (sides < 3)\n            return Bodies.circle(x, y, radius, options);\n\n        var theta = 2 * Math.PI / sides,\n            path = '',\n            offset = theta * 0.5;\n\n        for (var i = 0; i < sides; i += 1) {\n            var angle = offset + (i * theta),\n                xx = Math.cos(angle) * radius,\n                yy = Math.sin(angle) * radius;\n\n            path += 'L ' + xx.toFixed(3) + ' ' + yy.toFixed(3) + ' ';\n        }\n\n        var polygon = { \n            label: 'Polygon Body',\n            position: { x: x, y: y },\n            vertices: Vertices.fromPath(path)\n        };\n\n        if (options.chamfer) {\n            var chamfer = options.chamfer;\n            polygon.vertices = Vertices.chamfer(polygon.vertices, chamfer.radius, \n                chamfer.quality, chamfer.qualityMin, chamfer.qualityMax);\n            delete options.chamfer;\n        }\n\n        return Body.create(Common.extend({}, polygon, options));\n    };\n\n    /**\n     * Utility to create a compound body based on set(s) of vertices.\n     * \n     * _Note:_ To optionally enable automatic concave vertices decomposition the [poly-decomp](https://github.com/schteppe/poly-decomp.js) \n     * package must be first installed and provided see `Common.setDecomp`, otherwise the convex hull of each vertex set will be used.\n     * \n     * The resulting vertices are reorientated about their centre of mass,\n     * and offset such that `body.position` corresponds to this point.\n     * \n     * The resulting offset may be found if needed by subtracting `body.bounds` from the original input bounds.\n     * To later move the centre of mass see `Body.setCentre`.\n     * \n     * Note that automatic conconcave decomposition results are not always optimal. \n     * For best results, simplify the input vertices as much as possible first.\n     * By default this function applies some addtional simplification to help.\n     * \n     * Some outputs may also require further manual processing afterwards to be robust.\n     * In particular some parts may need to be overlapped to avoid collision gaps.\n     * Thin parts and sharp points should be avoided or removed where possible.\n     *\n     * The options parameter object specifies any `Matter.Body` properties you wish to override the defaults.\n     * \n     * See the properties section of the `Matter.Body` module for detailed information on what you can pass via the `options` object.\n     * @method fromVertices\n     * @param {number} x\n     * @param {number} y\n     * @param {array} vertexSets One or more arrays of vertex points e.g. `[[{ x: 0, y: 0 }...], ...]`.\n     * @param {object} [options] The body options.\n     * @param {bool} [flagInternal=false] Optionally marks internal edges with `isInternal`.\n     * @param {number} [removeCollinear=0.01] Threshold when simplifying vertices along the same edge.\n     * @param {number} [minimumArea=10] Threshold when removing small parts.\n     * @param {number} [removeDuplicatePoints=0.01] Threshold when simplifying nearby vertices.\n     * @return {body}\n     */\n    Bodies.fromVertices = function(x, y, vertexSets, options, flagInternal, removeCollinear, minimumArea, removeDuplicatePoints) {\n        var decomp = Common.getDecomp(),\n            canDecomp,\n            body,\n            parts,\n            isConvex,\n            isConcave,\n            vertices,\n            i,\n            j,\n            k,\n            v,\n            z;\n\n        // check decomp is as expected\n        canDecomp = Boolean(decomp && decomp.quickDecomp);\n\n        options = options || {};\n        parts = [];\n\n        flagInternal = typeof flagInternal !== 'undefined' ? flagInternal : false;\n        removeCollinear = typeof removeCollinear !== 'undefined' ? removeCollinear : 0.01;\n        minimumArea = typeof minimumArea !== 'undefined' ? minimumArea : 10;\n        removeDuplicatePoints = typeof removeDuplicatePoints !== 'undefined' ? removeDuplicatePoints : 0.01;\n\n        // ensure vertexSets is an array of arrays\n        if (!Common.isArray(vertexSets[0])) {\n            vertexSets = [vertexSets];\n        }\n\n        for (v = 0; v < vertexSets.length; v += 1) {\n            vertices = vertexSets[v];\n            isConvex = Vertices.isConvex(vertices);\n            isConcave = !isConvex;\n\n            if (isConcave && !canDecomp) {\n                Common.warnOnce(\n                    'Bodies.fromVertices: Install the \\'poly-decomp\\' library and use Common.setDecomp or provide \\'decomp\\' as a global to decompose concave vertices.'\n                );\n            }\n\n            if (isConvex || !canDecomp) {\n                if (isConvex) {\n                    vertices = Vertices.clockwiseSort(vertices);\n                } else {\n                    // fallback to convex hull when decomposition is not possible\n                    vertices = Vertices.hull(vertices);\n                }\n\n                parts.push({\n                    position: { x: x, y: y },\n                    vertices: vertices\n                });\n            } else {\n                // initialise a decomposition\n                var concave = vertices.map(function(vertex) {\n                    return [vertex.x, vertex.y];\n                });\n\n                // vertices are concave and simple, we can decompose into parts\n                decomp.makeCCW(concave);\n                if (removeCollinear !== false)\n                    decomp.removeCollinearPoints(concave, removeCollinear);\n                if (removeDuplicatePoints !== false && decomp.removeDuplicatePoints)\n                    decomp.removeDuplicatePoints(concave, removeDuplicatePoints);\n\n                // use the quick decomposition algorithm (Bayazit)\n                var decomposed = decomp.quickDecomp(concave);\n\n                // for each decomposed chunk\n                for (i = 0; i < decomposed.length; i++) {\n                    var chunk = decomposed[i];\n\n                    // convert vertices into the correct structure\n                    var chunkVertices = chunk.map(function(vertices) {\n                        return {\n                            x: vertices[0],\n                            y: vertices[1]\n                        };\n                    });\n\n                    // skip small chunks\n                    if (minimumArea > 0 && Vertices.area(chunkVertices) < minimumArea)\n                        continue;\n\n                    // create a compound part\n                    parts.push({\n                        position: Vertices.centre(chunkVertices),\n                        vertices: chunkVertices\n                    });\n                }\n            }\n        }\n\n        // create body parts\n        for (i = 0; i < parts.length; i++) {\n            parts[i] = Body.create(Common.extend(parts[i], options));\n        }\n\n        // flag internal edges (coincident part edges)\n        if (flagInternal) {\n            var coincident_max_dist = 5;\n\n            for (i = 0; i < parts.length; i++) {\n                var partA = parts[i];\n\n                for (j = i + 1; j < parts.length; j++) {\n                    var partB = parts[j];\n\n                    if (Bounds.overlaps(partA.bounds, partB.bounds)) {\n                        var pav = partA.vertices,\n                            pbv = partB.vertices;\n\n                        // iterate vertices of both parts\n                        for (k = 0; k < partA.vertices.length; k++) {\n                            for (z = 0; z < partB.vertices.length; z++) {\n                                // find distances between the vertices\n                                var da = Vector.magnitudeSquared(Vector.sub(pav[(k + 1) % pav.length], pbv[z])),\n                                    db = Vector.magnitudeSquared(Vector.sub(pav[k], pbv[(z + 1) % pbv.length]));\n\n                                // if both vertices are very close, consider the edge concident (internal)\n                                if (da < coincident_max_dist && db < coincident_max_dist) {\n                                    pav[k].isInternal = true;\n                                    pbv[z].isInternal = true;\n                                }\n                            }\n                        }\n\n                    }\n                }\n            }\n        }\n\n        if (parts.length > 1) {\n            // create the parent body to be returned, that contains generated compound parts\n            body = Body.create(Common.extend({ parts: parts.slice(0) }, options));\n\n            // offset such that body.position is at the centre off mass\n            Body.setPosition(body, { x: x, y: y });\n\n            return body;\n        } else {\n            return parts[0];\n        }\n    };\n\n})();\n\n\n/***/ }),\n/* 13 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Detector` module contains methods for efficiently detecting collisions between a list of bodies using a broadphase algorithm.\n*\n* @class Detector\n*/\n\nvar Detector = {};\n\nmodule.exports = Detector;\n\nvar Common = __webpack_require__(0);\nvar Collision = __webpack_require__(8);\n\n(function() {\n\n    /**\n     * Creates a new collision detector.\n     * @method create\n     * @param {} options\n     * @return {detector} A new collision detector\n     */\n    Detector.create = function(options) {\n        var defaults = {\n            bodies: [],\n            pairs: null\n        };\n\n        return Common.extend(defaults, options);\n    };\n\n    /**\n     * Sets the list of bodies in the detector.\n     * @method setBodies\n     * @param {detector} detector\n     * @param {body[]} bodies\n     */\n    Detector.setBodies = function(detector, bodies) {\n        detector.bodies = bodies.slice(0);\n    };\n\n    /**\n     * Clears the detector including its list of bodies.\n     * @method clear\n     * @param {detector} detector\n     */\n    Detector.clear = function(detector) {\n        detector.bodies = [];\n    };\n\n    /**\n     * Efficiently finds all collisions among all the bodies in `detector.bodies` using a broadphase algorithm.\n     * \n     * _Note:_ The specific ordering of collisions returned is not guaranteed between releases and may change for performance reasons.\n     * If a specific ordering is required then apply a sort to the resulting array.\n     * @method collisions\n     * @param {detector} detector\n     * @return {collision[]} collisions\n     */\n    Detector.collisions = function(detector) {\n        var collisions = [],\n            pairs = detector.pairs,\n            bodies = detector.bodies,\n            bodiesLength = bodies.length,\n            canCollide = Detector.canCollide,\n            collides = Collision.collides,\n            i,\n            j;\n\n        bodies.sort(Detector._compareBoundsX);\n\n        for (i = 0; i < bodiesLength; i++) {\n            var bodyA = bodies[i],\n                boundsA = bodyA.bounds,\n                boundXMax = bodyA.bounds.max.x,\n                boundYMax = bodyA.bounds.max.y,\n                boundYMin = bodyA.bounds.min.y,\n                bodyAStatic = bodyA.isStatic || bodyA.isSleeping,\n                partsALength = bodyA.parts.length,\n                partsASingle = partsALength === 1;\n\n            for (j = i + 1; j < bodiesLength; j++) {\n                var bodyB = bodies[j],\n                    boundsB = bodyB.bounds;\n\n                if (boundsB.min.x > boundXMax) {\n                    break;\n                }\n\n                if (boundYMax < boundsB.min.y || boundYMin > boundsB.max.y) {\n                    continue;\n                }\n\n                if (bodyAStatic && (bodyB.isStatic || bodyB.isSleeping)) {\n                    continue;\n                }\n\n                if (!canCollide(bodyA.collisionFilter, bodyB.collisionFilter)) {\n                    continue;\n                }\n\n                var partsBLength = bodyB.parts.length;\n\n                if (partsASingle && partsBLength === 1) {\n                    var collision = collides(bodyA, bodyB, pairs);\n\n                    if (collision) {\n                        collisions.push(collision);\n                    }\n                } else {\n                    var partsAStart = partsALength > 1 ? 1 : 0,\n                        partsBStart = partsBLength > 1 ? 1 : 0;\n                    \n                    for (var k = partsAStart; k < partsALength; k++) {\n                        var partA = bodyA.parts[k],\n                            boundsA = partA.bounds;\n\n                        for (var z = partsBStart; z < partsBLength; z++) {\n                            var partB = bodyB.parts[z],\n                                boundsB = partB.bounds;\n\n                            if (boundsA.min.x > boundsB.max.x || boundsA.max.x < boundsB.min.x\n                                || boundsA.max.y < boundsB.min.y || boundsA.min.y > boundsB.max.y) {\n                                continue;\n                            }\n\n                            var collision = collides(partA, partB, pairs);\n\n                            if (collision) {\n                                collisions.push(collision);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n\n        return collisions;\n    };\n\n    /**\n     * Returns `true` if both supplied collision filters will allow a collision to occur.\n     * See `body.collisionFilter` for more information.\n     * @method canCollide\n     * @param {} filterA\n     * @param {} filterB\n     * @return {bool} `true` if collision can occur\n     */\n    Detector.canCollide = function(filterA, filterB) {\n        if (filterA.group === filterB.group && filterA.group !== 0)\n            return filterA.group > 0;\n\n        return (filterA.mask & filterB.category) !== 0 && (filterB.mask & filterA.category) !== 0;\n    };\n\n    /**\n     * The comparison function used in the broadphase algorithm.\n     * Returns the signed delta of the bodies bounds on the x-axis.\n     * @private\n     * @method _sortCompare\n     * @param {body} bodyA\n     * @param {body} bodyB\n     * @return {number} The signed delta used for sorting\n     */\n    Detector._compareBoundsX = function(bodyA, bodyB) {\n        return bodyA.bounds.min.x - bodyB.bounds.min.x;\n    };\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * The array of `Matter.Body` between which the detector finds collisions.\n     * \n     * _Note:_ The order of bodies in this array _is not fixed_ and will be continually managed by the detector.\n     * @property bodies\n     * @type body[]\n     * @default []\n     */\n\n    /**\n     * Optional. A `Matter.Pairs` object from which previous collision objects may be reused. Intended for internal `Matter.Engine` usage.\n     * @property pairs\n     * @type {pairs|null}\n     * @default null\n     */\n\n})();\n\n\n/***/ }),\n/* 14 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Mouse` module contains methods for creating and manipulating mouse inputs.\n*\n* @class Mouse\n*/\n\nvar Mouse = {};\n\nmodule.exports = Mouse;\n\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * Creates a mouse input.\n     * @method create\n     * @param {HTMLElement} element\n     * @return {mouse} A new mouse\n     */\n    Mouse.create = function(element) {\n        var mouse = {};\n\n        if (!element) {\n            Common.log('Mouse.create: element was undefined, defaulting to document.body', 'warn');\n        }\n        \n        mouse.element = element || document.body;\n        mouse.absolute = { x: 0, y: 0 };\n        mouse.position = { x: 0, y: 0 };\n        mouse.mousedownPosition = { x: 0, y: 0 };\n        mouse.mouseupPosition = { x: 0, y: 0 };\n        mouse.offset = { x: 0, y: 0 };\n        mouse.scale = { x: 1, y: 1 };\n        mouse.wheelDelta = 0;\n        mouse.button = -1;\n        mouse.pixelRatio = parseInt(mouse.element.getAttribute('data-pixel-ratio'), 10) || 1;\n\n        mouse.sourceEvents = {\n            mousemove: null,\n            mousedown: null,\n            mouseup: null,\n            mousewheel: null\n        };\n        \n        mouse.mousemove = function(event) { \n            var position = Mouse._getRelativeMousePosition(event, mouse.element, mouse.pixelRatio),\n                touches = event.changedTouches;\n\n            if (touches) {\n                mouse.button = 0;\n                event.preventDefault();\n            }\n\n            mouse.absolute.x = position.x;\n            mouse.absolute.y = position.y;\n            mouse.position.x = mouse.absolute.x * mouse.scale.x + mouse.offset.x;\n            mouse.position.y = mouse.absolute.y * mouse.scale.y + mouse.offset.y;\n            mouse.sourceEvents.mousemove = event;\n        };\n        \n        mouse.mousedown = function(event) {\n            var position = Mouse._getRelativeMousePosition(event, mouse.element, mouse.pixelRatio),\n                touches = event.changedTouches;\n\n            if (touches) {\n                mouse.button = 0;\n                event.preventDefault();\n            } else {\n                mouse.button = event.button;\n            }\n\n            mouse.absolute.x = position.x;\n            mouse.absolute.y = position.y;\n            mouse.position.x = mouse.absolute.x * mouse.scale.x + mouse.offset.x;\n            mouse.position.y = mouse.absolute.y * mouse.scale.y + mouse.offset.y;\n            mouse.mousedownPosition.x = mouse.position.x;\n            mouse.mousedownPosition.y = mouse.position.y;\n            mouse.sourceEvents.mousedown = event;\n        };\n        \n        mouse.mouseup = function(event) {\n            var position = Mouse._getRelativeMousePosition(event, mouse.element, mouse.pixelRatio),\n                touches = event.changedTouches;\n\n            if (touches) {\n                event.preventDefault();\n            }\n            \n            mouse.button = -1;\n            mouse.absolute.x = position.x;\n            mouse.absolute.y = position.y;\n            mouse.position.x = mouse.absolute.x * mouse.scale.x + mouse.offset.x;\n            mouse.position.y = mouse.absolute.y * mouse.scale.y + mouse.offset.y;\n            mouse.mouseupPosition.x = mouse.position.x;\n            mouse.mouseupPosition.y = mouse.position.y;\n            mouse.sourceEvents.mouseup = event;\n        };\n\n        mouse.mousewheel = function(event) {\n            mouse.wheelDelta = Math.max(-1, Math.min(1, event.wheelDelta || -event.detail));\n            event.preventDefault();\n        };\n\n        Mouse.setElement(mouse, mouse.element);\n\n        return mouse;\n    };\n\n    /**\n     * Sets the element the mouse is bound to (and relative to).\n     * @method setElement\n     * @param {mouse} mouse\n     * @param {HTMLElement} element\n     */\n    Mouse.setElement = function(mouse, element) {\n        mouse.element = element;\n\n        element.addEventListener('mousemove', mouse.mousemove);\n        element.addEventListener('mousedown', mouse.mousedown);\n        element.addEventListener('mouseup', mouse.mouseup);\n        \n        element.addEventListener('mousewheel', mouse.mousewheel);\n        element.addEventListener('DOMMouseScroll', mouse.mousewheel);\n\n        element.addEventListener('touchmove', mouse.mousemove);\n        element.addEventListener('touchstart', mouse.mousedown);\n        element.addEventListener('touchend', mouse.mouseup);\n    };\n\n    /**\n     * Clears all captured source events.\n     * @method clearSourceEvents\n     * @param {mouse} mouse\n     */\n    Mouse.clearSourceEvents = function(mouse) {\n        mouse.sourceEvents.mousemove = null;\n        mouse.sourceEvents.mousedown = null;\n        mouse.sourceEvents.mouseup = null;\n        mouse.sourceEvents.mousewheel = null;\n        mouse.wheelDelta = 0;\n    };\n\n    /**\n     * Sets the mouse position offset.\n     * @method setOffset\n     * @param {mouse} mouse\n     * @param {vector} offset\n     */\n    Mouse.setOffset = function(mouse, offset) {\n        mouse.offset.x = offset.x;\n        mouse.offset.y = offset.y;\n        mouse.position.x = mouse.absolute.x * mouse.scale.x + mouse.offset.x;\n        mouse.position.y = mouse.absolute.y * mouse.scale.y + mouse.offset.y;\n    };\n\n    /**\n     * Sets the mouse position scale.\n     * @method setScale\n     * @param {mouse} mouse\n     * @param {vector} scale\n     */\n    Mouse.setScale = function(mouse, scale) {\n        mouse.scale.x = scale.x;\n        mouse.scale.y = scale.y;\n        mouse.position.x = mouse.absolute.x * mouse.scale.x + mouse.offset.x;\n        mouse.position.y = mouse.absolute.y * mouse.scale.y + mouse.offset.y;\n    };\n    \n    /**\n     * Gets the mouse position relative to an element given a screen pixel ratio.\n     * @method _getRelativeMousePosition\n     * @private\n     * @param {} event\n     * @param {} element\n     * @param {number} pixelRatio\n     * @return {}\n     */\n    Mouse._getRelativeMousePosition = function(event, element, pixelRatio) {\n        var elementBounds = element.getBoundingClientRect(),\n            rootNode = (document.documentElement || document.body.parentNode || document.body),\n            scrollX = (window.pageXOffset !== undefined) ? window.pageXOffset : rootNode.scrollLeft,\n            scrollY = (window.pageYOffset !== undefined) ? window.pageYOffset : rootNode.scrollTop,\n            touches = event.changedTouches,\n            x, y;\n        \n        if (touches) {\n            x = touches[0].pageX - elementBounds.left - scrollX;\n            y = touches[0].pageY - elementBounds.top - scrollY;\n        } else {\n            x = event.pageX - elementBounds.left - scrollX;\n            y = event.pageY - elementBounds.top - scrollY;\n        }\n\n        return { \n            x: x / (element.clientWidth / (element.width || element.clientWidth) * pixelRatio),\n            y: y / (element.clientHeight / (element.height || element.clientHeight) * pixelRatio)\n        };\n    };\n\n})();\n\n\n/***/ }),\n/* 15 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Plugin` module contains functions for registering and installing plugins on modules.\n*\n* @class Plugin\n*/\n\nvar Plugin = {};\n\nmodule.exports = Plugin;\n\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    Plugin._registry = {};\n\n    /**\n     * Registers a plugin object so it can be resolved later by name.\n     * @method register\n     * @param plugin {} The plugin to register.\n     * @return {object} The plugin.\n     */\n    Plugin.register = function(plugin) {\n        if (!Plugin.isPlugin(plugin)) {\n            Common.warn('Plugin.register:', Plugin.toString(plugin), 'does not implement all required fields.');\n        }\n\n        if (plugin.name in Plugin._registry) {\n            var registered = Plugin._registry[plugin.name],\n                pluginVersion = Plugin.versionParse(plugin.version).number,\n                registeredVersion = Plugin.versionParse(registered.version).number;\n\n            if (pluginVersion > registeredVersion) {\n                Common.warn('Plugin.register:', Plugin.toString(registered), 'was upgraded to', Plugin.toString(plugin));\n                Plugin._registry[plugin.name] = plugin;\n            } else if (pluginVersion < registeredVersion) {\n                Common.warn('Plugin.register:', Plugin.toString(registered), 'can not be downgraded to', Plugin.toString(plugin));\n            } else if (plugin !== registered) {\n                Common.warn('Plugin.register:', Plugin.toString(plugin), 'is already registered to different plugin object');\n            }\n        } else {\n            Plugin._registry[plugin.name] = plugin;\n        }\n\n        return plugin;\n    };\n\n    /**\n     * Resolves a dependency to a plugin object from the registry if it exists. \n     * The `dependency` may contain a version, but only the name matters when resolving.\n     * @method resolve\n     * @param dependency {string} The dependency.\n     * @return {object} The plugin if resolved, otherwise `undefined`.\n     */\n    Plugin.resolve = function(dependency) {\n        return Plugin._registry[Plugin.dependencyParse(dependency).name];\n    };\n\n    /**\n     * Returns a pretty printed plugin name and version.\n     * @method toString\n     * @param plugin {} The plugin.\n     * @return {string} Pretty printed plugin name and version.\n     */\n    Plugin.toString = function(plugin) {\n        return typeof plugin === 'string' ? plugin : (plugin.name || 'anonymous') + '@' + (plugin.version || plugin.range || '0.0.0');\n    };\n\n    /**\n     * Returns `true` if the object meets the minimum standard to be considered a plugin.\n     * This means it must define the following properties:\n     * - `name`\n     * - `version`\n     * - `install`\n     * @method isPlugin\n     * @param obj {} The obj to test.\n     * @return {boolean} `true` if the object can be considered a plugin otherwise `false`.\n     */\n    Plugin.isPlugin = function(obj) {\n        return obj && obj.name && obj.version && obj.install;\n    };\n\n    /**\n     * Returns `true` if a plugin with the given `name` been installed on `module`.\n     * @method isUsed\n     * @param module {} The module.\n     * @param name {string} The plugin name.\n     * @return {boolean} `true` if a plugin with the given `name` been installed on `module`, otherwise `false`.\n     */\n    Plugin.isUsed = function(module, name) {\n        return module.used.indexOf(name) > -1;\n    };\n\n    /**\n     * Returns `true` if `plugin.for` is applicable to `module` by comparing against `module.name` and `module.version`.\n     * If `plugin.for` is not specified then it is assumed to be applicable.\n     * The value of `plugin.for` is a string of the format `'module-name'` or `'module-name@version'`.\n     * @method isFor\n     * @param plugin {} The plugin.\n     * @param module {} The module.\n     * @return {boolean} `true` if `plugin.for` is applicable to `module`, otherwise `false`.\n     */\n    Plugin.isFor = function(plugin, module) {\n        var parsed = plugin.for && Plugin.dependencyParse(plugin.for);\n        return !plugin.for || (module.name === parsed.name && Plugin.versionSatisfies(module.version, parsed.range));\n    };\n\n    /**\n     * Installs the plugins by calling `plugin.install` on each plugin specified in `plugins` if passed, otherwise `module.uses`.\n     * For installing plugins on `Matter` see the convenience function `Matter.use`.\n     * Plugins may be specified either by their name or a reference to the plugin object.\n     * Plugins themselves may specify further dependencies, but each plugin is installed only once.\n     * Order is important, a topological sort is performed to find the best resulting order of installation.\n     * This sorting attempts to satisfy every dependency's requested ordering, but may not be exact in all cases.\n     * This function logs the resulting status of each dependency in the console, along with any warnings.\n     * - A green tick ✅ indicates a dependency was resolved and installed.\n     * - An orange diamond 🔶 indicates a dependency was resolved but a warning was thrown for it or one if its dependencies.\n     * - A red cross ❌ indicates a dependency could not be resolved.\n     * Avoid calling this function multiple times on the same module unless you intend to manually control installation order.\n     * @method use\n     * @param module {} The module install plugins on.\n     * @param [plugins=module.uses] {} The plugins to install on module (optional, defaults to `module.uses`).\n     */\n    Plugin.use = function(module, plugins) {\n        module.uses = (module.uses || []).concat(plugins || []);\n\n        if (module.uses.length === 0) {\n            Common.warn('Plugin.use:', Plugin.toString(module), 'does not specify any dependencies to install.');\n            return;\n        }\n\n        var dependencies = Plugin.dependencies(module),\n            sortedDependencies = Common.topologicalSort(dependencies),\n            status = [];\n\n        for (var i = 0; i < sortedDependencies.length; i += 1) {\n            if (sortedDependencies[i] === module.name) {\n                continue;\n            }\n\n            var plugin = Plugin.resolve(sortedDependencies[i]);\n\n            if (!plugin) {\n                status.push('❌ ' + sortedDependencies[i]);\n                continue;\n            }\n\n            if (Plugin.isUsed(module, plugin.name)) {\n                continue;\n            }\n\n            if (!Plugin.isFor(plugin, module)) {\n                Common.warn('Plugin.use:', Plugin.toString(plugin), 'is for', plugin.for, 'but installed on', Plugin.toString(module) + '.');\n                plugin._warned = true;\n            }\n\n            if (plugin.install) {\n                plugin.install(module);\n            } else {\n                Common.warn('Plugin.use:', Plugin.toString(plugin), 'does not specify an install function.');\n                plugin._warned = true;\n            }\n\n            if (plugin._warned) {\n                status.push('🔶 ' + Plugin.toString(plugin));\n                delete plugin._warned;\n            } else {\n                status.push('✅ ' + Plugin.toString(plugin));\n            }\n\n            module.used.push(plugin.name);\n        }\n\n        if (status.length > 0) {\n            Common.info(status.join('  '));\n        }\n    };\n\n    /**\n     * Recursively finds all of a module's dependencies and returns a flat dependency graph.\n     * @method dependencies\n     * @param module {} The module.\n     * @return {object} A dependency graph.\n     */\n    Plugin.dependencies = function(module, tracked) {\n        var parsedBase = Plugin.dependencyParse(module),\n            name = parsedBase.name;\n\n        tracked = tracked || {};\n\n        if (name in tracked) {\n            return;\n        }\n\n        module = Plugin.resolve(module) || module;\n\n        tracked[name] = Common.map(module.uses || [], function(dependency) {\n            if (Plugin.isPlugin(dependency)) {\n                Plugin.register(dependency);\n            }\n\n            var parsed = Plugin.dependencyParse(dependency),\n                resolved = Plugin.resolve(dependency);\n\n            if (resolved && !Plugin.versionSatisfies(resolved.version, parsed.range)) {\n                Common.warn(\n                    'Plugin.dependencies:', Plugin.toString(resolved), 'does not satisfy',\n                    Plugin.toString(parsed), 'used by', Plugin.toString(parsedBase) + '.'\n                );\n\n                resolved._warned = true;\n                module._warned = true;\n            } else if (!resolved) {\n                Common.warn(\n                    'Plugin.dependencies:', Plugin.toString(dependency), 'used by',\n                    Plugin.toString(parsedBase), 'could not be resolved.'\n                );\n\n                module._warned = true;\n            }\n\n            return parsed.name;\n        });\n\n        for (var i = 0; i < tracked[name].length; i += 1) {\n            Plugin.dependencies(tracked[name][i], tracked);\n        }\n\n        return tracked;\n    };\n\n    /**\n     * Parses a dependency string into its components.\n     * The `dependency` is a string of the format `'module-name'` or `'module-name@version'`.\n     * See documentation for `Plugin.versionParse` for a description of the format.\n     * This function can also handle dependencies that are already resolved (e.g. a module object).\n     * @method dependencyParse\n     * @param dependency {string} The dependency of the format `'module-name'` or `'module-name@version'`.\n     * @return {object} The dependency parsed into its components.\n     */\n    Plugin.dependencyParse = function(dependency) {\n        if (Common.isString(dependency)) {\n            var pattern = /^[\\w-]+(@(\\*|[\\^~]?\\d+\\.\\d+\\.\\d+(-[0-9A-Za-z-+]+)?))?$/;\n\n            if (!pattern.test(dependency)) {\n                Common.warn('Plugin.dependencyParse:', dependency, 'is not a valid dependency string.');\n            }\n\n            return {\n                name: dependency.split('@')[0],\n                range: dependency.split('@')[1] || '*'\n            };\n        }\n\n        return {\n            name: dependency.name,\n            range: dependency.range || dependency.version\n        };\n    };\n\n    /**\n     * Parses a version string into its components.  \n     * Versions are strictly of the format `x.y.z` (as in [semver](http://semver.org/)).\n     * Versions may optionally have a prerelease tag in the format `x.y.z-alpha`.\n     * Ranges are a strict subset of [npm ranges](https://docs.npmjs.com/misc/semver#advanced-range-syntax).\n     * Only the following range types are supported:\n     * - Tilde ranges e.g. `~1.2.3`\n     * - Caret ranges e.g. `^1.2.3`\n     * - Greater than ranges e.g. `>1.2.3`\n     * - Greater than or equal ranges e.g. `>=1.2.3`\n     * - Exact version e.g. `1.2.3`\n     * - Any version `*`\n     * @method versionParse\n     * @param range {string} The version string.\n     * @return {object} The version range parsed into its components.\n     */\n    Plugin.versionParse = function(range) {\n        var pattern = /^(\\*)|(\\^|~|>=|>)?\\s*((\\d+)\\.(\\d+)\\.(\\d+))(-[0-9A-Za-z-+]+)?$/;\n\n        if (!pattern.test(range)) {\n            Common.warn('Plugin.versionParse:', range, 'is not a valid version or range.');\n        }\n\n        var parts = pattern.exec(range);\n        var major = Number(parts[4]);\n        var minor = Number(parts[5]);\n        var patch = Number(parts[6]);\n\n        return {\n            isRange: Boolean(parts[1] || parts[2]),\n            version: parts[3],\n            range: range,\n            operator: parts[1] || parts[2] || '',\n            major: major,\n            minor: minor,\n            patch: patch,\n            parts: [major, minor, patch],\n            prerelease: parts[7],\n            number: major * 1e8 + minor * 1e4 + patch\n        };\n    };\n\n    /**\n     * Returns `true` if `version` satisfies the given `range`.\n     * See documentation for `Plugin.versionParse` for a description of the format.\n     * If a version or range is not specified, then any version (`*`) is assumed to satisfy.\n     * @method versionSatisfies\n     * @param version {string} The version string.\n     * @param range {string} The range string.\n     * @return {boolean} `true` if `version` satisfies `range`, otherwise `false`.\n     */\n    Plugin.versionSatisfies = function(version, range) {\n        range = range || '*';\n\n        var r = Plugin.versionParse(range),\n            v = Plugin.versionParse(version);\n\n        if (r.isRange) {\n            if (r.operator === '*' || version === '*') {\n                return true;\n            }\n\n            if (r.operator === '>') {\n                return v.number > r.number;\n            }\n\n            if (r.operator === '>=') {\n                return v.number >= r.number;\n            }\n\n            if (r.operator === '~') {\n                return v.major === r.major && v.minor === r.minor && v.patch >= r.patch;\n            }\n\n            if (r.operator === '^') {\n                if (r.major > 0) {\n                    return v.major === r.major && v.number >= r.number;\n                }\n\n                if (r.minor > 0) {\n                    return v.minor === r.minor && v.patch >= r.patch;\n                }\n\n                return v.patch === r.patch;\n            }\n        }\n\n        return version === range || version === '*';\n    };\n\n})();\n\n\n/***/ }),\n/* 16 */\n/***/ (function(module, exports) {\n\n/**\n* The `Matter.Contact` module contains methods for creating and manipulating collision contacts.\n*\n* @class Contact\n*/\n\nvar Contact = {};\n\nmodule.exports = Contact;\n\n(function() {\n\n    /**\n     * Creates a new contact.\n     * @method create\n     * @param {vertex} vertex\n     * @return {contact} A new contact\n     */\n    Contact.create = function(vertex) {\n        return {\n            vertex: vertex,\n            normalImpulse: 0,\n            tangentImpulse: 0\n        };\n    };\n\n})();\n\n\n/***/ }),\n/* 17 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Engine` module contains methods for creating and manipulating engines.\n* An engine is a controller that manages updating the simulation of the world.\n* See `Matter.Runner` for an optional game loop utility.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Engine\n*/\n\nvar Engine = {};\n\nmodule.exports = Engine;\n\nvar Sleeping = __webpack_require__(7);\nvar Resolver = __webpack_require__(18);\nvar Detector = __webpack_require__(13);\nvar Pairs = __webpack_require__(19);\nvar Events = __webpack_require__(5);\nvar Composite = __webpack_require__(6);\nvar Constraint = __webpack_require__(10);\nvar Common = __webpack_require__(0);\nvar Body = __webpack_require__(4);\n\n(function() {\n\n    /**\n     * Creates a new engine. The options parameter is an object that specifies any properties you wish to override the defaults.\n     * All properties have default values, and many are pre-calculated automatically based on other properties.\n     * See the properties section below for detailed information on what you can pass via the `options` object.\n     * @method create\n     * @param {object} [options]\n     * @return {engine} engine\n     */\n    Engine.create = function(options) {\n        options = options || {};\n\n        var defaults = {\n            positionIterations: 6,\n            velocityIterations: 4,\n            constraintIterations: 2,\n            enableSleeping: false,\n            events: [],\n            plugin: {},\n            gravity: {\n                x: 0,\n                y: 1,\n                scale: 0.001\n            },\n            timing: {\n                timestamp: 0,\n                timeScale: 1,\n                lastDelta: 0,\n                lastElapsed: 0\n            }\n        };\n\n        var engine = Common.extend(defaults, options);\n\n        engine.world = options.world || Composite.create({ label: 'World' });\n        engine.pairs = options.pairs || Pairs.create();\n        engine.detector = options.detector || Detector.create();\n\n        // for temporary back compatibility only\n        engine.grid = { buckets: [] };\n        engine.world.gravity = engine.gravity;\n        engine.broadphase = engine.grid;\n        engine.metrics = {};\n        \n        return engine;\n    };\n\n    /**\n     * Moves the simulation forward in time by `delta` milliseconds.\n     * Triggers `beforeUpdate` and `afterUpdate` events.\n     * Triggers `collisionStart`, `collisionActive` and `collisionEnd` events.\n     * @method update\n     * @param {engine} engine\n     * @param {number} [delta=16.666]\n     */\n    Engine.update = function(engine, delta) {\n        var startTime = Common.now();\n\n        var world = engine.world,\n            detector = engine.detector,\n            pairs = engine.pairs,\n            timing = engine.timing,\n            timestamp = timing.timestamp,\n            i;\n\n        delta = typeof delta !== 'undefined' ? delta : Common._baseDelta;\n        delta *= timing.timeScale;\n\n        // increment timestamp\n        timing.timestamp += delta;\n        timing.lastDelta = delta;\n\n        // create an event object\n        var event = {\n            timestamp: timing.timestamp,\n            delta: delta\n        };\n\n        Events.trigger(engine, 'beforeUpdate', event);\n\n        // get all bodies and all constraints in the world\n        var allBodies = Composite.allBodies(world),\n            allConstraints = Composite.allConstraints(world);\n\n        // if the world has changed\n        if (world.isModified) {\n            // update the detector bodies\n            Detector.setBodies(detector, allBodies);\n\n            // reset all composite modified flags\n            Composite.setModified(world, false, false, true);\n        }\n\n        // update sleeping if enabled\n        if (engine.enableSleeping)\n            Sleeping.update(allBodies, delta);\n\n        // apply gravity to all bodies\n        Engine._bodiesApplyGravity(allBodies, engine.gravity);\n\n        // update all body position and rotation by integration\n        if (delta > 0) {\n            Engine._bodiesUpdate(allBodies, delta);\n        }\n\n        // update all constraints (first pass)\n        Constraint.preSolveAll(allBodies);\n        for (i = 0; i < engine.constraintIterations; i++) {\n            Constraint.solveAll(allConstraints, delta);\n        }\n        Constraint.postSolveAll(allBodies);\n\n        // find all collisions\n        detector.pairs = engine.pairs;\n        var collisions = Detector.collisions(detector);\n\n        // update collision pairs\n        Pairs.update(pairs, collisions, timestamp);\n\n        // wake up bodies involved in collisions\n        if (engine.enableSleeping)\n            Sleeping.afterCollisions(pairs.list);\n\n        // trigger collision events\n        if (pairs.collisionStart.length > 0)\n            Events.trigger(engine, 'collisionStart', { pairs: pairs.collisionStart });\n\n        // iteratively resolve position between collisions\n        var positionDamping = Common.clamp(20 / engine.positionIterations, 0, 1);\n        \n        Resolver.preSolvePosition(pairs.list);\n        for (i = 0; i < engine.positionIterations; i++) {\n            Resolver.solvePosition(pairs.list, delta, positionDamping);\n        }\n        Resolver.postSolvePosition(allBodies);\n\n        // update all constraints (second pass)\n        Constraint.preSolveAll(allBodies);\n        for (i = 0; i < engine.constraintIterations; i++) {\n            Constraint.solveAll(allConstraints, delta);\n        }\n        Constraint.postSolveAll(allBodies);\n\n        // iteratively resolve velocity between collisions\n        Resolver.preSolveVelocity(pairs.list);\n        for (i = 0; i < engine.velocityIterations; i++) {\n            Resolver.solveVelocity(pairs.list, delta);\n        }\n\n        // update body speed and velocity properties\n        Engine._bodiesUpdateVelocities(allBodies);\n\n        // trigger collision events\n        if (pairs.collisionActive.length > 0)\n            Events.trigger(engine, 'collisionActive', { pairs: pairs.collisionActive });\n\n        if (pairs.collisionEnd.length > 0)\n            Events.trigger(engine, 'collisionEnd', { pairs: pairs.collisionEnd });\n\n        // clear force buffers\n        Engine._bodiesClearForces(allBodies);\n\n        Events.trigger(engine, 'afterUpdate', event);\n\n        // log the time elapsed computing this update\n        engine.timing.lastElapsed = Common.now() - startTime;\n\n        return engine;\n    };\n    \n    /**\n     * Merges two engines by keeping the configuration of `engineA` but replacing the world with the one from `engineB`.\n     * @method merge\n     * @param {engine} engineA\n     * @param {engine} engineB\n     */\n    Engine.merge = function(engineA, engineB) {\n        Common.extend(engineA, engineB);\n        \n        if (engineB.world) {\n            engineA.world = engineB.world;\n\n            Engine.clear(engineA);\n\n            var bodies = Composite.allBodies(engineA.world);\n\n            for (var i = 0; i < bodies.length; i++) {\n                var body = bodies[i];\n                Sleeping.set(body, false);\n                body.id = Common.nextId();\n            }\n        }\n    };\n\n    /**\n     * Clears the engine pairs and detector.\n     * @method clear\n     * @param {engine} engine\n     */\n    Engine.clear = function(engine) {\n        Pairs.clear(engine.pairs);\n        Detector.clear(engine.detector);\n    };\n\n    /**\n     * Zeroes the `body.force` and `body.torque` force buffers.\n     * @method _bodiesClearForces\n     * @private\n     * @param {body[]} bodies\n     */\n    Engine._bodiesClearForces = function(bodies) {\n        var bodiesLength = bodies.length;\n\n        for (var i = 0; i < bodiesLength; i++) {\n            var body = bodies[i];\n\n            // reset force buffers\n            body.force.x = 0;\n            body.force.y = 0;\n            body.torque = 0;\n        }\n    };\n\n    /**\n     * Applies gravitational acceleration to all `bodies`.\n     * This models a [uniform gravitational field](https://en.wikipedia.org/wiki/Gravity_of_Earth), similar to near the surface of a planet.\n     * \n     * @method _bodiesApplyGravity\n     * @private\n     * @param {body[]} bodies\n     * @param {vector} gravity\n     */\n    Engine._bodiesApplyGravity = function(bodies, gravity) {\n        var gravityScale = typeof gravity.scale !== 'undefined' ? gravity.scale : 0.001,\n            bodiesLength = bodies.length;\n\n        if ((gravity.x === 0 && gravity.y === 0) || gravityScale === 0) {\n            return;\n        }\n        \n        for (var i = 0; i < bodiesLength; i++) {\n            var body = bodies[i];\n\n            if (body.isStatic || body.isSleeping)\n                continue;\n\n            // add the resultant force of gravity\n            body.force.y += body.mass * gravity.y * gravityScale;\n            body.force.x += body.mass * gravity.x * gravityScale;\n        }\n    };\n\n    /**\n     * Applies `Body.update` to all given `bodies`.\n     * @method _bodiesUpdate\n     * @private\n     * @param {body[]} bodies\n     * @param {number} delta The amount of time elapsed between updates\n     */\n    Engine._bodiesUpdate = function(bodies, delta) {\n        var bodiesLength = bodies.length;\n\n        for (var i = 0; i < bodiesLength; i++) {\n            var body = bodies[i];\n\n            if (body.isStatic || body.isSleeping)\n                continue;\n\n            Body.update(body, delta);\n        }\n    };\n\n    /**\n     * Applies `Body.updateVelocities` to all given `bodies`.\n     * @method _bodiesUpdateVelocities\n     * @private\n     * @param {body[]} bodies\n     */\n    Engine._bodiesUpdateVelocities = function(bodies) {\n        var bodiesLength = bodies.length;\n\n        for (var i = 0; i < bodiesLength; i++) {\n            Body.updateVelocities(bodies[i]);\n        }\n    };\n\n    /**\n     * A deprecated alias for `Runner.run`, use `Matter.Runner.run(engine)` instead and see `Matter.Runner` for more information.\n     * @deprecated use Matter.Runner.run(engine) instead\n     * @method run\n     * @param {engine} engine\n     */\n\n    /**\n    * Fired just before an update\n    *\n    * @event beforeUpdate\n    * @param {object} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {number} event.delta The delta time in milliseconds value used in the update\n    * @param {engine} event.source The source object of the event\n    * @param {string} event.name The name of the event\n    */\n\n    /**\n    * Fired after engine update and all collision events\n    *\n    * @event afterUpdate\n    * @param {object} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {number} event.delta The delta time in milliseconds value used in the update\n    * @param {engine} event.source The source object of the event\n    * @param {string} event.name The name of the event\n    */\n\n    /**\n    * Fired after engine update, provides a list of all pairs that have started to collide in the current tick (if any)\n    *\n    * @event collisionStart\n    * @param {object} event An event object\n    * @param {pair[]} event.pairs List of affected pairs\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {number} event.delta The delta time in milliseconds value used in the update\n    * @param {engine} event.source The source object of the event\n    * @param {string} event.name The name of the event\n    */\n\n    /**\n    * Fired after engine update, provides a list of all pairs that are colliding in the current tick (if any)\n    *\n    * @event collisionActive\n    * @param {object} event An event object\n    * @param {pair[]} event.pairs List of affected pairs\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {number} event.delta The delta time in milliseconds value used in the update\n    * @param {engine} event.source The source object of the event\n    * @param {string} event.name The name of the event\n    */\n\n    /**\n    * Fired after engine update, provides a list of all pairs that have ended collision in the current tick (if any)\n    *\n    * @event collisionEnd\n    * @param {object} event An event object\n    * @param {pair[]} event.pairs List of affected pairs\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {number} event.delta The delta time in milliseconds value used in the update\n    * @param {engine} event.source The source object of the event\n    * @param {string} event.name The name of the event\n    */\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * An integer `Number` that specifies the number of position iterations to perform each update.\n     * The higher the value, the higher quality the simulation will be at the expense of performance.\n     *\n     * @property positionIterations\n     * @type number\n     * @default 6\n     */\n\n    /**\n     * An integer `Number` that specifies the number of velocity iterations to perform each update.\n     * The higher the value, the higher quality the simulation will be at the expense of performance.\n     *\n     * @property velocityIterations\n     * @type number\n     * @default 4\n     */\n\n    /**\n     * An integer `Number` that specifies the number of constraint iterations to perform each update.\n     * The higher the value, the higher quality the simulation will be at the expense of performance.\n     * The default value of `2` is usually very adequate.\n     *\n     * @property constraintIterations\n     * @type number\n     * @default 2\n     */\n\n    /**\n     * A flag that specifies whether the engine should allow sleeping via the `Matter.Sleeping` module.\n     * Sleeping can improve stability and performance, but often at the expense of accuracy.\n     *\n     * @property enableSleeping\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * An `Object` containing properties regarding the timing systems of the engine. \n     *\n     * @property timing\n     * @type object\n     */\n\n    /**\n     * A `Number` that specifies the global scaling factor of time for all bodies.\n     * A value of `0` freezes the simulation.\n     * A value of `0.1` gives a slow-motion effect.\n     * A value of `1.2` gives a speed-up effect.\n     *\n     * @property timing.timeScale\n     * @type number\n     * @default 1\n     */\n\n    /**\n     * A `Number` that specifies the current simulation-time in milliseconds starting from `0`. \n     * It is incremented on every `Engine.update` by the given `delta` argument. \n     * \n     * @property timing.timestamp\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `Number` that represents the total execution time elapsed during the last `Engine.update` in milliseconds.\n     * It is updated by timing from the start of the last `Engine.update` call until it ends.\n     *\n     * This value will also include the total execution time of all event handlers directly or indirectly triggered by the engine update.\n     * \n     * @property timing.lastElapsed\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `Number` that represents the `delta` value used in the last engine update.\n     * \n     * @property timing.lastDelta\n     * @type number\n     * @default 0\n     */\n\n    /**\n     * A `Matter.Detector` instance.\n     *\n     * @property detector\n     * @type detector\n     * @default a Matter.Detector instance\n     */\n\n    /**\n     * A `Matter.Grid` instance.\n     *\n     * @deprecated replaced by `engine.detector`\n     * @property grid\n     * @type grid\n     * @default a Matter.Grid instance\n     */\n\n    /**\n     * Replaced by and now alias for `engine.grid`.\n     *\n     * @deprecated replaced by `engine.detector`\n     * @property broadphase\n     * @type grid\n     * @default a Matter.Grid instance\n     */\n\n    /**\n     * The root `Matter.Composite` instance that will contain all bodies, constraints and other composites to be simulated by this engine.\n     *\n     * @property world\n     * @type composite\n     * @default a Matter.Composite instance\n     */\n\n    /**\n     * An object reserved for storing plugin-specific properties.\n     *\n     * @property plugin\n     * @type {}\n     */\n\n    /**\n     * An optional gravitational acceleration applied to all bodies in `engine.world` on every update.\n     * \n     * This models a [uniform gravitational field](https://en.wikipedia.org/wiki/Gravity_of_Earth), similar to near the surface of a planet. For gravity in other contexts, disable this and apply forces as needed.\n     * \n     * To disable set the `scale` component to `0`.\n     * \n     * This is split into three components for ease of use:  \n     * a normalised direction (`x` and `y`) and magnitude (`scale`).\n     *\n     * @property gravity\n     * @type object\n     */\n\n    /**\n     * The gravitational direction normal `x` component, to be multiplied by `gravity.scale`.\n     * \n     * @property gravity.x\n     * @type object\n     * @default 0\n     */\n\n    /**\n     * The gravitational direction normal `y` component, to be multiplied by `gravity.scale`.\n     *\n     * @property gravity.y\n     * @type object\n     * @default 1\n     */\n\n    /**\n     * The magnitude of the gravitational acceleration.\n     * \n     * @property gravity.scale\n     * @type object\n     * @default 0.001\n     */\n\n})();\n\n\n/***/ }),\n/* 18 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Resolver` module contains methods for resolving collision pairs.\n*\n* @class Resolver\n*/\n\nvar Resolver = {};\n\nmodule.exports = Resolver;\n\nvar Vertices = __webpack_require__(3);\nvar Common = __webpack_require__(0);\nvar Bounds = __webpack_require__(1);\n\n(function() {\n\n    Resolver._restingThresh = 2;\n    Resolver._restingThreshTangent = Math.sqrt(6);\n    Resolver._positionDampen = 0.9;\n    Resolver._positionWarming = 0.8;\n    Resolver._frictionNormalMultiplier = 5;\n    Resolver._frictionMaxStatic = Number.MAX_VALUE;\n\n    /**\n     * Prepare pairs for position solving.\n     * @method preSolvePosition\n     * @param {pair[]} pairs\n     */\n    Resolver.preSolvePosition = function(pairs) {\n        var i,\n            pair,\n            activeCount,\n            pairsLength = pairs.length;\n\n        // find total contacts on each body\n        for (i = 0; i < pairsLength; i++) {\n            pair = pairs[i];\n            \n            if (!pair.isActive)\n                continue;\n            \n            activeCount = pair.activeContacts.length;\n            pair.collision.parentA.totalContacts += activeCount;\n            pair.collision.parentB.totalContacts += activeCount;\n        }\n    };\n\n    /**\n     * Find a solution for pair positions.\n     * @method solvePosition\n     * @param {pair[]} pairs\n     * @param {number} delta\n     * @param {number} [damping=1]\n     */\n    Resolver.solvePosition = function(pairs, delta, damping) {\n        var i,\n            pair,\n            collision,\n            bodyA,\n            bodyB,\n            normal,\n            contactShare,\n            positionImpulse,\n            positionDampen = Resolver._positionDampen * (damping || 1),\n            slopDampen = Common.clamp(delta / Common._baseDelta, 0, 1),\n            pairsLength = pairs.length;\n\n        // find impulses required to resolve penetration\n        for (i = 0; i < pairsLength; i++) {\n            pair = pairs[i];\n            \n            if (!pair.isActive || pair.isSensor)\n                continue;\n\n            collision = pair.collision;\n            bodyA = collision.parentA;\n            bodyB = collision.parentB;\n            normal = collision.normal;\n\n            // get current separation between body edges involved in collision\n            pair.separation = \n                normal.x * (bodyB.positionImpulse.x + collision.penetration.x - bodyA.positionImpulse.x)\n                + normal.y * (bodyB.positionImpulse.y + collision.penetration.y - bodyA.positionImpulse.y);\n        }\n        \n        for (i = 0; i < pairsLength; i++) {\n            pair = pairs[i];\n\n            if (!pair.isActive || pair.isSensor)\n                continue;\n            \n            collision = pair.collision;\n            bodyA = collision.parentA;\n            bodyB = collision.parentB;\n            normal = collision.normal;\n            positionImpulse = pair.separation - pair.slop * slopDampen;\n\n            if (bodyA.isStatic || bodyB.isStatic)\n                positionImpulse *= 2;\n            \n            if (!(bodyA.isStatic || bodyA.isSleeping)) {\n                contactShare = positionDampen / bodyA.totalContacts;\n                bodyA.positionImpulse.x += normal.x * positionImpulse * contactShare;\n                bodyA.positionImpulse.y += normal.y * positionImpulse * contactShare;\n            }\n\n            if (!(bodyB.isStatic || bodyB.isSleeping)) {\n                contactShare = positionDampen / bodyB.totalContacts;\n                bodyB.positionImpulse.x -= normal.x * positionImpulse * contactShare;\n                bodyB.positionImpulse.y -= normal.y * positionImpulse * contactShare;\n            }\n        }\n    };\n\n    /**\n     * Apply position resolution.\n     * @method postSolvePosition\n     * @param {body[]} bodies\n     */\n    Resolver.postSolvePosition = function(bodies) {\n        var positionWarming = Resolver._positionWarming,\n            bodiesLength = bodies.length,\n            verticesTranslate = Vertices.translate,\n            boundsUpdate = Bounds.update;\n\n        for (var i = 0; i < bodiesLength; i++) {\n            var body = bodies[i],\n                positionImpulse = body.positionImpulse,\n                positionImpulseX = positionImpulse.x,\n                positionImpulseY = positionImpulse.y,\n                velocity = body.velocity;\n\n            // reset contact count\n            body.totalContacts = 0;\n\n            if (positionImpulseX !== 0 || positionImpulseY !== 0) {\n                // update body geometry\n                for (var j = 0; j < body.parts.length; j++) {\n                    var part = body.parts[j];\n                    verticesTranslate(part.vertices, positionImpulse);\n                    boundsUpdate(part.bounds, part.vertices, velocity);\n                    part.position.x += positionImpulseX;\n                    part.position.y += positionImpulseY;\n                }\n\n                // move the body without changing velocity\n                body.positionPrev.x += positionImpulseX;\n                body.positionPrev.y += positionImpulseY;\n\n                if (positionImpulseX * velocity.x + positionImpulseY * velocity.y < 0) {\n                    // reset cached impulse if the body has velocity along it\n                    positionImpulse.x = 0;\n                    positionImpulse.y = 0;\n                } else {\n                    // warm the next iteration\n                    positionImpulse.x *= positionWarming;\n                    positionImpulse.y *= positionWarming;\n                }\n            }\n        }\n    };\n\n    /**\n     * Prepare pairs for velocity solving.\n     * @method preSolveVelocity\n     * @param {pair[]} pairs\n     */\n    Resolver.preSolveVelocity = function(pairs) {\n        var pairsLength = pairs.length,\n            i,\n            j;\n        \n        for (i = 0; i < pairsLength; i++) {\n            var pair = pairs[i];\n            \n            if (!pair.isActive || pair.isSensor)\n                continue;\n            \n            var contacts = pair.activeContacts,\n                contactsLength = contacts.length,\n                collision = pair.collision,\n                bodyA = collision.parentA,\n                bodyB = collision.parentB,\n                normal = collision.normal,\n                tangent = collision.tangent;\n    \n            // resolve each contact\n            for (j = 0; j < contactsLength; j++) {\n                var contact = contacts[j],\n                    contactVertex = contact.vertex,\n                    normalImpulse = contact.normalImpulse,\n                    tangentImpulse = contact.tangentImpulse;\n    \n                if (normalImpulse !== 0 || tangentImpulse !== 0) {\n                    // total impulse from contact\n                    var impulseX = normal.x * normalImpulse + tangent.x * tangentImpulse,\n                        impulseY = normal.y * normalImpulse + tangent.y * tangentImpulse;\n                    \n                    // apply impulse from contact\n                    if (!(bodyA.isStatic || bodyA.isSleeping)) {\n                        bodyA.positionPrev.x += impulseX * bodyA.inverseMass;\n                        bodyA.positionPrev.y += impulseY * bodyA.inverseMass;\n                        bodyA.anglePrev += bodyA.inverseInertia * (\n                            (contactVertex.x - bodyA.position.x) * impulseY\n                            - (contactVertex.y - bodyA.position.y) * impulseX\n                        );\n                    }\n    \n                    if (!(bodyB.isStatic || bodyB.isSleeping)) {\n                        bodyB.positionPrev.x -= impulseX * bodyB.inverseMass;\n                        bodyB.positionPrev.y -= impulseY * bodyB.inverseMass;\n                        bodyB.anglePrev -= bodyB.inverseInertia * (\n                            (contactVertex.x - bodyB.position.x) * impulseY \n                            - (contactVertex.y - bodyB.position.y) * impulseX\n                        );\n                    }\n                }\n            }\n        }\n    };\n\n    /**\n     * Find a solution for pair velocities.\n     * @method solveVelocity\n     * @param {pair[]} pairs\n     * @param {number} delta\n     */\n    Resolver.solveVelocity = function(pairs, delta) {\n        var timeScale = delta / Common._baseDelta,\n            timeScaleSquared = timeScale * timeScale,\n            timeScaleCubed = timeScaleSquared * timeScale,\n            restingThresh = -Resolver._restingThresh * timeScale,\n            restingThreshTangent = Resolver._restingThreshTangent,\n            frictionNormalMultiplier = Resolver._frictionNormalMultiplier * timeScale,\n            frictionMaxStatic = Resolver._frictionMaxStatic,\n            pairsLength = pairs.length,\n            tangentImpulse,\n            maxFriction,\n            i,\n            j;\n\n        for (i = 0; i < pairsLength; i++) {\n            var pair = pairs[i];\n            \n            if (!pair.isActive || pair.isSensor)\n                continue;\n            \n            var collision = pair.collision,\n                bodyA = collision.parentA,\n                bodyB = collision.parentB,\n                bodyAVelocity = bodyA.velocity,\n                bodyBVelocity = bodyB.velocity,\n                normalX = collision.normal.x,\n                normalY = collision.normal.y,\n                tangentX = collision.tangent.x,\n                tangentY = collision.tangent.y,\n                contacts = pair.activeContacts,\n                contactsLength = contacts.length,\n                contactShare = 1 / contactsLength,\n                inverseMassTotal = bodyA.inverseMass + bodyB.inverseMass,\n                friction = pair.friction * pair.frictionStatic * frictionNormalMultiplier;\n\n            // update body velocities\n            bodyAVelocity.x = bodyA.position.x - bodyA.positionPrev.x;\n            bodyAVelocity.y = bodyA.position.y - bodyA.positionPrev.y;\n            bodyBVelocity.x = bodyB.position.x - bodyB.positionPrev.x;\n            bodyBVelocity.y = bodyB.position.y - bodyB.positionPrev.y;\n            bodyA.angularVelocity = bodyA.angle - bodyA.anglePrev;\n            bodyB.angularVelocity = bodyB.angle - bodyB.anglePrev;\n\n            // resolve each contact\n            for (j = 0; j < contactsLength; j++) {\n                var contact = contacts[j],\n                    contactVertex = contact.vertex;\n\n                var offsetAX = contactVertex.x - bodyA.position.x,\n                    offsetAY = contactVertex.y - bodyA.position.y,\n                    offsetBX = contactVertex.x - bodyB.position.x,\n                    offsetBY = contactVertex.y - bodyB.position.y;\n \n                var velocityPointAX = bodyAVelocity.x - offsetAY * bodyA.angularVelocity,\n                    velocityPointAY = bodyAVelocity.y + offsetAX * bodyA.angularVelocity,\n                    velocityPointBX = bodyBVelocity.x - offsetBY * bodyB.angularVelocity,\n                    velocityPointBY = bodyBVelocity.y + offsetBX * bodyB.angularVelocity;\n\n                var relativeVelocityX = velocityPointAX - velocityPointBX,\n                    relativeVelocityY = velocityPointAY - velocityPointBY;\n\n                var normalVelocity = normalX * relativeVelocityX + normalY * relativeVelocityY,\n                    tangentVelocity = tangentX * relativeVelocityX + tangentY * relativeVelocityY;\n\n                // coulomb friction\n                var normalOverlap = pair.separation + normalVelocity;\n                var normalForce = Math.min(normalOverlap, 1);\n                normalForce = normalOverlap < 0 ? 0 : normalForce;\n\n                var frictionLimit = normalForce * friction;\n\n                if (tangentVelocity < -frictionLimit || tangentVelocity > frictionLimit) {\n                    maxFriction = (tangentVelocity > 0 ? tangentVelocity : -tangentVelocity);\n                    tangentImpulse = pair.friction * (tangentVelocity > 0 ? 1 : -1) * timeScaleCubed;\n                    \n                    if (tangentImpulse < -maxFriction) {\n                        tangentImpulse = -maxFriction;\n                    } else if (tangentImpulse > maxFriction) {\n                        tangentImpulse = maxFriction;\n                    }\n                } else {\n                    tangentImpulse = tangentVelocity;\n                    maxFriction = frictionMaxStatic;\n                }\n\n                // account for mass, inertia and contact offset\n                var oAcN = offsetAX * normalY - offsetAY * normalX,\n                    oBcN = offsetBX * normalY - offsetBY * normalX,\n                    share = contactShare / (inverseMassTotal + bodyA.inverseInertia * oAcN * oAcN + bodyB.inverseInertia * oBcN * oBcN);\n\n                // raw impulses\n                var normalImpulse = (1 + pair.restitution) * normalVelocity * share;\n                tangentImpulse *= share;\n\n                // handle high velocity and resting collisions separately\n                if (normalVelocity < restingThresh) {\n                    // high normal velocity so clear cached contact normal impulse\n                    contact.normalImpulse = 0;\n                } else {\n                    // solve resting collision constraints using Erin Catto's method (GDC08)\n                    // impulse constraint tends to 0\n                    var contactNormalImpulse = contact.normalImpulse;\n                    contact.normalImpulse += normalImpulse;\n                    if (contact.normalImpulse > 0) contact.normalImpulse = 0;\n                    normalImpulse = contact.normalImpulse - contactNormalImpulse;\n                }\n\n                // handle high velocity and resting collisions separately\n                if (tangentVelocity < -restingThreshTangent || tangentVelocity > restingThreshTangent) {\n                    // high tangent velocity so clear cached contact tangent impulse\n                    contact.tangentImpulse = 0;\n                } else {\n                    // solve resting collision constraints using Erin Catto's method (GDC08)\n                    // tangent impulse tends to -tangentSpeed or +tangentSpeed\n                    var contactTangentImpulse = contact.tangentImpulse;\n                    contact.tangentImpulse += tangentImpulse;\n                    if (contact.tangentImpulse < -maxFriction) contact.tangentImpulse = -maxFriction;\n                    if (contact.tangentImpulse > maxFriction) contact.tangentImpulse = maxFriction;\n                    tangentImpulse = contact.tangentImpulse - contactTangentImpulse;\n                }\n\n                // total impulse from contact\n                var impulseX = normalX * normalImpulse + tangentX * tangentImpulse,\n                    impulseY = normalY * normalImpulse + tangentY * tangentImpulse;\n                \n                // apply impulse from contact\n                if (!(bodyA.isStatic || bodyA.isSleeping)) {\n                    bodyA.positionPrev.x += impulseX * bodyA.inverseMass;\n                    bodyA.positionPrev.y += impulseY * bodyA.inverseMass;\n                    bodyA.anglePrev += (offsetAX * impulseY - offsetAY * impulseX) * bodyA.inverseInertia;\n                }\n\n                if (!(bodyB.isStatic || bodyB.isSleeping)) {\n                    bodyB.positionPrev.x -= impulseX * bodyB.inverseMass;\n                    bodyB.positionPrev.y -= impulseY * bodyB.inverseMass;\n                    bodyB.anglePrev -= (offsetBX * impulseY - offsetBY * impulseX) * bodyB.inverseInertia;\n                }\n            }\n        }\n    };\n\n})();\n\n\n/***/ }),\n/* 19 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Pairs` module contains methods for creating and manipulating collision pair sets.\n*\n* @class Pairs\n*/\n\nvar Pairs = {};\n\nmodule.exports = Pairs;\n\nvar Pair = __webpack_require__(9);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * Creates a new pairs structure.\n     * @method create\n     * @param {object} options\n     * @return {pairs} A new pairs structure\n     */\n    Pairs.create = function(options) {\n        return Common.extend({ \n            table: {},\n            list: [],\n            collisionStart: [],\n            collisionActive: [],\n            collisionEnd: []\n        }, options);\n    };\n\n    /**\n     * Updates pairs given a list of collisions.\n     * @method update\n     * @param {object} pairs\n     * @param {collision[]} collisions\n     * @param {number} timestamp\n     */\n    Pairs.update = function(pairs, collisions, timestamp) {\n        var pairsList = pairs.list,\n            pairsListLength = pairsList.length,\n            pairsTable = pairs.table,\n            collisionsLength = collisions.length,\n            collisionStart = pairs.collisionStart,\n            collisionEnd = pairs.collisionEnd,\n            collisionActive = pairs.collisionActive,\n            collision,\n            pairIndex,\n            pair,\n            i;\n\n        // clear collision state arrays, but maintain old reference\n        collisionStart.length = 0;\n        collisionEnd.length = 0;\n        collisionActive.length = 0;\n\n        for (i = 0; i < pairsListLength; i++) {\n            pairsList[i].confirmedActive = false;\n        }\n\n        for (i = 0; i < collisionsLength; i++) {\n            collision = collisions[i];\n            pair = collision.pair;\n\n            if (pair) {\n                // pair already exists (but may or may not be active)\n                if (pair.isActive) {\n                    // pair exists and is active\n                    collisionActive.push(pair);\n                } else {\n                    // pair exists but was inactive, so a collision has just started again\n                    collisionStart.push(pair);\n                }\n\n                // update the pair\n                Pair.update(pair, collision, timestamp);\n                pair.confirmedActive = true;\n            } else {\n                // pair did not exist, create a new pair\n                pair = Pair.create(collision, timestamp);\n                pairsTable[pair.id] = pair;\n\n                // push the new pair\n                collisionStart.push(pair);\n                pairsList.push(pair);\n            }\n        }\n\n        // find pairs that are no longer active\n        var removePairIndex = [];\n        pairsListLength = pairsList.length;\n\n        for (i = 0; i < pairsListLength; i++) {\n            pair = pairsList[i];\n            \n            if (!pair.confirmedActive) {\n                Pair.setActive(pair, false, timestamp);\n                collisionEnd.push(pair);\n\n                if (!pair.collision.bodyA.isSleeping && !pair.collision.bodyB.isSleeping) {\n                    removePairIndex.push(i);\n                }\n            }\n        }\n\n        // remove inactive pairs\n        for (i = 0; i < removePairIndex.length; i++) {\n            pairIndex = removePairIndex[i] - i;\n            pair = pairsList[pairIndex];\n            pairsList.splice(pairIndex, 1);\n            delete pairsTable[pair.id];\n        }\n    };\n\n    /**\n     * Clears the given pairs structure.\n     * @method clear\n     * @param {pairs} pairs\n     * @return {pairs} pairs\n     */\n    Pairs.clear = function(pairs) {\n        pairs.table = {};\n        pairs.list.length = 0;\n        pairs.collisionStart.length = 0;\n        pairs.collisionActive.length = 0;\n        pairs.collisionEnd.length = 0;\n        return pairs;\n    };\n\n})();\n\n\n/***/ }),\n/* 20 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar Matter = module.exports = __webpack_require__(21);\r\n\r\nMatter.Axes = __webpack_require__(11);\r\nMatter.Bodies = __webpack_require__(12);\r\nMatter.Body = __webpack_require__(4);\r\nMatter.Bounds = __webpack_require__(1);\r\nMatter.Collision = __webpack_require__(8);\r\nMatter.Common = __webpack_require__(0);\r\nMatter.Composite = __webpack_require__(6);\r\nMatter.Composites = __webpack_require__(22);\r\nMatter.Constraint = __webpack_require__(10);\r\nMatter.Contact = __webpack_require__(16);\r\nMatter.Detector = __webpack_require__(13);\r\nMatter.Engine = __webpack_require__(17);\r\nMatter.Events = __webpack_require__(5);\r\nMatter.Grid = __webpack_require__(23);\r\nMatter.Mouse = __webpack_require__(14);\r\nMatter.MouseConstraint = __webpack_require__(24);\r\nMatter.Pair = __webpack_require__(9);\r\nMatter.Pairs = __webpack_require__(19);\r\nMatter.Plugin = __webpack_require__(15);\r\nMatter.Query = __webpack_require__(25);\r\nMatter.Render = __webpack_require__(26);\r\nMatter.Resolver = __webpack_require__(18);\r\nMatter.Runner = __webpack_require__(27);\r\nMatter.SAT = __webpack_require__(28);\r\nMatter.Sleeping = __webpack_require__(7);\r\nMatter.Svg = __webpack_require__(29);\r\nMatter.Vector = __webpack_require__(2);\r\nMatter.Vertices = __webpack_require__(3);\r\nMatter.World = __webpack_require__(30);\r\n\r\n// temporary back compatibility\r\nMatter.Engine.run = Matter.Runner.run;\r\nMatter.Common.deprecated(Matter.Engine, 'run', 'Engine.run ➤ use Matter.Runner.run(engine) instead');\r\n\n\n/***/ }),\n/* 21 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\r\n* The `Matter` module is the top level namespace. It also includes a function for installing plugins on top of the library.\r\n*\r\n* @class Matter\r\n*/\r\n\r\nvar Matter = {};\r\n\r\nmodule.exports = Matter;\r\n\r\nvar Plugin = __webpack_require__(15);\r\nvar Common = __webpack_require__(0);\r\n\r\n(function() {\r\n\r\n    /**\r\n     * The library name.\r\n     * @property name\r\n     * @readOnly\r\n     * @type {String}\r\n     */\r\n    Matter.name = 'matter-js';\r\n\r\n    /**\r\n     * The library version.\r\n     * @property version\r\n     * @readOnly\r\n     * @type {String}\r\n     */\r\n    Matter.version =  true ? \"0.19.0\" : undefined;\r\n\r\n    /**\r\n     * A list of plugin dependencies to be installed. These are normally set and installed through `Matter.use`.\r\n     * Alternatively you may set `Matter.uses` manually and install them by calling `Plugin.use(Matter)`.\r\n     * @property uses\r\n     * @type {Array}\r\n     */\r\n    Matter.uses = [];\r\n\r\n    /**\r\n     * The plugins that have been installed through `Matter.Plugin.install`. Read only.\r\n     * @property used\r\n     * @readOnly\r\n     * @type {Array}\r\n     */\r\n    Matter.used = [];\r\n\r\n    /**\r\n     * Installs the given plugins on the `Matter` namespace.\r\n     * This is a short-hand for `Plugin.use`, see it for more information.\r\n     * Call this function once at the start of your code, with all of the plugins you wish to install as arguments.\r\n     * Avoid calling this function multiple times unless you intend to manually control installation order.\r\n     * @method use\r\n     * @param ...plugin {Function} The plugin(s) to install on `base` (multi-argument).\r\n     */\r\n    Matter.use = function() {\r\n        Plugin.use(Matter, Array.prototype.slice.call(arguments));\r\n    };\r\n\r\n    /**\r\n     * Chains a function to excute before the original function on the given `path` relative to `Matter`.\r\n     * See also docs for `Common.chain`.\r\n     * @method before\r\n     * @param {string} path The path relative to `Matter`\r\n     * @param {function} func The function to chain before the original\r\n     * @return {function} The chained function that replaced the original\r\n     */\r\n    Matter.before = function(path, func) {\r\n        path = path.replace(/^Matter./, '');\r\n        return Common.chainPathBefore(Matter, path, func);\r\n    };\r\n\r\n    /**\r\n     * Chains a function to excute after the original function on the given `path` relative to `Matter`.\r\n     * See also docs for `Common.chain`.\r\n     * @method after\r\n     * @param {string} path The path relative to `Matter`\r\n     * @param {function} func The function to chain after the original\r\n     * @return {function} The chained function that replaced the original\r\n     */\r\n    Matter.after = function(path, func) {\r\n        path = path.replace(/^Matter./, '');\r\n        return Common.chainPathAfter(Matter, path, func);\r\n    };\r\n\r\n})();\r\n\n\n/***/ }),\n/* 22 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Composites` module contains factory methods for creating composite bodies\n* with commonly used configurations (such as stacks and chains).\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Composites\n*/\n\nvar Composites = {};\n\nmodule.exports = Composites;\n\nvar Composite = __webpack_require__(6);\nvar Constraint = __webpack_require__(10);\nvar Common = __webpack_require__(0);\nvar Body = __webpack_require__(4);\nvar Bodies = __webpack_require__(12);\nvar deprecated = Common.deprecated;\n\n(function() {\n\n    /**\n     * Create a new composite containing bodies created in the callback in a grid arrangement.\n     * This function uses the body's bounds to prevent overlaps.\n     * @method stack\n     * @param {number} xx\n     * @param {number} yy\n     * @param {number} columns\n     * @param {number} rows\n     * @param {number} columnGap\n     * @param {number} rowGap\n     * @param {function} callback\n     * @return {composite} A new composite containing objects created in the callback\n     */\n    Composites.stack = function(xx, yy, columns, rows, columnGap, rowGap, callback) {\n        var stack = Composite.create({ label: 'Stack' }),\n            x = xx,\n            y = yy,\n            lastBody,\n            i = 0;\n\n        for (var row = 0; row < rows; row++) {\n            var maxHeight = 0;\n            \n            for (var column = 0; column < columns; column++) {\n                var body = callback(x, y, column, row, lastBody, i);\n                    \n                if (body) {\n                    var bodyHeight = body.bounds.max.y - body.bounds.min.y,\n                        bodyWidth = body.bounds.max.x - body.bounds.min.x; \n\n                    if (bodyHeight > maxHeight)\n                        maxHeight = bodyHeight;\n                    \n                    Body.translate(body, { x: bodyWidth * 0.5, y: bodyHeight * 0.5 });\n\n                    x = body.bounds.max.x + columnGap;\n\n                    Composite.addBody(stack, body);\n                    \n                    lastBody = body;\n                    i += 1;\n                } else {\n                    x += columnGap;\n                }\n            }\n            \n            y += maxHeight + rowGap;\n            x = xx;\n        }\n\n        return stack;\n    };\n    \n    /**\n     * Chains all bodies in the given composite together using constraints.\n     * @method chain\n     * @param {composite} composite\n     * @param {number} xOffsetA\n     * @param {number} yOffsetA\n     * @param {number} xOffsetB\n     * @param {number} yOffsetB\n     * @param {object} options\n     * @return {composite} A new composite containing objects chained together with constraints\n     */\n    Composites.chain = function(composite, xOffsetA, yOffsetA, xOffsetB, yOffsetB, options) {\n        var bodies = composite.bodies;\n        \n        for (var i = 1; i < bodies.length; i++) {\n            var bodyA = bodies[i - 1],\n                bodyB = bodies[i],\n                bodyAHeight = bodyA.bounds.max.y - bodyA.bounds.min.y,\n                bodyAWidth = bodyA.bounds.max.x - bodyA.bounds.min.x, \n                bodyBHeight = bodyB.bounds.max.y - bodyB.bounds.min.y,\n                bodyBWidth = bodyB.bounds.max.x - bodyB.bounds.min.x;\n        \n            var defaults = {\n                bodyA: bodyA,\n                pointA: { x: bodyAWidth * xOffsetA, y: bodyAHeight * yOffsetA },\n                bodyB: bodyB,\n                pointB: { x: bodyBWidth * xOffsetB, y: bodyBHeight * yOffsetB }\n            };\n            \n            var constraint = Common.extend(defaults, options);\n        \n            Composite.addConstraint(composite, Constraint.create(constraint));\n        }\n\n        composite.label += ' Chain';\n        \n        return composite;\n    };\n\n    /**\n     * Connects bodies in the composite with constraints in a grid pattern, with optional cross braces.\n     * @method mesh\n     * @param {composite} composite\n     * @param {number} columns\n     * @param {number} rows\n     * @param {boolean} crossBrace\n     * @param {object} options\n     * @return {composite} The composite containing objects meshed together with constraints\n     */\n    Composites.mesh = function(composite, columns, rows, crossBrace, options) {\n        var bodies = composite.bodies,\n            row,\n            col,\n            bodyA,\n            bodyB,\n            bodyC;\n        \n        for (row = 0; row < rows; row++) {\n            for (col = 1; col < columns; col++) {\n                bodyA = bodies[(col - 1) + (row * columns)];\n                bodyB = bodies[col + (row * columns)];\n                Composite.addConstraint(composite, Constraint.create(Common.extend({ bodyA: bodyA, bodyB: bodyB }, options)));\n            }\n\n            if (row > 0) {\n                for (col = 0; col < columns; col++) {\n                    bodyA = bodies[col + ((row - 1) * columns)];\n                    bodyB = bodies[col + (row * columns)];\n                    Composite.addConstraint(composite, Constraint.create(Common.extend({ bodyA: bodyA, bodyB: bodyB }, options)));\n\n                    if (crossBrace && col > 0) {\n                        bodyC = bodies[(col - 1) + ((row - 1) * columns)];\n                        Composite.addConstraint(composite, Constraint.create(Common.extend({ bodyA: bodyC, bodyB: bodyB }, options)));\n                    }\n\n                    if (crossBrace && col < columns - 1) {\n                        bodyC = bodies[(col + 1) + ((row - 1) * columns)];\n                        Composite.addConstraint(composite, Constraint.create(Common.extend({ bodyA: bodyC, bodyB: bodyB }, options)));\n                    }\n                }\n            }\n        }\n\n        composite.label += ' Mesh';\n        \n        return composite;\n    };\n    \n    /**\n     * Create a new composite containing bodies created in the callback in a pyramid arrangement.\n     * This function uses the body's bounds to prevent overlaps.\n     * @method pyramid\n     * @param {number} xx\n     * @param {number} yy\n     * @param {number} columns\n     * @param {number} rows\n     * @param {number} columnGap\n     * @param {number} rowGap\n     * @param {function} callback\n     * @return {composite} A new composite containing objects created in the callback\n     */\n    Composites.pyramid = function(xx, yy, columns, rows, columnGap, rowGap, callback) {\n        return Composites.stack(xx, yy, columns, rows, columnGap, rowGap, function(x, y, column, row, lastBody, i) {\n            var actualRows = Math.min(rows, Math.ceil(columns / 2)),\n                lastBodyWidth = lastBody ? lastBody.bounds.max.x - lastBody.bounds.min.x : 0;\n            \n            if (row > actualRows)\n                return;\n            \n            // reverse row order\n            row = actualRows - row;\n            \n            var start = row,\n                end = columns - 1 - row;\n\n            if (column < start || column > end)\n                return;\n            \n            // retroactively fix the first body's position, since width was unknown\n            if (i === 1) {\n                Body.translate(lastBody, { x: (column + (columns % 2 === 1 ? 1 : -1)) * lastBodyWidth, y: 0 });\n            }\n\n            var xOffset = lastBody ? column * lastBodyWidth : 0;\n            \n            return callback(xx + xOffset + column * columnGap, y, column, row, lastBody, i);\n        });\n    };\n\n    /**\n     * This has now moved to the [newtonsCradle example](https://github.com/liabru/matter-js/blob/master/examples/newtonsCradle.js), follow that instead as this function is deprecated here.\n     * @deprecated moved to newtonsCradle example\n     * @method newtonsCradle\n     * @param {number} xx\n     * @param {number} yy\n     * @param {number} number\n     * @param {number} size\n     * @param {number} length\n     * @return {composite} A new composite newtonsCradle body\n     */\n    Composites.newtonsCradle = function(xx, yy, number, size, length) {\n        var newtonsCradle = Composite.create({ label: 'Newtons Cradle' });\n\n        for (var i = 0; i < number; i++) {\n            var separation = 1.9,\n                circle = Bodies.circle(xx + i * (size * separation), yy + length, size, \n                    { inertia: Infinity, restitution: 1, friction: 0, frictionAir: 0.0001, slop: 1 }),\n                constraint = Constraint.create({ pointA: { x: xx + i * (size * separation), y: yy }, bodyB: circle });\n\n            Composite.addBody(newtonsCradle, circle);\n            Composite.addConstraint(newtonsCradle, constraint);\n        }\n\n        return newtonsCradle;\n    };\n\n    deprecated(Composites, 'newtonsCradle', 'Composites.newtonsCradle ➤ moved to newtonsCradle example');\n    \n    /**\n     * This has now moved to the [car example](https://github.com/liabru/matter-js/blob/master/examples/car.js), follow that instead as this function is deprecated here.\n     * @deprecated moved to car example\n     * @method car\n     * @param {number} xx\n     * @param {number} yy\n     * @param {number} width\n     * @param {number} height\n     * @param {number} wheelSize\n     * @return {composite} A new composite car body\n     */\n    Composites.car = function(xx, yy, width, height, wheelSize) {\n        var group = Body.nextGroup(true),\n            wheelBase = 20,\n            wheelAOffset = -width * 0.5 + wheelBase,\n            wheelBOffset = width * 0.5 - wheelBase,\n            wheelYOffset = 0;\n    \n        var car = Composite.create({ label: 'Car' }),\n            body = Bodies.rectangle(xx, yy, width, height, { \n                collisionFilter: {\n                    group: group\n                },\n                chamfer: {\n                    radius: height * 0.5\n                },\n                density: 0.0002\n            });\n    \n        var wheelA = Bodies.circle(xx + wheelAOffset, yy + wheelYOffset, wheelSize, { \n            collisionFilter: {\n                group: group\n            },\n            friction: 0.8\n        });\n                    \n        var wheelB = Bodies.circle(xx + wheelBOffset, yy + wheelYOffset, wheelSize, { \n            collisionFilter: {\n                group: group\n            },\n            friction: 0.8\n        });\n                    \n        var axelA = Constraint.create({\n            bodyB: body,\n            pointB: { x: wheelAOffset, y: wheelYOffset },\n            bodyA: wheelA,\n            stiffness: 1,\n            length: 0\n        });\n                        \n        var axelB = Constraint.create({\n            bodyB: body,\n            pointB: { x: wheelBOffset, y: wheelYOffset },\n            bodyA: wheelB,\n            stiffness: 1,\n            length: 0\n        });\n        \n        Composite.addBody(car, body);\n        Composite.addBody(car, wheelA);\n        Composite.addBody(car, wheelB);\n        Composite.addConstraint(car, axelA);\n        Composite.addConstraint(car, axelB);\n\n        return car;\n    };\n\n    deprecated(Composites, 'car', 'Composites.car ➤ moved to car example');\n\n    /**\n     * This has now moved to the [softBody example](https://github.com/liabru/matter-js/blob/master/examples/softBody.js)\n     * and the [cloth example](https://github.com/liabru/matter-js/blob/master/examples/cloth.js), follow those instead as this function is deprecated here.\n     * @deprecated moved to softBody and cloth examples\n     * @method softBody\n     * @param {number} xx\n     * @param {number} yy\n     * @param {number} columns\n     * @param {number} rows\n     * @param {number} columnGap\n     * @param {number} rowGap\n     * @param {boolean} crossBrace\n     * @param {number} particleRadius\n     * @param {} particleOptions\n     * @param {} constraintOptions\n     * @return {composite} A new composite softBody\n     */\n    Composites.softBody = function(xx, yy, columns, rows, columnGap, rowGap, crossBrace, particleRadius, particleOptions, constraintOptions) {\n        particleOptions = Common.extend({ inertia: Infinity }, particleOptions);\n        constraintOptions = Common.extend({ stiffness: 0.2, render: { type: 'line', anchors: false } }, constraintOptions);\n\n        var softBody = Composites.stack(xx, yy, columns, rows, columnGap, rowGap, function(x, y) {\n            return Bodies.circle(x, y, particleRadius, particleOptions);\n        });\n\n        Composites.mesh(softBody, columns, rows, crossBrace, constraintOptions);\n\n        softBody.label = 'Soft Body';\n\n        return softBody;\n    };\n\n    deprecated(Composites, 'softBody', 'Composites.softBody ➤ moved to softBody and cloth examples');\n})();\n\n\n/***/ }),\n/* 23 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* This module has now been replaced by `Matter.Detector`.\n*\n* All usage should be migrated to `Matter.Detector` or another alternative.\n* For back-compatibility purposes this module will remain for a short term and then later removed in a future release.\n*\n* The `Matter.Grid` module contains methods for creating and manipulating collision broadphase grid structures.\n*\n* @class Grid\n* @deprecated\n*/\n\nvar Grid = {};\n\nmodule.exports = Grid;\n\nvar Pair = __webpack_require__(9);\nvar Common = __webpack_require__(0);\nvar deprecated = Common.deprecated;\n\n(function() {\n\n    /**\n     * Creates a new grid.\n     * @deprecated replaced by Matter.Detector\n     * @method create\n     * @param {} options\n     * @return {grid} A new grid\n     */\n    Grid.create = function(options) {\n        var defaults = {\n            buckets: {},\n            pairs: {},\n            pairsList: [],\n            bucketWidth: 48,\n            bucketHeight: 48\n        };\n\n        return Common.extend(defaults, options);\n    };\n\n    /**\n     * The width of a single grid bucket.\n     *\n     * @property bucketWidth\n     * @type number\n     * @default 48\n     */\n\n    /**\n     * The height of a single grid bucket.\n     *\n     * @property bucketHeight\n     * @type number\n     * @default 48\n     */\n\n    /**\n     * Updates the grid.\n     * @deprecated replaced by Matter.Detector\n     * @method update\n     * @param {grid} grid\n     * @param {body[]} bodies\n     * @param {engine} engine\n     * @param {boolean} forceUpdate\n     */\n    Grid.update = function(grid, bodies, engine, forceUpdate) {\n        var i, col, row,\n            world = engine.world,\n            buckets = grid.buckets,\n            bucket,\n            bucketId,\n            gridChanged = false;\n\n        for (i = 0; i < bodies.length; i++) {\n            var body = bodies[i];\n\n            if (body.isSleeping && !forceUpdate)\n                continue;\n\n            // temporary back compatibility bounds check\n            if (world.bounds && (body.bounds.max.x < world.bounds.min.x || body.bounds.min.x > world.bounds.max.x\n                || body.bounds.max.y < world.bounds.min.y || body.bounds.min.y > world.bounds.max.y))\n                continue;\n\n            var newRegion = Grid._getRegion(grid, body);\n\n            // if the body has changed grid region\n            if (!body.region || newRegion.id !== body.region.id || forceUpdate) {\n\n                if (!body.region || forceUpdate)\n                    body.region = newRegion;\n\n                var union = Grid._regionUnion(newRegion, body.region);\n\n                // update grid buckets affected by region change\n                // iterate over the union of both regions\n                for (col = union.startCol; col <= union.endCol; col++) {\n                    for (row = union.startRow; row <= union.endRow; row++) {\n                        bucketId = Grid._getBucketId(col, row);\n                        bucket = buckets[bucketId];\n\n                        var isInsideNewRegion = (col >= newRegion.startCol && col <= newRegion.endCol\n                                                && row >= newRegion.startRow && row <= newRegion.endRow);\n\n                        var isInsideOldRegion = (col >= body.region.startCol && col <= body.region.endCol\n                                                && row >= body.region.startRow && row <= body.region.endRow);\n\n                        // remove from old region buckets\n                        if (!isInsideNewRegion && isInsideOldRegion) {\n                            if (isInsideOldRegion) {\n                                if (bucket)\n                                    Grid._bucketRemoveBody(grid, bucket, body);\n                            }\n                        }\n\n                        // add to new region buckets\n                        if (body.region === newRegion || (isInsideNewRegion && !isInsideOldRegion) || forceUpdate) {\n                            if (!bucket)\n                                bucket = Grid._createBucket(buckets, bucketId);\n                            Grid._bucketAddBody(grid, bucket, body);\n                        }\n                    }\n                }\n\n                // set the new region\n                body.region = newRegion;\n\n                // flag changes so we can update pairs\n                gridChanged = true;\n            }\n        }\n\n        // update pairs list only if pairs changed (i.e. a body changed region)\n        if (gridChanged)\n            grid.pairsList = Grid._createActivePairsList(grid);\n    };\n\n    deprecated(Grid, 'update', 'Grid.update ➤ replaced by Matter.Detector');\n\n    /**\n     * Clears the grid.\n     * @deprecated replaced by Matter.Detector\n     * @method clear\n     * @param {grid} grid\n     */\n    Grid.clear = function(grid) {\n        grid.buckets = {};\n        grid.pairs = {};\n        grid.pairsList = [];\n    };\n\n    deprecated(Grid, 'clear', 'Grid.clear ➤ replaced by Matter.Detector');\n\n    /**\n     * Finds the union of two regions.\n     * @method _regionUnion\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} regionA\n     * @param {} regionB\n     * @return {} region\n     */\n    Grid._regionUnion = function(regionA, regionB) {\n        var startCol = Math.min(regionA.startCol, regionB.startCol),\n            endCol = Math.max(regionA.endCol, regionB.endCol),\n            startRow = Math.min(regionA.startRow, regionB.startRow),\n            endRow = Math.max(regionA.endRow, regionB.endRow);\n\n        return Grid._createRegion(startCol, endCol, startRow, endRow);\n    };\n\n    /**\n     * Gets the region a given body falls in for a given grid.\n     * @method _getRegion\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} grid\n     * @param {} body\n     * @return {} region\n     */\n    Grid._getRegion = function(grid, body) {\n        var bounds = body.bounds,\n            startCol = Math.floor(bounds.min.x / grid.bucketWidth),\n            endCol = Math.floor(bounds.max.x / grid.bucketWidth),\n            startRow = Math.floor(bounds.min.y / grid.bucketHeight),\n            endRow = Math.floor(bounds.max.y / grid.bucketHeight);\n\n        return Grid._createRegion(startCol, endCol, startRow, endRow);\n    };\n\n    /**\n     * Creates a region.\n     * @method _createRegion\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} startCol\n     * @param {} endCol\n     * @param {} startRow\n     * @param {} endRow\n     * @return {} region\n     */\n    Grid._createRegion = function(startCol, endCol, startRow, endRow) {\n        return { \n            id: startCol + ',' + endCol + ',' + startRow + ',' + endRow,\n            startCol: startCol, \n            endCol: endCol, \n            startRow: startRow, \n            endRow: endRow \n        };\n    };\n\n    /**\n     * Gets the bucket id at the given position.\n     * @method _getBucketId\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} column\n     * @param {} row\n     * @return {string} bucket id\n     */\n    Grid._getBucketId = function(column, row) {\n        return 'C' + column + 'R' + row;\n    };\n\n    /**\n     * Creates a bucket.\n     * @method _createBucket\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} buckets\n     * @param {} bucketId\n     * @return {} bucket\n     */\n    Grid._createBucket = function(buckets, bucketId) {\n        var bucket = buckets[bucketId] = [];\n        return bucket;\n    };\n\n    /**\n     * Adds a body to a bucket.\n     * @method _bucketAddBody\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} grid\n     * @param {} bucket\n     * @param {} body\n     */\n    Grid._bucketAddBody = function(grid, bucket, body) {\n        var gridPairs = grid.pairs,\n            pairId = Pair.id,\n            bucketLength = bucket.length,\n            i;\n\n        // add new pairs\n        for (i = 0; i < bucketLength; i++) {\n            var bodyB = bucket[i];\n\n            if (body.id === bodyB.id || (body.isStatic && bodyB.isStatic))\n                continue;\n\n            // keep track of the number of buckets the pair exists in\n            // important for Grid.update to work\n            var id = pairId(body, bodyB),\n                pair = gridPairs[id];\n\n            if (pair) {\n                pair[2] += 1;\n            } else {\n                gridPairs[id] = [body, bodyB, 1];\n            }\n        }\n\n        // add to bodies (after pairs, otherwise pairs with self)\n        bucket.push(body);\n    };\n\n    /**\n     * Removes a body from a bucket.\n     * @method _bucketRemoveBody\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} grid\n     * @param {} bucket\n     * @param {} body\n     */\n    Grid._bucketRemoveBody = function(grid, bucket, body) {\n        var gridPairs = grid.pairs,\n            pairId = Pair.id,\n            i;\n\n        // remove from bucket\n        bucket.splice(Common.indexOf(bucket, body), 1);\n\n        var bucketLength = bucket.length;\n\n        // update pair counts\n        for (i = 0; i < bucketLength; i++) {\n            // keep track of the number of buckets the pair exists in\n            // important for _createActivePairsList to work\n            var pair = gridPairs[pairId(body, bucket[i])];\n\n            if (pair)\n                pair[2] -= 1;\n        }\n    };\n\n    /**\n     * Generates a list of the active pairs in the grid.\n     * @method _createActivePairsList\n     * @deprecated replaced by Matter.Detector\n     * @private\n     * @param {} grid\n     * @return [] pairs\n     */\n    Grid._createActivePairsList = function(grid) {\n        var pair,\n            gridPairs = grid.pairs,\n            pairKeys = Common.keys(gridPairs),\n            pairKeysLength = pairKeys.length,\n            pairs = [],\n            k;\n\n        // iterate over grid.pairs\n        for (k = 0; k < pairKeysLength; k++) {\n            pair = gridPairs[pairKeys[k]];\n\n            // if pair exists in at least one bucket\n            // it is a pair that needs further collision testing so push it\n            if (pair[2] > 0) {\n                pairs.push(pair);\n            } else {\n                delete gridPairs[pairKeys[k]];\n            }\n        }\n\n        return pairs;\n    };\n    \n})();\n\n\n/***/ }),\n/* 24 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.MouseConstraint` module contains methods for creating mouse constraints.\n* Mouse constraints are used for allowing user interaction, providing the ability to move bodies via the mouse or touch.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class MouseConstraint\n*/\n\nvar MouseConstraint = {};\n\nmodule.exports = MouseConstraint;\n\nvar Vertices = __webpack_require__(3);\nvar Sleeping = __webpack_require__(7);\nvar Mouse = __webpack_require__(14);\nvar Events = __webpack_require__(5);\nvar Detector = __webpack_require__(13);\nvar Constraint = __webpack_require__(10);\nvar Composite = __webpack_require__(6);\nvar Common = __webpack_require__(0);\nvar Bounds = __webpack_require__(1);\n\n(function() {\n\n    /**\n     * Creates a new mouse constraint.\n     * All properties have default values, and many are pre-calculated automatically based on other properties.\n     * See the properties section below for detailed information on what you can pass via the `options` object.\n     * @method create\n     * @param {engine} engine\n     * @param {} options\n     * @return {MouseConstraint} A new MouseConstraint\n     */\n    MouseConstraint.create = function(engine, options) {\n        var mouse = (engine ? engine.mouse : null) || (options ? options.mouse : null);\n\n        if (!mouse) {\n            if (engine && engine.render && engine.render.canvas) {\n                mouse = Mouse.create(engine.render.canvas);\n            } else if (options && options.element) {\n                mouse = Mouse.create(options.element);\n            } else {\n                mouse = Mouse.create();\n                Common.warn('MouseConstraint.create: options.mouse was undefined, options.element was undefined, may not function as expected');\n            }\n        }\n\n        var constraint = Constraint.create({ \n            label: 'Mouse Constraint',\n            pointA: mouse.position,\n            pointB: { x: 0, y: 0 },\n            length: 0.01, \n            stiffness: 0.1,\n            angularStiffness: 1,\n            render: {\n                strokeStyle: '#90EE90',\n                lineWidth: 3\n            }\n        });\n\n        var defaults = {\n            type: 'mouseConstraint',\n            mouse: mouse,\n            element: null,\n            body: null,\n            constraint: constraint,\n            collisionFilter: {\n                category: 0x0001,\n                mask: 0xFFFFFFFF,\n                group: 0\n            }\n        };\n\n        var mouseConstraint = Common.extend(defaults, options);\n\n        Events.on(engine, 'beforeUpdate', function() {\n            var allBodies = Composite.allBodies(engine.world);\n            MouseConstraint.update(mouseConstraint, allBodies);\n            MouseConstraint._triggerEvents(mouseConstraint);\n        });\n\n        return mouseConstraint;\n    };\n\n    /**\n     * Updates the given mouse constraint.\n     * @private\n     * @method update\n     * @param {MouseConstraint} mouseConstraint\n     * @param {body[]} bodies\n     */\n    MouseConstraint.update = function(mouseConstraint, bodies) {\n        var mouse = mouseConstraint.mouse,\n            constraint = mouseConstraint.constraint,\n            body = mouseConstraint.body;\n\n        if (mouse.button === 0) {\n            if (!constraint.bodyB) {\n                for (var i = 0; i < bodies.length; i++) {\n                    body = bodies[i];\n                    if (Bounds.contains(body.bounds, mouse.position) \n                            && Detector.canCollide(body.collisionFilter, mouseConstraint.collisionFilter)) {\n                        for (var j = body.parts.length > 1 ? 1 : 0; j < body.parts.length; j++) {\n                            var part = body.parts[j];\n                            if (Vertices.contains(part.vertices, mouse.position)) {\n                                constraint.pointA = mouse.position;\n                                constraint.bodyB = mouseConstraint.body = body;\n                                constraint.pointB = { x: mouse.position.x - body.position.x, y: mouse.position.y - body.position.y };\n                                constraint.angleB = body.angle;\n\n                                Sleeping.set(body, false);\n                                Events.trigger(mouseConstraint, 'startdrag', { mouse: mouse, body: body });\n\n                                break;\n                            }\n                        }\n                    }\n                }\n            } else {\n                Sleeping.set(constraint.bodyB, false);\n                constraint.pointA = mouse.position;\n            }\n        } else {\n            constraint.bodyB = mouseConstraint.body = null;\n            constraint.pointB = null;\n\n            if (body)\n                Events.trigger(mouseConstraint, 'enddrag', { mouse: mouse, body: body });\n        }\n    };\n\n    /**\n     * Triggers mouse constraint events.\n     * @method _triggerEvents\n     * @private\n     * @param {mouse} mouseConstraint\n     */\n    MouseConstraint._triggerEvents = function(mouseConstraint) {\n        var mouse = mouseConstraint.mouse,\n            mouseEvents = mouse.sourceEvents;\n\n        if (mouseEvents.mousemove)\n            Events.trigger(mouseConstraint, 'mousemove', { mouse: mouse });\n\n        if (mouseEvents.mousedown)\n            Events.trigger(mouseConstraint, 'mousedown', { mouse: mouse });\n\n        if (mouseEvents.mouseup)\n            Events.trigger(mouseConstraint, 'mouseup', { mouse: mouse });\n\n        // reset the mouse state ready for the next step\n        Mouse.clearSourceEvents(mouse);\n    };\n\n    /*\n    *\n    *  Events Documentation\n    *\n    */\n\n    /**\n    * Fired when the mouse has moved (or a touch moves) during the last step\n    *\n    * @event mousemove\n    * @param {} event An event object\n    * @param {mouse} event.mouse The engine's mouse instance\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when the mouse is down (or a touch has started) during the last step\n    *\n    * @event mousedown\n    * @param {} event An event object\n    * @param {mouse} event.mouse The engine's mouse instance\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when the mouse is up (or a touch has ended) during the last step\n    *\n    * @event mouseup\n    * @param {} event An event object\n    * @param {mouse} event.mouse The engine's mouse instance\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when the user starts dragging a body\n    *\n    * @event startdrag\n    * @param {} event An event object\n    * @param {mouse} event.mouse The engine's mouse instance\n    * @param {body} event.body The body being dragged\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired when the user ends dragging a body\n    *\n    * @event enddrag\n    * @param {} event An event object\n    * @param {mouse} event.mouse The engine's mouse instance\n    * @param {body} event.body The body that has stopped being dragged\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * A `String` denoting the type of object.\n     *\n     * @property type\n     * @type string\n     * @default \"constraint\"\n     * @readOnly\n     */\n\n    /**\n     * The `Mouse` instance in use. If not supplied in `MouseConstraint.create`, one will be created.\n     *\n     * @property mouse\n     * @type mouse\n     * @default mouse\n     */\n\n    /**\n     * The `Body` that is currently being moved by the user, or `null` if no body.\n     *\n     * @property body\n     * @type body\n     * @default null\n     */\n\n    /**\n     * The `Constraint` object that is used to move the body during interaction.\n     *\n     * @property constraint\n     * @type constraint\n     */\n\n    /**\n     * An `Object` that specifies the collision filter properties.\n     * The collision filter allows the user to define which types of body this mouse constraint can interact with.\n     * See `body.collisionFilter` for more information.\n     *\n     * @property collisionFilter\n     * @type object\n     */\n\n})();\n\n\n/***/ }),\n/* 25 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Query` module contains methods for performing collision queries.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Query\n*/\n\nvar Query = {};\n\nmodule.exports = Query;\n\nvar Vector = __webpack_require__(2);\nvar Collision = __webpack_require__(8);\nvar Bounds = __webpack_require__(1);\nvar Bodies = __webpack_require__(12);\nvar Vertices = __webpack_require__(3);\n\n(function() {\n\n    /**\n     * Returns a list of collisions between `body` and `bodies`.\n     * @method collides\n     * @param {body} body\n     * @param {body[]} bodies\n     * @return {collision[]} Collisions\n     */\n    Query.collides = function(body, bodies) {\n        var collisions = [],\n            bodiesLength = bodies.length,\n            bounds = body.bounds,\n            collides = Collision.collides,\n            overlaps = Bounds.overlaps;\n\n        for (var i = 0; i < bodiesLength; i++) {\n            var bodyA = bodies[i],\n                partsALength = bodyA.parts.length,\n                partsAStart = partsALength === 1 ? 0 : 1;\n            \n            if (overlaps(bodyA.bounds, bounds)) {\n                for (var j = partsAStart; j < partsALength; j++) {\n                    var part = bodyA.parts[j];\n\n                    if (overlaps(part.bounds, bounds)) {\n                        var collision = collides(part, body);\n\n                        if (collision) {\n                            collisions.push(collision);\n                            break;\n                        }\n                    }\n                }\n            }\n        }\n\n        return collisions;\n    };\n\n    /**\n     * Casts a ray segment against a set of bodies and returns all collisions, ray width is optional. Intersection points are not provided.\n     * @method ray\n     * @param {body[]} bodies\n     * @param {vector} startPoint\n     * @param {vector} endPoint\n     * @param {number} [rayWidth]\n     * @return {collision[]} Collisions\n     */\n    Query.ray = function(bodies, startPoint, endPoint, rayWidth) {\n        rayWidth = rayWidth || 1e-100;\n\n        var rayAngle = Vector.angle(startPoint, endPoint),\n            rayLength = Vector.magnitude(Vector.sub(startPoint, endPoint)),\n            rayX = (endPoint.x + startPoint.x) * 0.5,\n            rayY = (endPoint.y + startPoint.y) * 0.5,\n            ray = Bodies.rectangle(rayX, rayY, rayLength, rayWidth, { angle: rayAngle }),\n            collisions = Query.collides(ray, bodies);\n\n        for (var i = 0; i < collisions.length; i += 1) {\n            var collision = collisions[i];\n            collision.body = collision.bodyB = collision.bodyA;            \n        }\n\n        return collisions;\n    };\n\n    /**\n     * Returns all bodies whose bounds are inside (or outside if set) the given set of bounds, from the given set of bodies.\n     * @method region\n     * @param {body[]} bodies\n     * @param {bounds} bounds\n     * @param {bool} [outside=false]\n     * @return {body[]} The bodies matching the query\n     */\n    Query.region = function(bodies, bounds, outside) {\n        var result = [];\n\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i],\n                overlaps = Bounds.overlaps(body.bounds, bounds);\n            if ((overlaps && !outside) || (!overlaps && outside))\n                result.push(body);\n        }\n\n        return result;\n    };\n\n    /**\n     * Returns all bodies whose vertices contain the given point, from the given set of bodies.\n     * @method point\n     * @param {body[]} bodies\n     * @param {vector} point\n     * @return {body[]} The bodies matching the query\n     */\n    Query.point = function(bodies, point) {\n        var result = [];\n\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i];\n            \n            if (Bounds.contains(body.bounds, point)) {\n                for (var j = body.parts.length === 1 ? 0 : 1; j < body.parts.length; j++) {\n                    var part = body.parts[j];\n\n                    if (Bounds.contains(part.bounds, point)\n                        && Vertices.contains(part.vertices, point)) {\n                        result.push(body);\n                        break;\n                    }\n                }\n            }\n        }\n\n        return result;\n    };\n\n})();\n\n\n/***/ }),\n/* 26 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Render` module is a simple canvas based renderer for visualising instances of `Matter.Engine`.\n* It is intended for development and debugging purposes, but may also be suitable for simple games.\n* It includes a number of drawing options including wireframe, vector with support for sprites and viewports.\n*\n* @class Render\n*/\n\nvar Render = {};\n\nmodule.exports = Render;\n\nvar Body = __webpack_require__(4);\nvar Common = __webpack_require__(0);\nvar Composite = __webpack_require__(6);\nvar Bounds = __webpack_require__(1);\nvar Events = __webpack_require__(5);\nvar Vector = __webpack_require__(2);\nvar Mouse = __webpack_require__(14);\n\n(function() {\n\n    var _requestAnimationFrame,\n        _cancelAnimationFrame;\n\n    if (typeof window !== 'undefined') {\n        _requestAnimationFrame = window.requestAnimationFrame || window.webkitRequestAnimationFrame\n                                      || window.mozRequestAnimationFrame || window.msRequestAnimationFrame\n                                      || function(callback){ window.setTimeout(function() { callback(Common.now()); }, 1000 / 60); };\n\n        _cancelAnimationFrame = window.cancelAnimationFrame || window.mozCancelAnimationFrame\n                                      || window.webkitCancelAnimationFrame || window.msCancelAnimationFrame;\n    }\n\n    Render._goodFps = 30;\n    Render._goodDelta = 1000 / 60;\n\n    /**\n     * Creates a new renderer. The options parameter is an object that specifies any properties you wish to override the defaults.\n     * All properties have default values, and many are pre-calculated automatically based on other properties.\n     * See the properties section below for detailed information on what you can pass via the `options` object.\n     * @method create\n     * @param {object} [options]\n     * @return {render} A new renderer\n     */\n    Render.create = function(options) {\n        var defaults = {\n            engine: null,\n            element: null,\n            canvas: null,\n            mouse: null,\n            frameRequestId: null,\n            timing: {\n                historySize: 60,\n                delta: 0,\n                deltaHistory: [],\n                lastTime: 0,\n                lastTimestamp: 0,\n                lastElapsed: 0,\n                timestampElapsed: 0,\n                timestampElapsedHistory: [],\n                engineDeltaHistory: [],\n                engineElapsedHistory: [],\n                elapsedHistory: []\n            },\n            options: {\n                width: 800,\n                height: 600,\n                pixelRatio: 1,\n                background: '#14151f',\n                wireframeBackground: '#14151f',\n                hasBounds: !!options.bounds,\n                enabled: true,\n                wireframes: true,\n                showSleeping: true,\n                showDebug: false,\n                showStats: false,\n                showPerformance: false,\n                showBounds: false,\n                showVelocity: false,\n                showCollisions: false,\n                showSeparations: false,\n                showAxes: false,\n                showPositions: false,\n                showAngleIndicator: false,\n                showIds: false,\n                showVertexNumbers: false,\n                showConvexHulls: false,\n                showInternalEdges: false,\n                showMousePosition: false\n            }\n        };\n\n        var render = Common.extend(defaults, options);\n\n        if (render.canvas) {\n            render.canvas.width = render.options.width || render.canvas.width;\n            render.canvas.height = render.options.height || render.canvas.height;\n        }\n\n        render.mouse = options.mouse;\n        render.engine = options.engine;\n        render.canvas = render.canvas || _createCanvas(render.options.width, render.options.height);\n        render.context = render.canvas.getContext('2d');\n        render.textures = {};\n\n        render.bounds = render.bounds || {\n            min: {\n                x: 0,\n                y: 0\n            },\n            max: {\n                x: render.canvas.width,\n                y: render.canvas.height\n            }\n        };\n\n        // for temporary back compatibility only\n        render.controller = Render;\n        render.options.showBroadphase = false;\n\n        if (render.options.pixelRatio !== 1) {\n            Render.setPixelRatio(render, render.options.pixelRatio);\n        }\n\n        if (Common.isElement(render.element)) {\n            render.element.appendChild(render.canvas);\n        }\n\n        return render;\n    };\n\n    /**\n     * Continuously updates the render canvas on the `requestAnimationFrame` event.\n     * @method run\n     * @param {render} render\n     */\n    Render.run = function(render) {\n        (function loop(time){\n            render.frameRequestId = _requestAnimationFrame(loop);\n            \n            _updateTiming(render, time);\n\n            Render.world(render, time);\n\n            if (render.options.showStats || render.options.showDebug) {\n                Render.stats(render, render.context, time);\n            }\n\n            if (render.options.showPerformance || render.options.showDebug) {\n                Render.performance(render, render.context, time);\n            }\n        })();\n    };\n\n    /**\n     * Ends execution of `Render.run` on the given `render`, by canceling the animation frame request event loop.\n     * @method stop\n     * @param {render} render\n     */\n    Render.stop = function(render) {\n        _cancelAnimationFrame(render.frameRequestId);\n    };\n\n    /**\n     * Sets the pixel ratio of the renderer and updates the canvas.\n     * To automatically detect the correct ratio, pass the string `'auto'` for `pixelRatio`.\n     * @method setPixelRatio\n     * @param {render} render\n     * @param {number} pixelRatio\n     */\n    Render.setPixelRatio = function(render, pixelRatio) {\n        var options = render.options,\n            canvas = render.canvas;\n\n        if (pixelRatio === 'auto') {\n            pixelRatio = _getPixelRatio(canvas);\n        }\n\n        options.pixelRatio = pixelRatio;\n        canvas.setAttribute('data-pixel-ratio', pixelRatio);\n        canvas.width = options.width * pixelRatio;\n        canvas.height = options.height * pixelRatio;\n        canvas.style.width = options.width + 'px';\n        canvas.style.height = options.height + 'px';\n    };\n\n    /**\n     * Positions and sizes the viewport around the given object bounds.\n     * Objects must have at least one of the following properties:\n     * - `object.bounds`\n     * - `object.position`\n     * - `object.min` and `object.max`\n     * - `object.x` and `object.y`\n     * @method lookAt\n     * @param {render} render\n     * @param {object[]} objects\n     * @param {vector} [padding]\n     * @param {bool} [center=true]\n     */\n    Render.lookAt = function(render, objects, padding, center) {\n        center = typeof center !== 'undefined' ? center : true;\n        objects = Common.isArray(objects) ? objects : [objects];\n        padding = padding || {\n            x: 0,\n            y: 0\n        };\n\n        // find bounds of all objects\n        var bounds = {\n            min: { x: Infinity, y: Infinity },\n            max: { x: -Infinity, y: -Infinity }\n        };\n\n        for (var i = 0; i < objects.length; i += 1) {\n            var object = objects[i],\n                min = object.bounds ? object.bounds.min : (object.min || object.position || object),\n                max = object.bounds ? object.bounds.max : (object.max || object.position || object);\n\n            if (min && max) {\n                if (min.x < bounds.min.x)\n                    bounds.min.x = min.x;\n\n                if (max.x > bounds.max.x)\n                    bounds.max.x = max.x;\n\n                if (min.y < bounds.min.y)\n                    bounds.min.y = min.y;\n\n                if (max.y > bounds.max.y)\n                    bounds.max.y = max.y;\n            }\n        }\n\n        // find ratios\n        var width = (bounds.max.x - bounds.min.x) + 2 * padding.x,\n            height = (bounds.max.y - bounds.min.y) + 2 * padding.y,\n            viewHeight = render.canvas.height,\n            viewWidth = render.canvas.width,\n            outerRatio = viewWidth / viewHeight,\n            innerRatio = width / height,\n            scaleX = 1,\n            scaleY = 1;\n\n        // find scale factor\n        if (innerRatio > outerRatio) {\n            scaleY = innerRatio / outerRatio;\n        } else {\n            scaleX = outerRatio / innerRatio;\n        }\n\n        // enable bounds\n        render.options.hasBounds = true;\n\n        // position and size\n        render.bounds.min.x = bounds.min.x;\n        render.bounds.max.x = bounds.min.x + width * scaleX;\n        render.bounds.min.y = bounds.min.y;\n        render.bounds.max.y = bounds.min.y + height * scaleY;\n\n        // center\n        if (center) {\n            render.bounds.min.x += width * 0.5 - (width * scaleX) * 0.5;\n            render.bounds.max.x += width * 0.5 - (width * scaleX) * 0.5;\n            render.bounds.min.y += height * 0.5 - (height * scaleY) * 0.5;\n            render.bounds.max.y += height * 0.5 - (height * scaleY) * 0.5;\n        }\n\n        // padding\n        render.bounds.min.x -= padding.x;\n        render.bounds.max.x -= padding.x;\n        render.bounds.min.y -= padding.y;\n        render.bounds.max.y -= padding.y;\n\n        // update mouse\n        if (render.mouse) {\n            Mouse.setScale(render.mouse, {\n                x: (render.bounds.max.x - render.bounds.min.x) / render.canvas.width,\n                y: (render.bounds.max.y - render.bounds.min.y) / render.canvas.height\n            });\n\n            Mouse.setOffset(render.mouse, render.bounds.min);\n        }\n    };\n\n    /**\n     * Applies viewport transforms based on `render.bounds` to a render context.\n     * @method startViewTransform\n     * @param {render} render\n     */\n    Render.startViewTransform = function(render) {\n        var boundsWidth = render.bounds.max.x - render.bounds.min.x,\n            boundsHeight = render.bounds.max.y - render.bounds.min.y,\n            boundsScaleX = boundsWidth / render.options.width,\n            boundsScaleY = boundsHeight / render.options.height;\n\n        render.context.setTransform(\n            render.options.pixelRatio / boundsScaleX, 0, 0, \n            render.options.pixelRatio / boundsScaleY, 0, 0\n        );\n        \n        render.context.translate(-render.bounds.min.x, -render.bounds.min.y);\n    };\n\n    /**\n     * Resets all transforms on the render context.\n     * @method endViewTransform\n     * @param {render} render\n     */\n    Render.endViewTransform = function(render) {\n        render.context.setTransform(render.options.pixelRatio, 0, 0, render.options.pixelRatio, 0, 0);\n    };\n\n    /**\n     * Renders the given `engine`'s `Matter.World` object.\n     * This is the entry point for all rendering and should be called every time the scene changes.\n     * @method world\n     * @param {render} render\n     */\n    Render.world = function(render, time) {\n        var startTime = Common.now(),\n            engine = render.engine,\n            world = engine.world,\n            canvas = render.canvas,\n            context = render.context,\n            options = render.options,\n            timing = render.timing;\n\n        var allBodies = Composite.allBodies(world),\n            allConstraints = Composite.allConstraints(world),\n            background = options.wireframes ? options.wireframeBackground : options.background,\n            bodies = [],\n            constraints = [],\n            i;\n\n        var event = {\n            timestamp: engine.timing.timestamp\n        };\n\n        Events.trigger(render, 'beforeRender', event);\n\n        // apply background if it has changed\n        if (render.currentBackground !== background)\n            _applyBackground(render, background);\n\n        // clear the canvas with a transparent fill, to allow the canvas background to show\n        context.globalCompositeOperation = 'source-in';\n        context.fillStyle = \"transparent\";\n        context.fillRect(0, 0, canvas.width, canvas.height);\n        context.globalCompositeOperation = 'source-over';\n\n        // handle bounds\n        if (options.hasBounds) {\n            // filter out bodies that are not in view\n            for (i = 0; i < allBodies.length; i++) {\n                var body = allBodies[i];\n                if (Bounds.overlaps(body.bounds, render.bounds))\n                    bodies.push(body);\n            }\n\n            // filter out constraints that are not in view\n            for (i = 0; i < allConstraints.length; i++) {\n                var constraint = allConstraints[i],\n                    bodyA = constraint.bodyA,\n                    bodyB = constraint.bodyB,\n                    pointAWorld = constraint.pointA,\n                    pointBWorld = constraint.pointB;\n\n                if (bodyA) pointAWorld = Vector.add(bodyA.position, constraint.pointA);\n                if (bodyB) pointBWorld = Vector.add(bodyB.position, constraint.pointB);\n\n                if (!pointAWorld || !pointBWorld)\n                    continue;\n\n                if (Bounds.contains(render.bounds, pointAWorld) || Bounds.contains(render.bounds, pointBWorld))\n                    constraints.push(constraint);\n            }\n\n            // transform the view\n            Render.startViewTransform(render);\n\n            // update mouse\n            if (render.mouse) {\n                Mouse.setScale(render.mouse, {\n                    x: (render.bounds.max.x - render.bounds.min.x) / render.options.width,\n                    y: (render.bounds.max.y - render.bounds.min.y) / render.options.height\n                });\n\n                Mouse.setOffset(render.mouse, render.bounds.min);\n            }\n        } else {\n            constraints = allConstraints;\n            bodies = allBodies;\n\n            if (render.options.pixelRatio !== 1) {\n                render.context.setTransform(render.options.pixelRatio, 0, 0, render.options.pixelRatio, 0, 0);\n            }\n        }\n\n        if (!options.wireframes || (engine.enableSleeping && options.showSleeping)) {\n            // fully featured rendering of bodies\n            Render.bodies(render, bodies, context);\n        } else {\n            if (options.showConvexHulls)\n                Render.bodyConvexHulls(render, bodies, context);\n\n            // optimised method for wireframes only\n            Render.bodyWireframes(render, bodies, context);\n        }\n\n        if (options.showBounds)\n            Render.bodyBounds(render, bodies, context);\n\n        if (options.showAxes || options.showAngleIndicator)\n            Render.bodyAxes(render, bodies, context);\n\n        if (options.showPositions)\n            Render.bodyPositions(render, bodies, context);\n\n        if (options.showVelocity)\n            Render.bodyVelocity(render, bodies, context);\n\n        if (options.showIds)\n            Render.bodyIds(render, bodies, context);\n\n        if (options.showSeparations)\n            Render.separations(render, engine.pairs.list, context);\n\n        if (options.showCollisions)\n            Render.collisions(render, engine.pairs.list, context);\n\n        if (options.showVertexNumbers)\n            Render.vertexNumbers(render, bodies, context);\n\n        if (options.showMousePosition)\n            Render.mousePosition(render, render.mouse, context);\n\n        Render.constraints(constraints, context);\n\n        if (options.hasBounds) {\n            // revert view transforms\n            Render.endViewTransform(render);\n        }\n\n        Events.trigger(render, 'afterRender', event);\n\n        // log the time elapsed computing this update\n        timing.lastElapsed = Common.now() - startTime;\n    };\n\n    /**\n     * Renders statistics about the engine and world useful for debugging.\n     * @private\n     * @method stats\n     * @param {render} render\n     * @param {RenderingContext} context\n     * @param {Number} time\n     */\n    Render.stats = function(render, context, time) {\n        var engine = render.engine,\n            world = engine.world,\n            bodies = Composite.allBodies(world),\n            parts = 0,\n            width = 55,\n            height = 44,\n            x = 0,\n            y = 0;\n        \n        // count parts\n        for (var i = 0; i < bodies.length; i += 1) {\n            parts += bodies[i].parts.length;\n        }\n\n        // sections\n        var sections = {\n            'Part': parts,\n            'Body': bodies.length,\n            'Cons': Composite.allConstraints(world).length,\n            'Comp': Composite.allComposites(world).length,\n            'Pair': engine.pairs.list.length\n        };\n\n        // background\n        context.fillStyle = '#0e0f19';\n        context.fillRect(x, y, width * 5.5, height);\n\n        context.font = '12px Arial';\n        context.textBaseline = 'top';\n        context.textAlign = 'right';\n\n        // sections\n        for (var key in sections) {\n            var section = sections[key];\n            // label\n            context.fillStyle = '#aaa';\n            context.fillText(key, x + width, y + 8);\n\n            // value\n            context.fillStyle = '#eee';\n            context.fillText(section, x + width, y + 26);\n\n            x += width;\n        }\n    };\n\n    /**\n     * Renders engine and render performance information.\n     * @private\n     * @method performance\n     * @param {render} render\n     * @param {RenderingContext} context\n     */\n    Render.performance = function(render, context) {\n        var engine = render.engine,\n            timing = render.timing,\n            deltaHistory = timing.deltaHistory,\n            elapsedHistory = timing.elapsedHistory,\n            timestampElapsedHistory = timing.timestampElapsedHistory,\n            engineDeltaHistory = timing.engineDeltaHistory,\n            engineElapsedHistory = timing.engineElapsedHistory,\n            lastEngineDelta = engine.timing.lastDelta;\n        \n        var deltaMean = _mean(deltaHistory),\n            elapsedMean = _mean(elapsedHistory),\n            engineDeltaMean = _mean(engineDeltaHistory),\n            engineElapsedMean = _mean(engineElapsedHistory),\n            timestampElapsedMean = _mean(timestampElapsedHistory),\n            rateMean = (timestampElapsedMean / deltaMean) || 0,\n            fps = (1000 / deltaMean) || 0;\n\n        var graphHeight = 4,\n            gap = 12,\n            width = 60,\n            height = 34,\n            x = 10,\n            y = 69;\n\n        // background\n        context.fillStyle = '#0e0f19';\n        context.fillRect(0, 50, gap * 4 + width * 5 + 22, height);\n\n        // show FPS\n        Render.status(\n            context, x, y, width, graphHeight, deltaHistory.length, \n            Math.round(fps) + ' fps', \n            fps / Render._goodFps,\n            function(i) { return (deltaHistory[i] / deltaMean) - 1; }\n        );\n\n        // show engine delta\n        Render.status(\n            context, x + gap + width, y, width, graphHeight, engineDeltaHistory.length,\n            lastEngineDelta.toFixed(2) + ' dt', \n            Render._goodDelta / lastEngineDelta,\n            function(i) { return (engineDeltaHistory[i] / engineDeltaMean) - 1; }\n        );\n\n        // show engine update time\n        Render.status(\n            context, x + (gap + width) * 2, y, width, graphHeight, engineElapsedHistory.length,\n            engineElapsedMean.toFixed(2) + ' ut', \n            1 - (engineElapsedMean / Render._goodFps),\n            function(i) { return (engineElapsedHistory[i] / engineElapsedMean) - 1; }\n        );\n\n        // show render time\n        Render.status(\n            context, x + (gap + width) * 3, y, width, graphHeight, elapsedHistory.length,\n            elapsedMean.toFixed(2) + ' rt', \n            1 - (elapsedMean / Render._goodFps),\n            function(i) { return (elapsedHistory[i] / elapsedMean) - 1; }\n        );\n\n        // show effective speed\n        Render.status(\n            context, x + (gap + width) * 4, y, width, graphHeight, timestampElapsedHistory.length, \n            rateMean.toFixed(2) + ' x', \n            rateMean * rateMean * rateMean,\n            function(i) { return (((timestampElapsedHistory[i] / deltaHistory[i]) / rateMean) || 0) - 1; }\n        );\n    };\n\n    /**\n     * Renders a label, indicator and a chart.\n     * @private\n     * @method status\n     * @param {RenderingContext} context\n     * @param {number} x\n     * @param {number} y\n     * @param {number} width\n     * @param {number} height\n     * @param {number} count\n     * @param {string} label\n     * @param {string} indicator\n     * @param {function} plotY\n     */\n    Render.status = function(context, x, y, width, height, count, label, indicator, plotY) {\n        // background\n        context.strokeStyle = '#888';\n        context.fillStyle = '#444';\n        context.lineWidth = 1;\n        context.fillRect(x, y + 7, width, 1);\n\n        // chart\n        context.beginPath();\n        context.moveTo(x, y + 7 - height * Common.clamp(0.4 * plotY(0), -2, 2));\n        for (var i = 0; i < width; i += 1) {\n            context.lineTo(x + i, y + 7 - (i < count ? height * Common.clamp(0.4 * plotY(i), -2, 2) : 0));\n        }\n        context.stroke();\n\n        // indicator\n        context.fillStyle = 'hsl(' + Common.clamp(25 + 95 * indicator, 0, 120) + ',100%,60%)';\n        context.fillRect(x, y - 7, 4, 4);\n\n        // label\n        context.font = '12px Arial';\n        context.textBaseline = 'middle';\n        context.textAlign = 'right';\n        context.fillStyle = '#eee';\n        context.fillText(label, x + width, y - 5);\n    };\n\n    /**\n     * Description\n     * @private\n     * @method constraints\n     * @param {constraint[]} constraints\n     * @param {RenderingContext} context\n     */\n    Render.constraints = function(constraints, context) {\n        var c = context;\n\n        for (var i = 0; i < constraints.length; i++) {\n            var constraint = constraints[i];\n\n            if (!constraint.render.visible || !constraint.pointA || !constraint.pointB)\n                continue;\n\n            var bodyA = constraint.bodyA,\n                bodyB = constraint.bodyB,\n                start,\n                end;\n\n            if (bodyA) {\n                start = Vector.add(bodyA.position, constraint.pointA);\n            } else {\n                start = constraint.pointA;\n            }\n\n            if (constraint.render.type === 'pin') {\n                c.beginPath();\n                c.arc(start.x, start.y, 3, 0, 2 * Math.PI);\n                c.closePath();\n            } else {\n                if (bodyB) {\n                    end = Vector.add(bodyB.position, constraint.pointB);\n                } else {\n                    end = constraint.pointB;\n                }\n\n                c.beginPath();\n                c.moveTo(start.x, start.y);\n\n                if (constraint.render.type === 'spring') {\n                    var delta = Vector.sub(end, start),\n                        normal = Vector.perp(Vector.normalise(delta)),\n                        coils = Math.ceil(Common.clamp(constraint.length / 5, 12, 20)),\n                        offset;\n\n                    for (var j = 1; j < coils; j += 1) {\n                        offset = j % 2 === 0 ? 1 : -1;\n\n                        c.lineTo(\n                            start.x + delta.x * (j / coils) + normal.x * offset * 4,\n                            start.y + delta.y * (j / coils) + normal.y * offset * 4\n                        );\n                    }\n                }\n\n                c.lineTo(end.x, end.y);\n            }\n\n            if (constraint.render.lineWidth) {\n                c.lineWidth = constraint.render.lineWidth;\n                c.strokeStyle = constraint.render.strokeStyle;\n                c.stroke();\n            }\n\n            if (constraint.render.anchors) {\n                c.fillStyle = constraint.render.strokeStyle;\n                c.beginPath();\n                c.arc(start.x, start.y, 3, 0, 2 * Math.PI);\n                c.arc(end.x, end.y, 3, 0, 2 * Math.PI);\n                c.closePath();\n                c.fill();\n            }\n        }\n    };\n\n    /**\n     * Description\n     * @private\n     * @method bodies\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodies = function(render, bodies, context) {\n        var c = context,\n            engine = render.engine,\n            options = render.options,\n            showInternalEdges = options.showInternalEdges || !options.wireframes,\n            body,\n            part,\n            i,\n            k;\n\n        for (i = 0; i < bodies.length; i++) {\n            body = bodies[i];\n\n            if (!body.render.visible)\n                continue;\n\n            // handle compound parts\n            for (k = body.parts.length > 1 ? 1 : 0; k < body.parts.length; k++) {\n                part = body.parts[k];\n\n                if (!part.render.visible)\n                    continue;\n\n                if (options.showSleeping && body.isSleeping) {\n                    c.globalAlpha = 0.5 * part.render.opacity;\n                } else if (part.render.opacity !== 1) {\n                    c.globalAlpha = part.render.opacity;\n                }\n\n                if (part.render.sprite && part.render.sprite.texture && !options.wireframes) {\n                    // part sprite\n                    var sprite = part.render.sprite,\n                        texture = _getTexture(render, sprite.texture);\n\n                    c.translate(part.position.x, part.position.y);\n                    c.rotate(part.angle);\n\n                    c.drawImage(\n                        texture,\n                        texture.width * -sprite.xOffset * sprite.xScale,\n                        texture.height * -sprite.yOffset * sprite.yScale,\n                        texture.width * sprite.xScale,\n                        texture.height * sprite.yScale\n                    );\n\n                    // revert translation, hopefully faster than save / restore\n                    c.rotate(-part.angle);\n                    c.translate(-part.position.x, -part.position.y);\n                } else {\n                    // part polygon\n                    if (part.circleRadius) {\n                        c.beginPath();\n                        c.arc(part.position.x, part.position.y, part.circleRadius, 0, 2 * Math.PI);\n                    } else {\n                        c.beginPath();\n                        c.moveTo(part.vertices[0].x, part.vertices[0].y);\n\n                        for (var j = 1; j < part.vertices.length; j++) {\n                            if (!part.vertices[j - 1].isInternal || showInternalEdges) {\n                                c.lineTo(part.vertices[j].x, part.vertices[j].y);\n                            } else {\n                                c.moveTo(part.vertices[j].x, part.vertices[j].y);\n                            }\n\n                            if (part.vertices[j].isInternal && !showInternalEdges) {\n                                c.moveTo(part.vertices[(j + 1) % part.vertices.length].x, part.vertices[(j + 1) % part.vertices.length].y);\n                            }\n                        }\n\n                        c.lineTo(part.vertices[0].x, part.vertices[0].y);\n                        c.closePath();\n                    }\n\n                    if (!options.wireframes) {\n                        c.fillStyle = part.render.fillStyle;\n\n                        if (part.render.lineWidth) {\n                            c.lineWidth = part.render.lineWidth;\n                            c.strokeStyle = part.render.strokeStyle;\n                            c.stroke();\n                        }\n\n                        c.fill();\n                    } else {\n                        c.lineWidth = 1;\n                        c.strokeStyle = '#bbb';\n                        c.stroke();\n                    }\n                }\n\n                c.globalAlpha = 1;\n            }\n        }\n    };\n\n    /**\n     * Optimised method for drawing body wireframes in one pass\n     * @private\n     * @method bodyWireframes\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyWireframes = function(render, bodies, context) {\n        var c = context,\n            showInternalEdges = render.options.showInternalEdges,\n            body,\n            part,\n            i,\n            j,\n            k;\n\n        c.beginPath();\n\n        // render all bodies\n        for (i = 0; i < bodies.length; i++) {\n            body = bodies[i];\n\n            if (!body.render.visible)\n                continue;\n\n            // handle compound parts\n            for (k = body.parts.length > 1 ? 1 : 0; k < body.parts.length; k++) {\n                part = body.parts[k];\n\n                c.moveTo(part.vertices[0].x, part.vertices[0].y);\n\n                for (j = 1; j < part.vertices.length; j++) {\n                    if (!part.vertices[j - 1].isInternal || showInternalEdges) {\n                        c.lineTo(part.vertices[j].x, part.vertices[j].y);\n                    } else {\n                        c.moveTo(part.vertices[j].x, part.vertices[j].y);\n                    }\n\n                    if (part.vertices[j].isInternal && !showInternalEdges) {\n                        c.moveTo(part.vertices[(j + 1) % part.vertices.length].x, part.vertices[(j + 1) % part.vertices.length].y);\n                    }\n                }\n\n                c.lineTo(part.vertices[0].x, part.vertices[0].y);\n            }\n        }\n\n        c.lineWidth = 1;\n        c.strokeStyle = '#bbb';\n        c.stroke();\n    };\n\n    /**\n     * Optimised method for drawing body convex hull wireframes in one pass\n     * @private\n     * @method bodyConvexHulls\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyConvexHulls = function(render, bodies, context) {\n        var c = context,\n            body,\n            part,\n            i,\n            j,\n            k;\n\n        c.beginPath();\n\n        // render convex hulls\n        for (i = 0; i < bodies.length; i++) {\n            body = bodies[i];\n\n            if (!body.render.visible || body.parts.length === 1)\n                continue;\n\n            c.moveTo(body.vertices[0].x, body.vertices[0].y);\n\n            for (j = 1; j < body.vertices.length; j++) {\n                c.lineTo(body.vertices[j].x, body.vertices[j].y);\n            }\n\n            c.lineTo(body.vertices[0].x, body.vertices[0].y);\n        }\n\n        c.lineWidth = 1;\n        c.strokeStyle = 'rgba(255,255,255,0.2)';\n        c.stroke();\n    };\n\n    /**\n     * Renders body vertex numbers.\n     * @private\n     * @method vertexNumbers\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.vertexNumbers = function(render, bodies, context) {\n        var c = context,\n            i,\n            j,\n            k;\n\n        for (i = 0; i < bodies.length; i++) {\n            var parts = bodies[i].parts;\n            for (k = parts.length > 1 ? 1 : 0; k < parts.length; k++) {\n                var part = parts[k];\n                for (j = 0; j < part.vertices.length; j++) {\n                    c.fillStyle = 'rgba(255,255,255,0.2)';\n                    c.fillText(i + '_' + j, part.position.x + (part.vertices[j].x - part.position.x) * 0.8, part.position.y + (part.vertices[j].y - part.position.y) * 0.8);\n                }\n            }\n        }\n    };\n\n    /**\n     * Renders mouse position.\n     * @private\n     * @method mousePosition\n     * @param {render} render\n     * @param {mouse} mouse\n     * @param {RenderingContext} context\n     */\n    Render.mousePosition = function(render, mouse, context) {\n        var c = context;\n        c.fillStyle = 'rgba(255,255,255,0.8)';\n        c.fillText(mouse.position.x + '  ' + mouse.position.y, mouse.position.x + 5, mouse.position.y - 5);\n    };\n\n    /**\n     * Draws body bounds\n     * @private\n     * @method bodyBounds\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyBounds = function(render, bodies, context) {\n        var c = context,\n            engine = render.engine,\n            options = render.options;\n\n        c.beginPath();\n\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i];\n\n            if (body.render.visible) {\n                var parts = bodies[i].parts;\n                for (var j = parts.length > 1 ? 1 : 0; j < parts.length; j++) {\n                    var part = parts[j];\n                    c.rect(part.bounds.min.x, part.bounds.min.y, part.bounds.max.x - part.bounds.min.x, part.bounds.max.y - part.bounds.min.y);\n                }\n            }\n        }\n\n        if (options.wireframes) {\n            c.strokeStyle = 'rgba(255,255,255,0.08)';\n        } else {\n            c.strokeStyle = 'rgba(0,0,0,0.1)';\n        }\n\n        c.lineWidth = 1;\n        c.stroke();\n    };\n\n    /**\n     * Draws body angle indicators and axes\n     * @private\n     * @method bodyAxes\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyAxes = function(render, bodies, context) {\n        var c = context,\n            engine = render.engine,\n            options = render.options,\n            part,\n            i,\n            j,\n            k;\n\n        c.beginPath();\n\n        for (i = 0; i < bodies.length; i++) {\n            var body = bodies[i],\n                parts = body.parts;\n\n            if (!body.render.visible)\n                continue;\n\n            if (options.showAxes) {\n                // render all axes\n                for (j = parts.length > 1 ? 1 : 0; j < parts.length; j++) {\n                    part = parts[j];\n                    for (k = 0; k < part.axes.length; k++) {\n                        var axis = part.axes[k];\n                        c.moveTo(part.position.x, part.position.y);\n                        c.lineTo(part.position.x + axis.x * 20, part.position.y + axis.y * 20);\n                    }\n                }\n            } else {\n                for (j = parts.length > 1 ? 1 : 0; j < parts.length; j++) {\n                    part = parts[j];\n                    for (k = 0; k < part.axes.length; k++) {\n                        // render a single axis indicator\n                        c.moveTo(part.position.x, part.position.y);\n                        c.lineTo((part.vertices[0].x + part.vertices[part.vertices.length-1].x) / 2,\n                            (part.vertices[0].y + part.vertices[part.vertices.length-1].y) / 2);\n                    }\n                }\n            }\n        }\n\n        if (options.wireframes) {\n            c.strokeStyle = 'indianred';\n            c.lineWidth = 1;\n        } else {\n            c.strokeStyle = 'rgba(255, 255, 255, 0.4)';\n            c.globalCompositeOperation = 'overlay';\n            c.lineWidth = 2;\n        }\n\n        c.stroke();\n        c.globalCompositeOperation = 'source-over';\n    };\n\n    /**\n     * Draws body positions\n     * @private\n     * @method bodyPositions\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyPositions = function(render, bodies, context) {\n        var c = context,\n            engine = render.engine,\n            options = render.options,\n            body,\n            part,\n            i,\n            k;\n\n        c.beginPath();\n\n        // render current positions\n        for (i = 0; i < bodies.length; i++) {\n            body = bodies[i];\n\n            if (!body.render.visible)\n                continue;\n\n            // handle compound parts\n            for (k = 0; k < body.parts.length; k++) {\n                part = body.parts[k];\n                c.arc(part.position.x, part.position.y, 3, 0, 2 * Math.PI, false);\n                c.closePath();\n            }\n        }\n\n        if (options.wireframes) {\n            c.fillStyle = 'indianred';\n        } else {\n            c.fillStyle = 'rgba(0,0,0,0.5)';\n        }\n        c.fill();\n\n        c.beginPath();\n\n        // render previous positions\n        for (i = 0; i < bodies.length; i++) {\n            body = bodies[i];\n            if (body.render.visible) {\n                c.arc(body.positionPrev.x, body.positionPrev.y, 2, 0, 2 * Math.PI, false);\n                c.closePath();\n            }\n        }\n\n        c.fillStyle = 'rgba(255,165,0,0.8)';\n        c.fill();\n    };\n\n    /**\n     * Draws body velocity\n     * @private\n     * @method bodyVelocity\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyVelocity = function(render, bodies, context) {\n        var c = context;\n\n        c.beginPath();\n\n        for (var i = 0; i < bodies.length; i++) {\n            var body = bodies[i];\n\n            if (!body.render.visible)\n                continue;\n\n            var velocity = Body.getVelocity(body);\n\n            c.moveTo(body.position.x, body.position.y);\n            c.lineTo(body.position.x + velocity.x, body.position.y + velocity.y);\n        }\n\n        c.lineWidth = 3;\n        c.strokeStyle = 'cornflowerblue';\n        c.stroke();\n    };\n\n    /**\n     * Draws body ids\n     * @private\n     * @method bodyIds\n     * @param {render} render\n     * @param {body[]} bodies\n     * @param {RenderingContext} context\n     */\n    Render.bodyIds = function(render, bodies, context) {\n        var c = context,\n            i,\n            j;\n\n        for (i = 0; i < bodies.length; i++) {\n            if (!bodies[i].render.visible)\n                continue;\n\n            var parts = bodies[i].parts;\n            for (j = parts.length > 1 ? 1 : 0; j < parts.length; j++) {\n                var part = parts[j];\n                c.font = \"12px Arial\";\n                c.fillStyle = 'rgba(255,255,255,0.5)';\n                c.fillText(part.id, part.position.x + 10, part.position.y - 10);\n            }\n        }\n    };\n\n    /**\n     * Description\n     * @private\n     * @method collisions\n     * @param {render} render\n     * @param {pair[]} pairs\n     * @param {RenderingContext} context\n     */\n    Render.collisions = function(render, pairs, context) {\n        var c = context,\n            options = render.options,\n            pair,\n            collision,\n            corrected,\n            bodyA,\n            bodyB,\n            i,\n            j;\n\n        c.beginPath();\n\n        // render collision positions\n        for (i = 0; i < pairs.length; i++) {\n            pair = pairs[i];\n\n            if (!pair.isActive)\n                continue;\n\n            collision = pair.collision;\n            for (j = 0; j < pair.activeContacts.length; j++) {\n                var contact = pair.activeContacts[j],\n                    vertex = contact.vertex;\n                c.rect(vertex.x - 1.5, vertex.y - 1.5, 3.5, 3.5);\n            }\n        }\n\n        if (options.wireframes) {\n            c.fillStyle = 'rgba(255,255,255,0.7)';\n        } else {\n            c.fillStyle = 'orange';\n        }\n        c.fill();\n\n        c.beginPath();\n\n        // render collision normals\n        for (i = 0; i < pairs.length; i++) {\n            pair = pairs[i];\n\n            if (!pair.isActive)\n                continue;\n\n            collision = pair.collision;\n\n            if (pair.activeContacts.length > 0) {\n                var normalPosX = pair.activeContacts[0].vertex.x,\n                    normalPosY = pair.activeContacts[0].vertex.y;\n\n                if (pair.activeContacts.length === 2) {\n                    normalPosX = (pair.activeContacts[0].vertex.x + pair.activeContacts[1].vertex.x) / 2;\n                    normalPosY = (pair.activeContacts[0].vertex.y + pair.activeContacts[1].vertex.y) / 2;\n                }\n\n                if (collision.bodyB === collision.supports[0].body || collision.bodyA.isStatic === true) {\n                    c.moveTo(normalPosX - collision.normal.x * 8, normalPosY - collision.normal.y * 8);\n                } else {\n                    c.moveTo(normalPosX + collision.normal.x * 8, normalPosY + collision.normal.y * 8);\n                }\n\n                c.lineTo(normalPosX, normalPosY);\n            }\n        }\n\n        if (options.wireframes) {\n            c.strokeStyle = 'rgba(255,165,0,0.7)';\n        } else {\n            c.strokeStyle = 'orange';\n        }\n\n        c.lineWidth = 1;\n        c.stroke();\n    };\n\n    /**\n     * Description\n     * @private\n     * @method separations\n     * @param {render} render\n     * @param {pair[]} pairs\n     * @param {RenderingContext} context\n     */\n    Render.separations = function(render, pairs, context) {\n        var c = context,\n            options = render.options,\n            pair,\n            collision,\n            corrected,\n            bodyA,\n            bodyB,\n            i,\n            j;\n\n        c.beginPath();\n\n        // render separations\n        for (i = 0; i < pairs.length; i++) {\n            pair = pairs[i];\n\n            if (!pair.isActive)\n                continue;\n\n            collision = pair.collision;\n            bodyA = collision.bodyA;\n            bodyB = collision.bodyB;\n\n            var k = 1;\n\n            if (!bodyB.isStatic && !bodyA.isStatic) k = 0.5;\n            if (bodyB.isStatic) k = 0;\n\n            c.moveTo(bodyB.position.x, bodyB.position.y);\n            c.lineTo(bodyB.position.x - collision.penetration.x * k, bodyB.position.y - collision.penetration.y * k);\n\n            k = 1;\n\n            if (!bodyB.isStatic && !bodyA.isStatic) k = 0.5;\n            if (bodyA.isStatic) k = 0;\n\n            c.moveTo(bodyA.position.x, bodyA.position.y);\n            c.lineTo(bodyA.position.x + collision.penetration.x * k, bodyA.position.y + collision.penetration.y * k);\n        }\n\n        if (options.wireframes) {\n            c.strokeStyle = 'rgba(255,165,0,0.5)';\n        } else {\n            c.strokeStyle = 'orange';\n        }\n        c.stroke();\n    };\n\n    /**\n     * Description\n     * @private\n     * @method inspector\n     * @param {inspector} inspector\n     * @param {RenderingContext} context\n     */\n    Render.inspector = function(inspector, context) {\n        var engine = inspector.engine,\n            selected = inspector.selected,\n            render = inspector.render,\n            options = render.options,\n            bounds;\n\n        if (options.hasBounds) {\n            var boundsWidth = render.bounds.max.x - render.bounds.min.x,\n                boundsHeight = render.bounds.max.y - render.bounds.min.y,\n                boundsScaleX = boundsWidth / render.options.width,\n                boundsScaleY = boundsHeight / render.options.height;\n\n            context.scale(1 / boundsScaleX, 1 / boundsScaleY);\n            context.translate(-render.bounds.min.x, -render.bounds.min.y);\n        }\n\n        for (var i = 0; i < selected.length; i++) {\n            var item = selected[i].data;\n\n            context.translate(0.5, 0.5);\n            context.lineWidth = 1;\n            context.strokeStyle = 'rgba(255,165,0,0.9)';\n            context.setLineDash([1,2]);\n\n            switch (item.type) {\n\n            case 'body':\n\n                // render body selections\n                bounds = item.bounds;\n                context.beginPath();\n                context.rect(Math.floor(bounds.min.x - 3), Math.floor(bounds.min.y - 3),\n                    Math.floor(bounds.max.x - bounds.min.x + 6), Math.floor(bounds.max.y - bounds.min.y + 6));\n                context.closePath();\n                context.stroke();\n\n                break;\n\n            case 'constraint':\n\n                // render constraint selections\n                var point = item.pointA;\n                if (item.bodyA)\n                    point = item.pointB;\n                context.beginPath();\n                context.arc(point.x, point.y, 10, 0, 2 * Math.PI);\n                context.closePath();\n                context.stroke();\n\n                break;\n\n            }\n\n            context.setLineDash([]);\n            context.translate(-0.5, -0.5);\n        }\n\n        // render selection region\n        if (inspector.selectStart !== null) {\n            context.translate(0.5, 0.5);\n            context.lineWidth = 1;\n            context.strokeStyle = 'rgba(255,165,0,0.6)';\n            context.fillStyle = 'rgba(255,165,0,0.1)';\n            bounds = inspector.selectBounds;\n            context.beginPath();\n            context.rect(Math.floor(bounds.min.x), Math.floor(bounds.min.y),\n                Math.floor(bounds.max.x - bounds.min.x), Math.floor(bounds.max.y - bounds.min.y));\n            context.closePath();\n            context.stroke();\n            context.fill();\n            context.translate(-0.5, -0.5);\n        }\n\n        if (options.hasBounds)\n            context.setTransform(1, 0, 0, 1, 0, 0);\n    };\n\n    /**\n     * Updates render timing.\n     * @method _updateTiming\n     * @private\n     * @param {render} render\n     * @param {number} time\n     */\n    var _updateTiming = function(render, time) {\n        var engine = render.engine,\n            timing = render.timing,\n            historySize = timing.historySize,\n            timestamp = engine.timing.timestamp;\n\n        timing.delta = time - timing.lastTime || Render._goodDelta;\n        timing.lastTime = time;\n\n        timing.timestampElapsed = timestamp - timing.lastTimestamp || 0;\n        timing.lastTimestamp = timestamp;\n\n        timing.deltaHistory.unshift(timing.delta);\n        timing.deltaHistory.length = Math.min(timing.deltaHistory.length, historySize);\n\n        timing.engineDeltaHistory.unshift(engine.timing.lastDelta);\n        timing.engineDeltaHistory.length = Math.min(timing.engineDeltaHistory.length, historySize);\n\n        timing.timestampElapsedHistory.unshift(timing.timestampElapsed);\n        timing.timestampElapsedHistory.length = Math.min(timing.timestampElapsedHistory.length, historySize);\n\n        timing.engineElapsedHistory.unshift(engine.timing.lastElapsed);\n        timing.engineElapsedHistory.length = Math.min(timing.engineElapsedHistory.length, historySize);\n\n        timing.elapsedHistory.unshift(timing.lastElapsed);\n        timing.elapsedHistory.length = Math.min(timing.elapsedHistory.length, historySize);\n    };\n\n    /**\n     * Returns the mean value of the given numbers.\n     * @method _mean\n     * @private\n     * @param {Number[]} values\n     * @return {Number} the mean of given values\n     */\n    var _mean = function(values) {\n        var result = 0;\n        for (var i = 0; i < values.length; i += 1) {\n            result += values[i];\n        }\n        return (result / values.length) || 0;\n    };\n\n    /**\n     * @method _createCanvas\n     * @private\n     * @param {} width\n     * @param {} height\n     * @return canvas\n     */\n    var _createCanvas = function(width, height) {\n        var canvas = document.createElement('canvas');\n        canvas.width = width;\n        canvas.height = height;\n        canvas.oncontextmenu = function() { return false; };\n        canvas.onselectstart = function() { return false; };\n        return canvas;\n    };\n\n    /**\n     * Gets the pixel ratio of the canvas.\n     * @method _getPixelRatio\n     * @private\n     * @param {HTMLElement} canvas\n     * @return {Number} pixel ratio\n     */\n    var _getPixelRatio = function(canvas) {\n        var context = canvas.getContext('2d'),\n            devicePixelRatio = window.devicePixelRatio || 1,\n            backingStorePixelRatio = context.webkitBackingStorePixelRatio || context.mozBackingStorePixelRatio\n                                      || context.msBackingStorePixelRatio || context.oBackingStorePixelRatio\n                                      || context.backingStorePixelRatio || 1;\n\n        return devicePixelRatio / backingStorePixelRatio;\n    };\n\n    /**\n     * Gets the requested texture (an Image) via its path\n     * @method _getTexture\n     * @private\n     * @param {render} render\n     * @param {string} imagePath\n     * @return {Image} texture\n     */\n    var _getTexture = function(render, imagePath) {\n        var image = render.textures[imagePath];\n\n        if (image)\n            return image;\n\n        image = render.textures[imagePath] = new Image();\n        image.src = imagePath;\n\n        return image;\n    };\n\n    /**\n     * Applies the background to the canvas using CSS.\n     * @method applyBackground\n     * @private\n     * @param {render} render\n     * @param {string} background\n     */\n    var _applyBackground = function(render, background) {\n        var cssBackground = background;\n\n        if (/(jpg|gif|png)$/.test(background))\n            cssBackground = 'url(' + background + ')';\n\n        render.canvas.style.background = cssBackground;\n        render.canvas.style.backgroundSize = \"contain\";\n        render.currentBackground = background;\n    };\n\n    /*\n    *\n    *  Events Documentation\n    *\n    */\n\n    /**\n    * Fired before rendering\n    *\n    * @event beforeRender\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired after rendering\n    *\n    * @event afterRender\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * A back-reference to the `Matter.Render` module.\n     *\n     * @deprecated\n     * @property controller\n     * @type render\n     */\n\n    /**\n     * A reference to the `Matter.Engine` instance to be used.\n     *\n     * @property engine\n     * @type engine\n     */\n\n    /**\n     * A reference to the element where the canvas is to be inserted (if `render.canvas` has not been specified)\n     *\n     * @property element\n     * @type HTMLElement\n     * @default null\n     */\n\n    /**\n     * The canvas element to render to. If not specified, one will be created if `render.element` has been specified.\n     *\n     * @property canvas\n     * @type HTMLCanvasElement\n     * @default null\n     */\n\n    /**\n     * A `Bounds` object that specifies the drawing view region.\n     * Rendering will be automatically transformed and scaled to fit within the canvas size (`render.options.width` and `render.options.height`).\n     * This allows for creating views that can pan or zoom around the scene.\n     * You must also set `render.options.hasBounds` to `true` to enable bounded rendering.\n     *\n     * @property bounds\n     * @type bounds\n     */\n\n    /**\n     * The 2d rendering context from the `render.canvas` element.\n     *\n     * @property context\n     * @type CanvasRenderingContext2D\n     */\n\n    /**\n     * The sprite texture cache.\n     *\n     * @property textures\n     * @type {}\n     */\n\n    /**\n     * The mouse to render if `render.options.showMousePosition` is enabled.\n     *\n     * @property mouse\n     * @type mouse\n     * @default null\n     */\n\n    /**\n     * The configuration options of the renderer.\n     *\n     * @property options\n     * @type {}\n     */\n\n    /**\n     * The target width in pixels of the `render.canvas` to be created.\n     * See also the `options.pixelRatio` property to change render quality.\n     *\n     * @property options.width\n     * @type number\n     * @default 800\n     */\n\n    /**\n     * The target height in pixels of the `render.canvas` to be created.\n     * See also the `options.pixelRatio` property to change render quality.\n     *\n     * @property options.height\n     * @type number\n     * @default 600\n     */\n\n    /**\n     * The [pixel ratio](https://developer.mozilla.org/en-US/docs/Web/API/Window/devicePixelRatio) to use when rendering.\n     *\n     * @property options.pixelRatio\n     * @type number\n     * @default 1\n     */\n\n    /**\n     * A CSS background color string to use when `render.options.wireframes` is disabled.\n     * This may be also set to `'transparent'` or equivalent.\n     *\n     * @property options.background\n     * @type string\n     * @default '#14151f'\n     */\n\n    /**\n     * A CSS background color string to use when `render.options.wireframes` is enabled.\n     * This may be also set to `'transparent'` or equivalent.\n     *\n     * @property options.wireframeBackground\n     * @type string\n     * @default '#14151f'\n     */\n\n    /**\n     * A flag that specifies if `render.bounds` should be used when rendering.\n     *\n     * @property options.hasBounds\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable all debug information overlays together.  \n     * This includes and has priority over the values of:\n     *\n     * - `render.options.showStats`\n     * - `render.options.showPerformance`\n     *\n     * @property options.showDebug\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the engine stats info overlay.  \n     * From left to right, the values shown are:\n     *\n     * - body parts total\n     * - body total\n     * - constraints total\n     * - composites total\n     * - collision pairs total\n     *\n     * @property options.showStats\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable performance charts.  \n     * From left to right, the values shown are:\n     *\n     * - average render frequency (e.g. 60 fps)\n     * - exact engine delta time used for last update (e.g. 16.66ms)\n     * - average engine execution duration (e.g. 5.00ms)\n     * - average render execution duration (e.g. 0.40ms)\n     * - average effective play speed (e.g. '1.00x' is 'real-time')\n     *\n     * Each value is recorded over a fixed sample of past frames (60 frames).\n     *\n     * A chart shown below each value indicates the variance from the average over the sample.\n     * The more stable or fixed the value is the flatter the chart will appear.\n     *\n     * @property options.showPerformance\n     * @type boolean\n     * @default false\n     */\n    \n    /**\n     * A flag to enable or disable rendering entirely.\n     *\n     * @property options.enabled\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to toggle wireframe rendering otherwise solid fill rendering is used.\n     *\n     * @property options.wireframes\n     * @type boolean\n     * @default true\n     */\n\n    /**\n     * A flag to enable or disable sleeping bodies indicators.\n     *\n     * @property options.showSleeping\n     * @type boolean\n     * @default true\n     */\n\n    /**\n     * A flag to enable or disable the debug information overlay.\n     *\n     * @property options.showDebug\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the collision broadphase debug overlay.\n     *\n     * @deprecated no longer implemented\n     * @property options.showBroadphase\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body bounds debug overlay.\n     *\n     * @property options.showBounds\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body velocity debug overlay.\n     *\n     * @property options.showVelocity\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body collisions debug overlay.\n     *\n     * @property options.showCollisions\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the collision resolver separations debug overlay.\n     *\n     * @property options.showSeparations\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body axes debug overlay.\n     *\n     * @property options.showAxes\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body positions debug overlay.\n     *\n     * @property options.showPositions\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body angle debug overlay.\n     *\n     * @property options.showAngleIndicator\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body and part ids debug overlay.\n     *\n     * @property options.showIds\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body vertex numbers debug overlay.\n     *\n     * @property options.showVertexNumbers\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body convex hulls debug overlay.\n     *\n     * @property options.showConvexHulls\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the body internal edges debug overlay.\n     *\n     * @property options.showInternalEdges\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A flag to enable or disable the mouse position debug overlay.\n     *\n     * @property options.showMousePosition\n     * @type boolean\n     * @default false\n     */\n\n})();\n\n\n/***/ }),\n/* 27 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Runner` module is an optional utility which provides a game loop, \n* that handles continuously updating a `Matter.Engine` for you within a browser.\n* It is intended for development and debugging purposes, but may also be suitable for simple games.\n* If you are using your own game loop instead, then you do not need the `Matter.Runner` module.\n* Instead just call `Engine.update(engine, delta)` in your own loop.\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Runner\n*/\n\nvar Runner = {};\n\nmodule.exports = Runner;\n\nvar Events = __webpack_require__(5);\nvar Engine = __webpack_require__(17);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    var _requestAnimationFrame,\n        _cancelAnimationFrame;\n\n    if (typeof window !== 'undefined') {\n        _requestAnimationFrame = window.requestAnimationFrame || window.webkitRequestAnimationFrame\n                                      || window.mozRequestAnimationFrame || window.msRequestAnimationFrame;\n   \n        _cancelAnimationFrame = window.cancelAnimationFrame || window.mozCancelAnimationFrame \n                                      || window.webkitCancelAnimationFrame || window.msCancelAnimationFrame;\n    }\n\n    if (!_requestAnimationFrame) {\n        var _frameTimeout;\n\n        _requestAnimationFrame = function(callback){ \n            _frameTimeout = setTimeout(function() { \n                callback(Common.now()); \n            }, 1000 / 60);\n        };\n\n        _cancelAnimationFrame = function() {\n            clearTimeout(_frameTimeout);\n        };\n    }\n\n    /**\n     * Creates a new Runner. The options parameter is an object that specifies any properties you wish to override the defaults.\n     * @method create\n     * @param {} options\n     */\n    Runner.create = function(options) {\n        var defaults = {\n            fps: 60,\n            deltaSampleSize: 60,\n            counterTimestamp: 0,\n            frameCounter: 0,\n            deltaHistory: [],\n            timePrev: null,\n            frameRequestId: null,\n            isFixed: false,\n            enabled: true\n        };\n\n        var runner = Common.extend(defaults, options);\n\n        runner.delta = runner.delta || 1000 / runner.fps;\n        runner.deltaMin = runner.deltaMin || 1000 / runner.fps;\n        runner.deltaMax = runner.deltaMax || 1000 / (runner.fps * 0.5);\n        runner.fps = 1000 / runner.delta;\n\n        return runner;\n    };\n\n    /**\n     * Continuously ticks a `Matter.Engine` by calling `Runner.tick` on the `requestAnimationFrame` event.\n     * @method run\n     * @param {engine} engine\n     */\n    Runner.run = function(runner, engine) {\n        // create runner if engine is first argument\n        if (typeof runner.positionIterations !== 'undefined') {\n            engine = runner;\n            runner = Runner.create();\n        }\n\n        (function run(time){\n            runner.frameRequestId = _requestAnimationFrame(run);\n\n            if (time && runner.enabled) {\n                Runner.tick(runner, engine, time);\n            }\n        })();\n\n        return runner;\n    };\n\n    /**\n     * A game loop utility that updates the engine and renderer by one step (a 'tick').\n     * Features delta smoothing, time correction and fixed or dynamic timing.\n     * Consider just `Engine.update(engine, delta)` if you're using your own loop.\n     * @method tick\n     * @param {runner} runner\n     * @param {engine} engine\n     * @param {number} time\n     */\n    Runner.tick = function(runner, engine, time) {\n        var timing = engine.timing,\n            delta;\n\n        if (runner.isFixed) {\n            // fixed timestep\n            delta = runner.delta;\n        } else {\n            // dynamic timestep based on wall clock between calls\n            delta = (time - runner.timePrev) || runner.delta;\n            runner.timePrev = time;\n\n            // optimistically filter delta over a few frames, to improve stability\n            runner.deltaHistory.push(delta);\n            runner.deltaHistory = runner.deltaHistory.slice(-runner.deltaSampleSize);\n            delta = Math.min.apply(null, runner.deltaHistory);\n\n            // limit delta\n            delta = delta < runner.deltaMin ? runner.deltaMin : delta;\n            delta = delta > runner.deltaMax ? runner.deltaMax : delta;\n\n            // update engine timing object\n            runner.delta = delta;\n        }\n\n        // create an event object\n        var event = {\n            timestamp: timing.timestamp\n        };\n\n        Events.trigger(runner, 'beforeTick', event);\n\n        // fps counter\n        runner.frameCounter += 1;\n        if (time - runner.counterTimestamp >= 1000) {\n            runner.fps = runner.frameCounter * ((time - runner.counterTimestamp) / 1000);\n            runner.counterTimestamp = time;\n            runner.frameCounter = 0;\n        }\n\n        Events.trigger(runner, 'tick', event);\n\n        // update\n        Events.trigger(runner, 'beforeUpdate', event);\n\n        Engine.update(engine, delta);\n\n        Events.trigger(runner, 'afterUpdate', event);\n\n        Events.trigger(runner, 'afterTick', event);\n    };\n\n    /**\n     * Ends execution of `Runner.run` on the given `runner`, by canceling the animation frame request event loop.\n     * If you wish to only temporarily pause the engine, see `engine.enabled` instead.\n     * @method stop\n     * @param {runner} runner\n     */\n    Runner.stop = function(runner) {\n        _cancelAnimationFrame(runner.frameRequestId);\n    };\n\n    /**\n     * Alias for `Runner.run`.\n     * @method start\n     * @param {runner} runner\n     * @param {engine} engine\n     */\n    Runner.start = function(runner, engine) {\n        Runner.run(runner, engine);\n    };\n\n    /*\n    *\n    *  Events Documentation\n    *\n    */\n\n    /**\n    * Fired at the start of a tick, before any updates to the engine or timing\n    *\n    * @event beforeTick\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired after engine timing updated, but just before update\n    *\n    * @event tick\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired at the end of a tick, after engine update and after rendering\n    *\n    * @event afterTick\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired before update\n    *\n    * @event beforeUpdate\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /**\n    * Fired after update\n    *\n    * @event afterUpdate\n    * @param {} event An event object\n    * @param {number} event.timestamp The engine.timing.timestamp of the event\n    * @param {} event.source The source object of the event\n    * @param {} event.name The name of the event\n    */\n\n    /*\n    *\n    *  Properties Documentation\n    *\n    */\n\n    /**\n     * A flag that specifies whether the runner is running or not.\n     *\n     * @property enabled\n     * @type boolean\n     * @default true\n     */\n\n    /**\n     * A `Boolean` that specifies if the runner should use a fixed timestep (otherwise it is variable).\n     * If timing is fixed, then the apparent simulation speed will change depending on the frame rate (but behaviour will be deterministic).\n     * If the timing is variable, then the apparent simulation speed will be constant (approximately, but at the cost of determininism).\n     *\n     * @property isFixed\n     * @type boolean\n     * @default false\n     */\n\n    /**\n     * A `Number` that specifies the time step between updates in milliseconds.\n     * If `engine.timing.isFixed` is set to `true`, then `delta` is fixed.\n     * If it is `false`, then `delta` can dynamically change to maintain the correct apparent simulation speed.\n     *\n     * @property delta\n     * @type number\n     * @default 1000 / 60\n     */\n\n})();\n\n\n/***/ }),\n/* 28 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* This module has now been replaced by `Matter.Collision`.\n*\n* All usage should be migrated to `Matter.Collision`.\n* For back-compatibility purposes this module will remain for a short term and then later removed in a future release.\n*\n* The `Matter.SAT` module contains methods for detecting collisions using the Separating Axis Theorem.\n*\n* @class SAT\n* @deprecated\n*/\n\nvar SAT = {};\n\nmodule.exports = SAT;\n\nvar Collision = __webpack_require__(8);\nvar Common = __webpack_require__(0);\nvar deprecated = Common.deprecated;\n\n(function() {\n\n    /**\n     * Detect collision between two bodies using the Separating Axis Theorem.\n     * @deprecated replaced by Collision.collides\n     * @method collides\n     * @param {body} bodyA\n     * @param {body} bodyB\n     * @return {collision} collision\n     */\n    SAT.collides = function(bodyA, bodyB) {\n        return Collision.collides(bodyA, bodyB);\n    };\n\n    deprecated(SAT, 'collides', 'SAT.collides ➤ replaced by Collision.collides');\n\n})();\n\n\n/***/ }),\n/* 29 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* The `Matter.Svg` module contains methods for converting SVG images into an array of vector points.\n*\n* To use this module you also need the SVGPathSeg polyfill: https://github.com/progers/pathseg\n*\n* See the included usage [examples](https://github.com/liabru/matter-js/tree/master/examples).\n*\n* @class Svg\n*/\n\nvar Svg = {};\n\nmodule.exports = Svg;\n\nvar Bounds = __webpack_require__(1);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * Converts an SVG path into an array of vector points.\n     * If the input path forms a concave shape, you must decompose the result into convex parts before use.\n     * See `Bodies.fromVertices` which provides support for this.\n     * Note that this function is not guaranteed to support complex paths (such as those with holes).\n     * You must load the `pathseg.js` polyfill on newer browsers.\n     * @method pathToVertices\n     * @param {SVGPathElement} path\n     * @param {Number} [sampleLength=15]\n     * @return {Vector[]} points\n     */\n    Svg.pathToVertices = function(path, sampleLength) {\n        if (typeof window !== 'undefined' && !('SVGPathSeg' in window)) {\n            Common.warn('Svg.pathToVertices: SVGPathSeg not defined, a polyfill is required.');\n        }\n\n        // https://github.com/wout/svg.topoly.js/blob/master/svg.topoly.js\n        var i, il, total, point, segment, segments, \n            segmentsQueue, lastSegment, \n            lastPoint, segmentIndex, points = [],\n            lx, ly, length = 0, x = 0, y = 0;\n\n        sampleLength = sampleLength || 15;\n\n        var addPoint = function(px, py, pathSegType) {\n            // all odd-numbered path types are relative except PATHSEG_CLOSEPATH (1)\n            var isRelative = pathSegType % 2 === 1 && pathSegType > 1;\n\n            // when the last point doesn't equal the current point add the current point\n            if (!lastPoint || px != lastPoint.x || py != lastPoint.y) {\n                if (lastPoint && isRelative) {\n                    lx = lastPoint.x;\n                    ly = lastPoint.y;\n                } else {\n                    lx = 0;\n                    ly = 0;\n                }\n\n                var point = {\n                    x: lx + px,\n                    y: ly + py\n                };\n\n                // set last point\n                if (isRelative || !lastPoint) {\n                    lastPoint = point;\n                }\n\n                points.push(point);\n\n                x = lx + px;\n                y = ly + py;\n            }\n        };\n\n        var addSegmentPoint = function(segment) {\n            var segType = segment.pathSegTypeAsLetter.toUpperCase();\n\n            // skip path ends\n            if (segType === 'Z') \n                return;\n\n            // map segment to x and y\n            switch (segType) {\n\n            case 'M':\n            case 'L':\n            case 'T':\n            case 'C':\n            case 'S':\n            case 'Q':\n                x = segment.x;\n                y = segment.y;\n                break;\n            case 'H':\n                x = segment.x;\n                break;\n            case 'V':\n                y = segment.y;\n                break;\n            }\n\n            addPoint(x, y, segment.pathSegType);\n        };\n\n        // ensure path is absolute\n        Svg._svgPathToAbsolute(path);\n\n        // get total length\n        total = path.getTotalLength();\n\n        // queue segments\n        segments = [];\n        for (i = 0; i < path.pathSegList.numberOfItems; i += 1)\n            segments.push(path.pathSegList.getItem(i));\n\n        segmentsQueue = segments.concat();\n\n        // sample through path\n        while (length < total) {\n            // get segment at position\n            segmentIndex = path.getPathSegAtLength(length);\n            segment = segments[segmentIndex];\n\n            // new segment\n            if (segment != lastSegment) {\n                while (segmentsQueue.length && segmentsQueue[0] != segment)\n                    addSegmentPoint(segmentsQueue.shift());\n\n                lastSegment = segment;\n            }\n\n            // add points in between when curving\n            // TODO: adaptive sampling\n            switch (segment.pathSegTypeAsLetter.toUpperCase()) {\n\n            case 'C':\n            case 'T':\n            case 'S':\n            case 'Q':\n            case 'A':\n                point = path.getPointAtLength(length);\n                addPoint(point.x, point.y, 0);\n                break;\n\n            }\n\n            // increment by sample value\n            length += sampleLength;\n        }\n\n        // add remaining segments not passed by sampling\n        for (i = 0, il = segmentsQueue.length; i < il; ++i)\n            addSegmentPoint(segmentsQueue[i]);\n\n        return points;\n    };\n\n    Svg._svgPathToAbsolute = function(path) {\n        // http://phrogz.net/convert-svg-path-to-all-absolute-commands\n        // Copyright (c) Gavin Kistner\n        // http://phrogz.net/js/_ReuseLicense.txt\n        // Modifications: tidy formatting and naming\n        var x0, y0, x1, y1, x2, y2, segs = path.pathSegList,\n            x = 0, y = 0, len = segs.numberOfItems;\n\n        for (var i = 0; i < len; ++i) {\n            var seg = segs.getItem(i),\n                segType = seg.pathSegTypeAsLetter;\n\n            if (/[MLHVCSQTA]/.test(segType)) {\n                if ('x' in seg) x = seg.x;\n                if ('y' in seg) y = seg.y;\n            } else {\n                if ('x1' in seg) x1 = x + seg.x1;\n                if ('x2' in seg) x2 = x + seg.x2;\n                if ('y1' in seg) y1 = y + seg.y1;\n                if ('y2' in seg) y2 = y + seg.y2;\n                if ('x' in seg) x += seg.x;\n                if ('y' in seg) y += seg.y;\n\n                switch (segType) {\n\n                case 'm':\n                    segs.replaceItem(path.createSVGPathSegMovetoAbs(x, y), i);\n                    break;\n                case 'l':\n                    segs.replaceItem(path.createSVGPathSegLinetoAbs(x, y), i);\n                    break;\n                case 'h':\n                    segs.replaceItem(path.createSVGPathSegLinetoHorizontalAbs(x), i);\n                    break;\n                case 'v':\n                    segs.replaceItem(path.createSVGPathSegLinetoVerticalAbs(y), i);\n                    break;\n                case 'c':\n                    segs.replaceItem(path.createSVGPathSegCurvetoCubicAbs(x, y, x1, y1, x2, y2), i);\n                    break;\n                case 's':\n                    segs.replaceItem(path.createSVGPathSegCurvetoCubicSmoothAbs(x, y, x2, y2), i);\n                    break;\n                case 'q':\n                    segs.replaceItem(path.createSVGPathSegCurvetoQuadraticAbs(x, y, x1, y1), i);\n                    break;\n                case 't':\n                    segs.replaceItem(path.createSVGPathSegCurvetoQuadraticSmoothAbs(x, y), i);\n                    break;\n                case 'a':\n                    segs.replaceItem(path.createSVGPathSegArcAbs(x, y, seg.r1, seg.r2, seg.angle, seg.largeArcFlag, seg.sweepFlag), i);\n                    break;\n                case 'z':\n                case 'Z':\n                    x = x0;\n                    y = y0;\n                    break;\n\n                }\n            }\n\n            if (segType == 'M' || segType == 'm') {\n                x0 = x;\n                y0 = y;\n            }\n        }\n    };\n\n})();\n\n/***/ }),\n/* 30 */\n/***/ (function(module, exports, __webpack_require__) {\n\n/**\n* This module has now been replaced by `Matter.Composite`.\n*\n* All usage should be migrated to the equivalent functions found on `Matter.Composite`.\n* For example `World.add(world, body)` now becomes `Composite.add(world, body)`.\n*\n* The property `world.gravity` has been moved to `engine.gravity`.\n*\n* For back-compatibility purposes this module will remain as a direct alias to `Matter.Composite` in the short term during migration.\n* Eventually this alias module will be marked as deprecated and then later removed in a future release.\n*\n* @class World\n*/\n\nvar World = {};\n\nmodule.exports = World;\n\nvar Composite = __webpack_require__(6);\nvar Common = __webpack_require__(0);\n\n(function() {\n\n    /**\n     * See above, aliases for back compatibility only\n     */\n    World.create = Composite.create;\n    World.add = Composite.add;\n    World.remove = Composite.remove;\n    World.clear = Composite.clear;\n    World.addComposite = Composite.addComposite;\n    World.addBody = Composite.addBody;\n    World.addConstraint = Composite.addConstraint;\n\n})();\n\n\n/***/ })\n/******/ ]);\n});"], "names": ["root", "factory", "module", "this", "modules", "installedModules", "__webpack_require__", "moduleId", "exports", "name", "getter", "value", "mode", "ns", "key", "object", "property", "Common", "obj", "deep", "argsStart", "deepClone", "i", "source", "prop", "keys", "values", "path", "begin", "end", "val", "parts", "array", "j", "temp", "choices", "min", "max", "_<PERSON><PERSON><PERSON><PERSON>", "colorString", "message", "warning", "haystack", "needle", "list", "func", "mapped", "graph", "result", "visited", "node", "neighbors", "neighbor", "funcs", "chain", "lastResult", "args", "l", "base", "decomp", "global", "Bounds", "vertices", "bounds", "velocity", "vertex", "point", "boundsA", "boundsB", "vector", "position", "deltaX", "deltaY", "Vector", "x", "y", "angle", "output", "cos", "sin", "magnitude", "vectorA", "vectorB", "vectorC", "scalar", "negate", "Vertices", "points", "body", "pathPattern", "match", "area", "centre", "cross", "average", "signed", "mass", "numerator", "denominator", "v", "n", "vertices<PERSON>ength", "translateX", "translateY", "pointX", "pointY", "dx", "dy", "nextVertex", "scaleX", "scaleY", "delta", "radius", "quality", "qualityMin", "qualityMax", "newVertices", "prevVertex", "currentRadius", "prevNormal", "nextNormal", "diagonalRadius", "radiusVector", "midNormal", "scaledVertex", "precision", "alpha", "theta", "vertexA", "vertexB", "flag", "k", "z", "upper", "lower", "Body", "Sleeping", "Axes", "options", "defaults", "_initProperties", "isNonColliding", "defaultFillStyle", "defaultStrokeStyle", "defaultLineWidth", "settings", "isStatic", "part", "moment", "density", "inertia", "autoHull", "hull", "hullCentre", "total", "relative", "updateVelocity", "timeScale", "speed", "translation", "rotation", "totalArea", "totalInertia", "deltaTime", "deltaTimeSquared", "correction", "frictionAir", "velocityPrevX", "velocityPrevY", "bodyVelocity", "force", "offset", "properties", "Events", "eventNames", "callback", "names", "callbacks", "newCallbacks", "event", "eventClone", "events", "Composite", "composite", "isModified", "updateParents", "update<PERSON><PERSON><PERSON>n", "childComposite", "objects", "compositeA", "compositeB", "constraint", "keepStatic", "bodies", "constraints", "composites", "id", "type", "recursive", "motionSleepThreshold", "angularSpeed", "motion", "minMotion", "maxMotion", "pairs", "pair", "collision", "bodyA", "bodyB", "sleepingBody", "movingBody", "isSleeping", "wasSleeping", "Collision", "Pair", "_supports", "_overlapAB", "_overlapBA", "minOverlap", "normal", "supports", "minAxis", "minAxisX", "minAxisY", "supportsB", "supportCount", "supportsA", "verticesA", "verticesB", "axes", "verticesALength", "verticesBLength", "verticesAX", "verticesAY", "verticesBX", "verticesBY", "<PERSON><PERSON><PERSON><PERSON>", "overlapMin", "overlapAxisNumber", "overlap", "overlapAB", "overlapBA", "dot", "axis", "axisX", "axisY", "minA", "minB", "maxA", "maxB", "projection", "direction", "bodyAPositionX", "bodyAPositionY", "normalX", "normalY", "nearestDistance", "vertexC", "distance", "Contact", "timestamp", "contacts", "activeContacts", "parentA", "parentB", "parentAVerticesLength", "support", "contactId", "contact", "isActive", "Constraint", "initialPointA", "initialPointB", "length", "render", "impulse", "fixedA", "fixedB", "pointA", "pointB", "pointAWorld", "pointBWorld", "<PERSON><PERSON><PERSON><PERSON>", "difference", "isRigid", "stiffness", "damping", "massTotal", "inertiaTotal", "resistanceTotal", "torque", "share", "normalVelocity", "relativeVelocity", "zero", "gradient", "xx", "Bodies", "width", "height", "rectangle", "chamfer", "slope", "roof", "x1", "x2", "x3", "verticesPath", "trapezoid", "maxSides", "circle", "sides", "yy", "polygon", "vertexSets", "flagInternal", "removeCollinear", "minimumArea", "removeDuplicatePoints", "canDecomp", "isConvex", "isConcave", "concave", "decomposed", "chunk", "chunkVertices", "coincident_max_dist", "partA", "partB", "pav", "pbv", "da", "db", "Detector", "detector", "collisions", "bodies<PERSON>ength", "canCollide", "collides", "boundXMax", "boundYMax", "boundYMin", "bodyAStatic", "partsALength", "partsASingle", "partsBLength", "partsAStart", "partsBStart", "filterA", "filterB", "Mouse", "element", "mouse", "touches", "scale", "pixelRatio", "elementBounds", "rootNode", "scrollX", "scrollY", "Plugin", "plugin", "registered", "pluginVersion", "registeredVersion", "dependency", "parsed", "plugins", "dependencies", "sortedDependencies", "status", "tracked", "parsedBase", "resolved", "pattern", "range", "major", "minor", "patch", "version", "r", "Engine", "Resolver", "Pairs", "engine", "startTime", "world", "timing", "allBodies", "allConstraints", "positionDamping", "engineA", "engineB", "gravity", "gravityScale", "activeCount", "<PERSON><PERSON><PERSON><PERSON>", "contactShare", "positionImpulse", "positionDampen", "slopDampen", "positionWarming", "verticesTranslate", "boundsUpdate", "positionImpulseX", "positionImpulseY", "<PERSON><PERSON><PERSON><PERSON>", "tangent", "contactVertex", "normalImpulse", "tangentImpulse", "impulseX", "impulseY", "timeScaleSquared", "timeScaleCubed", "<PERSON><PERSON><PERSON><PERSON>", "restingT<PERSON><PERSON><PERSON><PERSON>ent", "frictionNormalMultiplier", "frictionMaxStatic", "maxFriction", "bodyAVelocity", "bodyBVelocity", "tangentX", "tangentY", "inverseMassTotal", "friction", "offsetAX", "offsetAY", "offsetBX", "offsetBY", "velocityPointAX", "velocityPointAY", "velocityPointBX", "velocityPointBY", "relativeVelocityX", "relativeVelocityY", "tangentVelocity", "normalOverlap", "normalForce", "frictionLimit", "oAcN", "oBcN", "contactNormalImpulse", "contactTangentImpulse", "pairsList", "<PERSON>L<PERSON><PERSON><PERSON><PERSON>", "pairsTable", "collisionsLength", "collisionStart", "collisionEnd", "collisionActive", "pairIndex", "removePairIndex", "Matter", "Composites", "deprecated", "columns", "rows", "columnGap", "rowGap", "stack", "lastBody", "row", "maxHeight", "column", "bodyHeight", "bodyWidth", "xOffsetA", "yOffsetA", "xOffsetB", "yOffsetB", "bodyAHeight", "bodyA<PERSON>idth", "bodyBHeight", "bodyB<PERSON>idth", "crossBrace", "col", "bodyC", "actualRows", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "start", "xOffset", "number", "size", "newtonsCradle", "separation", "wheelSize", "group", "wheelBase", "wheelAOffset", "wheelBOffset", "wheelYOffset", "car", "wheelA", "wheelB", "axelA", "axelB", "particleRadius", "particleOptions", "constraintOptions", "softBody", "Grid", "grid", "forceUpdate", "buckets", "bucket", "bucketId", "gridChanged", "newRegion", "union", "isInsideNewRegion", "isInsideOldRegion", "regionA", "regionB", "startCol", "endCol", "startRow", "endRow", "gridPairs", "pairId", "bucket<PERSON>ength", "pair<PERSON><PERSON>s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MouseConstraint", "mouseConstraint", "mouseEvents", "Query", "overlaps", "startPoint", "endPoint", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "rayX", "rayY", "ray", "outside", "Render", "_requestAnimationFrame", "_cancelAnimationFrame", "_createCanvas", "loop", "time", "_updateTiming", "canvas", "_getPixelRatio", "padding", "center", "viewHeight", "viewWidth", "outerRatio", "innerRatio", "boundsWidth", "boundsHeight", "boundsScaleX", "boundsScaleY", "context", "background", "_applyBackground", "sections", "section", "deltaHistory", "elapsedHistory", "timestampElapsedHistory", "engineDeltaHistory", "engineElapsedHistory", "lastEngineDel<PERSON>", "deltaMean", "_mean", "elapsed<PERSON><PERSON>", "engineDeltaMean", "engineElapsedMean", "timestampElapsedMean", "rateMean", "fps", "graphHeight", "gap", "count", "label", "indicator", "plotY", "c", "coils", "showInternalEdges", "sprite", "texture", "_getTexture", "normalPosX", "normalPosY", "inspector", "selected", "item", "historySize", "devicePixelRatio", "backingStorePixelRatio", "imagePath", "image", "cssBackground", "Runner", "_frameTimeout", "runner", "run", "SAT", "Svg", "sampleLength", "il", "segment", "segments", "segmentsQueue", "lastSegment", "lastPoint", "segmentIndex", "lx", "ly", "addPoint", "px", "py", "pathSegType", "isRelative", "addSegmentPoint", "segType", "x0", "y0", "y1", "y2", "segs", "len", "seg", "World"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;qBA2BC,SAA0CA,EAAMC,EAAS,CAExDC,GAAA,QAAiBD,EAAO,IAOvBE,EAAM,UAAW,CACpB,OAAiB,SAASC,EAAS,CAEzB,IAAIC,EAAmB,CAAA,EAGvB,SAASC,EAAoBC,EAAU,CAGtC,GAAGF,EAAiBE,CAAQ,EAC3B,OAAOF,EAAiBE,CAAQ,EAAE,QAGnC,IAAIL,EAASG,EAAiBE,CAAQ,EAAI,CACzC,EAAGA,EACH,EAAG,GACH,QAAS,CAAA,CACrB,EAGW,OAAAH,EAAQG,CAAQ,EAAE,KAAKL,EAAO,QAASA,EAAQA,EAAO,QAASI,CAAmB,EAGlFJ,EAAO,EAAI,GAGJA,EAAO,QAKf,OAAAI,EAAoB,EAAIF,EAGxBE,EAAoB,EAAID,EAGxBC,EAAoB,EAAI,SAASE,EAASC,EAAMC,EAAQ,CACnDJ,EAAoB,EAAEE,EAASC,CAAI,GACtC,OAAO,eAAeD,EAASC,EAAM,CAAE,WAAY,GAAM,IAAKC,EAAQ,CAElF,EAGUJ,EAAoB,EAAI,SAASE,EAAS,CACtC,OAAO,OAAW,KAAe,OAAO,aAC1C,OAAO,eAAeA,EAAS,OAAO,YAAa,CAAE,MAAO,SAAU,EAEvE,OAAO,eAAeA,EAAS,aAAc,CAAE,MAAO,GAAM,CACvE,EAOUF,EAAoB,EAAI,SAASK,EAAOC,EAAM,CAG7C,GAFGA,EAAO,IAAGD,EAAQL,EAAoBK,CAAK,GAC3CC,EAAO,GACNA,EAAO,GAAM,OAAOD,GAAU,UAAYA,GAASA,EAAM,WAAY,OAAOA,EAChF,IAAIE,EAAK,OAAO,OAAO,IAAI,EAG3B,GAFAP,EAAoB,EAAEO,CAAE,EACxB,OAAO,eAAeA,EAAI,UAAW,CAAE,WAAY,GAAM,MAAOF,EAAO,EACpEC,EAAO,GAAK,OAAOD,GAAS,SAAU,QAAQG,KAAOH,EAAOL,EAAoB,EAAEO,EAAIC,EAAK,SAASA,EAAK,CAAE,OAAOH,EAAMG,CAAG,CAAE,EAAG,KAAK,KAAMA,CAAG,CAAC,EAClJ,OAAOD,CAClB,EAGUP,EAAoB,EAAI,SAASJ,EAAQ,CACxC,IAAIQ,EAASR,GAAUA,EAAO,WAC7B,UAAsB,CAAE,OAAOA,EAAO,OAAW,EACjD,UAA4B,CAAE,OAAOA,CAAO,EAC7C,OAAAI,EAAoB,EAAEI,EAAQ,IAAKA,CAAM,EAClCA,CAClB,EAGUJ,EAAoB,EAAI,SAASS,EAAQC,EAAU,CAAE,OAAO,OAAO,UAAU,eAAe,KAAKD,EAAQC,CAAQ,CAAE,EAGnHV,EAAoB,EAAI,GAIjBA,EAAoBA,EAAoB,EAAI,EAAE,GAGrD,CAEH,SAASJ,EAAQM,EAAS,CAQjC,IAAIS,EAAS,CAAA,EAEbf,EAAO,QAAUe,EAEhB,UAAW,CAERA,EAAO,WAAa,IAAO,GAC3BA,EAAO,QAAU,EACjBA,EAAO,MAAQ,EACfA,EAAO,cAAgB,CAAE,IAAI,KAC7BA,EAAO,YAAc,CAAA,EACrBA,EAAO,QAAU,KASjBA,EAAO,OAAS,SAASC,EAAKC,EAAM,KAC5BC,EAEAC,EAEA,OAAOF,GAAS,WAChBC,EAAY,EACZC,EAAYF,IAEZC,EAAY,EACZC,EAAY,IAGhB,QAASC,EAAIF,EAAWE,EAAI,UAAU,OAAQA,IAAK,CAC/C,IAAIC,EAAS,UAAUD,CAAC,EAExB,GAAIC,EACA,QAASC,KAAQD,EACTF,GAAaE,EAAOC,CAAI,GAAKD,EAAOC,CAAI,EAAE,cAAgB,SACtD,CAACN,EAAIM,CAAI,GAAKN,EAAIM,CAAI,EAAE,cAAgB,SACxCN,EAAIM,CAAI,EAAIN,EAAIM,CAAI,GAAK,CAAA,EACzBP,EAAO,OAAOC,EAAIM,CAAI,EAAGH,EAAWE,EAAOC,CAAI,CAAC,GAKpDN,EAAIM,CAAI,EAAID,EAAOC,CAAI,EAMvC,OAAON,CACf,EASID,EAAO,MAAQ,SAASC,EAAKC,EAAM,CAC/B,OAAOF,EAAO,OAAO,GAAIE,EAAMD,CAAG,CAC1C,EAQID,EAAO,KAAO,SAASC,EAAK,CACxB,GAAI,OAAO,KACP,OAAO,OAAO,KAAKA,CAAG,EAG1B,IAAIO,EAAO,CAAA,EACX,QAASX,KAAOI,EACZO,EAAK,KAAKX,CAAG,EACjB,OAAOW,CACf,EAQIR,EAAO,OAAS,SAASC,EAAK,CAC1B,IAAIQ,EAAS,CAAA,EAEb,GAAI,OAAO,KAAM,CAEb,QADID,EAAO,OAAO,KAAKP,CAAG,EACjBI,EAAI,EAAGA,EAAIG,EAAK,OAAQH,IAC7BI,EAAO,KAAKR,EAAIO,EAAKH,CAAC,CAAC,CAAC,EAE5B,OAAOI,EAIX,QAASZ,KAAOI,EACZQ,EAAO,KAAKR,EAAIJ,CAAG,CAAC,EACxB,OAAOY,CACf,EAWIT,EAAO,IAAM,SAASC,EAAKS,EAAMC,EAAOC,EAAK,CACzCF,EAAOA,EAAK,MAAM,GAAG,EAAE,MAAMC,EAAOC,CAAG,EAEvC,QAASP,EAAI,EAAGA,EAAIK,EAAK,OAAQL,GAAK,EAClCJ,EAAMA,EAAIS,EAAKL,CAAC,CAAC,EAGrB,OAAOJ,CACf,EAYID,EAAO,IAAM,SAASC,EAAKS,EAAMG,EAAKF,EAAOC,EAAK,CAC9C,IAAIE,EAAQJ,EAAK,MAAM,GAAG,EAAE,MAAMC,EAAOC,CAAG,EAC5C,OAAAZ,EAAO,IAAIC,EAAKS,EAAM,EAAG,EAAE,EAAEI,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAID,EACjDA,CACf,EASIb,EAAO,QAAU,SAASe,EAAO,CAC7B,QAASV,EAAIU,EAAM,OAAS,EAAGV,EAAI,EAAGA,IAAK,CACvC,IAAIW,EAAI,KAAK,MAAMhB,EAAO,UAAYK,EAAI,EAAE,EACxCY,EAAOF,EAAMV,CAAC,EAClBU,EAAMV,CAAC,EAAIU,EAAMC,CAAC,EAClBD,EAAMC,CAAC,EAAIC,EAEf,OAAOF,CACf,EASIf,EAAO,OAAS,SAASkB,EAAS,CAC9B,OAAOA,EAAQ,KAAK,MAAMlB,EAAO,SAAWkB,EAAQ,MAAM,CAAC,CACnE,EAQIlB,EAAO,UAAY,SAASC,EAAK,CAC7B,OAAI,OAAO,YAAgB,IAChBA,aAAe,YAGnB,CAAC,EAAEA,GAAOA,EAAI,UAAYA,EAAI,SAC7C,EAQID,EAAO,QAAU,SAASC,EAAK,CAC3B,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAG,IAAM,gBACvD,EAQID,EAAO,WAAa,SAASC,EAAK,CAC9B,OAAO,OAAOA,GAAQ,UAC9B,EAQID,EAAO,cAAgB,SAASC,EAAK,CACjC,OAAO,OAAOA,GAAQ,UAAYA,EAAI,cAAgB,MAC9D,EAQID,EAAO,SAAW,SAASC,EAAK,CAC5B,OAAO,SAAS,KAAKA,CAAG,IAAM,iBACtC,EAUID,EAAO,MAAQ,SAASN,EAAOyB,EAAKC,EAAK,CACrC,OAAI1B,EAAQyB,EACDA,EACPzB,EAAQ0B,EACDA,EACJ1B,CACf,EAQIM,EAAO,KAAO,SAASN,EAAO,CAC1B,OAAOA,EAAQ,EAAI,GAAK,CAChC,EAQIM,EAAO,IAAM,UAAW,CACpB,GAAI,OAAO,OAAW,KAAe,OAAO,YAAa,CACrD,GAAI,OAAO,YAAY,IACnB,OAAO,OAAO,YAAY,IAAG,EAC1B,GAAI,OAAO,YAAY,UAC1B,OAAO,OAAO,YAAY,UAAS,EAI3C,OAAI,KAAK,IACE,KAAK,IAAG,EAGX,IAAI,KAAUA,EAAO,aACrC,EAUIA,EAAO,OAAS,SAASmB,EAAKC,EAAK,CAC/B,OAAAD,EAAO,OAAOA,EAAQ,IAAeA,EAAM,EAC3CC,EAAO,OAAOA,EAAQ,IAAeA,EAAM,EACpCD,EAAME,KAAmBD,EAAMD,EAC9C,EAEI,IAAIE,EAAgB,UAAW,CAE3B,OAAArB,EAAO,OAASA,EAAO,MAAQ,KAAO,OAAS,OACxCA,EAAO,MAAQ,MAC9B,EAQIA,EAAO,cAAgB,SAASsB,EAAa,CACzC,OAAAA,EAAcA,EAAY,QAAQ,IAAI,EAAE,EAEpCA,EAAY,QAAU,IACtBA,EAAcA,EAAY,OAAO,CAAC,EAAIA,EAAY,OAAO,CAAC,EAC5CA,EAAY,OAAO,CAAC,EAAIA,EAAY,OAAO,CAAC,EAC5CA,EAAY,OAAO,CAAC,EAAIA,EAAY,OAAO,CAAC,GAGvD,SAASA,EAAa,EAAE,CACvC,EAiBItB,EAAO,SAAW,EAQlBA,EAAO,IAAM,UAAW,CAChB,SAAWA,EAAO,SAAW,GAAKA,EAAO,UAAY,GACrD,QAAQ,IAAI,MAAM,QAAS,CAAC,YAAY,EAAE,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC,CAAC,CAEnG,EAQIA,EAAO,KAAO,UAAW,CACjB,SAAWA,EAAO,SAAW,GAAKA,EAAO,UAAY,GACrD,QAAQ,KAAK,MAAM,QAAS,CAAC,YAAY,EAAE,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC,CAAC,CAEpG,EAQIA,EAAO,KAAO,UAAW,CACjB,SAAWA,EAAO,SAAW,GAAKA,EAAO,UAAY,GACrD,QAAQ,KAAK,MAAM,QAAS,CAAC,YAAY,EAAE,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC,CAAC,CAEpG,EAOIA,EAAO,SAAW,UAAW,CACzB,IAAIuB,EAAU,MAAM,UAAU,MAAM,KAAK,SAAS,EAAE,KAAK,GAAG,EAEvDvB,EAAO,YAAYuB,CAAO,IAC3BvB,EAAO,KAAKuB,CAAO,EACnBvB,EAAO,YAAYuB,CAAO,EAAI,GAE1C,EAWIvB,EAAO,WAAa,SAASC,EAAKM,EAAMiB,EAAS,CAC7CvB,EAAIM,CAAI,EAAIP,EAAO,MAAM,UAAW,CAChCA,EAAO,SAAS,mBAAoBwB,CAAO,CACvD,EAAWvB,EAAIM,CAAI,CAAC,CACpB,EAOIP,EAAO,OAAS,UAAW,CACvB,OAAOA,EAAO,SACtB,EASIA,EAAO,QAAU,SAASyB,EAAUC,EAAQ,CACxC,GAAID,EAAS,QACT,OAAOA,EAAS,QAAQC,CAAM,EAElC,QAASrB,EAAI,EAAGA,EAAIoB,EAAS,OAAQpB,IACjC,GAAIoB,EAASpB,CAAC,IAAMqB,EAChB,OAAOrB,EAGf,MAAO,EACf,EASIL,EAAO,IAAM,SAAS2B,EAAMC,EAAM,CAC9B,GAAID,EAAK,IACL,OAAOA,EAAK,IAAIC,CAAI,EAKxB,QAFIC,EAAS,CAAA,EAEJxB,EAAI,EAAGA,EAAIsB,EAAK,OAAQtB,GAAK,EAClCwB,EAAO,KAAKD,EAAKD,EAAKtB,CAAC,CAAC,CAAC,EAG7B,OAAOwB,CACf,EASI7B,EAAO,gBAAkB,SAAS8B,EAAO,CAIrC,IAAIC,EAAS,CAAA,EACTC,EAAU,CAAA,EACVf,EAAO,CAAA,EAEX,QAASgB,KAAQH,EACT,CAACE,EAAQC,CAAI,GAAK,CAAChB,EAAKgB,CAAI,GAC5BjC,EAAO,iBAAiBiC,EAAMD,EAASf,EAAMa,EAAOC,CAAM,EAIlE,OAAOA,CACf,EAEI/B,EAAO,iBAAmB,SAASiC,EAAMD,EAASf,EAAMa,EAAOC,EAAQ,CACnE,IAAIG,EAAYJ,EAAMG,CAAI,GAAK,CAAA,EAC/BhB,EAAKgB,CAAI,EAAI,GAEb,QAAS5B,EAAI,EAAGA,EAAI6B,EAAU,OAAQ7B,GAAK,EAAG,CAC1C,IAAI8B,EAAWD,EAAU7B,CAAC,EAEtBY,EAAKkB,CAAQ,GAKZH,EAAQG,CAAQ,GACjBnC,EAAO,iBAAiBmC,EAAUH,EAASf,EAAMa,EAAOC,CAAM,EAItEd,EAAKgB,CAAI,EAAI,GACbD,EAAQC,CAAI,EAAI,GAEhBF,EAAO,KAAKE,CAAI,CACxB,EAaIjC,EAAO,MAAQ,UAAW,CAGtB,QAFIoC,EAAQ,CAAA,EAEH/B,EAAI,EAAGA,EAAI,UAAU,OAAQA,GAAK,EAAG,CAC1C,IAAIuB,EAAO,UAAUvB,CAAC,EAElBuB,EAAK,SAELQ,EAAM,KAAK,MAAMA,EAAOR,EAAK,QAAQ,EAErCQ,EAAM,KAAKR,CAAI,EAIvB,IAAIS,EAAQ,UAAW,CAKnB,QAHIC,EACAC,EAAO,IAAI,MAAM,UAAU,MAAM,EAE5BlC,EAAI,EAAGmC,EAAI,UAAU,OAAQnC,EAAImC,EAAGnC,IACzCkC,EAAKlC,CAAC,EAAI,UAAUA,CAAC,EAGzB,IAAKA,EAAI,EAAGA,EAAI+B,EAAM,OAAQ/B,GAAK,EAAG,CAClC,IAAI0B,EAASK,EAAM/B,CAAC,EAAE,MAAMiC,EAAYC,CAAI,EAExC,OAAOR,EAAW,MAClBO,EAAaP,GAIrB,OAAOO,CACnB,EAEQ,OAAAD,EAAM,SAAWD,EAEVC,CACf,EAWIrC,EAAO,gBAAkB,SAASyC,EAAM/B,EAAMkB,EAAM,CAChD,OAAO5B,EAAO,IAAIyC,EAAM/B,EAAMV,EAAO,MACjC4B,EACA5B,EAAO,IAAIyC,EAAM/B,CAAI,CACjC,CAAS,CACT,EAWIV,EAAO,eAAiB,SAASyC,EAAM/B,EAAMkB,EAAM,CAC/C,OAAO5B,EAAO,IAAIyC,EAAM/B,EAAMV,EAAO,MACjCA,EAAO,IAAIyC,EAAM/B,CAAI,EACrBkB,CACZ,CAAS,CACT,EAQI5B,EAAO,UAAY,SAAS0C,EAAQ,CAChC1C,EAAO,QAAU0C,CACzB,EAQI1C,EAAO,UAAY,UAAW,CAE1B,IAAI0C,EAAS1C,EAAO,QAEpB,GAAI,CAEI,CAAC0C,GAAU,OAAO,OAAW,MAC7BA,EAAS,OAAO,QAIhB,CAACA,GAAU,OAAOC,EAAW,MAC7BD,EAASC,EAAO,aAEZ,CAERD,EAAS,KAGb,OAAOA,CACf,CACA,EAAC,GAKM,SAASzD,EAAQM,EAAS,CAQjC,IAAIqD,EAAS,CAAA,EAEb3D,EAAO,QAAU2D,EAEhB,UAAW,CAQRA,EAAO,OAAS,SAASC,EAAU,CAC/B,IAAIC,EAAS,CACT,IAAK,CAAE,EAAG,EAAG,EAAG,CAAC,EACjB,IAAK,CAAE,EAAG,EAAG,EAAG,CAAC,CAC7B,EAEQ,OAAID,GACAD,EAAO,OAAOE,EAAQD,CAAQ,EAE3BC,CACf,EASIF,EAAO,OAAS,SAASE,EAAQD,EAAUE,EAAU,CACjDD,EAAO,IAAI,EAAI,IACfA,EAAO,IAAI,EAAI,KACfA,EAAO,IAAI,EAAI,IACfA,EAAO,IAAI,EAAI,KAEf,QAASzC,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IAAK,CACtC,IAAI2C,EAASH,EAASxC,CAAC,EACnB2C,EAAO,EAAIF,EAAO,IAAI,IAAGA,EAAO,IAAI,EAAIE,EAAO,GAC/CA,EAAO,EAAIF,EAAO,IAAI,IAAGA,EAAO,IAAI,EAAIE,EAAO,GAC/CA,EAAO,EAAIF,EAAO,IAAI,IAAGA,EAAO,IAAI,EAAIE,EAAO,GAC/CA,EAAO,EAAIF,EAAO,IAAI,IAAGA,EAAO,IAAI,EAAIE,EAAO,GAGnDD,IACIA,EAAS,EAAI,EACbD,EAAO,IAAI,GAAKC,EAAS,EAEzBD,EAAO,IAAI,GAAKC,EAAS,EAGzBA,EAAS,EAAI,EACbD,EAAO,IAAI,GAAKC,EAAS,EAEzBD,EAAO,IAAI,GAAKC,EAAS,EAGzC,EASIH,EAAO,SAAW,SAASE,EAAQG,EAAO,CACtC,OAAOA,EAAM,GAAKH,EAAO,IAAI,GAAKG,EAAM,GAAKH,EAAO,IAAI,GAC9CG,EAAM,GAAKH,EAAO,IAAI,GAAKG,EAAM,GAAKH,EAAO,IAAI,CACnE,EASIF,EAAO,SAAW,SAASM,EAASC,EAAS,CACzC,OAAQD,EAAQ,IAAI,GAAKC,EAAQ,IAAI,GAAKD,EAAQ,IAAI,GAAKC,EAAQ,IAAI,GAC5DD,EAAQ,IAAI,GAAKC,EAAQ,IAAI,GAAKD,EAAQ,IAAI,GAAKC,EAAQ,IAAI,CAClF,EAQIP,EAAO,UAAY,SAASE,EAAQM,EAAQ,CACxCN,EAAO,IAAI,GAAKM,EAAO,EACvBN,EAAO,IAAI,GAAKM,EAAO,EACvBN,EAAO,IAAI,GAAKM,EAAO,EACvBN,EAAO,IAAI,GAAKM,EAAO,CAC/B,EAQIR,EAAO,MAAQ,SAASE,EAAQO,EAAU,CACtC,IAAIC,EAASR,EAAO,IAAI,EAAIA,EAAO,IAAI,EACnCS,EAAST,EAAO,IAAI,EAAIA,EAAO,IAAI,EAEvCA,EAAO,IAAI,EAAIO,EAAS,EACxBP,EAAO,IAAI,EAAIO,EAAS,EAAIC,EAC5BR,EAAO,IAAI,EAAIO,EAAS,EACxBP,EAAO,IAAI,EAAIO,EAAS,EAAIE,CACpC,CAEA,EAAC,GAKM,SAAStE,EAAQM,EAAS,CAcjC,IAAIiE,EAAS,CAAA,EAEbvE,EAAO,QAAUuE,EAEhB,UAAW,CASRA,EAAO,OAAS,SAASC,EAAGC,EAAG,CAC3B,MAAO,CAAE,EAAGD,GAAK,EAAG,EAAGC,GAAK,CAAC,CACrC,EAQIF,EAAO,MAAQ,SAASJ,EAAQ,CAC5B,MAAO,CAAE,EAAGA,EAAO,EAAG,EAAGA,EAAO,CAAC,CACzC,EAQII,EAAO,UAAY,SAASJ,EAAQ,CAChC,OAAO,KAAK,KAAMA,EAAO,EAAIA,EAAO,EAAMA,EAAO,EAAIA,EAAO,CAAE,CACtE,EAQII,EAAO,iBAAmB,SAASJ,EAAQ,CACvC,OAAQA,EAAO,EAAIA,EAAO,EAAMA,EAAO,EAAIA,EAAO,CAC1D,EAUII,EAAO,OAAS,SAASJ,EAAQO,EAAOC,EAAQ,CAC5C,IAAIC,EAAM,KAAK,IAAIF,CAAK,EAAGG,EAAM,KAAK,IAAIH,CAAK,EAC1CC,IAAQA,EAAS,CAAA,GACtB,IAAIH,EAAIL,EAAO,EAAIS,EAAMT,EAAO,EAAIU,EACpC,OAAAF,EAAO,EAAIR,EAAO,EAAIU,EAAMV,EAAO,EAAIS,EACvCD,EAAO,EAAIH,EACJG,CACf,EAWIJ,EAAO,YAAc,SAASJ,EAAQO,EAAOV,EAAOW,EAAQ,CACxD,IAAIC,EAAM,KAAK,IAAIF,CAAK,EAAGG,EAAM,KAAK,IAAIH,CAAK,EAC1CC,IAAQA,EAAS,CAAA,GACtB,IAAIH,EAAIR,EAAM,IAAMG,EAAO,EAAIH,EAAM,GAAKY,GAAOT,EAAO,EAAIH,EAAM,GAAKa,GACvE,OAAAF,EAAO,EAAIX,EAAM,IAAMG,EAAO,EAAIH,EAAM,GAAKa,GAAOV,EAAO,EAAIH,EAAM,GAAKY,GAC1ED,EAAO,EAAIH,EACJG,CACf,EAQIJ,EAAO,UAAY,SAASJ,EAAQ,CAChC,IAAIW,EAAYP,EAAO,UAAUJ,CAAM,EACvC,OAAIW,IAAc,EACP,CAAE,EAAG,EAAG,EAAG,CAAC,EAChB,CAAE,EAAGX,EAAO,EAAIW,EAAW,EAAGX,EAAO,EAAIW,CAAS,CACjE,EASIP,EAAO,IAAM,SAASQ,EAASC,EAAS,CACpC,OAAQD,EAAQ,EAAIC,EAAQ,EAAMD,EAAQ,EAAIC,EAAQ,CAC9D,EASIT,EAAO,MAAQ,SAASQ,EAASC,EAAS,CACtC,OAAQD,EAAQ,EAAIC,EAAQ,EAAMD,EAAQ,EAAIC,EAAQ,CAC9D,EAUIT,EAAO,OAAS,SAASQ,EAASC,EAASC,EAAS,CAChD,OAAQD,EAAQ,EAAID,EAAQ,IAAME,EAAQ,EAAIF,EAAQ,IAAMC,EAAQ,EAAID,EAAQ,IAAME,EAAQ,EAAIF,EAAQ,EAClH,EAUIR,EAAO,IAAM,SAASQ,EAASC,EAASL,EAAQ,CAC5C,OAAKA,IAAQA,EAAS,CAAA,GACtBA,EAAO,EAAII,EAAQ,EAAIC,EAAQ,EAC/BL,EAAO,EAAII,EAAQ,EAAIC,EAAQ,EACxBL,CACf,EAUIJ,EAAO,IAAM,SAASQ,EAASC,EAASL,EAAQ,CAC5C,OAAKA,IAAQA,EAAS,CAAA,GACtBA,EAAO,EAAII,EAAQ,EAAIC,EAAQ,EAC/BL,EAAO,EAAII,EAAQ,EAAIC,EAAQ,EACxBL,CACf,EASIJ,EAAO,KAAO,SAASJ,EAAQe,EAAQ,CACnC,MAAO,CAAE,EAAGf,EAAO,EAAIe,EAAQ,EAAGf,EAAO,EAAIe,CAAM,CAC3D,EASIX,EAAO,IAAM,SAASJ,EAAQe,EAAQ,CAClC,MAAO,CAAE,EAAGf,EAAO,EAAIe,EAAQ,EAAGf,EAAO,EAAIe,CAAM,CAC3D,EASIX,EAAO,KAAO,SAASJ,EAAQgB,EAAQ,CACnC,OAAAA,EAASA,IAAW,GAAO,GAAK,EACzB,CAAE,EAAGA,EAAS,CAAChB,EAAO,EAAG,EAAGgB,EAAShB,EAAO,CAAC,CAC5D,EAQII,EAAO,IAAM,SAASJ,EAAQ,CAC1B,MAAO,CAAE,EAAG,CAACA,EAAO,EAAG,EAAG,CAACA,EAAO,CAAC,CAC3C,EASII,EAAO,MAAQ,SAASQ,EAASC,EAAS,CACtC,OAAO,KAAK,MAAMA,EAAQ,EAAID,EAAQ,EAAGC,EAAQ,EAAID,EAAQ,CAAC,CACtE,EAQIR,EAAO,MAAQ,CACXA,EAAO,OAAM,EAAIA,EAAO,OAAM,EAC9BA,EAAO,OAAM,EAAIA,EAAO,OAAM,EAC9BA,EAAO,SAAUA,EAAO,OAAM,CACtC,CAEA,EAAC,GAIM,SAASvE,EAAQM,EAASF,EAAqB,CAYtD,IAAIgF,EAAW,CAAA,EAEfpF,EAAO,QAAUoF,EAEjB,IAAIb,EAASnE,EAAoB,CAAC,EAC9BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAmBRgF,EAAS,OAAS,SAASC,EAAQC,EAAM,CAGrC,QAFI1B,EAAW,CAAA,EAENxC,EAAI,EAAGA,EAAIiE,EAAO,OAAQjE,IAAK,CACpC,IAAI4C,EAAQqB,EAAOjE,CAAC,EAChB2C,EAAS,CACL,EAAGC,EAAM,EACT,EAAGA,EAAM,EACT,MAAO5C,EACP,KAAMkE,EACN,WAAY,EAChC,EAEY1B,EAAS,KAAKG,CAAM,EAGxB,OAAOH,CACf,EAWIwB,EAAS,SAAW,SAAS3D,EAAM6D,EAAM,CACrC,IAAIC,EAAc,qCACdF,EAAS,CAAA,EAEb,OAAA5D,EAAK,QAAQ8D,EAAa,SAASC,EAAOhB,EAAGC,EAAG,CAC5CY,EAAO,KAAK,CAAE,EAAG,WAAWb,CAAC,EAAG,EAAG,WAAWC,CAAC,EAAG,CAC9D,CAAS,EAEMW,EAAS,OAAOC,EAAQC,CAAI,CAC3C,EAQIF,EAAS,OAAS,SAASxB,EAAU,CAOjC,QANI6B,EAAOL,EAAS,KAAKxB,EAAU,EAAI,EACnC8B,EAAS,CAAE,EAAG,EAAG,EAAG,CAAC,EACrBC,EACA3D,EACAD,EAEKX,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IACjCW,GAAKX,EAAI,GAAKwC,EAAS,OACvB+B,EAAQpB,EAAO,MAAMX,EAASxC,CAAC,EAAGwC,EAAS7B,CAAC,CAAC,EAC7CC,EAAOuC,EAAO,KAAKA,EAAO,IAAIX,EAASxC,CAAC,EAAGwC,EAAS7B,CAAC,CAAC,EAAG4D,CAAK,EAC9DD,EAASnB,EAAO,IAAImB,EAAQ1D,CAAI,EAGpC,OAAOuC,EAAO,IAAImB,EAAQ,EAAID,CAAI,CAC1C,EAQIL,EAAS,KAAO,SAASxB,EAAU,CAG/B,QAFIgC,EAAU,CAAE,EAAG,EAAG,EAAG,CAAC,EAEjBxE,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IACjCwE,EAAQ,GAAKhC,EAASxC,CAAC,EAAE,EACzBwE,EAAQ,GAAKhC,EAASxC,CAAC,EAAE,EAG7B,OAAOmD,EAAO,IAAIqB,EAAShC,EAAS,MAAM,CAClD,EASIwB,EAAS,KAAO,SAASxB,EAAUiC,EAAQ,CAIvC,QAHIJ,EAAO,EACP1D,EAAI6B,EAAS,OAAS,EAEjBxC,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IACjCqE,IAAS7B,EAAS7B,CAAC,EAAE,EAAI6B,EAASxC,CAAC,EAAE,IAAMwC,EAAS7B,CAAC,EAAE,EAAI6B,EAASxC,CAAC,EAAE,GACvEW,EAAIX,EAGR,OAAIyE,EACOJ,EAAO,EAEX,KAAK,IAAIA,CAAI,EAAI,CAChC,EASIL,EAAS,QAAU,SAASxB,EAAUkC,EAAM,CASxC,QARIC,EAAY,EACZC,EAAc,EACdC,EAAIrC,EACJ+B,EACA5D,EAIKmE,EAAI,EAAGA,EAAID,EAAE,OAAQC,IAC1BnE,GAAKmE,EAAI,GAAKD,EAAE,OAChBN,EAAQ,KAAK,IAAIpB,EAAO,MAAM0B,EAAElE,CAAC,EAAGkE,EAAEC,CAAC,CAAC,CAAC,EACzCH,GAAaJ,GAASpB,EAAO,IAAI0B,EAAElE,CAAC,EAAGkE,EAAElE,CAAC,CAAC,EAAIwC,EAAO,IAAI0B,EAAElE,CAAC,EAAGkE,EAAEC,CAAC,CAAC,EAAI3B,EAAO,IAAI0B,EAAEC,CAAC,EAAGD,EAAEC,CAAC,CAAC,GAC7FF,GAAeL,EAGnB,OAAQG,EAAO,GAAMC,EAAYC,EACzC,EASIZ,EAAS,UAAY,SAASxB,EAAUO,EAAQe,EAAQ,CACpDA,EAAS,OAAOA,EAAW,IAAcA,EAAS,EAElD,IAAIiB,EAAiBvC,EAAS,OAC1BwC,EAAajC,EAAO,EAAIe,EACxBmB,EAAalC,EAAO,EAAIe,EACxB9D,EAEJ,IAAKA,EAAI,EAAGA,EAAI+E,EAAgB/E,IAC5BwC,EAASxC,CAAC,EAAE,GAAKgF,EACjBxC,EAASxC,CAAC,EAAE,GAAKiF,EAGrB,OAAOzC,CACf,EASIwB,EAAS,OAAS,SAASxB,EAAUc,EAAOV,EAAO,CAC/C,GAAIU,IAAU,EAGd,KAAIE,EAAM,KAAK,IAAIF,CAAK,EACpBG,EAAM,KAAK,IAAIH,CAAK,EACpB4B,EAAStC,EAAM,EACfuC,EAASvC,EAAM,EACfmC,EAAiBvC,EAAS,OAC1BG,EACAyC,EACAC,EACArF,EAEJ,IAAKA,EAAI,EAAGA,EAAI+E,EAAgB/E,IAC5B2C,EAASH,EAASxC,CAAC,EACnBoF,EAAKzC,EAAO,EAAIuC,EAChBG,EAAK1C,EAAO,EAAIwC,EAChBxC,EAAO,EAAIuC,GAAUE,EAAK5B,EAAM6B,EAAK5B,GACrCd,EAAO,EAAIwC,GAAUC,EAAK3B,EAAM4B,EAAK7B,GAGzC,OAAOhB,EACf,EASIwB,EAAS,SAAW,SAASxB,EAAUI,EAAO,CAO1C,QANIsC,EAAStC,EAAM,EACfuC,EAASvC,EAAM,EACfmC,EAAiBvC,EAAS,OAC1BG,EAASH,EAASuC,EAAiB,CAAC,EACpCO,EAEK,EAAI,EAAG,EAAIP,EAAgB,IAAK,CAGrC,GAFAO,EAAa9C,EAAS,CAAC,GAElB0C,EAASvC,EAAO,IAAM2C,EAAW,EAAI3C,EAAO,IAC1CwC,EAASxC,EAAO,IAAMA,EAAO,EAAI2C,EAAW,GAAK,EACpD,MAAO,GAGX3C,EAAS2C,EAGb,MAAO,EACf,EAUItB,EAAS,MAAQ,SAASxB,EAAU+C,EAAQC,EAAQ5C,EAAO,CACvD,GAAI2C,IAAW,GAAKC,IAAW,EAC3B,OAAOhD,EAEXI,EAAQA,GAASoB,EAAS,OAAOxB,CAAQ,EAKzC,QAHIG,EACA8C,EAEKzF,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IACjC2C,EAASH,EAASxC,CAAC,EACnByF,EAAQtC,EAAO,IAAIR,EAAQC,CAAK,EAChCJ,EAASxC,CAAC,EAAE,EAAI4C,EAAM,EAAI6C,EAAM,EAAIF,EACpC/C,EAASxC,CAAC,EAAE,EAAI4C,EAAM,EAAI6C,EAAM,EAAID,EAGxC,OAAOhD,CACf,EAYIwB,EAAS,QAAU,SAASxB,EAAUkD,EAAQC,EAASC,EAAYC,EAAY,CACvE,OAAOH,GAAW,SAClBA,EAAS,CAACA,CAAM,EAEhBA,EAASA,GAAU,CAAC,CAAC,EAIzBC,EAAW,OAAOA,EAAY,IAAeA,EAAU,GACvDC,EAAaA,GAAc,EAC3BC,EAAaA,GAAc,GAI3B,QAFIC,EAAc,CAAA,EAET9F,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IAAK,CACtC,IAAI+F,EAAavD,EAASxC,EAAI,GAAK,EAAIA,EAAI,EAAIwC,EAAS,OAAS,CAAC,EAC9DG,EAASH,EAASxC,CAAC,EACnBsF,EAAa9C,GAAUxC,EAAI,GAAKwC,EAAS,MAAM,EAC/CwD,EAAgBN,EAAO1F,EAAI0F,EAAO,OAAS1F,EAAI0F,EAAO,OAAS,CAAC,EAEpE,GAAIM,IAAkB,EAAG,CACrBF,EAAY,KAAKnD,CAAM,EACvB,SAGJ,IAAIsD,EAAa9C,EAAO,UAAU,CAC9B,EAAGR,EAAO,EAAIoD,EAAW,EACzB,EAAGA,EAAW,EAAIpD,EAAO,CACzC,CAAa,EAEGuD,EAAa/C,EAAO,UAAU,CAC9B,EAAGmC,EAAW,EAAI3C,EAAO,EACzB,EAAGA,EAAO,EAAI2C,EAAW,CACzC,CAAa,EAEGa,EAAiB,KAAK,KAAK,EAAI,KAAK,IAAIH,EAAe,CAAC,CAAC,EACzDI,EAAejD,EAAO,KAAKxD,EAAO,MAAMsG,CAAU,EAAGD,CAAa,EAClEK,EAAYlD,EAAO,UAAUA,EAAO,KAAKA,EAAO,IAAI8C,EAAYC,CAAU,EAAG,EAAG,CAAC,EACjFI,EAAenD,EAAO,IAAIR,EAAQQ,EAAO,KAAKkD,EAAWF,CAAc,CAAC,EAExEI,EAAYZ,EAEZA,IAAY,KAEZY,EAAY,KAAK,IAAIP,EAAe,GAAI,EAAI,MAGhDO,EAAY5G,EAAO,MAAM4G,EAAWX,EAAYC,CAAU,EAGtDU,EAAY,IAAM,IAClBA,GAAa,GAKjB,QAHIC,EAAQ,KAAK,KAAKrD,EAAO,IAAI8C,EAAYC,CAAU,CAAC,EACpDO,EAAQD,EAAQD,EAEX5F,EAAI,EAAGA,EAAI4F,EAAW5F,IAC3BmF,EAAY,KAAK3C,EAAO,IAAIA,EAAO,OAAOiD,EAAcK,EAAQ9F,CAAC,EAAG2F,CAAY,CAAC,EAIzF,OAAOR,CACf,EAQI9B,EAAS,cAAgB,SAASxB,EAAU,CACxC,IAAI8B,EAASN,EAAS,KAAKxB,CAAQ,EAEnC,OAAAA,EAAS,KAAK,SAASkE,EAASC,EAAS,CACrC,OAAOxD,EAAO,MAAMmB,EAAQoC,CAAO,EAAIvD,EAAO,MAAMmB,EAAQqC,CAAO,CAC/E,CAAS,EAEMnE,CACf,EAQIwB,EAAS,SAAW,SAASxB,EAAU,CAInC,IAAIoE,EAAO,EACP9B,EAAItC,EAAS,OACbxC,EACAW,EACAkG,EACAC,EAEJ,GAAIhC,EAAI,EACJ,OAAO,KAEX,IAAK9E,EAAI,EAAGA,EAAI8E,EAAG9E,IAYf,GAXAW,GAAKX,EAAI,GAAK8E,EACd+B,GAAK7G,EAAI,GAAK8E,EACdgC,GAAKtE,EAAS7B,CAAC,EAAE,EAAI6B,EAASxC,CAAC,EAAE,IAAMwC,EAASqE,CAAC,EAAE,EAAIrE,EAAS7B,CAAC,EAAE,GACnEmG,IAAMtE,EAAS7B,CAAC,EAAE,EAAI6B,EAASxC,CAAC,EAAE,IAAMwC,EAASqE,CAAC,EAAE,EAAIrE,EAAS7B,CAAC,EAAE,GAEhEmG,EAAI,EACJF,GAAQ,EACDE,EAAI,IACXF,GAAQ,GAGRA,IAAS,EACT,MAAO,GAIf,OAAIA,IAAS,EACF,GAEA,IAEnB,EAQI5C,EAAS,KAAO,SAASxB,EAAU,CAG/B,IAAIuE,EAAQ,CAAA,EACRC,EAAQ,CAAA,EACRrE,EACA3C,EAUJ,IAPAwC,EAAWA,EAAS,MAAM,CAAC,EAC3BA,EAAS,KAAK,SAASkE,EAASC,EAAS,CACrC,IAAIvB,EAAKsB,EAAQ,EAAIC,EAAQ,EAC7B,OAAOvB,IAAO,EAAIA,EAAKsB,EAAQ,EAAIC,EAAQ,CACvD,CAAS,EAGI3G,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,GAAK,EAAG,CAGrC,IAFA2C,EAASH,EAASxC,CAAC,EAEZgH,EAAM,QAAU,GACb7D,EAAO,OAAO6D,EAAMA,EAAM,OAAS,CAAC,EAAGA,EAAMA,EAAM,OAAS,CAAC,EAAGrE,CAAM,GAAK,GACjFqE,EAAM,IAAG,EAGbA,EAAM,KAAKrE,CAAM,EAIrB,IAAK3C,EAAIwC,EAAS,OAAS,EAAGxC,GAAK,EAAGA,GAAK,EAAG,CAG1C,IAFA2C,EAASH,EAASxC,CAAC,EAEZ+G,EAAM,QAAU,GACb5D,EAAO,OAAO4D,EAAMA,EAAM,OAAS,CAAC,EAAGA,EAAMA,EAAM,OAAS,CAAC,EAAGpE,CAAM,GAAK,GACjFoE,EAAM,IAAG,EAGbA,EAAM,KAAKpE,CAAM,EAKrB,OAAAoE,EAAM,IAAG,EACTC,EAAM,IAAG,EAEFD,EAAM,OAAOC,CAAK,CACjC,CAEA,GAAC,GAKM,SAASpI,EAAQM,EAASF,EAAqB,CAWtD,IAAIiI,EAAO,CAAA,EAEXrI,EAAO,QAAUqI,EAEjB,IAAIjD,EAAWhF,EAAoB,CAAC,EAChCmE,EAASnE,EAAoB,CAAC,EAC9BkI,EAAWlI,EAAoB,CAAC,EAChCW,EAASX,EAAoB,CAAC,EAC9BuD,EAASvD,EAAoB,CAAC,EAC9BmI,EAAOnI,EAAoB,EAAE,GAEhC,UAAW,CAERiI,EAAK,gBAAkB,GACvBA,EAAK,cAAgB,EACrBA,EAAK,sBAAwB,EAC7BA,EAAK,yBAA2B,GAChCA,EAAK,cAAgB,EACrBA,EAAK,WAAa,IAAO,GAWzBA,EAAK,OAAS,SAASG,EAAS,CAC5B,IAAIC,EAAW,CACX,GAAI1H,EAAO,OAAM,EACjB,KAAM,OACN,MAAO,OACP,MAAO,CAAA,EACP,OAAQ,CAAA,EACR,MAAO,EACP,SAAUqE,EAAS,SAAS,6BAA6B,EACzD,SAAU,CAAE,EAAG,EAAG,EAAG,CAAC,EACtB,MAAO,CAAE,EAAG,EAAG,EAAG,CAAC,EACnB,OAAQ,EACR,gBAAiB,CAAE,EAAG,EAAG,EAAG,CAAC,EAC7B,kBAAmB,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,CAAC,EACzC,cAAe,EACf,MAAO,EACP,aAAc,EACd,SAAU,CAAE,EAAG,EAAG,EAAG,CAAC,EACtB,gBAAiB,EACjB,SAAU,GACV,SAAU,GACV,WAAY,GACZ,OAAQ,EACR,eAAgB,GAChB,QAAS,KACT,YAAa,EACb,SAAU,GACV,eAAgB,GAChB,YAAa,IACb,gBAAiB,CACb,SAAU,EACV,KAAM,WACN,MAAO,GAEX,KAAM,IACN,UAAW,EACX,OAAQ,CACJ,QAAS,GACT,QAAS,EACT,YAAa,KACb,UAAW,KACX,UAAW,KACX,OAAQ,CACJ,OAAQ,EACR,OAAQ,EACR,QAAS,EACT,QAAS,IAGjB,OAAQ,KACR,OAAQ,KACR,QAAS,KACT,aAAc,EACd,aAAc,KACd,UAAW,EACX,OAAQ,KACR,KAAM,KACN,KAAM,EACN,KAAM,EACN,QAAS,EACT,UAAW,mBACX,UAAW,IACvB,EAEYE,EAAOvE,EAAO,OAAO0H,EAAUD,CAAO,EAE1C,OAAAE,EAAgBpD,EAAMkD,CAAO,EAEtBlD,CACf,EAUI+C,EAAK,UAAY,SAASM,EAAgB,CACtC,OAAIA,EACON,EAAK,2BAETA,EAAK,uBACpB,EAQIA,EAAK,aAAe,UAAW,CAC3B,OAAAA,EAAK,cAAgBA,EAAK,eAAiB,EACpCA,EAAK,aACpB,EASI,IAAIK,EAAkB,SAASpD,EAAMkD,EAAS,CAC1CA,EAAUA,GAAW,CAAA,EAGrBH,EAAK,IAAI/C,EAAM,CACX,OAAQA,EAAK,QAAU3B,EAAO,OAAO2B,EAAK,QAAQ,EAClD,aAAcA,EAAK,cAAgBf,EAAO,MAAMe,EAAK,QAAQ,EAC7D,UAAWA,EAAK,WAAaA,EAAK,MAClC,SAAUA,EAAK,SACf,MAAOA,EAAK,OAAS,CAACA,CAAI,EAC1B,SAAUA,EAAK,SACf,WAAYA,EAAK,WACjB,OAAQA,EAAK,QAAUA,CACnC,CAAS,EAEDF,EAAS,OAAOE,EAAK,SAAUA,EAAK,MAAOA,EAAK,QAAQ,EACxDiD,EAAK,OAAOjD,EAAK,KAAMA,EAAK,KAAK,EACjC3B,EAAO,OAAO2B,EAAK,OAAQA,EAAK,SAAUA,EAAK,QAAQ,EAGvD+C,EAAK,IAAI/C,EAAM,CACX,KAAMkD,EAAQ,MAAQlD,EAAK,KAC3B,KAAMkD,EAAQ,MAAQlD,EAAK,KAC3B,KAAMkD,EAAQ,MAAQlD,EAAK,KAC3B,QAASkD,EAAQ,SAAWlD,EAAK,OAC7C,CAAS,EAGD,IAAIsD,EAAoBtD,EAAK,SAAW,UAAYvE,EAAO,OAAO,CAAC,UAAW,UAAW,UAAW,UAAW,SAAS,CAAC,EACrH8H,EAAqBvD,EAAK,SAAW,OAAS,OAC9CwD,EAAmBxD,EAAK,UAAYA,EAAK,OAAO,YAAc,KAAO,EAAI,EAC7EA,EAAK,OAAO,UAAYA,EAAK,OAAO,WAAasD,EACjDtD,EAAK,OAAO,YAAcA,EAAK,OAAO,aAAeuD,EACrDvD,EAAK,OAAO,UAAYA,EAAK,OAAO,WAAawD,EACjDxD,EAAK,OAAO,OAAO,SAAW,EAAEA,EAAK,OAAO,IAAI,EAAIA,EAAK,SAAS,IAAMA,EAAK,OAAO,IAAI,EAAIA,EAAK,OAAO,IAAI,GAC5GA,EAAK,OAAO,OAAO,SAAW,EAAEA,EAAK,OAAO,IAAI,EAAIA,EAAK,SAAS,IAAMA,EAAK,OAAO,IAAI,EAAIA,EAAK,OAAO,IAAI,EACpH,EAUI+C,EAAK,IAAM,SAAS/C,EAAMyD,EAAUtI,EAAO,CACvC,IAAIK,EAEA,OAAOiI,GAAa,WACpBjI,EAAWiI,EACXA,EAAW,CAAA,EACXA,EAASjI,CAAQ,EAAIL,GAGzB,IAAKK,KAAYiI,EACb,GAAK,OAAO,UAAU,eAAe,KAAKA,EAAUjI,CAAQ,EAI5D,OADAL,EAAQsI,EAASjI,CAAQ,EACjBA,EAAQ,CAEhB,IAAK,WACDuH,EAAK,UAAU/C,EAAM7E,CAAK,EAC1B,MACJ,IAAK,aACD6H,EAAS,IAAIhD,EAAM7E,CAAK,EACxB,MACJ,IAAK,OACD4H,EAAK,QAAQ/C,EAAM7E,CAAK,EACxB,MACJ,IAAK,UACD4H,EAAK,WAAW/C,EAAM7E,CAAK,EAC3B,MACJ,IAAK,UACD4H,EAAK,WAAW/C,EAAM7E,CAAK,EAC3B,MACJ,IAAK,WACD4H,EAAK,YAAY/C,EAAM7E,CAAK,EAC5B,MACJ,IAAK,WACD4H,EAAK,YAAY/C,EAAM7E,CAAK,EAC5B,MACJ,IAAK,QACD4H,EAAK,SAAS/C,EAAM7E,CAAK,EACzB,MACJ,IAAK,WACD4H,EAAK,YAAY/C,EAAM7E,CAAK,EAC5B,MACJ,IAAK,kBACD4H,EAAK,mBAAmB/C,EAAM7E,CAAK,EACnC,MACJ,IAAK,QACD4H,EAAK,SAAS/C,EAAM7E,CAAK,EACzB,MACJ,IAAK,eACD4H,EAAK,gBAAgB/C,EAAM7E,CAAK,EAChC,MACJ,IAAK,QACD4H,EAAK,SAAS/C,EAAM7E,CAAK,EACzB,MACJ,IAAK,SACD4H,EAAK,UAAU/C,EAAM7E,CAAK,EAC1B,MACJ,QACI6E,EAAKxE,CAAQ,EAAIL,EAIjC,EAQI4H,EAAK,UAAY,SAAS/C,EAAM0D,EAAU,CACtC,QAAS,EAAI,EAAG,EAAI1D,EAAK,MAAM,OAAQ,IAAK,CACxC,IAAI2D,EAAO3D,EAAK,MAAM,CAAC,EACvB2D,EAAK,SAAWD,EAEZA,GACAC,EAAK,UAAY,CACb,YAAaA,EAAK,YAClB,SAAUA,EAAK,SACf,KAAMA,EAAK,KACX,QAASA,EAAK,QACd,QAASA,EAAK,QACd,YAAaA,EAAK,YAClB,eAAgBA,EAAK,cACzC,EAEgBA,EAAK,YAAc,EACnBA,EAAK,SAAW,EAChBA,EAAK,KAAOA,EAAK,QAAUA,EAAK,QAAU,IAC1CA,EAAK,YAAcA,EAAK,eAAiB,EAEzCA,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,UAAYA,EAAK,MACtBA,EAAK,gBAAkB,EACvBA,EAAK,MAAQ,EACbA,EAAK,aAAe,EACpBA,EAAK,OAAS,GACPA,EAAK,YACZA,EAAK,YAAcA,EAAK,UAAU,YAClCA,EAAK,SAAWA,EAAK,UAAU,SAC/BA,EAAK,KAAOA,EAAK,UAAU,KAC3BA,EAAK,QAAUA,EAAK,UAAU,QAC9BA,EAAK,QAAUA,EAAK,UAAU,QAC9BA,EAAK,YAAcA,EAAK,UAAU,YAClCA,EAAK,eAAiBA,EAAK,UAAU,eAErCA,EAAK,UAAY,MAGjC,EAQIZ,EAAK,QAAU,SAAS/C,EAAMQ,EAAM,CAChC,IAAIoD,EAAS5D,EAAK,SAAWA,EAAK,KAAO,GACzCA,EAAK,QAAU4D,GAAUpD,EAAO,GAChCR,EAAK,eAAiB,EAAIA,EAAK,QAE/BA,EAAK,KAAOQ,EACZR,EAAK,YAAc,EAAIA,EAAK,KAC5BA,EAAK,QAAUA,EAAK,KAAOA,EAAK,IACxC,EAQI+C,EAAK,WAAa,SAAS/C,EAAM6D,EAAS,CACtCd,EAAK,QAAQ/C,EAAM6D,EAAU7D,EAAK,IAAI,EACtCA,EAAK,QAAU6D,CACvB,EASId,EAAK,WAAa,SAAS/C,EAAM8D,EAAS,CACtC9D,EAAK,QAAU8D,EACf9D,EAAK,eAAiB,EAAIA,EAAK,OACvC,EAcI+C,EAAK,YAAc,SAAS/C,EAAM1B,EAAU,CAEpCA,EAAS,CAAC,EAAE,OAAS0B,EACrBA,EAAK,SAAW1B,EAEhB0B,EAAK,SAAWF,EAAS,OAAOxB,EAAU0B,CAAI,EAIlDA,EAAK,KAAOiD,EAAK,aAAajD,EAAK,QAAQ,EAC3CA,EAAK,KAAOF,EAAS,KAAKE,EAAK,QAAQ,EACvC+C,EAAK,QAAQ/C,EAAMA,EAAK,QAAUA,EAAK,IAAI,EAG3C,IAAII,EAASN,EAAS,OAAOE,EAAK,QAAQ,EAC1CF,EAAS,UAAUE,EAAK,SAAUI,EAAQ,EAAE,EAG5C2C,EAAK,WAAW/C,EAAM+C,EAAK,cAAgBjD,EAAS,QAAQE,EAAK,SAAUA,EAAK,IAAI,CAAC,EAGrFF,EAAS,UAAUE,EAAK,SAAUA,EAAK,QAAQ,EAC/C3B,EAAO,OAAO2B,EAAK,OAAQA,EAAK,SAAUA,EAAK,QAAQ,CAC/D,EAYI+C,EAAK,SAAW,SAAS/C,EAAMzD,EAAOwH,EAAU,CAC5C,IAAIjI,EAQJ,IALAS,EAAQA,EAAM,MAAM,CAAC,EACrByD,EAAK,MAAM,OAAS,EACpBA,EAAK,MAAM,KAAKA,CAAI,EACpBA,EAAK,OAASA,EAETlE,EAAI,EAAGA,EAAIS,EAAM,OAAQT,IAAK,CAC/B,IAAI6H,EAAOpH,EAAMT,CAAC,EACd6H,IAAS3D,IACT2D,EAAK,OAAS3D,EACdA,EAAK,MAAM,KAAK2D,CAAI,GAI5B,GAAI3D,EAAK,MAAM,SAAW,EAM1B,IAHA+D,EAAW,OAAOA,EAAa,IAAcA,EAAW,GAGpDA,EAAU,CACV,IAAIzF,EAAW,CAAA,EACf,IAAKxC,EAAI,EAAGA,EAAIS,EAAM,OAAQT,IAC1BwC,EAAWA,EAAS,OAAO/B,EAAMT,CAAC,EAAE,QAAQ,EAGhDgE,EAAS,cAAcxB,CAAQ,EAE/B,IAAI0F,EAAOlE,EAAS,KAAKxB,CAAQ,EAC7B2F,EAAanE,EAAS,OAAOkE,CAAI,EAErCjB,EAAK,YAAY/C,EAAMgE,CAAI,EAC3BlE,EAAS,UAAUE,EAAK,SAAUiE,CAAU,EAIhD,IAAIC,EAAQnB,EAAK,iBAAiB/C,CAAI,EAEtCA,EAAK,KAAOkE,EAAM,KAClBlE,EAAK,OAASA,EACdA,EAAK,SAAS,EAAIkE,EAAM,OAAO,EAC/BlE,EAAK,SAAS,EAAIkE,EAAM,OAAO,EAC/BlE,EAAK,aAAa,EAAIkE,EAAM,OAAO,EACnClE,EAAK,aAAa,EAAIkE,EAAM,OAAO,EAEnCnB,EAAK,QAAQ/C,EAAMkE,EAAM,IAAI,EAC7BnB,EAAK,WAAW/C,EAAMkE,EAAM,OAAO,EACnCnB,EAAK,YAAY/C,EAAMkE,EAAM,MAAM,EAC3C,EAaInB,EAAK,UAAY,SAAS/C,EAAMI,EAAQ+D,EAAU,CACzCA,GAMDnE,EAAK,aAAa,GAAKI,EAAO,EAC9BJ,EAAK,aAAa,GAAKI,EAAO,EAC9BJ,EAAK,SAAS,GAAKI,EAAO,EAC1BJ,EAAK,SAAS,GAAKI,EAAO,IAR1BJ,EAAK,aAAa,EAAII,EAAO,GAAKJ,EAAK,SAAS,EAAIA,EAAK,aAAa,GACtEA,EAAK,aAAa,EAAII,EAAO,GAAKJ,EAAK,SAAS,EAAIA,EAAK,aAAa,GACtEA,EAAK,SAAS,EAAII,EAAO,EACzBJ,EAAK,SAAS,EAAII,EAAO,EAOrC,EAUI2C,EAAK,YAAc,SAAS/C,EAAMlB,EAAUsF,EAAgB,CACxD,IAAI7C,EAAQtC,EAAO,IAAIH,EAAUkB,EAAK,QAAQ,EAE1CoE,GACApE,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,SAAS,EAAIuB,EAAM,EACxBvB,EAAK,SAAS,EAAIuB,EAAM,EACxBvB,EAAK,MAAQf,EAAO,UAAUsC,CAAK,IAEnCvB,EAAK,aAAa,GAAKuB,EAAM,EAC7BvB,EAAK,aAAa,GAAKuB,EAAM,GAGjC,QAASzF,EAAI,EAAGA,EAAIkE,EAAK,MAAM,OAAQlE,IAAK,CACxC,IAAI6H,EAAO3D,EAAK,MAAMlE,CAAC,EACvB6H,EAAK,SAAS,GAAKpC,EAAM,EACzBoC,EAAK,SAAS,GAAKpC,EAAM,EACzBzB,EAAS,UAAU6D,EAAK,SAAUpC,CAAK,EACvClD,EAAO,OAAOsF,EAAK,OAAQA,EAAK,SAAU3D,EAAK,QAAQ,EAEnE,EAUI+C,EAAK,SAAW,SAAS/C,EAAMZ,EAAOgF,EAAgB,CAClD,IAAI7C,EAAQnC,EAAQY,EAAK,MAErBoE,GACApE,EAAK,UAAYA,EAAK,MACtBA,EAAK,gBAAkBuB,EACvBvB,EAAK,aAAe,KAAK,IAAIuB,CAAK,GAElCvB,EAAK,WAAauB,EAGtB,QAASzF,EAAI,EAAGA,EAAIkE,EAAK,MAAM,OAAQlE,IAAK,CACxC,IAAI6H,EAAO3D,EAAK,MAAMlE,CAAC,EACvB6H,EAAK,OAASpC,EACdzB,EAAS,OAAO6D,EAAK,SAAUpC,EAAOvB,EAAK,QAAQ,EACnDiD,EAAK,OAAOU,EAAK,KAAMpC,CAAK,EAC5BlD,EAAO,OAAOsF,EAAK,OAAQA,EAAK,SAAU3D,EAAK,QAAQ,EACnDlE,EAAI,GACJmD,EAAO,YAAY0E,EAAK,SAAUpC,EAAOvB,EAAK,SAAU2D,EAAK,QAAQ,EAGrF,EASIZ,EAAK,YAAc,SAAS/C,EAAMxB,EAAU,CACxC,IAAI6F,EAAYrE,EAAK,UAAY+C,EAAK,WACtC/C,EAAK,aAAa,EAAIA,EAAK,SAAS,EAAIxB,EAAS,EAAI6F,EACrDrE,EAAK,aAAa,EAAIA,EAAK,SAAS,EAAIxB,EAAS,EAAI6F,EACrDrE,EAAK,SAAS,GAAKA,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAKqE,EAC5DrE,EAAK,SAAS,GAAKA,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAKqE,EAC5DrE,EAAK,MAAQf,EAAO,UAAUe,EAAK,QAAQ,CACnD,EAQI+C,EAAK,YAAc,SAAS/C,EAAM,CAC9B,IAAIqE,EAAYtB,EAAK,WAAa/C,EAAK,UAEvC,MAAO,CACH,GAAIA,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAKqE,EAC7C,GAAIrE,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAKqE,CACzD,CACA,EASItB,EAAK,SAAW,SAAS/C,EAAM,CAC3B,OAAOf,EAAO,UAAU8D,EAAK,YAAY/C,CAAI,CAAC,CACtD,EASI+C,EAAK,SAAW,SAAS/C,EAAMsE,EAAO,CAClCvB,EAAK,YAAY/C,EAAMf,EAAO,KAAKA,EAAO,UAAU8D,EAAK,YAAY/C,CAAI,CAAC,EAAGsE,CAAK,CAAC,CAC3F,EASIvB,EAAK,mBAAqB,SAAS/C,EAAMxB,EAAU,CAC/C,IAAI6F,EAAYrE,EAAK,UAAY+C,EAAK,WACtC/C,EAAK,UAAYA,EAAK,MAAQxB,EAAW6F,EACzCrE,EAAK,iBAAmBA,EAAK,MAAQA,EAAK,WAAaqE,EACvDrE,EAAK,aAAe,KAAK,IAAIA,EAAK,eAAe,CACzD,EAQI+C,EAAK,mBAAqB,SAAS/C,EAAM,CACrC,OAAQA,EAAK,MAAQA,EAAK,WAAa+C,EAAK,WAAa/C,EAAK,SACtE,EASI+C,EAAK,gBAAkB,SAAS/C,EAAM,CAClC,OAAO,KAAK,IAAI+C,EAAK,mBAAmB/C,CAAI,CAAC,CACrD,EASI+C,EAAK,gBAAkB,SAAS/C,EAAMsE,EAAO,CACzCvB,EAAK,mBAAmB/C,EAAMvE,EAAO,KAAKsH,EAAK,mBAAmB/C,CAAI,CAAC,EAAIsE,CAAK,CACxF,EAUIvB,EAAK,UAAY,SAAS/C,EAAMuE,EAAaH,EAAgB,CACzDrB,EAAK,YAAY/C,EAAMf,EAAO,IAAIe,EAAK,SAAUuE,CAAW,EAAGH,CAAc,CACrF,EAWIrB,EAAK,OAAS,SAAS/C,EAAMwE,EAAU9F,EAAO0F,EAAgB,CAC1D,GAAI,CAAC1F,EACDqE,EAAK,SAAS/C,EAAMA,EAAK,MAAQwE,EAAUJ,CAAc,MACtD,CACH,IAAI9E,EAAM,KAAK,IAAIkF,CAAQ,EACvBjF,EAAM,KAAK,IAAIiF,CAAQ,EACvBtD,EAAKlB,EAAK,SAAS,EAAItB,EAAM,EAC7ByC,EAAKnB,EAAK,SAAS,EAAItB,EAAM,EAEjCqE,EAAK,YAAY/C,EAAM,CACnB,EAAGtB,EAAM,GAAKwC,EAAK5B,EAAM6B,EAAK5B,GAC9B,EAAGb,EAAM,GAAKwC,EAAK3B,EAAM4B,EAAK7B,IAC/B8E,CAAc,EAEjBrB,EAAK,SAAS/C,EAAMA,EAAK,MAAQwE,EAAUJ,CAAc,EAErE,EAUIrB,EAAK,MAAQ,SAAS/C,EAAMqB,EAAQC,EAAQ5C,EAAO,CAC/C,IAAI+F,EAAY,EACZC,EAAe,EAEnBhG,EAAQA,GAASsB,EAAK,SAEtB,QAASlE,EAAI,EAAGA,EAAIkE,EAAK,MAAM,OAAQlE,IAAK,CACxC,IAAI6H,EAAO3D,EAAK,MAAMlE,CAAC,EAGvBgE,EAAS,MAAM6D,EAAK,SAAUtC,EAAQC,EAAQ5C,CAAK,EAGnDiF,EAAK,KAAOV,EAAK,aAAaU,EAAK,QAAQ,EAC3CA,EAAK,KAAO7D,EAAS,KAAK6D,EAAK,QAAQ,EACvCZ,EAAK,QAAQY,EAAM3D,EAAK,QAAU2D,EAAK,IAAI,EAG3C7D,EAAS,UAAU6D,EAAK,SAAU,CAAE,EAAG,CAACA,EAAK,SAAS,EAAG,EAAG,CAACA,EAAK,SAAS,CAAC,CAAE,EAC9EZ,EAAK,WAAWY,EAAMZ,EAAK,cAAgBjD,EAAS,QAAQ6D,EAAK,SAAUA,EAAK,IAAI,CAAC,EACrF7D,EAAS,UAAU6D,EAAK,SAAU,CAAE,EAAGA,EAAK,SAAS,EAAG,EAAGA,EAAK,SAAS,CAAC,CAAE,EAExE7H,EAAI,IACJ2I,GAAad,EAAK,KAClBe,GAAgBf,EAAK,SAIzBA,EAAK,SAAS,EAAIjF,EAAM,GAAKiF,EAAK,SAAS,EAAIjF,EAAM,GAAK2C,EAC1DsC,EAAK,SAAS,EAAIjF,EAAM,GAAKiF,EAAK,SAAS,EAAIjF,EAAM,GAAK4C,EAG1DjD,EAAO,OAAOsF,EAAK,OAAQA,EAAK,SAAU3D,EAAK,QAAQ,EAIvDA,EAAK,MAAM,OAAS,IACpBA,EAAK,KAAOyE,EAEPzE,EAAK,WACN+C,EAAK,QAAQ/C,EAAMA,EAAK,QAAUyE,CAAS,EAC3C1B,EAAK,WAAW/C,EAAM0E,CAAY,IAKtC1E,EAAK,eACDqB,IAAWC,EACXtB,EAAK,cAAgBqB,EAGrBrB,EAAK,aAAe,KAGpC,EASI+C,EAAK,OAAS,SAAS/C,EAAM2E,EAAW,CACpCA,GAAa,OAAOA,EAAc,IAAcA,EAAa,IAAO,IAAO3E,EAAK,UAEhF,IAAI4E,EAAmBD,EAAYA,EAC/BE,EAAa9B,EAAK,gBAAkB4B,GAAa3E,EAAK,WAAa2E,GAAa,EAGhFG,EAAc,EAAI9E,EAAK,aAAe2E,EAAYlJ,EAAO,YACzDsJ,GAAiB/E,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAK6E,EAC1DG,GAAiBhF,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAK6E,EAG9D7E,EAAK,SAAS,EAAK+E,EAAgBD,EAAgB9E,EAAK,MAAM,EAAIA,EAAK,KAAQ4E,EAC/E5E,EAAK,SAAS,EAAKgF,EAAgBF,EAAgB9E,EAAK,MAAM,EAAIA,EAAK,KAAQ4E,EAE/E5E,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,SAAS,GAAKA,EAAK,SAAS,EACjCA,EAAK,SAAS,GAAKA,EAAK,SAAS,EACjCA,EAAK,UAAY2E,EAGjB3E,EAAK,iBAAoBA,EAAK,MAAQA,EAAK,WAAa8E,EAAcD,EAAe7E,EAAK,OAASA,EAAK,QAAW4E,EACnH5E,EAAK,UAAYA,EAAK,MACtBA,EAAK,OAASA,EAAK,gBAGnB,QAASlE,EAAI,EAAGA,EAAIkE,EAAK,MAAM,OAAQlE,IAAK,CACxC,IAAI6H,EAAO3D,EAAK,MAAMlE,CAAC,EAEvBgE,EAAS,UAAU6D,EAAK,SAAU3D,EAAK,QAAQ,EAE3ClE,EAAI,IACJ6H,EAAK,SAAS,GAAK3D,EAAK,SAAS,EACjC2D,EAAK,SAAS,GAAK3D,EAAK,SAAS,GAGjCA,EAAK,kBAAoB,IACzBF,EAAS,OAAO6D,EAAK,SAAU3D,EAAK,gBAAiBA,EAAK,QAAQ,EAClEiD,EAAK,OAAOU,EAAK,KAAM3D,EAAK,eAAe,EACvClE,EAAI,GACJmD,EAAO,YAAY0E,EAAK,SAAU3D,EAAK,gBAAiBA,EAAK,SAAU2D,EAAK,QAAQ,GAI5FtF,EAAO,OAAOsF,EAAK,OAAQA,EAAK,SAAU3D,EAAK,QAAQ,EAEnE,EAOI+C,EAAK,iBAAmB,SAAS/C,EAAM,CACnC,IAAIqE,EAAYtB,EAAK,WAAa/C,EAAK,UACnCiF,EAAejF,EAAK,SAExBiF,EAAa,GAAKjF,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAKqE,EAC3DY,EAAa,GAAKjF,EAAK,SAAS,EAAIA,EAAK,aAAa,GAAKqE,EAC3DrE,EAAK,MAAQ,KAAK,KAAMiF,EAAa,EAAIA,EAAa,EAAMA,EAAa,EAAIA,EAAa,CAAE,EAE5FjF,EAAK,iBAAmBA,EAAK,MAAQA,EAAK,WAAaqE,EACvDrE,EAAK,aAAe,KAAK,IAAIA,EAAK,eAAe,CACzD,EAqBI+C,EAAK,WAAa,SAAS/C,EAAMlB,EAAUoG,EAAO,CAC9C,IAAIC,EAAS,CAAE,EAAGrG,EAAS,EAAIkB,EAAK,SAAS,EAAG,EAAGlB,EAAS,EAAIkB,EAAK,SAAS,CAAC,EAC/EA,EAAK,MAAM,GAAKkF,EAAM,EACtBlF,EAAK,MAAM,GAAKkF,EAAM,EACtBlF,EAAK,QAAUmF,EAAO,EAAID,EAAM,EAAIC,EAAO,EAAID,EAAM,CAC7D,EASInC,EAAK,iBAAmB,SAAS/C,EAAM,CAanC,QARIoF,EAAa,CACb,KAAM,EACN,KAAM,EACN,QAAS,EACT,OAAQ,CAAE,EAAG,EAAG,EAAG,CAAC,CAChC,EAGiB,EAAIpF,EAAK,MAAM,SAAW,EAAI,EAAI,EAAG,EAAIA,EAAK,MAAM,OAAQ,IAAK,CACtE,IAAI2D,EAAO3D,EAAK,MAAM,CAAC,EACnBQ,EAAOmD,EAAK,OAAS,IAAWA,EAAK,KAAO,EAEhDyB,EAAW,MAAQ5E,EACnB4E,EAAW,MAAQzB,EAAK,KACxByB,EAAW,SAAWzB,EAAK,QAC3ByB,EAAW,OAASnG,EAAO,IAAImG,EAAW,OAAQnG,EAAO,KAAK0E,EAAK,SAAUnD,CAAI,CAAC,EAGtF,OAAA4E,EAAW,OAASnG,EAAO,IAAImG,EAAW,OAAQA,EAAW,IAAI,EAE1DA,CACf,CAkkBA,GAAC,GAKM,SAAS1K,EAAQM,EAASF,EAAqB,CAUtD,IAAIuK,EAAS,CAAA,EAEb3K,EAAO,QAAU2K,EAEjB,IAAI5J,EAASX,EAAoB,CAAC,GAEjC,UAAW,CASRuK,EAAO,GAAK,SAAS9J,EAAQ+J,EAAYC,EAAU,CAI/C,QAHIC,EAAQF,EAAW,MAAM,GAAG,EAC5BrK,EAEKa,EAAI,EAAGA,EAAI0J,EAAM,OAAQ1J,IAC9Bb,EAAOuK,EAAM1J,CAAC,EACdP,EAAO,OAASA,EAAO,QAAU,CAAA,EACjCA,EAAO,OAAON,CAAI,EAAIM,EAAO,OAAON,CAAI,GAAK,CAAA,EAC7CM,EAAO,OAAON,CAAI,EAAE,KAAKsK,CAAQ,EAGrC,OAAOA,CACf,EASIF,EAAO,IAAM,SAAS9J,EAAQ+J,EAAYC,EAAU,CAChD,GAAI,CAACD,EAAY,CACb/J,EAAO,OAAS,CAAA,EAChB,OAIA,OAAO+J,GAAe,aACtBC,EAAWD,EACXA,EAAa7J,EAAO,KAAKF,EAAO,MAAM,EAAE,KAAK,GAAG,GAKpD,QAFIiK,EAAQF,EAAW,MAAM,GAAG,EAEvBxJ,EAAI,EAAGA,EAAI0J,EAAM,OAAQ1J,IAAK,CACnC,IAAI2J,EAAYlK,EAAO,OAAOiK,EAAM1J,CAAC,CAAC,EAClC4J,EAAe,CAAA,EAEnB,GAAIH,GAAYE,EACZ,QAAShJ,EAAI,EAAGA,EAAIgJ,EAAU,OAAQhJ,IAC9BgJ,EAAUhJ,CAAC,IAAM8I,GACjBG,EAAa,KAAKD,EAAUhJ,CAAC,CAAC,EAI1ClB,EAAO,OAAOiK,EAAM1J,CAAC,CAAC,EAAI4J,EAEtC,EASIL,EAAO,QAAU,SAAS9J,EAAQ+J,EAAYK,EAAO,CACjD,IAAIH,EACAvK,EACAwK,EACAG,EAEAC,EAAStK,EAAO,OAEpB,GAAIsK,GAAUpK,EAAO,KAAKoK,CAAM,EAAE,OAAS,EAAG,CACrCF,IACDA,EAAQ,CAAA,GAEZH,EAAQF,EAAW,MAAM,GAAG,EAE5B,QAAS,EAAI,EAAG,EAAIE,EAAM,OAAQ,IAI9B,GAHAvK,EAAOuK,EAAM,CAAC,EACdC,EAAYI,EAAO5K,CAAI,EAEnBwK,EAAW,CACXG,EAAanK,EAAO,MAAMkK,EAAO,EAAK,EACtCC,EAAW,KAAO3K,EAClB2K,EAAW,OAASrK,EAEpB,QAASkB,EAAI,EAAGA,EAAIgJ,EAAU,OAAQhJ,IAClCgJ,EAAUhJ,CAAC,EAAE,MAAMlB,EAAQ,CAACqK,CAAU,CAAC,GAK/D,CAEA,GAAC,GAKM,SAASlL,EAAQM,EAASF,EAAqB,CAetD,IAAIgL,EAAY,CAAA,EAEhBpL,EAAO,QAAUoL,EAEjB,IAAIT,EAASvK,EAAoB,CAAC,EAC9BW,EAASX,EAAoB,CAAC,EAC9BuD,EAASvD,EAAoB,CAAC,EAC9BiI,EAAOjI,EAAoB,CAAC,GAE/B,UAAW,CASRgL,EAAU,OAAS,SAAS5C,EAAS,CACjC,OAAOzH,EAAO,OAAO,CACjB,GAAIA,EAAO,OAAM,EACjB,KAAM,YACN,OAAQ,KACR,WAAY,GACZ,OAAQ,CAAA,EACR,YAAa,CAAA,EACb,WAAY,CAAA,EACZ,MAAO,YACP,OAAQ,CAAA,EACR,MAAO,CACH,UAAW,KACX,eAAgB,KAChB,cAAe,OAEpByH,CAAO,CAClB,EAaI4C,EAAU,YAAc,SAASC,EAAWC,EAAYC,EAAeC,EAAgB,CAanF,GAZAH,EAAU,WAAaC,EAEnBA,GAAcD,EAAU,QACxBA,EAAU,MAAM,UAAY,KAC5BA,EAAU,MAAM,eAAiB,KACjCA,EAAU,MAAM,cAAgB,MAGhCE,GAAiBF,EAAU,QAC3BD,EAAU,YAAYC,EAAU,OAAQC,EAAYC,EAAeC,CAAc,EAGjFA,EACA,QAASpK,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAAK,CAClD,IAAIqK,EAAiBJ,EAAU,WAAWjK,CAAC,EAC3CgK,EAAU,YAAYK,EAAgBH,EAAYC,EAAeC,CAAc,EAG/F,EAUIJ,EAAU,IAAM,SAASC,EAAWxK,EAAQ,CACxC,IAAI6K,EAAU,CAAA,EAAG,OAAO7K,CAAM,EAE9B8J,EAAO,QAAQU,EAAW,YAAa,CAAE,OAAQxK,EAAQ,EAEzD,QAASO,EAAI,EAAGA,EAAIsK,EAAQ,OAAQtK,IAAK,CACrC,IAAIJ,EAAM0K,EAAQtK,CAAC,EAEnB,OAAQJ,EAAI,KAAI,CAEhB,IAAK,OAED,GAAIA,EAAI,SAAWA,EAAK,CACpBD,EAAO,KAAK,sFAAsF,EAClG,MAGJqK,EAAU,QAAQC,EAAWrK,CAAG,EAChC,MACJ,IAAK,aACDoK,EAAU,cAAcC,EAAWrK,CAAG,EACtC,MACJ,IAAK,YACDoK,EAAU,aAAaC,EAAWrK,CAAG,EACrC,MACJ,IAAK,kBACDoK,EAAU,cAAcC,EAAWrK,EAAI,UAAU,EACjD,OAKR,OAAA2J,EAAO,QAAQU,EAAW,WAAY,CAAE,OAAQxK,EAAQ,EAEjDwK,CACf,EAYID,EAAU,OAAS,SAASC,EAAWxK,EAAQI,EAAM,CACjD,IAAIyK,EAAU,CAAA,EAAG,OAAO7K,CAAM,EAE9B8J,EAAO,QAAQU,EAAW,eAAgB,CAAE,OAAQxK,EAAQ,EAE5D,QAASO,EAAI,EAAGA,EAAIsK,EAAQ,OAAQtK,IAAK,CACrC,IAAIJ,EAAM0K,EAAQtK,CAAC,EAEnB,OAAQJ,EAAI,KAAI,CAEhB,IAAK,OACDoK,EAAU,WAAWC,EAAWrK,EAAKC,CAAI,EACzC,MACJ,IAAK,aACDmK,EAAU,iBAAiBC,EAAWrK,EAAKC,CAAI,EAC/C,MACJ,IAAK,YACDmK,EAAU,gBAAgBC,EAAWrK,EAAKC,CAAI,EAC9C,MACJ,IAAK,kBACDmK,EAAU,iBAAiBC,EAAWrK,EAAI,UAAU,EACpD,OAKR,OAAA2J,EAAO,QAAQU,EAAW,cAAe,CAAE,OAAQxK,EAAQ,EAEpDwK,CACf,EAUID,EAAU,aAAe,SAASO,EAAYC,EAAY,CACtD,OAAAD,EAAW,WAAW,KAAKC,CAAU,EACrCA,EAAW,OAASD,EACpBP,EAAU,YAAYO,EAAY,GAAM,GAAM,EAAK,EAC5CA,CACf,EAWIP,EAAU,gBAAkB,SAASO,EAAYC,EAAY3K,EAAM,CAC/D,IAAImD,EAAWrD,EAAO,QAAQ4K,EAAW,WAAYC,CAAU,EAK/D,GAJIxH,IAAa,IACbgH,EAAU,kBAAkBO,EAAYvH,CAAQ,EAGhDnD,EACA,QAASG,EAAI,EAAGA,EAAIuK,EAAW,WAAW,OAAQvK,IAC9CgK,EAAU,gBAAgBO,EAAW,WAAWvK,CAAC,EAAGwK,EAAY,EAAI,EAI5E,OAAOD,CACf,EAUIP,EAAU,kBAAoB,SAASC,EAAWjH,EAAU,CACxD,OAAAiH,EAAU,WAAW,OAAOjH,EAAU,CAAC,EACvCgH,EAAU,YAAYC,EAAW,GAAM,GAAM,EAAK,EAC3CA,CACf,EAUID,EAAU,QAAU,SAASC,EAAW/F,EAAM,CAC1C,OAAA+F,EAAU,OAAO,KAAK/F,CAAI,EAC1B8F,EAAU,YAAYC,EAAW,GAAM,GAAM,EAAK,EAC3CA,CACf,EAWID,EAAU,WAAa,SAASC,EAAW/F,EAAMrE,EAAM,CACnD,IAAImD,EAAWrD,EAAO,QAAQsK,EAAU,OAAQ/F,CAAI,EAKpD,GAJIlB,IAAa,IACbgH,EAAU,aAAaC,EAAWjH,CAAQ,EAG1CnD,EACA,QAASG,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAC7CgK,EAAU,WAAWC,EAAU,WAAWjK,CAAC,EAAGkE,EAAM,EAAI,EAIhE,OAAO+F,CACf,EAUID,EAAU,aAAe,SAASC,EAAWjH,EAAU,CACnD,OAAAiH,EAAU,OAAO,OAAOjH,EAAU,CAAC,EACnCgH,EAAU,YAAYC,EAAW,GAAM,GAAM,EAAK,EAC3CA,CACf,EAUID,EAAU,cAAgB,SAASC,EAAWQ,EAAY,CACtD,OAAAR,EAAU,YAAY,KAAKQ,CAAU,EACrCT,EAAU,YAAYC,EAAW,GAAM,GAAM,EAAK,EAC3CA,CACf,EAWID,EAAU,iBAAmB,SAASC,EAAWQ,EAAY5K,EAAM,CAC/D,IAAImD,EAAWrD,EAAO,QAAQsK,EAAU,YAAaQ,CAAU,EAK/D,GAJIzH,IAAa,IACbgH,EAAU,mBAAmBC,EAAWjH,CAAQ,EAGhDnD,EACA,QAASG,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAC7CgK,EAAU,iBAAiBC,EAAU,WAAWjK,CAAC,EAAGyK,EAAY,EAAI,EAI5E,OAAOR,CACf,EAUID,EAAU,mBAAqB,SAASC,EAAWjH,EAAU,CACzD,OAAAiH,EAAU,YAAY,OAAOjH,EAAU,CAAC,EACxCgH,EAAU,YAAYC,EAAW,GAAM,GAAM,EAAK,EAC3CA,CACf,EAUID,EAAU,MAAQ,SAASC,EAAWS,EAAY7K,EAAM,CACpD,GAAIA,EACA,QAASG,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAC7CgK,EAAU,MAAMC,EAAU,WAAWjK,CAAC,EAAG0K,EAAY,EAAI,EAIjE,OAAIA,EACAT,EAAU,OAASA,EAAU,OAAO,OAAO,SAAS/F,EAAM,CAAE,OAAOA,EAAK,SAAW,EAEnF+F,EAAU,OAAO,OAAS,EAG9BA,EAAU,YAAY,OAAS,EAC/BA,EAAU,WAAW,OAAS,EAE9BD,EAAU,YAAYC,EAAW,GAAM,GAAM,EAAK,EAE3CA,CACf,EAQID,EAAU,UAAY,SAASC,EAAW,CACtC,GAAIA,EAAU,OAASA,EAAU,MAAM,UACnC,OAAOA,EAAU,MAAM,UAK3B,QAFIU,EAAS,CAAA,EAAG,OAAOV,EAAU,MAAM,EAE9BjK,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAC7C2K,EAASA,EAAO,OAAOX,EAAU,UAAUC,EAAU,WAAWjK,CAAC,CAAC,CAAC,EAEvE,OAAIiK,EAAU,QACVA,EAAU,MAAM,UAAYU,GAGzBA,CACf,EAQIX,EAAU,eAAiB,SAASC,EAAW,CAC3C,GAAIA,EAAU,OAASA,EAAU,MAAM,eACnC,OAAOA,EAAU,MAAM,eAK3B,QAFIW,EAAc,CAAA,EAAG,OAAOX,EAAU,WAAW,EAExCjK,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAC7C4K,EAAcA,EAAY,OAAOZ,EAAU,eAAeC,EAAU,WAAWjK,CAAC,CAAC,CAAC,EAEtF,OAAIiK,EAAU,QACVA,EAAU,MAAM,eAAiBW,GAG9BA,CACf,EAQIZ,EAAU,cAAgB,SAASC,EAAW,CAC1C,GAAIA,EAAU,OAASA,EAAU,MAAM,cACnC,OAAOA,EAAU,MAAM,cAK3B,QAFIY,EAAa,CAAA,EAAG,OAAOZ,EAAU,UAAU,EAEtCjK,EAAI,EAAGA,EAAIiK,EAAU,WAAW,OAAQjK,IAC7C6K,EAAaA,EAAW,OAAOb,EAAU,cAAcC,EAAU,WAAWjK,CAAC,CAAC,CAAC,EAEnF,OAAIiK,EAAU,QACVA,EAAU,MAAM,cAAgBY,GAG7BA,CACf,EAUIb,EAAU,IAAM,SAASC,EAAWa,EAAIC,EAAM,CAC1C,IAAIT,EACA7K,EAEJ,OAAQsL,EAAI,CACZ,IAAK,OACDT,EAAUN,EAAU,UAAUC,CAAS,EACvC,MACJ,IAAK,aACDK,EAAUN,EAAU,eAAeC,CAAS,EAC5C,MACJ,IAAK,YACDK,EAAUN,EAAU,cAAcC,CAAS,EAAE,OAAOA,CAAS,EAC7D,MAGJ,OAAKK,GAGL7K,EAAS6K,EAAQ,OAAO,SAAS7K,EAAQ,CACrC,OAAOA,EAAO,GAAG,SAAQ,IAAOqL,EAAG,SAAQ,CACvD,CAAS,EAEMrL,EAAO,SAAW,EAAI,KAAOA,EAAO,CAAC,GANjC,IAOnB,EAUIuK,EAAU,KAAO,SAASO,EAAYD,EAASE,EAAY,CACvD,OAAAR,EAAU,OAAOO,EAAYD,CAAO,EACpCN,EAAU,IAAIQ,EAAYF,CAAO,EAC1BC,CACf,EAQIP,EAAU,OAAS,SAASC,EAAW,CAKnC,QAJIK,EAAUN,EAAU,UAAUC,CAAS,EACtC,OAAOD,EAAU,eAAeC,CAAS,CAAC,EAC1C,OAAOD,EAAU,cAAcC,CAAS,CAAC,EAErCjK,EAAI,EAAGA,EAAIsK,EAAQ,OAAQtK,IAChCsK,EAAQtK,CAAC,EAAE,GAAKL,EAAO,OAAM,EAGjC,OAAOsK,CACf,EAUID,EAAU,UAAY,SAASC,EAAWxB,EAAauC,EAAW,CAG9D,QAFIL,EAASK,EAAYhB,EAAU,UAAUC,CAAS,EAAIA,EAAU,OAE3DjK,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAC/BiH,EAAK,UAAU0D,EAAO3K,CAAC,EAAGyI,CAAW,EAGzC,OAAOwB,CACf,EAUID,EAAU,OAAS,SAASC,EAAWvB,EAAU9F,EAAOoI,EAAW,CAK/D,QAJIxH,EAAM,KAAK,IAAIkF,CAAQ,EACvBjF,EAAM,KAAK,IAAIiF,CAAQ,EACvBiC,EAASK,EAAYhB,EAAU,UAAUC,CAAS,EAAIA,EAAU,OAE3DjK,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACfoF,EAAKlB,EAAK,SAAS,EAAItB,EAAM,EAC7ByC,EAAKnB,EAAK,SAAS,EAAItB,EAAM,EAEjCqE,EAAK,YAAY/C,EAAM,CACnB,EAAGtB,EAAM,GAAKwC,EAAK5B,EAAM6B,EAAK5B,GAC9B,EAAGb,EAAM,GAAKwC,EAAK3B,EAAM4B,EAAK7B,EAC9C,CAAa,EAEDyD,EAAK,OAAO/C,EAAMwE,CAAQ,EAG9B,OAAOuB,CACf,EAWID,EAAU,MAAQ,SAASC,EAAW1E,EAAQC,EAAQ5C,EAAOoI,EAAW,CAGpE,QAFIL,EAASK,EAAYhB,EAAU,UAAUC,CAAS,EAAIA,EAAU,OAE3DjK,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACfoF,EAAKlB,EAAK,SAAS,EAAItB,EAAM,EAC7ByC,EAAKnB,EAAK,SAAS,EAAItB,EAAM,EAEjCqE,EAAK,YAAY/C,EAAM,CACnB,EAAGtB,EAAM,EAAIwC,EAAKG,EAClB,EAAG3C,EAAM,EAAIyC,EAAKG,CAClC,CAAa,EAEDyB,EAAK,MAAM/C,EAAMqB,EAAQC,CAAM,EAGnC,OAAOyE,CACf,EAQID,EAAU,OAAS,SAASC,EAAW,CAInC,QAHIU,EAASX,EAAU,UAAUC,CAAS,EACtCzH,EAAW,CAAA,EAENxC,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,GAAK,EAAG,CACvC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACnBwC,EAAS,KAAK0B,EAAK,OAAO,IAAKA,EAAK,OAAO,GAAG,EAGlD,OAAO3B,EAAO,OAAOC,CAAQ,CACrC,CA6IA,GAAC,GAKM,SAAS5D,EAAQM,EAASF,EAAqB,CAQtD,IAAIkI,EAAW,CAAA,EAEftI,EAAO,QAAUsI,EAEjB,IAAID,EAAOjI,EAAoB,CAAC,EAC5BuK,EAASvK,EAAoB,CAAC,EAC9BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAERkI,EAAS,qBAAuB,IAChCA,EAAS,sBAAwB,IACjCA,EAAS,SAAW,GAQpBA,EAAS,OAAS,SAASyD,EAAQlF,EAAO,CAKtC,QAJI8C,EAAY9C,EAAQ9F,EAAO,WAC3BsL,EAAuB/D,EAAS,sBAG3BlH,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACfwI,EAAQvB,EAAK,SAAS/C,CAAI,EAC1BgH,EAAejE,EAAK,gBAAgB/C,CAAI,EACxCiH,EAAS3C,EAAQA,EAAQ0C,EAAeA,EAG5C,GAAIhH,EAAK,MAAM,IAAM,GAAKA,EAAK,MAAM,IAAM,EAAG,CAC1CgD,EAAS,IAAIhD,EAAM,EAAK,EACxB,SAGJ,IAAIkH,EAAY,KAAK,IAAIlH,EAAK,OAAQiH,CAAM,EACxCE,EAAY,KAAK,IAAInH,EAAK,OAAQiH,CAAM,EAG5CjH,EAAK,OAASgD,EAAS,SAAWkE,GAAa,EAAIlE,EAAS,UAAYmE,EAEpEnH,EAAK,eAAiB,GAAKA,EAAK,OAAS+G,GACzC/G,EAAK,cAAgB,EAEjBA,EAAK,cAAgBA,EAAK,eAAiBqE,GAC3CrB,EAAS,IAAIhD,EAAM,EAAI,GAEpBA,EAAK,aAAe,IAC3BA,EAAK,cAAgB,GAGrC,EAOIgD,EAAS,gBAAkB,SAASoE,EAAO,CAIvC,QAHIL,EAAuB/D,EAAS,sBAG3BlH,EAAI,EAAGA,EAAIsL,EAAM,OAAQtL,IAAK,CACnC,IAAIuL,EAAOD,EAAMtL,CAAC,EAGlB,GAAKuL,EAAK,SAGV,KAAIC,EAAYD,EAAK,UACjBE,EAAQD,EAAU,MAAM,OACxBE,EAAQF,EAAU,MAAM,OAG5B,GAAK,EAAAC,EAAM,YAAcC,EAAM,YAAeD,EAAM,UAAYC,EAAM,YAGlED,EAAM,YAAcC,EAAM,YAAY,CACtC,IAAIC,EAAgBF,EAAM,YAAc,CAACA,EAAM,SAAYA,EAAQC,EAC/DE,EAAaD,IAAiBF,EAAQC,EAAQD,EAE9C,CAACE,EAAa,UAAYC,EAAW,OAASX,GAC9C/D,EAAS,IAAIyE,EAAc,EAAK,IAIpD,EAQIzE,EAAS,IAAM,SAAShD,EAAM2H,EAAY,CACtC,IAAIC,EAAc5H,EAAK,WAEnB2H,GACA3H,EAAK,WAAa,GAClBA,EAAK,aAAeA,EAAK,eAEzBA,EAAK,gBAAgB,EAAI,EACzBA,EAAK,gBAAgB,EAAI,EAEzBA,EAAK,aAAa,EAAIA,EAAK,SAAS,EACpCA,EAAK,aAAa,EAAIA,EAAK,SAAS,EAEpCA,EAAK,UAAYA,EAAK,MACtBA,EAAK,MAAQ,EACbA,EAAK,aAAe,EACpBA,EAAK,OAAS,EAET4H,GACDvC,EAAO,QAAQrF,EAAM,YAAY,IAGrCA,EAAK,WAAa,GAClBA,EAAK,aAAe,EAEhB4H,GACAvC,EAAO,QAAQrF,EAAM,UAAU,EAG/C,CAEA,GAAC,GAKM,SAAStF,EAAQM,EAASF,EAAqB,CAYtD,IAAI+M,EAAY,CAAA,EAEhBnN,EAAO,QAAUmN,EAEjB,IAAI/H,EAAWhF,EAAoB,CAAC,EAChCgN,EAAOhN,EAAoB,CAAC,GAE/B,UAAW,CACR,IAAIiN,EAAY,CAAA,EAEZC,EAAa,CACb,QAAS,EACT,KAAM,IACd,EAEQC,EAAa,CACb,QAAS,EACT,KAAM,IACd,EASIJ,EAAU,OAAS,SAASN,EAAOC,EAAO,CACtC,MAAO,CACH,KAAM,KACN,SAAU,GACV,MAAOD,EACP,MAAOC,EACP,QAASD,EAAM,OACf,QAASC,EAAM,OACf,MAAO,EACP,OAAQ,CAAE,EAAG,EAAG,EAAG,CAAC,EACpB,QAAS,CAAE,EAAG,EAAG,EAAG,CAAC,EACrB,YAAa,CAAE,EAAG,EAAG,EAAG,CAAC,EACzB,SAAU,CAAA,CACtB,CACA,EAUIK,EAAU,SAAW,SAASN,EAAOC,EAAOJ,EAAO,CAS/C,GARAS,EAAU,aAAaG,EAAYT,EAAM,SAAUC,EAAM,SAAUD,EAAM,IAAI,EAEzES,EAAW,SAAW,IAI1BH,EAAU,aAAaI,EAAYT,EAAM,SAAUD,EAAM,SAAUC,EAAM,IAAI,EAEzES,EAAW,SAAW,GACtB,OAAO,KAIX,IAAIZ,EAAOD,GAASA,EAAM,MAAMU,EAAK,GAAGP,EAAOC,CAAK,CAAC,EACjDF,EAECD,EAQDC,EAAYD,EAAK,WAPjBC,EAAYO,EAAU,OAAON,EAAOC,CAAK,EACzCF,EAAU,SAAW,GACrBA,EAAU,MAAQC,EAAM,GAAKC,EAAM,GAAKD,EAAQC,EAChDF,EAAU,MAAQC,EAAM,GAAKC,EAAM,GAAKA,EAAQD,EAChDD,EAAU,QAAUA,EAAU,MAAM,OACpCA,EAAU,QAAUA,EAAU,MAAM,QAKxCC,EAAQD,EAAU,MAClBE,EAAQF,EAAU,MAElB,IAAIY,EAEAF,EAAW,QAAUC,EAAW,QAChCC,EAAaF,EAEbE,EAAaD,EAGjB,IAAIE,EAASb,EAAU,OACnBc,EAAWd,EAAU,SACrBe,EAAUH,EAAW,KACrBI,EAAWD,EAAQ,EACnBE,EAAWF,EAAQ,EAGnBC,GAAYd,EAAM,SAAS,EAAID,EAAM,SAAS,GAAKgB,GAAYf,EAAM,SAAS,EAAID,EAAM,SAAS,GAAK,GACtGY,EAAO,EAAIG,EACXH,EAAO,EAAII,IAEXJ,EAAO,EAAI,CAACG,EACZH,EAAO,EAAI,CAACI,GAGhBjB,EAAU,QAAQ,EAAI,CAACa,EAAO,EAC9Bb,EAAU,QAAQ,EAAIa,EAAO,EAE7Bb,EAAU,MAAQY,EAAW,QAE7BZ,EAAU,YAAY,EAAIa,EAAO,EAAIb,EAAU,MAC/CA,EAAU,YAAY,EAAIa,EAAO,EAAIb,EAAU,MAG/C,IAAIkB,EAAYX,EAAU,cAAcN,EAAOC,EAAOW,EAAQ,CAAC,EAC3DM,EAAe,EAYnB,GATI3I,EAAS,SAASyH,EAAM,SAAUiB,EAAU,CAAC,CAAC,IAC9CJ,EAASK,GAAc,EAAID,EAAU,CAAC,GAGtC1I,EAAS,SAASyH,EAAM,SAAUiB,EAAU,CAAC,CAAC,IAC9CJ,EAASK,GAAc,EAAID,EAAU,CAAC,GAItCC,EAAe,EAAG,CAClB,IAAIC,EAAYb,EAAU,cAAcL,EAAOD,EAAOY,EAAQ,EAAE,EAE5DrI,EAAS,SAAS0H,EAAM,SAAUkB,EAAU,CAAC,CAAC,IAC9CN,EAASK,GAAc,EAAIC,EAAU,CAAC,GAGtCD,EAAe,GAAK3I,EAAS,SAAS0H,EAAM,SAAUkB,EAAU,CAAC,CAAC,IAClEN,EAASK,GAAc,EAAIC,EAAU,CAAC,GAK9C,OAAID,IAAiB,IACjBL,EAASK,GAAc,EAAID,EAAU,CAAC,GAI1CJ,EAAS,OAASK,EAEXnB,CACf,EAWIO,EAAU,aAAe,SAASrK,EAAQmL,EAAWC,EAAWC,EAAM,CAClE,IAAIC,EAAkBH,EAAU,OAC5BI,EAAkBH,EAAU,OAC5BI,EAAaL,EAAU,CAAC,EAAE,EAC1BM,EAAaN,EAAU,CAAC,EAAE,EAC1BO,EAAaN,EAAU,CAAC,EAAE,EAC1BO,EAAaP,EAAU,CAAC,EAAE,EAC1BQ,EAAaP,EAAK,OAClBQ,EAAa,OAAO,UACpBC,EAAoB,EACpBC,EACAC,EACAC,EACAC,EACA5N,EACAW,EAEJ,IAAKX,EAAI,EAAGA,EAAIsN,EAAYtN,IAAK,CAC7B,IAAI6N,EAAOd,EAAK/M,CAAC,EACb8N,EAAQD,EAAK,EACbE,EAAQF,EAAK,EACbG,EAAOd,EAAaY,EAAQX,EAAaY,EACzCE,EAAOb,EAAaU,EAAQT,EAAaU,EACzCG,EAAOF,EACPG,EAAOF,EAEX,IAAKtN,EAAI,EAAGA,EAAIqM,EAAiBrM,GAAK,EAClCiN,EAAMf,EAAUlM,CAAC,EAAE,EAAImN,EAAQjB,EAAUlM,CAAC,EAAE,EAAIoN,EAE5CH,EAAMM,EACNA,EAAON,EACAA,EAAMI,IACbA,EAAOJ,GAIf,IAAKjN,EAAI,EAAGA,EAAIsM,EAAiBtM,GAAK,EAClCiN,EAAMd,EAAUnM,CAAC,EAAE,EAAImN,EAAQhB,EAAUnM,CAAC,EAAE,EAAIoN,EAE5CH,EAAMO,EACNA,EAAOP,EACAA,EAAMK,IACbA,EAAOL,GAQf,GAJAF,EAAYQ,EAAOD,EACnBN,EAAYQ,EAAOH,EACnBP,EAAUC,EAAYC,EAAYD,EAAYC,EAE1CF,EAAUF,IACVA,EAAaE,EACbD,EAAoBxN,EAEhByN,GAAW,GAEX,MAKZ/L,EAAO,KAAOqL,EAAKS,CAAiB,EACpC9L,EAAO,QAAU6L,CACzB,EAUIxB,EAAU,eAAiB,SAASqC,EAAY5L,EAAUqL,EAAM,CAI5D,QAHI/M,EAAM0B,EAAS,CAAC,EAAE,EAAIqL,EAAK,EAAIrL,EAAS,CAAC,EAAE,EAAIqL,EAAK,EACpD9M,EAAMD,EAEDd,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,GAAK,EAAG,CACzC,IAAI4N,EAAMpL,EAASxC,CAAC,EAAE,EAAI6N,EAAK,EAAIrL,EAASxC,CAAC,EAAE,EAAI6N,EAAK,EAEpDD,EAAM7M,EACNA,EAAM6M,EACCA,EAAM9M,IACbA,EAAM8M,GAIdQ,EAAW,IAAMtN,EACjBsN,EAAW,IAAMrN,CACzB,EAYIgL,EAAU,cAAgB,SAASN,EAAOC,EAAOW,EAAQgC,EAAW,CAChE,IAAI7L,EAAWkJ,EAAM,SACjB3G,EAAiBvC,EAAS,OAC1B8L,EAAiB7C,EAAM,SAAS,EAChC8C,EAAiB9C,EAAM,SAAS,EAChC+C,EAAUnC,EAAO,EAAIgC,EACrBI,EAAUpC,EAAO,EAAIgC,EACrBK,EAAkB,OAAO,UACzBhI,EACAC,EACAgI,EACAC,EACAjO,EAGJ,IAAKA,EAAI,EAAGA,EAAIoE,EAAgBpE,GAAK,EACjCgG,EAAUnE,EAAS7B,CAAC,EACpBiO,EAAWJ,GAAWF,EAAiB3H,EAAQ,GAAK8H,GAAWF,EAAiB5H,EAAQ,GAGpFiI,EAAWF,IACXA,EAAkBE,EAClBlI,EAAUC,GAUlB,OALAgI,EAAUnM,GAAUuC,EAAiB2B,EAAQ,MAAQ,GAAK3B,CAAc,EACxE2J,EAAkBF,GAAWF,EAAiBK,EAAQ,GAAKF,GAAWF,EAAiBI,EAAQ,GAG/FhI,EAAUnE,GAAUkE,EAAQ,MAAQ,GAAK3B,CAAc,EACnDyJ,GAAWF,EAAiB3H,EAAQ,GAAK8H,GAAWF,EAAiB5H,EAAQ,GAAK+H,GAClFzC,EAAU,CAAC,EAAIvF,EACfuF,EAAU,CAAC,EAAItF,EAERsF,IAGXA,EAAU,CAAC,EAAIvF,EACfuF,EAAU,CAAC,EAAI0C,EAER1C,EACf,CA8FA,GAAC,GAKM,SAASrN,EAAQM,EAASF,EAAqB,CAQtD,IAAIgN,EAAO,CAAA,EAEXpN,EAAO,QAAUoN,EAEjB,IAAI6C,EAAU7P,EAAoB,EAAE,GAEnC,UAAW,CASRgN,EAAK,OAAS,SAASR,EAAWsD,EAAW,CACzC,IAAIrD,EAAQD,EAAU,MAClBE,EAAQF,EAAU,MAElBD,EAAO,CACP,GAAIS,EAAK,GAAGP,EAAOC,CAAK,EACxB,MAAOD,EACP,MAAOC,EACP,UAAWF,EACX,SAAU,CAAA,EACV,eAAgB,CAAA,EAChB,WAAY,EACZ,SAAU,GACV,gBAAiB,GACjB,SAAUC,EAAM,UAAYC,EAAM,SAClC,YAAaoD,EACb,YAAaA,EACb,YAAa,EACb,SAAU,EACV,eAAgB,EAChB,YAAa,EACb,KAAM,CAClB,EAEQ,OAAA9C,EAAK,OAAOT,EAAMC,EAAWsD,CAAS,EAE/BvD,CACf,EASIS,EAAK,OAAS,SAAST,EAAMC,EAAWsD,EAAW,CAC/C,IAAIC,EAAWxD,EAAK,SAChBe,EAAWd,EAAU,SACrBwD,EAAiBzD,EAAK,eACtB0D,EAAUzD,EAAU,QACpB0D,EAAU1D,EAAU,QACpB2D,EAAwBF,EAAQ,SAAS,OAE7C1D,EAAK,SAAW,GAChBA,EAAK,YAAcuD,EACnBvD,EAAK,UAAYC,EACjBD,EAAK,WAAaC,EAAU,MAC5BD,EAAK,YAAc0D,EAAQ,YAAcC,EAAQ,YACjD3D,EAAK,SAAW0D,EAAQ,SAAWC,EAAQ,SAAWD,EAAQ,SAAWC,EAAQ,SACjF3D,EAAK,eAAiB0D,EAAQ,eAAiBC,EAAQ,eAAiBD,EAAQ,eAAiBC,EAAQ,eACzG3D,EAAK,YAAc0D,EAAQ,YAAcC,EAAQ,YAAcD,EAAQ,YAAcC,EAAQ,YAC7F3D,EAAK,KAAO0D,EAAQ,KAAOC,EAAQ,KAAOD,EAAQ,KAAOC,EAAQ,KAEjE1D,EAAU,KAAOD,EACjByD,EAAe,OAAS,EAExB,QAAShP,EAAI,EAAGA,EAAIsM,EAAS,OAAQtM,IAAK,CACtC,IAAIoP,EAAU9C,EAAStM,CAAC,EACpBqP,EAAYD,EAAQ,OAASH,EAAUG,EAAQ,MAAQD,EAAwBC,EAAQ,MACvFE,EAAUP,EAASM,CAAS,EAE5BC,EACAN,EAAe,KAAKM,CAAO,EAE3BN,EAAe,KAAKD,EAASM,CAAS,EAAIR,EAAQ,OAAOO,CAAO,CAAC,EAGjF,EASIpD,EAAK,UAAY,SAAST,EAAMgE,EAAUT,EAAW,CAC7CS,GACAhE,EAAK,SAAW,GAChBA,EAAK,YAAcuD,IAEnBvD,EAAK,SAAW,GAChBA,EAAK,eAAe,OAAS,EAEzC,EASIS,EAAK,GAAK,SAASP,EAAOC,EAAO,CAC7B,OAAID,EAAM,GAAKC,EAAM,GACV,IAAMD,EAAM,GAAK,IAAMC,EAAM,GAE7B,IAAMA,EAAM,GAAK,IAAMD,EAAM,EAEhD,CAEA,GAAC,GAKM,SAAS7M,EAAQM,EAASF,EAAqB,CAYtD,IAAIwQ,EAAa,CAAA,EAEjB5Q,EAAO,QAAU4Q,EAEjB,IAAIxL,EAAWhF,EAAoB,CAAC,EAChCmE,EAASnE,EAAoB,CAAC,EAC9BkI,EAAWlI,EAAoB,CAAC,EAChCuD,EAASvD,EAAoB,CAAC,EAC9BmI,EAAOnI,EAAoB,EAAE,EAC7BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAERwQ,EAAW,SAAW,GACtBA,EAAW,cAAgB,EAC3BA,EAAW,WAAa,KAaxBA,EAAW,OAAS,SAASpI,EAAS,CAClC,IAAIqD,EAAarD,EAGbqD,EAAW,OAAS,CAACA,EAAW,SAChCA,EAAW,OAAS,CAAE,EAAG,EAAG,EAAG,CAAC,GAChCA,EAAW,OAAS,CAACA,EAAW,SAChCA,EAAW,OAAS,CAAE,EAAG,EAAG,EAAG,CAAC,GAGpC,IAAIgF,EAAgBhF,EAAW,MAAQtH,EAAO,IAAIsH,EAAW,MAAM,SAAUA,EAAW,MAAM,EAAIA,EAAW,OACzGiF,EAAgBjF,EAAW,MAAQtH,EAAO,IAAIsH,EAAW,MAAM,SAAUA,EAAW,MAAM,EAAIA,EAAW,OACzGkF,EAASxM,EAAO,UAAUA,EAAO,IAAIsM,EAAeC,CAAa,CAAC,EAEtEjF,EAAW,OAAS,OAAOA,EAAW,OAAW,IAAcA,EAAW,OAASkF,EAGnFlF,EAAW,GAAKA,EAAW,IAAM9K,EAAO,OAAM,EAC9C8K,EAAW,MAAQA,EAAW,OAAS,aACvCA,EAAW,KAAO,aAClBA,EAAW,UAAYA,EAAW,YAAcA,EAAW,OAAS,EAAI,EAAI,IAC5EA,EAAW,QAAUA,EAAW,SAAW,EAC3CA,EAAW,iBAAmBA,EAAW,kBAAoB,EAC7DA,EAAW,OAASA,EAAW,MAAQA,EAAW,MAAM,MAAQA,EAAW,OAC3EA,EAAW,OAASA,EAAW,MAAQA,EAAW,MAAM,MAAQA,EAAW,OAC3EA,EAAW,OAAS,CAAA,EAGpB,IAAImF,EAAS,CACT,QAAS,GACT,UAAW,EACX,YAAa,UACb,KAAM,OACN,QAAS,EACrB,EAEQ,OAAInF,EAAW,SAAW,GAAKA,EAAW,UAAY,IAClDmF,EAAO,KAAO,MACdA,EAAO,QAAU,IACVnF,EAAW,UAAY,KAC9BmF,EAAO,KAAO,UAGlBnF,EAAW,OAAS9K,EAAO,OAAOiQ,EAAQnF,EAAW,MAAM,EAEpDA,CACf,EAQI+E,EAAW,YAAc,SAAS7E,EAAQ,CACtC,QAAS3K,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,GAAK,EAAG,CACvC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACf6P,EAAU3L,EAAK,kBAEfA,EAAK,UAAa2L,EAAQ,IAAM,GAAKA,EAAQ,IAAM,GAAKA,EAAQ,QAAU,IAI9E3L,EAAK,SAAS,GAAK2L,EAAQ,EAC3B3L,EAAK,SAAS,GAAK2L,EAAQ,EAC3B3L,EAAK,OAAS2L,EAAQ,OAElC,EASIL,EAAW,SAAW,SAAS5E,EAAanF,EAAO,CAI/C,QAHI8C,EAAY5I,EAAO,MAAM8F,EAAQ9F,EAAO,WAAY,EAAG,CAAC,EAGnD,EAAI,EAAG,EAAIiL,EAAY,OAAQ,GAAK,EAAG,CAC5C,IAAIH,EAAaG,EAAY,CAAC,EAC1BkF,EAAS,CAACrF,EAAW,OAAUA,EAAW,OAASA,EAAW,MAAM,SACpEsF,EAAS,CAACtF,EAAW,OAAUA,EAAW,OAASA,EAAW,MAAM,UAEpEqF,GAAUC,IACVP,EAAW,MAAM5E,EAAY,CAAC,EAAGrC,CAAS,EAKlD,IAAK,EAAI,EAAG,EAAIqC,EAAY,OAAQ,GAAK,EACrCH,EAAaG,EAAY,CAAC,EAC1BkF,EAAS,CAACrF,EAAW,OAAUA,EAAW,OAASA,EAAW,MAAM,SACpEsF,EAAS,CAACtF,EAAW,OAAUA,EAAW,OAASA,EAAW,MAAM,SAEhE,CAACqF,GAAU,CAACC,GACZP,EAAW,MAAM5E,EAAY,CAAC,EAAGrC,CAAS,CAG1D,EASIiH,EAAW,MAAQ,SAAS/E,EAAYlC,EAAW,CAC/C,IAAIkD,EAAQhB,EAAW,MACnBiB,EAAQjB,EAAW,MACnBuF,EAASvF,EAAW,OACpBwF,EAASxF,EAAW,OAExB,GAAI,GAACgB,GAAS,CAACC,GAIf,CAAID,GAAS,CAACA,EAAM,WAChBtI,EAAO,OAAO6M,EAAQvE,EAAM,MAAQhB,EAAW,OAAQuF,CAAM,EAC7DvF,EAAW,OAASgB,EAAM,OAI1BC,GAAS,CAACA,EAAM,WAChBvI,EAAO,OAAO8M,EAAQvE,EAAM,MAAQjB,EAAW,OAAQwF,CAAM,EAC7DxF,EAAW,OAASiB,EAAM,OAG9B,IAAIwE,EAAcF,EACdG,EAAcF,EAKlB,GAHIxE,IAAOyE,EAAc/M,EAAO,IAAIsI,EAAM,SAAUuE,CAAM,GACtDtE,IAAOyE,EAAchN,EAAO,IAAIuI,EAAM,SAAUuE,CAAM,GAEtD,GAACC,GAAe,CAACC,GAGrB,KAAI1K,EAAQtC,EAAO,IAAI+M,EAAaC,CAAW,EAC3CC,EAAgBjN,EAAO,UAAUsC,CAAK,EAGtC2K,EAAgBZ,EAAW,aAC3BY,EAAgBZ,EAAW,YAI/B,IAAIa,GAAcD,EAAgB3F,EAAW,QAAU2F,EACnDE,EAAU7F,EAAW,WAAa,GAAKA,EAAW,SAAW,EAC7D8F,EAAYD,EAAU7F,EAAW,UAAYlC,EACvCkC,EAAW,UAAYlC,EAAYA,EACzCiI,EAAU/F,EAAW,QAAUlC,EAC/Ba,EAAQjG,EAAO,KAAKsC,EAAO4K,EAAaE,CAAS,EACjDE,GAAahF,EAAQA,EAAM,YAAc,IAAMC,EAAQA,EAAM,YAAc,GAC3EgF,GAAgBjF,EAAQA,EAAM,eAAiB,IAAMC,EAAQA,EAAM,eAAiB,GACpFiF,EAAkBF,EAAYC,EAC9BE,EACAC,EACAxE,EACAyE,EACAC,EAEJ,GAAIP,EAAU,EAAG,CACb,IAAIQ,EAAO7N,EAAO,OAAM,EACxBkJ,EAASlJ,EAAO,IAAIsC,EAAO2K,CAAa,EAExCW,EAAmB5N,EAAO,IACtBuI,GAASvI,EAAO,IAAIuI,EAAM,SAAUA,EAAM,YAAY,GAAKsF,EAC3DvF,GAAStI,EAAO,IAAIsI,EAAM,SAAUA,EAAM,YAAY,GAAKuF,CAC3E,EAEYF,EAAiB3N,EAAO,IAAIkJ,EAAQ0E,CAAgB,EAGpDtF,GAAS,CAACA,EAAM,WAChBoF,EAAQpF,EAAM,YAAcgF,EAG5BhF,EAAM,kBAAkB,GAAKrC,EAAM,EAAIyH,EACvCpF,EAAM,kBAAkB,GAAKrC,EAAM,EAAIyH,EAGvCpF,EAAM,SAAS,GAAKrC,EAAM,EAAIyH,EAC9BpF,EAAM,SAAS,GAAKrC,EAAM,EAAIyH,EAG1BL,EAAU,IACV/E,EAAM,aAAa,GAAK+E,EAAUnE,EAAO,EAAIyE,EAAiBD,EAC9DpF,EAAM,aAAa,GAAK+E,EAAUnE,EAAO,EAAIyE,EAAiBD,GAIlED,EAAUzN,EAAO,MAAM6M,EAAQ5G,CAAK,EAAIuH,EAAmBnB,EAAW,cAAgB/D,EAAM,gBAAkB,EAAIhB,EAAW,kBAC7HgB,EAAM,kBAAkB,OAASmF,EACjCnF,EAAM,OAASmF,GAGflF,GAAS,CAACA,EAAM,WAChBmF,EAAQnF,EAAM,YAAc+E,EAG5B/E,EAAM,kBAAkB,GAAKtC,EAAM,EAAIyH,EACvCnF,EAAM,kBAAkB,GAAKtC,EAAM,EAAIyH,EAGvCnF,EAAM,SAAS,GAAKtC,EAAM,EAAIyH,EAC9BnF,EAAM,SAAS,GAAKtC,EAAM,EAAIyH,EAG1BL,EAAU,IACV9E,EAAM,aAAa,GAAK8E,EAAUnE,EAAO,EAAIyE,EAAiBD,EAC9DnF,EAAM,aAAa,GAAK8E,EAAUnE,EAAO,EAAIyE,EAAiBD,GAIlED,EAAUzN,EAAO,MAAM8M,EAAQ7G,CAAK,EAAIuH,EAAmBnB,EAAW,cAAgB9D,EAAM,gBAAkB,EAAIjB,EAAW,kBAC7HiB,EAAM,kBAAkB,OAASkF,EACjClF,EAAM,OAASkF,IAG3B,EAQIpB,EAAW,aAAe,SAAS7E,EAAQ,CACvC,QAAS3K,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACf6P,EAAU3L,EAAK,kBAEnB,GAAI,EAAAA,EAAK,UAAa2L,EAAQ,IAAM,GAAKA,EAAQ,IAAM,GAAKA,EAAQ,QAAU,GAI9E,CAAA3I,EAAS,IAAIhD,EAAM,EAAK,EAGxB,QAASvD,EAAI,EAAGA,EAAIuD,EAAK,MAAM,OAAQvD,IAAK,CACxC,IAAIkH,EAAO3D,EAAK,MAAMvD,CAAC,EAEvBqD,EAAS,UAAU6D,EAAK,SAAUgI,CAAO,EAErClP,EAAI,IACJkH,EAAK,SAAS,GAAKgI,EAAQ,EAC3BhI,EAAK,SAAS,GAAKgI,EAAQ,GAG3BA,EAAQ,QAAU,IAClB7L,EAAS,OAAO6D,EAAK,SAAUgI,EAAQ,MAAO3L,EAAK,QAAQ,EAC3DiD,EAAK,OAAOU,EAAK,KAAMgI,EAAQ,KAAK,EAChClP,EAAI,GACJwC,EAAO,YAAY0E,EAAK,SAAUgI,EAAQ,MAAO3L,EAAK,SAAU2D,EAAK,QAAQ,GAIrFtF,EAAO,OAAOsF,EAAK,OAAQA,EAAK,SAAU3D,EAAK,QAAQ,EAI3D2L,EAAQ,OAASL,EAAW,SAC5BK,EAAQ,GAAKL,EAAW,SACxBK,EAAQ,GAAKL,EAAW,UAEpC,EAQIA,EAAW,YAAc,SAAS/E,EAAY,CAC1C,MAAO,CACH,GAAIA,EAAW,MAAQA,EAAW,MAAM,SAAS,EAAI,IAC9CA,EAAW,OAASA,EAAW,OAAO,EAAI,GACjD,GAAIA,EAAW,MAAQA,EAAW,MAAM,SAAS,EAAI,IAC9CA,EAAW,OAASA,EAAW,OAAO,EAAI,EAC7D,CACA,EAQI+E,EAAW,YAAc,SAAS/E,EAAY,CAC1C,MAAO,CACH,GAAIA,EAAW,MAAQA,EAAW,MAAM,SAAS,EAAI,IAC9CA,EAAW,OAASA,EAAW,OAAO,EAAI,GACjD,GAAIA,EAAW,MAAQA,EAAW,MAAM,SAAS,EAAI,IAC9CA,EAAW,OAASA,EAAW,OAAO,EAAI,EAC7D,CACA,CAwJA,GAAC,GAKM,SAAS7L,EAAQM,EAASF,EAAqB,CAQtD,IAAImI,EAAO,CAAA,EAEXvI,EAAO,QAAUuI,EAEjB,IAAIhE,EAASnE,EAAoB,CAAC,EAC9BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAQRmI,EAAK,aAAe,SAAS3E,EAAU,CAInC,QAHIuK,EAAO,CAAA,EAGF/M,EAAI,EAAGA,EAAIwC,EAAS,OAAQxC,IAAK,CACtC,IAAIW,GAAKX,EAAI,GAAKwC,EAAS,OACvB6J,EAASlJ,EAAO,UAAU,CACtB,EAAGX,EAAS7B,CAAC,EAAE,EAAI6B,EAASxC,CAAC,EAAE,EAC/B,EAAGwC,EAASxC,CAAC,EAAE,EAAIwC,EAAS7B,CAAC,EAAE,CACnD,CAAiB,EACDsQ,EAAY5E,EAAO,IAAM,EAAK,IAAYA,EAAO,EAAIA,EAAO,EAGhE4E,EAAWA,EAAS,QAAQ,CAAC,EAAE,SAAQ,EACvClE,EAAKkE,CAAQ,EAAI5E,EAGrB,OAAO1M,EAAO,OAAOoN,CAAI,CACjC,EAQI5F,EAAK,OAAS,SAAS4F,EAAMzJ,EAAO,CAChC,GAAIA,IAAU,EAMd,QAHIE,EAAM,KAAK,IAAIF,CAAK,EACpBG,EAAM,KAAK,IAAIH,CAAK,EAEftD,EAAI,EAAGA,EAAI+M,EAAK,OAAQ/M,IAAK,CAClC,IAAI6N,EAAOd,EAAK/M,CAAC,EACbkR,EACJA,EAAKrD,EAAK,EAAIrK,EAAMqK,EAAK,EAAIpK,EAC7BoK,EAAK,EAAIA,EAAK,EAAIpK,EAAMoK,EAAK,EAAIrK,EACjCqK,EAAK,EAAIqD,EAErB,CAEA,GAAC,GAKM,SAAStS,EAAQM,EAASF,EAAqB,CAatD,IAAImS,EAAS,CAAA,EAEbvS,EAAO,QAAUuS,EAEjB,IAAInN,EAAWhF,EAAoB,CAAC,EAChCW,EAASX,EAAoB,CAAC,EAC9BiI,EAAOjI,EAAoB,CAAC,EAC5BuD,EAASvD,EAAoB,CAAC,EAC9BmE,EAASnE,EAAoB,CAAC,GAEjC,UAAW,CAcRmS,EAAO,UAAY,SAAS/N,EAAGC,EAAG+N,EAAOC,EAAQjK,EAAS,CACtDA,EAAUA,GAAW,CAAA,EAErB,IAAIkK,EAAY,CACZ,MAAO,iBACP,SAAU,CAAE,EAAGlO,EAAG,EAAGC,CAAC,EACtB,SAAUW,EAAS,SAAS,WAAaoN,EAAQ,QAAUA,EAAQ,IAAMC,EAAS,QAAUA,CAAM,CAC9G,EAEQ,GAAIjK,EAAQ,QAAS,CACjB,IAAImK,EAAUnK,EAAQ,QACtBkK,EAAU,SAAWtN,EAAS,QAAQsN,EAAU,SAAUC,EAAQ,OAC9DA,EAAQ,QAASA,EAAQ,WAAYA,EAAQ,UAAU,EAC3D,OAAOnK,EAAQ,QAGnB,OAAOH,EAAK,OAAOtH,EAAO,OAAO,GAAI2R,EAAWlK,CAAO,CAAC,CAChE,EAeI+J,EAAO,UAAY,SAAS/N,EAAGC,EAAG+N,EAAOC,EAAQG,EAAOpK,EAAS,CAC7DA,EAAUA,GAAW,CAAA,EAErBoK,GAAS,GACT,IAAIC,GAAQ,EAAKD,EAAQ,GAAMJ,EAE3BM,EAAKN,EAAQI,EACbG,EAAKD,EAAKD,EACVG,EAAKD,EAAKD,EACVG,EAEAL,EAAQ,GACRK,EAAe,WAAaH,EAAK,IAAO,CAACL,EAAU,MAAQM,EAAK,IAAO,CAACN,EAAU,MAAQO,EAAK,KAE/FC,EAAe,WAAaF,EAAK,IAAO,CAACN,EAAU,MAAQO,EAAK,KAGpE,IAAIE,EAAY,CACZ,MAAO,iBACP,SAAU,CAAE,EAAG1O,EAAG,EAAGC,CAAC,EACtB,SAAUW,EAAS,SAAS6N,CAAY,CACpD,EAEQ,GAAIzK,EAAQ,QAAS,CACjB,IAAImK,EAAUnK,EAAQ,QACtB0K,EAAU,SAAW9N,EAAS,QAAQ8N,EAAU,SAAUP,EAAQ,OAC9DA,EAAQ,QAASA,EAAQ,WAAYA,EAAQ,UAAU,EAC3D,OAAOnK,EAAQ,QAGnB,OAAOH,EAAK,OAAOtH,EAAO,OAAO,GAAImS,EAAW1K,CAAO,CAAC,CAChE,EAcI+J,EAAO,OAAS,SAAS/N,EAAGC,EAAGqC,EAAQ0B,EAAS2K,EAAU,CACtD3K,EAAUA,GAAW,CAAA,EAErB,IAAI4K,EAAS,CACT,MAAO,cACP,aAActM,CAC1B,EAGQqM,EAAWA,GAAY,GACvB,IAAIE,EAAQ,KAAK,KAAK,KAAK,IAAI,GAAI,KAAK,IAAIF,EAAUrM,CAAM,CAAC,CAAC,EAG9D,OAAIuM,EAAQ,IAAM,IACdA,GAAS,GAENd,EAAO,QAAQ/N,EAAGC,EAAG4O,EAAOvM,EAAQ/F,EAAO,OAAO,CAAA,EAAIqS,EAAQ5K,CAAO,CAAC,CACrF,EAcI+J,EAAO,QAAU,SAAS/N,EAAGC,EAAG4O,EAAOvM,EAAQ0B,EAAS,CAGpD,GAFAA,EAAUA,GAAW,CAAA,EAEjB6K,EAAQ,EACR,OAAOd,EAAO,OAAO/N,EAAGC,EAAGqC,EAAQ0B,CAAO,EAM9C,QAJIX,EAAQ,EAAI,KAAK,GAAKwL,EACtB5R,EAAO,GACPgJ,EAAS5C,EAAQ,GAEZzG,EAAI,EAAGA,EAAIiS,EAAOjS,GAAK,EAAG,CAC/B,IAAIsD,EAAQ+F,EAAUrJ,EAAIyG,EACtByK,EAAK,KAAK,IAAI5N,CAAK,EAAIoC,EACvBwM,EAAK,KAAK,IAAI5O,CAAK,EAAIoC,EAE3BrF,GAAQ,KAAO6Q,EAAG,QAAQ,CAAC,EAAI,IAAMgB,EAAG,QAAQ,CAAC,EAAI,IAGzD,IAAIC,EAAU,CACV,MAAO,eACP,SAAU,CAAE,EAAG/O,EAAG,EAAGC,CAAC,EACtB,SAAUW,EAAS,SAAS3D,CAAI,CAC5C,EAEQ,GAAI+G,EAAQ,QAAS,CACjB,IAAImK,EAAUnK,EAAQ,QACtB+K,EAAQ,SAAWnO,EAAS,QAAQmO,EAAQ,SAAUZ,EAAQ,OAC1DA,EAAQ,QAASA,EAAQ,WAAYA,EAAQ,UAAU,EAC3D,OAAOnK,EAAQ,QAGnB,OAAOH,EAAK,OAAOtH,EAAO,OAAO,GAAIwS,EAAS/K,CAAO,CAAC,CAC9D,EAoCI+J,EAAO,aAAe,SAAS/N,EAAGC,EAAG+O,EAAYhL,EAASiL,EAAcC,EAAiBC,EAAaC,EAAuB,CACzH,IAAInQ,EAAS1C,EAAO,UAAS,EACzB8S,EACAvO,EACAzD,EACAiS,EACAC,EACAnQ,EACAxC,EACAW,EACAkG,EACAhC,EACAiC,EAkBJ,IAfA2L,EAAY,GAAQpQ,GAAUA,EAAO,aAErC+E,EAAUA,GAAW,CAAA,EACrB3G,EAAQ,CAAA,EAER4R,EAAe,OAAOA,EAAiB,IAAcA,EAAe,GACpEC,EAAkB,OAAOA,EAAoB,IAAcA,EAAkB,IAC7EC,EAAc,OAAOA,EAAgB,IAAcA,EAAc,GACjEC,EAAwB,OAAOA,EAA0B,IAAcA,EAAwB,IAG1F7S,EAAO,QAAQyS,EAAW,CAAC,CAAC,IAC7BA,EAAa,CAACA,CAAU,GAGvBvN,EAAI,EAAGA,EAAIuN,EAAW,OAAQvN,GAAK,EAWpC,GAVArC,EAAW4P,EAAWvN,CAAC,EACvB6N,EAAW1O,EAAS,SAASxB,CAAQ,EACrCmQ,EAAY,CAACD,EAETC,GAAa,CAACF,GACd9S,EAAO,SACH,gJACpB,EAGgB+S,GAAY,CAACD,EACTC,EACAlQ,EAAWwB,EAAS,cAAcxB,CAAQ,EAG1CA,EAAWwB,EAAS,KAAKxB,CAAQ,EAGrC/B,EAAM,KAAK,CACP,SAAU,CAAE,EAAG2C,EAAG,EAAGC,CAAC,EACtB,SAAUb,CAC9B,CAAiB,MACE,CAEH,IAAIoQ,EAAUpQ,EAAS,IAAI,SAASG,EAAQ,CACxC,MAAO,CAACA,EAAO,EAAGA,EAAO,CAAC,CAC9C,CAAiB,EAGDN,EAAO,QAAQuQ,CAAO,EAClBN,IAAoB,IACpBjQ,EAAO,sBAAsBuQ,EAASN,CAAe,EACrDE,IAA0B,IAASnQ,EAAO,uBAC1CA,EAAO,sBAAsBuQ,EAASJ,CAAqB,EAG/D,IAAIK,EAAaxQ,EAAO,YAAYuQ,CAAO,EAG3C,IAAK5S,EAAI,EAAGA,EAAI6S,EAAW,OAAQ7S,IAAK,CACpC,IAAI8S,EAAQD,EAAW7S,CAAC,EAGpB+S,EAAgBD,EAAM,IAAI,SAAStQ,EAAU,CAC7C,MAAO,CACH,EAAGA,EAAS,CAAC,EACb,EAAGA,EAAS,CAAC,CACzC,CACA,CAAqB,EAGG+P,EAAc,GAAKvO,EAAS,KAAK+O,CAAa,EAAIR,GAItD9R,EAAM,KAAK,CACP,SAAUuD,EAAS,OAAO+O,CAAa,EACvC,SAAUA,CAClC,CAAqB,GAMb,IAAK/S,EAAI,EAAGA,EAAIS,EAAM,OAAQT,IAC1BS,EAAMT,CAAC,EAAIiH,EAAK,OAAOtH,EAAO,OAAOc,EAAMT,CAAC,EAAGoH,CAAO,CAAC,EAI3D,GAAIiL,EAAc,CACd,IAAIW,EAAsB,EAE1B,IAAKhT,EAAI,EAAGA,EAAIS,EAAM,OAAQT,IAAK,CAC/B,IAAIiT,EAAQxS,EAAMT,CAAC,EAEnB,IAAKW,EAAIX,EAAI,EAAGW,EAAIF,EAAM,OAAQE,IAAK,CACnC,IAAIuS,EAAQzS,EAAME,CAAC,EAEnB,GAAI4B,EAAO,SAAS0Q,EAAM,OAAQC,EAAM,MAAM,EAAG,CAC7C,IAAIC,EAAMF,EAAM,SACZG,EAAMF,EAAM,SAGhB,IAAKrM,EAAI,EAAGA,EAAIoM,EAAM,SAAS,OAAQpM,IACnC,IAAKC,EAAI,EAAGA,EAAIoM,EAAM,SAAS,OAAQpM,IAAK,CAExC,IAAIuM,EAAKlQ,EAAO,iBAAiBA,EAAO,IAAIgQ,GAAKtM,EAAI,GAAKsM,EAAI,MAAM,EAAGC,EAAItM,CAAC,CAAC,CAAC,EAC1EwM,EAAKnQ,EAAO,iBAAiBA,EAAO,IAAIgQ,EAAItM,CAAC,EAAGuM,GAAKtM,EAAI,GAAKsM,EAAI,MAAM,CAAC,CAAC,EAG1EC,EAAKL,GAAuBM,EAAKN,IACjCG,EAAItM,CAAC,EAAE,WAAa,GACpBuM,EAAItM,CAAC,EAAE,WAAa,QAUhD,OAAIrG,EAAM,OAAS,GAEfyD,EAAO+C,EAAK,OAAOtH,EAAO,OAAO,CAAE,MAAOc,EAAM,MAAM,CAAC,CAAC,EAAI2G,CAAO,CAAC,EAGpEH,EAAK,YAAY/C,EAAM,CAAE,EAAGd,EAAG,EAAGC,EAAG,EAE9Ba,GAEAzD,EAAM,CAAC,CAE1B,CAEA,GAAC,GAKM,SAAS7B,EAAQM,EAASF,EAAqB,CAQtD,IAAIuU,EAAW,CAAA,EAEf3U,EAAO,QAAU2U,EAEjB,IAAI5T,EAASX,EAAoB,CAAC,EAC9B+M,EAAY/M,EAAoB,CAAC,GAEpC,UAAW,CAQRuU,EAAS,OAAS,SAASnM,EAAS,CAChC,IAAIC,EAAW,CACX,OAAQ,CAAA,EACR,MAAO,IACnB,EAEQ,OAAO1H,EAAO,OAAO0H,EAAUD,CAAO,CAC9C,EAQImM,EAAS,UAAY,SAASC,EAAU7I,EAAQ,CAC5C6I,EAAS,OAAS7I,EAAO,MAAM,CAAC,CACxC,EAOI4I,EAAS,MAAQ,SAASC,EAAU,CAChCA,EAAS,OAAS,CAAA,CAC1B,EAWID,EAAS,WAAa,SAASC,EAAU,CACrC,IAAIC,EAAa,CAAA,EACbnI,EAAQkI,EAAS,MACjB7I,EAAS6I,EAAS,OAClBE,EAAe/I,EAAO,OACtBgJ,EAAaJ,EAAS,WACtBK,EAAW7H,EAAU,SACrB,EACApL,EAIJ,IAFAgK,EAAO,KAAK4I,EAAS,eAAe,EAE/B,EAAI,EAAG,EAAIG,EAAc,IAAK,CAC/B,IAAIjI,EAAQd,EAAO,CAAC,EAChB9H,EAAU4I,EAAM,OAChBoI,EAAYpI,EAAM,OAAO,IAAI,EAC7BqI,EAAYrI,EAAM,OAAO,IAAI,EAC7BsI,EAAYtI,EAAM,OAAO,IAAI,EAC7BuI,EAAcvI,EAAM,UAAYA,EAAM,WACtCwI,EAAexI,EAAM,MAAM,OAC3ByI,EAAeD,IAAiB,EAEpC,IAAKtT,EAAI,EAAI,EAAGA,EAAI+S,EAAc/S,IAAK,CACnC,IAAI+K,EAAQf,EAAOhK,CAAC,EAChBmC,EAAU4I,EAAM,OAEpB,GAAI5I,EAAQ,IAAI,EAAI+Q,EAChB,MAGJ,GAAI,EAAAC,EAAYhR,EAAQ,IAAI,GAAKiR,EAAYjR,EAAQ,IAAI,IAIrD,EAAAkR,IAAgBtI,EAAM,UAAYA,EAAM,cAIvCiI,EAAWlI,EAAM,gBAAiBC,EAAM,eAAe,EAI5D,KAAIyI,EAAezI,EAAM,MAAM,OAE/B,GAAIwI,GAAgBC,IAAiB,EAAG,CACpC,IAAI3I,EAAYoI,EAASnI,EAAOC,EAAOJ,CAAK,EAExCE,GACAiI,EAAW,KAAKjI,CAAS,CAEjD,KAIoB,SAHI4I,EAAcH,EAAe,EAAI,EAAI,EACrCI,EAAcF,EAAe,EAAI,EAAI,EAEhCtN,EAAIuN,EAAavN,EAAIoN,EAAcpN,IAIxC,QAHIoM,EAAQxH,EAAM,MAAM5E,CAAC,EACrBhE,EAAUoQ,EAAM,OAEXnM,EAAIuN,EAAavN,EAAIqN,EAAcrN,IAAK,CAC7C,IAAIoM,EAAQxH,EAAM,MAAM5E,CAAC,EACrBhE,EAAUoQ,EAAM,OAEpB,GAAI,EAAArQ,EAAQ,IAAI,EAAIC,EAAQ,IAAI,GAAKD,EAAQ,IAAI,EAAIC,EAAQ,IAAI,GAC1DD,EAAQ,IAAI,EAAIC,EAAQ,IAAI,GAAKD,EAAQ,IAAI,EAAIC,EAAQ,IAAI,GAIpE,KAAI0I,EAAYoI,EAASX,EAAOC,EAAO5H,CAAK,EAExCE,GACAiI,EAAW,KAAKjI,CAAS,MAQjD,OAAOiI,CACf,EAUIF,EAAS,WAAa,SAASe,EAASC,EAAS,CAC7C,OAAID,EAAQ,QAAUC,EAAQ,OAASD,EAAQ,QAAU,EAC9CA,EAAQ,MAAQ,GAEnBA,EAAQ,KAAOC,EAAQ,YAAc,IAAMA,EAAQ,KAAOD,EAAQ,YAAc,CAChG,EAWIf,EAAS,gBAAkB,SAAS9H,EAAOC,EAAO,CAC9C,OAAOD,EAAM,OAAO,IAAI,EAAIC,EAAM,OAAO,IAAI,CACrD,CAwBA,GAAC,GAKM,SAAS9M,EAAQM,EAASF,EAAqB,CAQtD,IAAIwV,EAAQ,CAAA,EAEZ5V,EAAO,QAAU4V,EAEjB,IAAI7U,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAQRwV,EAAM,OAAS,SAASC,EAAS,CAC7B,IAAIC,EAAQ,CAAA,EAEZ,OAAKD,GACD9U,EAAO,IAAI,mEAAoE,MAAM,EAGzF+U,EAAM,QAAUD,GAAW,SAAS,KACpCC,EAAM,SAAW,CAAE,EAAG,EAAG,EAAG,CAAC,EAC7BA,EAAM,SAAW,CAAE,EAAG,EAAG,EAAG,CAAC,EAC7BA,EAAM,kBAAoB,CAAE,EAAG,EAAG,EAAG,CAAC,EACtCA,EAAM,gBAAkB,CAAE,EAAG,EAAG,EAAG,CAAC,EACpCA,EAAM,OAAS,CAAE,EAAG,EAAG,EAAG,CAAC,EAC3BA,EAAM,MAAQ,CAAE,EAAG,EAAG,EAAG,CAAC,EAC1BA,EAAM,WAAa,EACnBA,EAAM,OAAS,GACfA,EAAM,WAAa,SAASA,EAAM,QAAQ,aAAa,kBAAkB,EAAG,EAAE,GAAK,EAEnFA,EAAM,aAAe,CACjB,UAAW,KACX,UAAW,KACX,QAAS,KACT,WAAY,IACxB,EAEQA,EAAM,UAAY,SAAS7K,EAAO,CAC9B,IAAI7G,EAAWwR,EAAM,0BAA0B3K,EAAO6K,EAAM,QAASA,EAAM,UAAU,EACjFC,EAAU9K,EAAM,eAEhB8K,IACAD,EAAM,OAAS,EACf7K,EAAM,eAAc,GAGxB6K,EAAM,SAAS,EAAI1R,EAAS,EAC5B0R,EAAM,SAAS,EAAI1R,EAAS,EAC5B0R,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,aAAa,UAAY7K,CAC3C,EAEQ6K,EAAM,UAAY,SAAS7K,EAAO,CAC9B,IAAI7G,EAAWwR,EAAM,0BAA0B3K,EAAO6K,EAAM,QAASA,EAAM,UAAU,EACjFC,EAAU9K,EAAM,eAEhB8K,GACAD,EAAM,OAAS,EACf7K,EAAM,eAAc,GAEpB6K,EAAM,OAAS7K,EAAM,OAGzB6K,EAAM,SAAS,EAAI1R,EAAS,EAC5B0R,EAAM,SAAS,EAAI1R,EAAS,EAC5B0R,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,kBAAkB,EAAIA,EAAM,SAAS,EAC3CA,EAAM,kBAAkB,EAAIA,EAAM,SAAS,EAC3CA,EAAM,aAAa,UAAY7K,CAC3C,EAEQ6K,EAAM,QAAU,SAAS7K,EAAO,CAC5B,IAAI7G,EAAWwR,EAAM,0BAA0B3K,EAAO6K,EAAM,QAASA,EAAM,UAAU,EACjFC,EAAU9K,EAAM,eAEhB8K,GACA9K,EAAM,eAAc,EAGxB6K,EAAM,OAAS,GACfA,EAAM,SAAS,EAAI1R,EAAS,EAC5B0R,EAAM,SAAS,EAAI1R,EAAS,EAC5B0R,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,gBAAgB,EAAIA,EAAM,SAAS,EACzCA,EAAM,gBAAgB,EAAIA,EAAM,SAAS,EACzCA,EAAM,aAAa,QAAU7K,CACzC,EAEQ6K,EAAM,WAAa,SAAS7K,EAAO,CAC/B6K,EAAM,WAAa,KAAK,IAAI,GAAI,KAAK,IAAI,EAAG7K,EAAM,YAAc,CAACA,EAAM,MAAM,CAAC,EAC9EA,EAAM,eAAc,CAChC,EAEQ2K,EAAM,WAAWE,EAAOA,EAAM,OAAO,EAE9BA,CACf,EAQIF,EAAM,WAAa,SAASE,EAAOD,EAAS,CACxCC,EAAM,QAAUD,EAEhBA,EAAQ,iBAAiB,YAAaC,EAAM,SAAS,EACrDD,EAAQ,iBAAiB,YAAaC,EAAM,SAAS,EACrDD,EAAQ,iBAAiB,UAAWC,EAAM,OAAO,EAEjDD,EAAQ,iBAAiB,aAAcC,EAAM,UAAU,EACvDD,EAAQ,iBAAiB,iBAAkBC,EAAM,UAAU,EAE3DD,EAAQ,iBAAiB,YAAaC,EAAM,SAAS,EACrDD,EAAQ,iBAAiB,aAAcC,EAAM,SAAS,EACtDD,EAAQ,iBAAiB,WAAYC,EAAM,OAAO,CAC1D,EAOIF,EAAM,kBAAoB,SAASE,EAAO,CACtCA,EAAM,aAAa,UAAY,KAC/BA,EAAM,aAAa,UAAY,KAC/BA,EAAM,aAAa,QAAU,KAC7BA,EAAM,aAAa,WAAa,KAChCA,EAAM,WAAa,CAC3B,EAQIF,EAAM,UAAY,SAASE,EAAOrL,EAAQ,CACtCqL,EAAM,OAAO,EAAIrL,EAAO,EACxBqL,EAAM,OAAO,EAAIrL,EAAO,EACxBqL,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,CAC3E,EAQIF,EAAM,SAAW,SAASE,EAAOE,EAAO,CACpCF,EAAM,MAAM,EAAIE,EAAM,EACtBF,EAAM,MAAM,EAAIE,EAAM,EACtBF,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,EACnEA,EAAM,SAAS,EAAIA,EAAM,SAAS,EAAIA,EAAM,MAAM,EAAIA,EAAM,OAAO,CAC3E,EAWIF,EAAM,0BAA4B,SAAS3K,EAAO4K,EAASI,EAAY,CACnE,IAAIC,EAAgBL,EAAQ,sBAAqB,EAC7CM,EAAY,SAAS,iBAAmB,SAAS,KAAK,YAAc,SAAS,KAC7EC,EAAW,OAAO,cAAgB,OAAa,OAAO,YAAcD,EAAS,WAC7EE,EAAW,OAAO,cAAgB,OAAa,OAAO,YAAcF,EAAS,UAC7EJ,EAAU9K,EAAM,eAChBzG,EAAGC,EAEP,OAAIsR,GACAvR,EAAIuR,EAAQ,CAAC,EAAE,MAAQG,EAAc,KAAOE,EAC5C3R,EAAIsR,EAAQ,CAAC,EAAE,MAAQG,EAAc,IAAMG,IAE3C7R,EAAIyG,EAAM,MAAQiL,EAAc,KAAOE,EACvC3R,EAAIwG,EAAM,MAAQiL,EAAc,IAAMG,GAGnC,CACH,EAAG7R,GAAKqR,EAAQ,aAAeA,EAAQ,OAASA,EAAQ,aAAeI,GACvE,EAAGxR,GAAKoR,EAAQ,cAAgBA,EAAQ,QAAUA,EAAQ,cAAgBI,EACtF,CACA,CAEA,GAAC,GAKM,SAASjW,EAAQM,EAASF,EAAqB,CAQtD,IAAIkW,EAAS,CAAA,EAEbtW,EAAO,QAAUsW,EAEjB,IAAIvV,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAERkW,EAAO,UAAY,CAAA,EAQnBA,EAAO,SAAW,SAASC,EAAQ,CAK/B,GAJKD,EAAO,SAASC,CAAM,GACvBxV,EAAO,KAAK,mBAAoBuV,EAAO,SAASC,CAAM,EAAG,yCAAyC,EAGlGA,EAAO,QAAQD,EAAO,UAAW,CACjC,IAAIE,EAAaF,EAAO,UAAUC,EAAO,IAAI,EACzCE,EAAgBH,EAAO,aAAaC,EAAO,OAAO,EAAE,OACpDG,EAAoBJ,EAAO,aAAaE,EAAW,OAAO,EAAE,OAE5DC,EAAgBC,GAChB3V,EAAO,KAAK,mBAAoBuV,EAAO,SAASE,CAAU,EAAG,kBAAmBF,EAAO,SAASC,CAAM,CAAC,EACvGD,EAAO,UAAUC,EAAO,IAAI,EAAIA,GACzBE,EAAgBC,EACvB3V,EAAO,KAAK,mBAAoBuV,EAAO,SAASE,CAAU,EAAG,2BAA4BF,EAAO,SAASC,CAAM,CAAC,EACzGA,IAAWC,GAClBzV,EAAO,KAAK,mBAAoBuV,EAAO,SAASC,CAAM,EAAG,kDAAkD,CAE3H,MACYD,EAAO,UAAUC,EAAO,IAAI,EAAIA,EAGpC,OAAOA,CACf,EASID,EAAO,QAAU,SAASK,EAAY,CAClC,OAAOL,EAAO,UAAUA,EAAO,gBAAgBK,CAAU,EAAE,IAAI,CACvE,EAQIL,EAAO,SAAW,SAASC,EAAQ,CAC/B,OAAO,OAAOA,GAAW,SAAWA,GAAUA,EAAO,MAAQ,aAAe,KAAOA,EAAO,SAAWA,EAAO,OAAS,QAC7H,EAYID,EAAO,SAAW,SAAStV,EAAK,CAC5B,OAAOA,GAAOA,EAAI,MAAQA,EAAI,SAAWA,EAAI,OACrD,EASIsV,EAAO,OAAS,SAAStW,EAAQO,EAAM,CACnC,OAAOP,EAAO,KAAK,QAAQO,CAAI,EAAI,EAC3C,EAWI+V,EAAO,MAAQ,SAASC,EAAQvW,EAAQ,CACpC,IAAI4W,EAASL,EAAO,KAAOD,EAAO,gBAAgBC,EAAO,GAAG,EAC5D,MAAO,CAACA,EAAO,KAAQvW,EAAO,OAAS4W,EAAO,MAAQN,EAAO,iBAAiBtW,EAAO,QAAS4W,EAAO,KAAK,CAClH,EAkBIN,EAAO,IAAM,SAAStW,EAAQ6W,EAAS,CAGnC,GAFA7W,EAAO,MAAQA,EAAO,MAAQ,CAAA,GAAI,OAAO6W,GAAW,EAAE,EAElD7W,EAAO,KAAK,SAAW,EAAG,CAC1Be,EAAO,KAAK,cAAeuV,EAAO,SAAStW,CAAM,EAAG,+CAA+C,EACnG,OAOJ,QAJI8W,EAAeR,EAAO,aAAatW,CAAM,EACzC+W,EAAqBhW,EAAO,gBAAgB+V,CAAY,EACxDE,EAAS,CAAA,EAEJ5V,EAAI,EAAGA,EAAI2V,EAAmB,OAAQ3V,GAAK,EAChD,GAAI2V,EAAmB3V,CAAC,IAAMpB,EAAO,KAIrC,KAAIuW,EAASD,EAAO,QAAQS,EAAmB3V,CAAC,CAAC,EAEjD,GAAI,CAACmV,EAAQ,CACTS,EAAO,KAAK,KAAOD,EAAmB3V,CAAC,CAAC,EACxC,SAGAkV,EAAO,OAAOtW,EAAQuW,EAAO,IAAI,IAIhCD,EAAO,MAAMC,EAAQvW,CAAM,IAC5Be,EAAO,KAAK,cAAeuV,EAAO,SAASC,CAAM,EAAG,SAAUA,EAAO,IAAK,mBAAoBD,EAAO,SAAStW,CAAM,EAAI,GAAG,EAC3HuW,EAAO,QAAU,IAGjBA,EAAO,QACPA,EAAO,QAAQvW,CAAM,GAErBe,EAAO,KAAK,cAAeuV,EAAO,SAASC,CAAM,EAAG,uCAAuC,EAC3FA,EAAO,QAAU,IAGjBA,EAAO,SACPS,EAAO,KAAK,MAAQV,EAAO,SAASC,CAAM,CAAC,EAC3C,OAAOA,EAAO,SAEdS,EAAO,KAAK,KAAOV,EAAO,SAASC,CAAM,CAAC,EAG9CvW,EAAO,KAAK,KAAKuW,EAAO,IAAI,GAG5BS,EAAO,OAAS,GAChBjW,EAAO,KAAKiW,EAAO,KAAK,IAAI,CAAC,CAEzC,EAQIV,EAAO,aAAe,SAAStW,EAAQiX,EAAS,CAC5C,IAAIC,EAAaZ,EAAO,gBAAgBtW,CAAM,EAC1CO,EAAO2W,EAAW,KAItB,GAFAD,EAAUA,GAAW,CAAA,EAEjB,EAAA1W,KAAQ0W,GAIZ,CAAAjX,EAASsW,EAAO,QAAQtW,CAAM,GAAKA,EAEnCiX,EAAQ1W,CAAI,EAAIQ,EAAO,IAAIf,EAAO,MAAQ,GAAI,SAAS2W,EAAY,CAC3DL,EAAO,SAASK,CAAU,GAC1BL,EAAO,SAASK,CAAU,EAG9B,IAAIC,EAASN,EAAO,gBAAgBK,CAAU,EAC1CQ,EAAWb,EAAO,QAAQK,CAAU,EAExC,OAAIQ,GAAY,CAACb,EAAO,iBAAiBa,EAAS,QAASP,EAAO,KAAK,GACnE7V,EAAO,KACH,uBAAwBuV,EAAO,SAASa,CAAQ,EAAG,mBACnDb,EAAO,SAASM,CAAM,EAAG,UAAWN,EAAO,SAASY,CAAU,EAAI,GACtF,EAEgBC,EAAS,QAAU,GACnBnX,EAAO,QAAU,IACTmX,IACRpW,EAAO,KACH,uBAAwBuV,EAAO,SAASK,CAAU,EAAG,UACrDL,EAAO,SAASY,CAAU,EAAG,wBACjD,EAEgBlX,EAAO,QAAU,IAGd4W,EAAO,IAC1B,CAAS,EAED,QAASxV,EAAI,EAAGA,EAAI6V,EAAQ1W,CAAI,EAAE,OAAQa,GAAK,EAC3CkV,EAAO,aAAaW,EAAQ1W,CAAI,EAAEa,CAAC,EAAG6V,CAAO,EAGjD,OAAOA,EACf,EAWIX,EAAO,gBAAkB,SAASK,EAAY,CAC1C,GAAI5V,EAAO,SAAS4V,CAAU,EAAG,CAC7B,IAAIS,EAAU,yDAEd,OAAKA,EAAQ,KAAKT,CAAU,GACxB5V,EAAO,KAAK,0BAA2B4V,EAAY,mCAAmC,EAGnF,CACH,KAAMA,EAAW,MAAM,GAAG,EAAE,CAAC,EAC7B,MAAOA,EAAW,MAAM,GAAG,EAAE,CAAC,GAAK,GACnD,EAGQ,MAAO,CACH,KAAMA,EAAW,KACjB,MAAOA,EAAW,OAASA,EAAW,OAClD,CACA,EAkBIL,EAAO,aAAe,SAASe,EAAO,CAClC,IAAID,EAAU,gEAETA,EAAQ,KAAKC,CAAK,GACnBtW,EAAO,KAAK,uBAAwBsW,EAAO,kCAAkC,EAGjF,IAAIxV,EAAQuV,EAAQ,KAAKC,CAAK,EAC1BC,EAAQ,OAAOzV,EAAM,CAAC,CAAC,EACvB0V,EAAQ,OAAO1V,EAAM,CAAC,CAAC,EACvB2V,EAAQ,OAAO3V,EAAM,CAAC,CAAC,EAE3B,MAAO,CACH,QAAS,GAAQA,EAAM,CAAC,GAAKA,EAAM,CAAC,GACpC,QAASA,EAAM,CAAC,EAChB,MAAOwV,EACP,SAAUxV,EAAM,CAAC,GAAKA,EAAM,CAAC,GAAK,GAClC,MAAOyV,EACP,MAAOC,EACP,MAAOC,EACP,MAAO,CAACF,EAAOC,EAAOC,CAAK,EAC3B,WAAY3V,EAAM,CAAC,EACnB,OAAQyV,EAAQ,IAAMC,EAAQ,IAAMC,CAChD,CACA,EAWIlB,EAAO,iBAAmB,SAASmB,EAASJ,EAAO,CAC/CA,EAAQA,GAAS,IAEjB,IAAIK,EAAIpB,EAAO,aAAae,CAAK,EAC7BpR,EAAIqQ,EAAO,aAAamB,CAAO,EAEnC,GAAIC,EAAE,QAAS,CACX,GAAIA,EAAE,WAAa,KAAOD,IAAY,IAClC,MAAO,GAGX,GAAIC,EAAE,WAAa,IACf,OAAOzR,EAAE,OAASyR,EAAE,OAGxB,GAAIA,EAAE,WAAa,KACf,OAAOzR,EAAE,QAAUyR,EAAE,OAGzB,GAAIA,EAAE,WAAa,IACf,OAAOzR,EAAE,QAAUyR,EAAE,OAASzR,EAAE,QAAUyR,EAAE,OAASzR,EAAE,OAASyR,EAAE,MAGtE,GAAIA,EAAE,WAAa,IACf,OAAIA,EAAE,MAAQ,EACHzR,EAAE,QAAUyR,EAAE,OAASzR,EAAE,QAAUyR,EAAE,OAG5CA,EAAE,MAAQ,EACHzR,EAAE,QAAUyR,EAAE,OAASzR,EAAE,OAASyR,EAAE,MAGxCzR,EAAE,QAAUyR,EAAE,MAI7B,OAAOD,IAAYJ,GAASI,IAAY,GAChD,CAEA,GAAC,GAKM,SAASzX,EAAQM,EAAS,CAQjC,IAAI2P,EAAU,CAAA,EAEdjQ,EAAO,QAAUiQ,EAEhB,UAAW,CAQRA,EAAQ,OAAS,SAASlM,EAAQ,CAC9B,MAAO,CACH,OAAQA,EACR,cAAe,EACf,eAAgB,CAC5B,CACA,CAEA,EAAC,GAKM,SAAS/D,EAAQM,EAASF,EAAqB,CAYtD,IAAIuX,EAAS,CAAA,EAEb3X,EAAO,QAAU2X,EAEjB,IAAIrP,EAAWlI,EAAoB,CAAC,EAChCwX,EAAWxX,EAAoB,EAAE,EACjCuU,EAAWvU,EAAoB,EAAE,EACjCyX,EAAQzX,EAAoB,EAAE,EAC9BuK,EAASvK,EAAoB,CAAC,EAC9BgL,EAAYhL,EAAoB,CAAC,EACjCwQ,EAAaxQ,EAAoB,EAAE,EACnCW,EAASX,EAAoB,CAAC,EAC9BiI,EAAOjI,EAAoB,CAAC,GAE/B,UAAW,CAURuX,EAAO,OAAS,SAASnP,EAAS,CAC9BA,EAAUA,GAAW,CAAA,EAErB,IAAIC,EAAW,CACX,mBAAoB,EACpB,mBAAoB,EACpB,qBAAsB,EACtB,eAAgB,GAChB,OAAQ,CAAA,EACR,OAAQ,CAAA,EACR,QAAS,CACL,EAAG,EACH,EAAG,EACH,MAAO,MAEX,OAAQ,CACJ,UAAW,EACX,UAAW,EACX,UAAW,EACX,YAAa,EAE7B,EAEYqP,EAAS/W,EAAO,OAAO0H,EAAUD,CAAO,EAE5C,OAAAsP,EAAO,MAAQtP,EAAQ,OAAS4C,EAAU,OAAO,CAAE,MAAO,QAAS,EACnE0M,EAAO,MAAQtP,EAAQ,OAASqP,EAAM,OAAM,EAC5CC,EAAO,SAAWtP,EAAQ,UAAYmM,EAAS,OAAM,EAGrDmD,EAAO,KAAO,CAAE,QAAS,EAAE,EAC3BA,EAAO,MAAM,QAAUA,EAAO,QAC9BA,EAAO,WAAaA,EAAO,KAC3BA,EAAO,QAAU,CAAA,EAEVA,CACf,EAUIH,EAAO,OAAS,SAASG,EAAQjR,EAAO,CACpC,IAAIkR,EAAYhX,EAAO,IAAG,EAEtBiX,EAAQF,EAAO,MACflD,EAAWkD,EAAO,SAClBpL,EAAQoL,EAAO,MACfG,EAASH,EAAO,OAChB5H,EAAY+H,EAAO,UACnB7W,EAEJyF,EAAQ,OAAOA,EAAU,IAAcA,EAAQ9F,EAAO,WACtD8F,GAASoR,EAAO,UAGhBA,EAAO,WAAapR,EACpBoR,EAAO,UAAYpR,EAGnB,IAAIoE,EAAQ,CACR,UAAWgN,EAAO,UAClB,MAAOpR,CACnB,EAEQ8D,EAAO,QAAQmN,EAAQ,eAAgB7M,CAAK,EAG5C,IAAIiN,EAAY9M,EAAU,UAAU4M,CAAK,EACrCG,EAAiB/M,EAAU,eAAe4M,CAAK,EAyBnD,IAtBIA,EAAM,aAENrD,EAAS,UAAUC,EAAUsD,CAAS,EAGtC9M,EAAU,YAAY4M,EAAO,GAAO,GAAO,EAAI,GAI/CF,EAAO,gBACPxP,EAAS,OAAO4P,EAAWrR,CAAK,EAGpC8Q,EAAO,oBAAoBO,EAAWJ,EAAO,OAAO,EAGhDjR,EAAQ,GACR8Q,EAAO,cAAcO,EAAWrR,CAAK,EAIzC+J,EAAW,YAAYsH,CAAS,EAC3B9W,EAAI,EAAGA,EAAI0W,EAAO,qBAAsB1W,IACzCwP,EAAW,SAASuH,EAAgBtR,CAAK,EAE7C+J,EAAW,aAAasH,CAAS,EAGjCtD,EAAS,MAAQkD,EAAO,MACxB,IAAIjD,EAAaF,EAAS,WAAWC,CAAQ,EAG7CiD,EAAM,OAAOnL,EAAOmI,EAAY3E,CAAS,EAGrC4H,EAAO,gBACPxP,EAAS,gBAAgBoE,EAAM,IAAI,EAGnCA,EAAM,eAAe,OAAS,GAC9B/B,EAAO,QAAQmN,EAAQ,iBAAkB,CAAE,MAAOpL,EAAM,eAAgB,EAG5E,IAAI0L,EAAkBrX,EAAO,MAAM,GAAK+W,EAAO,mBAAoB,EAAG,CAAC,EAGvE,IADAF,EAAS,iBAAiBlL,EAAM,IAAI,EAC/BtL,EAAI,EAAGA,EAAI0W,EAAO,mBAAoB1W,IACvCwW,EAAS,cAAclL,EAAM,KAAM7F,EAAOuR,CAAe,EAM7D,IAJAR,EAAS,kBAAkBM,CAAS,EAGpCtH,EAAW,YAAYsH,CAAS,EAC3B9W,EAAI,EAAGA,EAAI0W,EAAO,qBAAsB1W,IACzCwP,EAAW,SAASuH,EAAgBtR,CAAK,EAM7C,IAJA+J,EAAW,aAAasH,CAAS,EAGjCN,EAAS,iBAAiBlL,EAAM,IAAI,EAC/BtL,EAAI,EAAGA,EAAI0W,EAAO,mBAAoB1W,IACvCwW,EAAS,cAAclL,EAAM,KAAM7F,CAAK,EAI5C,OAAA8Q,EAAO,wBAAwBO,CAAS,EAGpCxL,EAAM,gBAAgB,OAAS,GAC/B/B,EAAO,QAAQmN,EAAQ,kBAAmB,CAAE,MAAOpL,EAAM,gBAAiB,EAE1EA,EAAM,aAAa,OAAS,GAC5B/B,EAAO,QAAQmN,EAAQ,eAAgB,CAAE,MAAOpL,EAAM,aAAc,EAGxEiL,EAAO,mBAAmBO,CAAS,EAEnCvN,EAAO,QAAQmN,EAAQ,cAAe7M,CAAK,EAG3C6M,EAAO,OAAO,YAAc/W,EAAO,IAAG,EAAKgX,EAEpCD,CACf,EAQIH,EAAO,MAAQ,SAASU,EAASC,EAAS,CAGtC,GAFAvX,EAAO,OAAOsX,EAASC,CAAO,EAE1BA,EAAQ,MAAO,CACfD,EAAQ,MAAQC,EAAQ,MAExBX,EAAO,MAAMU,CAAO,EAIpB,QAFItM,EAASX,EAAU,UAAUiN,EAAQ,KAAK,EAErCjX,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACnBkH,EAAS,IAAIhD,EAAM,EAAK,EACxBA,EAAK,GAAKvE,EAAO,OAAM,GAGvC,EAOI4W,EAAO,MAAQ,SAASG,EAAQ,CAC5BD,EAAM,MAAMC,EAAO,KAAK,EACxBnD,EAAS,MAAMmD,EAAO,QAAQ,CACtC,EAQIH,EAAO,mBAAqB,SAAS5L,EAAQ,CAGzC,QAFI+I,EAAe/I,EAAO,OAEjB3K,EAAI,EAAGA,EAAI0T,EAAc1T,IAAK,CACnC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAGnBkE,EAAK,MAAM,EAAI,EACfA,EAAK,MAAM,EAAI,EACfA,EAAK,OAAS,EAE1B,EAWIqS,EAAO,oBAAsB,SAAS5L,EAAQwM,EAAS,CACnD,IAAIC,EAAe,OAAOD,EAAQ,MAAU,IAAcA,EAAQ,MAAQ,KACtEzD,EAAe/I,EAAO,OAE1B,GAAK,EAAAwM,EAAQ,IAAM,GAAKA,EAAQ,IAAM,GAAMC,IAAiB,GAI7D,QAASpX,EAAI,EAAGA,EAAI0T,EAAc1T,IAAK,CACnC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAEfkE,EAAK,UAAYA,EAAK,aAI1BA,EAAK,MAAM,GAAKA,EAAK,KAAOiT,EAAQ,EAAIC,EACxClT,EAAK,MAAM,GAAKA,EAAK,KAAOiT,EAAQ,EAAIC,GAEpD,EASIb,EAAO,cAAgB,SAAS5L,EAAQlF,EAAO,CAG3C,QAFIiO,EAAe/I,EAAO,OAEjB3K,EAAI,EAAGA,EAAI0T,EAAc1T,IAAK,CACnC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAEfkE,EAAK,UAAYA,EAAK,YAG1B+C,EAAK,OAAO/C,EAAMuB,CAAK,EAEnC,EAQI8Q,EAAO,wBAA0B,SAAS5L,EAAQ,CAG9C,QAFI+I,EAAe/I,EAAO,OAEjB3K,EAAI,EAAGA,EAAI0T,EAAc1T,IAC9BiH,EAAK,iBAAiB0D,EAAO3K,CAAC,CAAC,CAE3C,CA2OA,GAAC,GAKM,SAASpB,EAAQM,EAASF,EAAqB,CAQtD,IAAIwX,EAAW,CAAA,EAEf5X,EAAO,QAAU4X,EAEjB,IAAIxS,EAAWhF,EAAoB,CAAC,EAChCW,EAASX,EAAoB,CAAC,EAC9BuD,EAASvD,EAAoB,CAAC,GAEjC,UAAW,CAERwX,EAAS,eAAiB,EAC1BA,EAAS,sBAAwB,KAAK,KAAK,CAAC,EAC5CA,EAAS,gBAAkB,GAC3BA,EAAS,iBAAmB,GAC5BA,EAAS,0BAA4B,EACrCA,EAAS,mBAAqB,OAAO,UAOrCA,EAAS,iBAAmB,SAASlL,EAAO,CACxC,IAAItL,EACAuL,EACA8L,EACAC,EAAchM,EAAM,OAGxB,IAAKtL,EAAI,EAAGA,EAAIsX,EAAatX,IACzBuL,EAAOD,EAAMtL,CAAC,EAETuL,EAAK,WAGV8L,EAAc9L,EAAK,eAAe,OAClCA,EAAK,UAAU,QAAQ,eAAiB8L,EACxC9L,EAAK,UAAU,QAAQ,eAAiB8L,EAEpD,EASIb,EAAS,cAAgB,SAASlL,EAAO7F,EAAO+K,EAAS,CACrD,IAAIxQ,EACAuL,EACAC,EACAC,EACAC,EACAW,EACAkL,EACAC,EACAC,EAAiBjB,EAAS,iBAAmBhG,GAAW,GACxDkH,EAAa/X,EAAO,MAAM8F,EAAQ9F,EAAO,WAAY,EAAG,CAAC,EACzD2X,EAAchM,EAAM,OAGxB,IAAKtL,EAAI,EAAGA,EAAIsX,EAAatX,IACzBuL,EAAOD,EAAMtL,CAAC,EAEV,GAACuL,EAAK,UAAYA,EAAK,YAG3BC,EAAYD,EAAK,UACjBE,EAAQD,EAAU,QAClBE,EAAQF,EAAU,QAClBa,EAASb,EAAU,OAGnBD,EAAK,WACDc,EAAO,GAAKX,EAAM,gBAAgB,EAAIF,EAAU,YAAY,EAAIC,EAAM,gBAAgB,GACpFY,EAAO,GAAKX,EAAM,gBAAgB,EAAIF,EAAU,YAAY,EAAIC,EAAM,gBAAgB,IAGhG,IAAKzL,EAAI,EAAGA,EAAIsX,EAAatX,IACzBuL,EAAOD,EAAMtL,CAAC,EAEV,GAACuL,EAAK,UAAYA,EAAK,YAG3BC,EAAYD,EAAK,UACjBE,EAAQD,EAAU,QAClBE,EAAQF,EAAU,QAClBa,EAASb,EAAU,OACnBgM,EAAkBjM,EAAK,WAAaA,EAAK,KAAOmM,GAE5CjM,EAAM,UAAYC,EAAM,YACxB8L,GAAmB,GAEjB/L,EAAM,UAAYA,EAAM,aAC1B8L,EAAeE,EAAiBhM,EAAM,cACtCA,EAAM,gBAAgB,GAAKY,EAAO,EAAImL,EAAkBD,EACxD9L,EAAM,gBAAgB,GAAKY,EAAO,EAAImL,EAAkBD,GAGtD7L,EAAM,UAAYA,EAAM,aAC1B6L,EAAeE,EAAiB/L,EAAM,cACtCA,EAAM,gBAAgB,GAAKW,EAAO,EAAImL,EAAkBD,EACxD7L,EAAM,gBAAgB,GAAKW,EAAO,EAAImL,EAAkBD,GAGxE,EAOIf,EAAS,kBAAoB,SAAS7L,EAAQ,CAM1C,QALIgN,EAAkBnB,EAAS,iBAC3B9C,EAAe/I,EAAO,OACtBiN,EAAoB5T,EAAS,UAC7B6T,EAAetV,EAAO,OAEjBvC,EAAI,EAAGA,EAAI0T,EAAc1T,IAAK,CACnC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACfwX,EAAkBtT,EAAK,gBACvB4T,EAAmBN,EAAgB,EACnCO,EAAmBP,EAAgB,EACnC9U,EAAWwB,EAAK,SAKpB,GAFAA,EAAK,cAAgB,EAEjB4T,IAAqB,GAAKC,IAAqB,EAAG,CAElD,QAASpX,EAAI,EAAGA,EAAIuD,EAAK,MAAM,OAAQvD,IAAK,CACxC,IAAIkH,EAAO3D,EAAK,MAAMvD,CAAC,EACvBiX,EAAkB/P,EAAK,SAAU2P,CAAe,EAChDK,EAAahQ,EAAK,OAAQA,EAAK,SAAUnF,CAAQ,EACjDmF,EAAK,SAAS,GAAKiQ,EACnBjQ,EAAK,SAAS,GAAKkQ,EAIvB7T,EAAK,aAAa,GAAK4T,EACvB5T,EAAK,aAAa,GAAK6T,EAEnBD,EAAmBpV,EAAS,EAAIqV,EAAmBrV,EAAS,EAAI,GAEhE8U,EAAgB,EAAI,EACpBA,EAAgB,EAAI,IAGpBA,EAAgB,GAAKG,EACrBH,EAAgB,GAAKG,IAIzC,EAOInB,EAAS,iBAAmB,SAASlL,EAAO,CACxC,IAAIgM,EAAchM,EAAM,OACpBtL,EACAW,EAEJ,IAAKX,EAAI,EAAGA,EAAIsX,EAAatX,IAAK,CAC9B,IAAIuL,EAAOD,EAAMtL,CAAC,EAElB,GAAI,GAACuL,EAAK,UAAYA,EAAK,UAG3B,KAAIwD,EAAWxD,EAAK,eAChByM,EAAiBjJ,EAAS,OAC1BvD,EAAYD,EAAK,UACjBE,EAAQD,EAAU,QAClBE,EAAQF,EAAU,QAClBa,EAASb,EAAU,OACnByM,EAAUzM,EAAU,QAGxB,IAAK7K,EAAI,EAAGA,EAAIqX,EAAgBrX,IAAK,CACjC,IAAI2O,EAAUP,EAASpO,CAAC,EACpBuX,EAAgB5I,EAAQ,OACxB6I,EAAgB7I,EAAQ,cACxB8I,EAAiB9I,EAAQ,eAE7B,GAAI6I,IAAkB,GAAKC,IAAmB,EAAG,CAE7C,IAAIC,EAAWhM,EAAO,EAAI8L,EAAgBF,EAAQ,EAAIG,EAClDE,EAAWjM,EAAO,EAAI8L,EAAgBF,EAAQ,EAAIG,EAGhD3M,EAAM,UAAYA,EAAM,aAC1BA,EAAM,aAAa,GAAK4M,EAAW5M,EAAM,YACzCA,EAAM,aAAa,GAAK6M,EAAW7M,EAAM,YACzCA,EAAM,WAAaA,EAAM,iBACpByM,EAAc,EAAIzM,EAAM,SAAS,GAAK6M,GACpCJ,EAAc,EAAIzM,EAAM,SAAS,GAAK4M,IAI3C3M,EAAM,UAAYA,EAAM,aAC1BA,EAAM,aAAa,GAAK2M,EAAW3M,EAAM,YACzCA,EAAM,aAAa,GAAK4M,EAAW5M,EAAM,YACzCA,EAAM,WAAaA,EAAM,iBACpBwM,EAAc,EAAIxM,EAAM,SAAS,GAAK4M,GACpCJ,EAAc,EAAIxM,EAAM,SAAS,GAAK2M,OAMrE,EAQI7B,EAAS,cAAgB,SAASlL,EAAO7F,EAAO,CAC5C,IAAI8C,EAAY9C,EAAQ9F,EAAO,WAC3B4Y,EAAmBhQ,EAAYA,EAC/BiQ,EAAiBD,EAAmBhQ,EACpCkQ,EAAgB,CAACjC,EAAS,eAAiBjO,EAC3CmQ,EAAuBlC,EAAS,sBAChCmC,EAA2BnC,EAAS,0BAA4BjO,EAChEqQ,EAAoBpC,EAAS,mBAC7Bc,EAAchM,EAAM,OACpB8M,EACAS,EACA7Y,EACAW,EAEJ,IAAKX,EAAI,EAAGA,EAAIsX,EAAatX,IAAK,CAC9B,IAAIuL,EAAOD,EAAMtL,CAAC,EAElB,GAAI,GAACuL,EAAK,UAAYA,EAAK,UAG3B,KAAIC,EAAYD,EAAK,UACjBE,EAAQD,EAAU,QAClBE,EAAQF,EAAU,QAClBsN,EAAgBrN,EAAM,SACtBsN,EAAgBrN,EAAM,SACtB8C,EAAUhD,EAAU,OAAO,EAC3BiD,EAAUjD,EAAU,OAAO,EAC3BwN,EAAWxN,EAAU,QAAQ,EAC7ByN,EAAWzN,EAAU,QAAQ,EAC7BuD,EAAWxD,EAAK,eAChByM,EAAiBjJ,EAAS,OAC1BwI,EAAe,EAAIS,EACnBkB,EAAmBzN,EAAM,YAAcC,EAAM,YAC7CyN,EAAW5N,EAAK,SAAWA,EAAK,eAAiBoN,EAWrD,IARAG,EAAc,EAAIrN,EAAM,SAAS,EAAIA,EAAM,aAAa,EACxDqN,EAAc,EAAIrN,EAAM,SAAS,EAAIA,EAAM,aAAa,EACxDsN,EAAc,EAAIrN,EAAM,SAAS,EAAIA,EAAM,aAAa,EACxDqN,EAAc,EAAIrN,EAAM,SAAS,EAAIA,EAAM,aAAa,EACxDD,EAAM,gBAAkBA,EAAM,MAAQA,EAAM,UAC5CC,EAAM,gBAAkBA,EAAM,MAAQA,EAAM,UAGvC/K,EAAI,EAAGA,EAAIqX,EAAgBrX,IAAK,CACjC,IAAI2O,EAAUP,EAASpO,CAAC,EACpBuX,EAAgB5I,EAAQ,OAExB8J,EAAWlB,EAAc,EAAIzM,EAAM,SAAS,EAC5C4N,EAAWnB,EAAc,EAAIzM,EAAM,SAAS,EAC5C6N,EAAWpB,EAAc,EAAIxM,EAAM,SAAS,EAC5C6N,EAAWrB,EAAc,EAAIxM,EAAM,SAAS,EAE5C8N,EAAkBV,EAAc,EAAIO,EAAW5N,EAAM,gBACrDgO,GAAkBX,EAAc,EAAIM,EAAW3N,EAAM,gBACrDiO,GAAkBX,EAAc,EAAIQ,EAAW7N,EAAM,gBACrDiO,GAAkBZ,EAAc,EAAIO,EAAW5N,EAAM,gBAErDkO,EAAoBJ,EAAkBE,GACtCG,EAAoBJ,GAAkBE,GAEtC7I,EAAiBtC,EAAUoL,EAAoBnL,EAAUoL,EACzDC,EAAkBd,EAAWY,EAAoBX,EAAWY,EAG5DE,EAAgBxO,EAAK,WAAauF,EAClCkJ,EAAc,KAAK,IAAID,EAAe,CAAC,EAC3CC,EAAcD,EAAgB,EAAI,EAAIC,EAEtC,IAAIC,EAAgBD,EAAcb,EAE9BW,EAAkB,CAACG,GAAiBH,EAAkBG,GACtDpB,EAAeiB,EAAkB,EAAIA,EAAkB,CAACA,EACxD1B,EAAiB7M,EAAK,UAAYuO,EAAkB,EAAI,EAAI,IAAMtB,EAE9DJ,EAAiB,CAACS,EAClBT,EAAiB,CAACS,EACXT,EAAiBS,IACxBT,EAAiBS,KAGrBT,EAAiB0B,EACjBjB,EAAcD,GAIlB,IAAIsB,GAAOd,EAAW3K,EAAU4K,EAAW7K,EACvC2L,GAAOb,EAAW7K,EAAU8K,EAAW/K,EACvCqC,GAAQ0G,GAAgB2B,EAAmBzN,EAAM,eAAiByO,GAAOA,GAAOxO,EAAM,eAAiByO,GAAOA,IAG9GhC,GAAiB,EAAI5M,EAAK,aAAeuF,EAAiBD,GAI9D,GAHAuH,GAAkBvH,GAGdC,EAAiB2H,EAEjBnJ,EAAQ,cAAgB,MACrB,CAGH,IAAI8K,GAAuB9K,EAAQ,cACnCA,EAAQ,eAAiB6I,EACrB7I,EAAQ,cAAgB,IAAGA,EAAQ,cAAgB,GACvD6I,EAAgB7I,EAAQ,cAAgB8K,GAI5C,GAAIN,EAAkB,CAACpB,GAAwBoB,EAAkBpB,EAE7DpJ,EAAQ,eAAiB,MACtB,CAGH,IAAI+K,GAAwB/K,EAAQ,eACpCA,EAAQ,gBAAkB8I,EACtB9I,EAAQ,eAAiB,CAACuJ,IAAavJ,EAAQ,eAAiB,CAACuJ,GACjEvJ,EAAQ,eAAiBuJ,IAAavJ,EAAQ,eAAiBuJ,GACnET,EAAiB9I,EAAQ,eAAiB+K,GAI9C,IAAIhC,EAAW7J,EAAU2J,EAAgBa,EAAWZ,EAChDE,EAAW7J,EAAU0J,EAAgBc,EAAWb,EAG9C3M,EAAM,UAAYA,EAAM,aAC1BA,EAAM,aAAa,GAAK4M,EAAW5M,EAAM,YACzCA,EAAM,aAAa,GAAK6M,EAAW7M,EAAM,YACzCA,EAAM,YAAc2N,EAAWd,EAAWe,EAAWhB,GAAY5M,EAAM,gBAGrEC,EAAM,UAAYA,EAAM,aAC1BA,EAAM,aAAa,GAAK2M,EAAW3M,EAAM,YACzCA,EAAM,aAAa,GAAK4M,EAAW5M,EAAM,YACzCA,EAAM,YAAc4N,EAAWhB,EAAWiB,EAAWlB,GAAY3M,EAAM,kBAI3F,CAEA,GAAC,GAKM,SAAS9M,EAAQM,EAASF,EAAqB,CAQtD,IAAIyX,EAAQ,CAAA,EAEZ7X,EAAO,QAAU6X,EAEjB,IAAIzK,EAAOhN,EAAoB,CAAC,EAC5BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAQRyX,EAAM,OAAS,SAASrP,EAAS,CAC7B,OAAOzH,EAAO,OAAO,CACjB,MAAO,CAAA,EACP,KAAM,CAAA,EACN,eAAgB,CAAA,EAChB,gBAAiB,CAAA,EACjB,aAAc,CAAA,GACfyH,CAAO,CAClB,EASIqP,EAAM,OAAS,SAASnL,EAAOmI,EAAY3E,EAAW,CAClD,IAAIwL,EAAYhP,EAAM,KAClBiP,EAAkBD,EAAU,OAC5BE,EAAalP,EAAM,MACnBmP,EAAmBhH,EAAW,OAC9BiH,EAAiBpP,EAAM,eACvBqP,EAAerP,EAAM,aACrBsP,EAAkBtP,EAAM,gBACxBE,EACAqP,EACAtP,EACAvL,EAOJ,IAJA0a,EAAe,OAAS,EACxBC,EAAa,OAAS,EACtBC,EAAgB,OAAS,EAEpB5a,EAAI,EAAGA,EAAIua,EAAiBva,IAC7Bsa,EAAUta,CAAC,EAAE,gBAAkB,GAGnC,IAAKA,EAAI,EAAGA,EAAIya,EAAkBza,IAC9BwL,EAAYiI,EAAWzT,CAAC,EACxBuL,EAAOC,EAAU,KAEbD,GAEIA,EAAK,SAELqP,EAAgB,KAAKrP,CAAI,EAGzBmP,EAAe,KAAKnP,CAAI,EAI5BS,EAAK,OAAOT,EAAMC,EAAWsD,CAAS,EACtCvD,EAAK,gBAAkB,KAGvBA,EAAOS,EAAK,OAAOR,EAAWsD,CAAS,EACvC0L,EAAWjP,EAAK,EAAE,EAAIA,EAGtBmP,EAAe,KAAKnP,CAAI,EACxB+O,EAAU,KAAK/O,CAAI,GAK3B,IAAIuP,EAAkB,CAAA,EAGtB,IAFAP,EAAkBD,EAAU,OAEvBta,EAAI,EAAGA,EAAIua,EAAiBva,IAC7BuL,EAAO+O,EAAUta,CAAC,EAEbuL,EAAK,kBACNS,EAAK,UAAUT,EAAM,GAAOuD,CAAS,EACrC6L,EAAa,KAAKpP,CAAI,EAElB,CAACA,EAAK,UAAU,MAAM,YAAc,CAACA,EAAK,UAAU,MAAM,YAC1DuP,EAAgB,KAAK9a,CAAC,GAMlC,IAAKA,EAAI,EAAGA,EAAI8a,EAAgB,OAAQ9a,IACpC6a,EAAYC,EAAgB9a,CAAC,EAAIA,EACjCuL,EAAO+O,EAAUO,CAAS,EAC1BP,EAAU,OAAOO,EAAW,CAAC,EAC7B,OAAOL,EAAWjP,EAAK,EAAE,CAErC,EAQIkL,EAAM,MAAQ,SAASnL,EAAO,CAC1B,OAAAA,EAAM,MAAQ,CAAA,EACdA,EAAM,KAAK,OAAS,EACpBA,EAAM,eAAe,OAAS,EAC9BA,EAAM,gBAAgB,OAAS,EAC/BA,EAAM,aAAa,OAAS,EACrBA,CACf,CAEA,GAAC,GAKM,SAAS1M,EAAQM,EAASF,EAAqB,CAEtD,IAAI+b,EAASnc,EAAO,QAAUI,EAAoB,EAAE,EAEpD+b,EAAO,KAAO/b,EAAoB,EAAE,EACpC+b,EAAO,OAAS/b,EAAoB,EAAE,EACtC+b,EAAO,KAAO/b,EAAoB,CAAC,EACnC+b,EAAO,OAAS/b,EAAoB,CAAC,EACrC+b,EAAO,UAAY/b,EAAoB,CAAC,EACxC+b,EAAO,OAAS/b,EAAoB,CAAC,EACrC+b,EAAO,UAAY/b,EAAoB,CAAC,EACxC+b,EAAO,WAAa/b,EAAoB,EAAE,EAC1C+b,EAAO,WAAa/b,EAAoB,EAAE,EAC1C+b,EAAO,QAAU/b,EAAoB,EAAE,EACvC+b,EAAO,SAAW/b,EAAoB,EAAE,EACxC+b,EAAO,OAAS/b,EAAoB,EAAE,EACtC+b,EAAO,OAAS/b,EAAoB,CAAC,EACrC+b,EAAO,KAAO/b,EAAoB,EAAE,EACpC+b,EAAO,MAAQ/b,EAAoB,EAAE,EACrC+b,EAAO,gBAAkB/b,EAAoB,EAAE,EAC/C+b,EAAO,KAAO/b,EAAoB,CAAC,EACnC+b,EAAO,MAAQ/b,EAAoB,EAAE,EACrC+b,EAAO,OAAS/b,EAAoB,EAAE,EACtC+b,EAAO,MAAQ/b,EAAoB,EAAE,EACrC+b,EAAO,OAAS/b,EAAoB,EAAE,EACtC+b,EAAO,SAAW/b,EAAoB,EAAE,EACxC+b,EAAO,OAAS/b,EAAoB,EAAE,EACtC+b,EAAO,IAAM/b,EAAoB,EAAE,EACnC+b,EAAO,SAAW/b,EAAoB,CAAC,EACvC+b,EAAO,IAAM/b,EAAoB,EAAE,EACnC+b,EAAO,OAAS/b,EAAoB,CAAC,EACrC+b,EAAO,SAAW/b,EAAoB,CAAC,EACvC+b,EAAO,MAAQ/b,EAAoB,EAAE,EAGrC+b,EAAO,OAAO,IAAMA,EAAO,OAAO,IAClCA,EAAO,OAAO,WAAWA,EAAO,OAAQ,MAAO,oDAAoD,GAK5F,SAASnc,EAAQM,EAASF,EAAqB,CAQtD,IAAI+b,EAAS,CAAA,EAEbnc,EAAO,QAAUmc,EAEjB,IAAI7F,EAASlW,EAAoB,EAAE,EAC/BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAQR+b,EAAO,KAAO,YAQdA,EAAO,QAAkB,SAQzBA,EAAO,KAAO,GAQdA,EAAO,KAAO,GAUdA,EAAO,IAAM,UAAW,CACpB7F,EAAO,IAAI6F,EAAQ,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC,CAChE,EAUIA,EAAO,OAAS,SAAS1a,EAAMkB,EAAM,CACjC,OAAAlB,EAAOA,EAAK,QAAQ,WAAY,EAAE,EAC3BV,EAAO,gBAAgBob,EAAQ1a,EAAMkB,CAAI,CACxD,EAUIwZ,EAAO,MAAQ,SAAS1a,EAAMkB,EAAM,CAChC,OAAAlB,EAAOA,EAAK,QAAQ,WAAY,EAAE,EAC3BV,EAAO,eAAeob,EAAQ1a,EAAMkB,CAAI,CACvD,CAEA,MAKO,SAAS3C,EAAQM,EAASF,EAAqB,CAWtD,IAAIgc,EAAa,CAAA,EAEjBpc,EAAO,QAAUoc,EAEjB,IAAIhR,EAAYhL,EAAoB,CAAC,EACjCwQ,EAAaxQ,EAAoB,EAAE,EACnCW,EAASX,EAAoB,CAAC,EAC9BiI,EAAOjI,EAAoB,CAAC,EAC5BmS,EAASnS,EAAoB,EAAE,EAC/Bic,EAAatb,EAAO,YAEvB,UAAW,CAeRqb,EAAW,MAAQ,SAAS9J,EAAIgB,EAAIgJ,EAASC,EAAMC,EAAWC,EAAQ5R,EAAU,CAO5E,QANI6R,EAAQtR,EAAU,OAAO,CAAE,MAAO,OAAO,CAAE,EAC3C5G,EAAI8N,EACJ7N,EAAI6O,EACJqJ,EACAvb,EAAI,EAECwb,EAAM,EAAGA,EAAML,EAAMK,IAAO,CAGjC,QAFIC,EAAY,EAEPC,EAAS,EAAGA,EAASR,EAASQ,IAAU,CAC7C,IAAIxX,EAAOuF,EAASrG,EAAGC,EAAGqY,EAAQF,EAAKD,EAAUvb,CAAC,EAElD,GAAIkE,EAAM,CACN,IAAIyX,EAAazX,EAAK,OAAO,IAAI,EAAIA,EAAK,OAAO,IAAI,EACjD0X,EAAY1X,EAAK,OAAO,IAAI,EAAIA,EAAK,OAAO,IAAI,EAEhDyX,EAAaF,IACbA,EAAYE,GAEhB1U,EAAK,UAAU/C,EAAM,CAAE,EAAG0X,EAAY,GAAK,EAAGD,EAAa,GAAK,EAEhEvY,EAAIc,EAAK,OAAO,IAAI,EAAIkX,EAExBpR,EAAU,QAAQsR,EAAOpX,CAAI,EAE7BqX,EAAWrX,EACXlE,GAAK,CACzB,MACoBoD,GAAKgY,EAIb/X,GAAKoY,EAAYJ,EACjBjY,EAAI8N,EAGR,OAAOoK,CACf,EAaIN,EAAW,MAAQ,SAAS/Q,EAAW4R,EAAUC,EAAUC,EAAUC,EAAU5U,EAAS,CAGpF,QAFIuD,EAASV,EAAU,OAEdjK,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIyL,EAAQd,EAAO3K,EAAI,CAAC,EACpB0L,EAAQf,EAAO3K,CAAC,EAChBic,EAAcxQ,EAAM,OAAO,IAAI,EAAIA,EAAM,OAAO,IAAI,EACpDyQ,EAAazQ,EAAM,OAAO,IAAI,EAAIA,EAAM,OAAO,IAAI,EACnD0Q,EAAczQ,EAAM,OAAO,IAAI,EAAIA,EAAM,OAAO,IAAI,EACpD0Q,EAAa1Q,EAAM,OAAO,IAAI,EAAIA,EAAM,OAAO,IAAI,EAEnDrE,EAAW,CACX,MAAOoE,EACP,OAAQ,CAAE,EAAGyQ,EAAaL,EAAU,EAAGI,EAAcH,CAAQ,EAC7D,MAAOpQ,EACP,OAAQ,CAAE,EAAG0Q,EAAaL,EAAU,EAAGI,EAAcH,CAAQ,CAC7E,EAEgBvR,EAAa9K,EAAO,OAAO0H,EAAUD,CAAO,EAEhD4C,EAAU,cAAcC,EAAWuF,EAAW,OAAO/E,CAAU,CAAC,EAGpE,OAAAR,EAAU,OAAS,SAEZA,CACf,EAYI+Q,EAAW,KAAO,SAAS/Q,EAAWiR,EAASC,EAAMkB,EAAYjV,EAAS,CACtE,IAAIuD,EAASV,EAAU,OACnBuR,EACAc,EACA7Q,EACAC,EACA6Q,EAEJ,IAAKf,EAAM,EAAGA,EAAML,EAAMK,IAAO,CAC7B,IAAKc,EAAM,EAAGA,EAAMpB,EAASoB,IACzB7Q,EAAQd,EAAQ2R,EAAM,EAAMd,EAAMN,CAAQ,EAC1CxP,EAAQf,EAAO2R,EAAOd,EAAMN,CAAQ,EACpClR,EAAU,cAAcC,EAAWuF,EAAW,OAAO7P,EAAO,OAAO,CAAE,MAAO8L,EAAO,MAAOC,CAAK,EAAItE,CAAO,CAAC,CAAC,EAGhH,GAAIoU,EAAM,EACN,IAAKc,EAAM,EAAGA,EAAMpB,EAASoB,IACzB7Q,EAAQd,EAAO2R,GAAQd,EAAM,GAAKN,CAAQ,EAC1CxP,EAAQf,EAAO2R,EAAOd,EAAMN,CAAQ,EACpClR,EAAU,cAAcC,EAAWuF,EAAW,OAAO7P,EAAO,OAAO,CAAE,MAAO8L,EAAO,MAAOC,CAAK,EAAItE,CAAO,CAAC,CAAC,EAExGiV,GAAcC,EAAM,IACpBC,EAAQ5R,EAAQ2R,EAAM,GAAOd,EAAM,GAAKN,CAAQ,EAChDlR,EAAU,cAAcC,EAAWuF,EAAW,OAAO7P,EAAO,OAAO,CAAE,MAAO4c,EAAO,MAAO7Q,CAAK,EAAItE,CAAO,CAAC,CAAC,GAG5GiV,GAAcC,EAAMpB,EAAU,IAC9BqB,EAAQ5R,EAAQ2R,EAAM,GAAOd,EAAM,GAAKN,CAAQ,EAChDlR,EAAU,cAAcC,EAAWuF,EAAW,OAAO7P,EAAO,OAAO,CAAE,MAAO4c,EAAO,MAAO7Q,CAAK,EAAItE,CAAO,CAAC,CAAC,GAM5H,OAAA6C,EAAU,OAAS,QAEZA,CACf,EAeI+Q,EAAW,QAAU,SAAS9J,EAAIgB,EAAIgJ,EAASC,EAAMC,EAAWC,EAAQ5R,EAAU,CAC9E,OAAOuR,EAAW,MAAM9J,EAAIgB,EAAIgJ,EAASC,EAAMC,EAAWC,EAAQ,SAASjY,EAAGC,EAAGqY,EAAQF,EAAKD,EAAUvb,EAAG,CACvG,IAAIwc,EAAa,KAAK,IAAIrB,EAAM,KAAK,KAAKD,EAAU,CAAC,CAAC,EAClDuB,EAAgBlB,EAAWA,EAAS,OAAO,IAAI,EAAIA,EAAS,OAAO,IAAI,EAAI,EAE/E,GAAI,EAAAC,EAAMgB,GAIV,CAAAhB,EAAMgB,EAAahB,EAEnB,IAAIkB,EAAQlB,EACRjb,EAAM2a,EAAU,EAAIM,EAExB,GAAI,EAAAE,EAASgB,GAAShB,EAASnb,GAI/B,CAAIP,IAAM,GACNiH,EAAK,UAAUsU,EAAU,CAAE,GAAIG,GAAUR,EAAU,IAAM,EAAI,EAAI,KAAOuB,EAAe,EAAG,EAAG,EAGjG,IAAIE,EAAUpB,EAAWG,EAASe,EAAgB,EAElD,OAAOhT,EAASyH,EAAKyL,EAAUjB,EAASN,EAAW/X,EAAGqY,EAAQF,EAAKD,EAAUvb,CAAC,GAC1F,CAAS,CACT,EAaIgb,EAAW,cAAgB,SAAS9J,EAAIgB,EAAI0K,EAAQC,EAAMlN,EAAQ,CAG9D,QAFImN,EAAgB9S,EAAU,OAAO,CAAE,MAAO,gBAAgB,CAAE,EAEvDhK,EAAI,EAAGA,EAAI4c,EAAQ5c,IAAK,CAC7B,IAAI+c,EAAa,IACb/K,EAASb,EAAO,OAAOD,EAAKlR,GAAK6c,EAAOE,GAAa7K,EAAKvC,EAAQkN,EAC9D,CAAE,QAAS,IAAU,YAAa,EAAG,SAAU,EAAG,YAAa,KAAQ,KAAM,CAAC,CAAE,EACpFpS,EAAa+E,EAAW,OAAO,CAAE,OAAQ,CAAE,EAAG0B,EAAKlR,GAAK6c,EAAOE,GAAa,EAAG7K,CAAE,EAAI,MAAOF,EAAQ,EAExGhI,EAAU,QAAQ8S,EAAe9K,CAAM,EACvChI,EAAU,cAAc8S,EAAerS,CAAU,EAGrD,OAAOqS,CACf,EAEI7B,EAAWD,EAAY,gBAAiB,2DAA2D,EAanGA,EAAW,IAAM,SAAS9J,EAAIgB,EAAId,EAAOC,EAAQ2L,EAAW,CACxD,IAAIC,EAAQhW,EAAK,UAAU,EAAI,EAC3BiW,EAAY,GACZC,EAAe,CAAC/L,EAAQ,GAAM8L,EAC9BE,EAAehM,EAAQ,GAAM8L,EAC7BG,EAAe,EAEfC,EAAMtT,EAAU,OAAO,CAAE,MAAO,KAAK,CAAE,EACvC9F,EAAOiN,EAAO,UAAUD,EAAIgB,EAAId,EAAOC,EAAQ,CAC3C,gBAAiB,CACb,MAAO4L,GAEX,QAAS,CACL,OAAQ5L,EAAS,IAErB,QAAS,IACzB,CAAa,EAEDkM,EAASpM,EAAO,OAAOD,EAAKiM,EAAcjL,EAAKmL,EAAcL,EAAW,CACxE,gBAAiB,CACb,MAAOC,GAEX,SAAU,EACtB,CAAS,EAEGO,EAASrM,EAAO,OAAOD,EAAKkM,EAAclL,EAAKmL,EAAcL,EAAW,CACxE,gBAAiB,CACb,MAAOC,GAEX,SAAU,EACtB,CAAS,EAEGQ,EAAQjO,EAAW,OAAO,CAC1B,MAAOtL,EACP,OAAQ,CAAE,EAAGiZ,EAAc,EAAGE,CAAY,EAC1C,MAAOE,EACP,UAAW,EACX,OAAQ,CACpB,CAAS,EAEGG,EAAQlO,EAAW,OAAO,CAC1B,MAAOtL,EACP,OAAQ,CAAE,EAAGkZ,EAAc,EAAGC,CAAY,EAC1C,MAAOG,EACP,UAAW,EACX,OAAQ,CACpB,CAAS,EAED,OAAAxT,EAAU,QAAQsT,EAAKpZ,CAAI,EAC3B8F,EAAU,QAAQsT,EAAKC,CAAM,EAC7BvT,EAAU,QAAQsT,EAAKE,CAAM,EAC7BxT,EAAU,cAAcsT,EAAKG,CAAK,EAClCzT,EAAU,cAAcsT,EAAKI,CAAK,EAE3BJ,CACf,EAEIrC,EAAWD,EAAY,MAAO,uCAAuC,EAmBrEA,EAAW,SAAW,SAAS9J,EAAIgB,EAAIgJ,EAASC,EAAMC,EAAWC,EAAQgB,EAAYsB,EAAgBC,EAAiBC,EAAmB,CACrID,EAAkBje,EAAO,OAAO,CAAE,QAAS,GAAQ,EAAIie,CAAe,EACtEC,EAAoBle,EAAO,OAAO,CAAE,UAAW,GAAK,OAAQ,CAAE,KAAM,OAAQ,QAAS,EAAK,CAAE,EAAIke,CAAiB,EAEjH,IAAIC,EAAW9C,EAAW,MAAM9J,EAAIgB,EAAIgJ,EAASC,EAAMC,EAAWC,EAAQ,SAASjY,EAAGC,EAAG,CACrF,OAAO8N,EAAO,OAAO/N,EAAGC,EAAGsa,EAAgBC,CAAe,CACtE,CAAS,EAED,OAAA5C,EAAW,KAAK8C,EAAU5C,EAASC,EAAMkB,EAAYwB,CAAiB,EAEtEC,EAAS,MAAQ,YAEVA,CACf,EAEI7C,EAAWD,EAAY,WAAY,4DAA4D,CACnG,GAAC,GAKM,SAASpc,EAAQM,EAASF,EAAqB,CActD,IAAI+e,EAAO,CAAA,EAEXnf,EAAO,QAAUmf,EAEjB,IAAI/R,EAAOhN,EAAoB,CAAC,EAC5BW,EAASX,EAAoB,CAAC,EAC9Bic,EAAatb,EAAO,YAEvB,UAAW,CASRoe,EAAK,OAAS,SAAS3W,EAAS,CAC5B,IAAIC,EAAW,CACX,QAAS,CAAA,EACT,MAAO,CAAA,EACP,UAAW,CAAA,EACX,YAAa,GACb,aAAc,EAC1B,EAEQ,OAAO1H,EAAO,OAAO0H,EAAUD,CAAO,CAC9C,EA2BI2W,EAAK,OAAS,SAASC,EAAMrT,EAAQ+L,EAAQuH,EAAa,CACtD,IAAIje,EAAGsc,EAAKd,EACR5E,EAAQF,EAAO,MACfwH,EAAUF,EAAK,QACfG,EACAC,EACAC,EAAc,GAElB,IAAKre,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CAChC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAEnB,GAAI,EAAAkE,EAAK,YAAc,CAAC+Z,IAIpB,EAAArH,EAAM,SAAW1S,EAAK,OAAO,IAAI,EAAI0S,EAAM,OAAO,IAAI,GAAK1S,EAAK,OAAO,IAAI,EAAI0S,EAAM,OAAO,IAAI,GAC7F1S,EAAK,OAAO,IAAI,EAAI0S,EAAM,OAAO,IAAI,GAAK1S,EAAK,OAAO,IAAI,EAAI0S,EAAM,OAAO,IAAI,IAGtF,KAAI0H,EAAYP,EAAK,WAAWC,EAAM9Z,CAAI,EAG1C,GAAI,CAACA,EAAK,QAAUoa,EAAU,KAAOpa,EAAK,OAAO,IAAM+Z,EAAa,EAE5D,CAAC/Z,EAAK,QAAU+Z,KAChB/Z,EAAK,OAASoa,GAElB,IAAIC,EAAQR,EAAK,aAAaO,EAAWpa,EAAK,MAAM,EAIpD,IAAKoY,EAAMiC,EAAM,SAAUjC,GAAOiC,EAAM,OAAQjC,IAC5C,IAAKd,EAAM+C,EAAM,SAAU/C,GAAO+C,EAAM,OAAQ/C,IAAO,CACnD4C,EAAWL,EAAK,aAAazB,EAAKd,CAAG,EACrC2C,EAASD,EAAQE,CAAQ,EAEzB,IAAII,EAAqBlC,GAAOgC,EAAU,UAAYhC,GAAOgC,EAAU,QAC5C9C,GAAO8C,EAAU,UAAY9C,GAAO8C,EAAU,OAErEG,EAAqBnC,GAAOpY,EAAK,OAAO,UAAYoY,GAAOpY,EAAK,OAAO,QAChDsX,GAAOtX,EAAK,OAAO,UAAYsX,GAAOtX,EAAK,OAAO,OAGzE,CAACsa,GAAqBC,GAClBA,GACIN,GACAJ,EAAK,kBAAkBC,EAAMG,EAAQja,CAAI,GAKjDA,EAAK,SAAWoa,GAAcE,GAAqB,CAACC,GAAsBR,KACrEE,IACDA,EAASJ,EAAK,cAAcG,EAASE,CAAQ,GACjDL,EAAK,eAAeC,EAAMG,EAAQja,CAAI,GAMlDA,EAAK,OAASoa,EAGdD,EAAc,KAKlBA,IACAL,EAAK,UAAYD,EAAK,uBAAuBC,CAAI,EAC7D,EAEI/C,EAAW8C,EAAM,SAAU,2CAA2C,EAQtEA,EAAK,MAAQ,SAASC,EAAM,CACxBA,EAAK,QAAU,CAAA,EACfA,EAAK,MAAQ,CAAA,EACbA,EAAK,UAAY,CAAA,CACzB,EAEI/C,EAAW8C,EAAM,QAAS,0CAA0C,EAWpEA,EAAK,aAAe,SAASW,EAASC,EAAS,CAC3C,IAAIC,EAAW,KAAK,IAAIF,EAAQ,SAAUC,EAAQ,QAAQ,EACtDE,EAAS,KAAK,IAAIH,EAAQ,OAAQC,EAAQ,MAAM,EAChDG,EAAW,KAAK,IAAIJ,EAAQ,SAAUC,EAAQ,QAAQ,EACtDI,EAAS,KAAK,IAAIL,EAAQ,OAAQC,EAAQ,MAAM,EAEpD,OAAOZ,EAAK,cAAca,EAAUC,EAAQC,EAAUC,CAAM,CACpE,EAWIhB,EAAK,WAAa,SAASC,EAAM9Z,EAAM,CACnC,IAAIzB,EAASyB,EAAK,OACd0a,EAAW,KAAK,MAAMnc,EAAO,IAAI,EAAIub,EAAK,WAAW,EACrDa,EAAS,KAAK,MAAMpc,EAAO,IAAI,EAAIub,EAAK,WAAW,EACnDc,EAAW,KAAK,MAAMrc,EAAO,IAAI,EAAIub,EAAK,YAAY,EACtDe,EAAS,KAAK,MAAMtc,EAAO,IAAI,EAAIub,EAAK,YAAY,EAExD,OAAOD,EAAK,cAAca,EAAUC,EAAQC,EAAUC,CAAM,CACpE,EAaIhB,EAAK,cAAgB,SAASa,EAAUC,EAAQC,EAAUC,EAAQ,CAC9D,MAAO,CACH,GAAIH,EAAW,IAAMC,EAAS,IAAMC,EAAW,IAAMC,EACrD,SAAUH,EACV,OAAQC,EACR,SAAUC,EACV,OAAQC,CACpB,CACA,EAWIhB,EAAK,aAAe,SAASrC,EAAQF,EAAK,CACtC,MAAO,IAAME,EAAS,IAAMF,CACpC,EAWIuC,EAAK,cAAgB,SAASG,EAASE,EAAU,CAC7C,IAAID,EAASD,EAAQE,CAAQ,EAAI,CAAA,EACjC,OAAOD,CACf,EAWIJ,EAAK,eAAiB,SAASC,EAAMG,EAAQja,EAAM,CAC/C,IAAI8a,EAAYhB,EAAK,MACjBiB,EAASjT,EAAK,GACdkT,EAAef,EAAO,OACtB,EAGJ,IAAK,EAAI,EAAG,EAAIe,EAAc,IAAK,CAC/B,IAAIxT,EAAQyS,EAAO,CAAC,EAEpB,GAAI,EAAAja,EAAK,KAAOwH,EAAM,IAAOxH,EAAK,UAAYwH,EAAM,UAKpD,KAAIZ,EAAKmU,EAAO/a,EAAMwH,CAAK,EACvBH,EAAOyT,EAAUlU,CAAE,EAEnBS,EACAA,EAAK,CAAC,GAAK,EAEXyT,EAAUlU,CAAE,EAAI,CAAC5G,EAAMwH,EAAO,CAAC,GAKvCyS,EAAO,KAAKja,CAAI,CACxB,EAWI6Z,EAAK,kBAAoB,SAASC,EAAMG,EAAQja,EAAM,CAClD,IAAI8a,EAAYhB,EAAK,MACjBiB,EAASjT,EAAK,GACdhM,EAGJme,EAAO,OAAOxe,EAAO,QAAQwe,EAAQja,CAAI,EAAG,CAAC,EAE7C,IAAIgb,EAAef,EAAO,OAG1B,IAAKne,EAAI,EAAGA,EAAIkf,EAAclf,IAAK,CAG/B,IAAIuL,EAAOyT,EAAUC,EAAO/a,EAAMia,EAAOne,CAAC,CAAC,CAAC,EAExCuL,IACAA,EAAK,CAAC,GAAK,GAE3B,EAUIwS,EAAK,uBAAyB,SAASC,EAAM,CACzC,IAAIzS,EACAyT,EAAYhB,EAAK,MACjBmB,EAAWxf,EAAO,KAAKqf,CAAS,EAChCI,EAAiBD,EAAS,OAC1B7T,EAAQ,CAAA,EACRzE,EAGJ,IAAKA,EAAI,EAAGA,EAAIuY,EAAgBvY,IAC5B0E,EAAOyT,EAAUG,EAAStY,CAAC,CAAC,EAIxB0E,EAAK,CAAC,EAAI,EACVD,EAAM,KAAKC,CAAI,EAEf,OAAOyT,EAAUG,EAAStY,CAAC,CAAC,EAIpC,OAAOyE,CACf,CAEA,GAAC,GAKM,SAAS1M,EAAQM,EAASF,EAAqB,CAWtD,IAAIqgB,EAAkB,CAAA,EAEtBzgB,EAAO,QAAUygB,EAEjB,IAAIrb,EAAWhF,EAAoB,CAAC,EAChCkI,EAAWlI,EAAoB,CAAC,EAChCwV,EAAQxV,EAAoB,EAAE,EAC9BuK,EAASvK,EAAoB,CAAC,EAC9BuU,EAAWvU,EAAoB,EAAE,EACjCwQ,EAAaxQ,EAAoB,EAAE,EACnCgL,EAAYhL,EAAoB,CAAC,EACjCW,EAASX,EAAoB,CAAC,EAC9BuD,EAASvD,EAAoB,CAAC,GAEjC,UAAW,CAWRqgB,EAAgB,OAAS,SAAS3I,EAAQtP,EAAS,CAC/C,IAAIsN,GAASgC,EAASA,EAAO,MAAQ,QAAUtP,EAAUA,EAAQ,MAAQ,MAEpEsN,IACGgC,GAAUA,EAAO,QAAUA,EAAO,OAAO,OACzChC,EAAQF,EAAM,OAAOkC,EAAO,OAAO,MAAM,EAClCtP,GAAWA,EAAQ,QAC1BsN,EAAQF,EAAM,OAAOpN,EAAQ,OAAO,GAEpCsN,EAAQF,EAAM,OAAM,EACpB7U,EAAO,KAAK,kHAAkH,IAItI,IAAI8K,EAAa+E,EAAW,OAAO,CAC/B,MAAO,mBACP,OAAQkF,EAAM,SACd,OAAQ,CAAE,EAAG,EAAG,EAAG,CAAC,EACpB,OAAQ,IACR,UAAW,GACX,iBAAkB,EAClB,OAAQ,CACJ,YAAa,UACb,UAAW,EAE3B,CAAS,EAEGrN,EAAW,CACX,KAAM,kBACN,MAAOqN,EACP,QAAS,KACT,KAAM,KACN,WAAYjK,EACZ,gBAAiB,CACb,SAAU,EACV,KAAM,WACN,MAAO,EAEvB,EAEY6U,EAAkB3f,EAAO,OAAO0H,EAAUD,CAAO,EAErD,OAAAmC,EAAO,GAAGmN,EAAQ,eAAgB,UAAW,CACzC,IAAII,EAAY9M,EAAU,UAAU0M,EAAO,KAAK,EAChD2I,EAAgB,OAAOC,EAAiBxI,CAAS,EACjDuI,EAAgB,eAAeC,CAAe,CAC1D,CAAS,EAEMA,CACf,EASID,EAAgB,OAAS,SAASC,EAAiB3U,EAAQ,CACvD,IAAI+J,EAAQ4K,EAAgB,MACxB7U,EAAa6U,EAAgB,WAC7Bpb,EAAOob,EAAgB,KAE3B,GAAI5K,EAAM,SAAW,GACjB,GAAKjK,EAAW,MAsBZvD,EAAS,IAAIuD,EAAW,MAAO,EAAK,EACpCA,EAAW,OAASiK,EAAM,aAtB1B,SAAS1U,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAE/B,GADAkE,EAAOyG,EAAO3K,CAAC,EACXuC,EAAO,SAAS2B,EAAK,OAAQwQ,EAAM,QAAQ,GACpCnB,EAAS,WAAWrP,EAAK,gBAAiBob,EAAgB,eAAe,EAChF,QAAS3e,EAAIuD,EAAK,MAAM,OAAS,EAAI,EAAI,EAAGvD,EAAIuD,EAAK,MAAM,OAAQvD,IAAK,CACpE,IAAIkH,EAAO3D,EAAK,MAAMvD,CAAC,EACvB,GAAIqD,EAAS,SAAS6D,EAAK,SAAU6M,EAAM,QAAQ,EAAG,CAClDjK,EAAW,OAASiK,EAAM,SAC1BjK,EAAW,MAAQ6U,EAAgB,KAAOpb,EAC1CuG,EAAW,OAAS,CAAE,EAAGiK,EAAM,SAAS,EAAIxQ,EAAK,SAAS,EAAG,EAAGwQ,EAAM,SAAS,EAAIxQ,EAAK,SAAS,CAAC,EAClGuG,EAAW,OAASvG,EAAK,MAEzBgD,EAAS,IAAIhD,EAAM,EAAK,EACxBqF,EAAO,QAAQ+V,EAAiB,YAAa,CAAE,MAAO5K,EAAO,KAAMxQ,EAAM,EAEzE,aAUpBuG,EAAW,MAAQ6U,EAAgB,KAAO,KAC1C7U,EAAW,OAAS,KAEhBvG,GACAqF,EAAO,QAAQ+V,EAAiB,UAAW,CAAE,MAAO5K,EAAO,KAAMxQ,EAAM,CAEvF,EAQImb,EAAgB,eAAiB,SAASC,EAAiB,CACvD,IAAI5K,EAAQ4K,EAAgB,MACxBC,EAAc7K,EAAM,aAEpB6K,EAAY,WACZhW,EAAO,QAAQ+V,EAAiB,YAAa,CAAE,MAAO5K,EAAO,EAE7D6K,EAAY,WACZhW,EAAO,QAAQ+V,EAAiB,YAAa,CAAE,MAAO5K,EAAO,EAE7D6K,EAAY,SACZhW,EAAO,QAAQ+V,EAAiB,UAAW,CAAE,MAAO5K,EAAO,EAG/DF,EAAM,kBAAkBE,CAAK,CACrC,CA2GA,GAAC,GAKM,SAAS9V,EAAQM,EAASF,EAAqB,CAUtD,IAAIwgB,EAAQ,CAAA,EAEZ5gB,EAAO,QAAU4gB,EAEjB,IAAIrc,EAASnE,EAAoB,CAAC,EAC9B+M,EAAY/M,EAAoB,CAAC,EACjCuD,EAASvD,EAAoB,CAAC,EAC9BmS,EAASnS,EAAoB,EAAE,EAC/BgF,EAAWhF,EAAoB,CAAC,GAEnC,UAAW,CASRwgB,EAAM,SAAW,SAAStb,EAAMyG,EAAQ,CAOpC,QANI8I,EAAa,CAAA,EACbC,EAAe/I,EAAO,OACtBlI,EAASyB,EAAK,OACd0P,EAAW7H,EAAU,SACrB0T,EAAWld,EAAO,SAEbvC,EAAI,EAAGA,EAAI0T,EAAc1T,IAAK,CACnC,IAAIyL,EAAQd,EAAO3K,CAAC,EAChBiU,EAAexI,EAAM,MAAM,OAC3B2I,EAAcH,IAAiB,EAAI,EAAI,EAE3C,GAAIwL,EAAShU,EAAM,OAAQhJ,CAAM,EAC7B,QAAS9B,EAAIyT,EAAazT,EAAIsT,EAActT,IAAK,CAC7C,IAAIkH,EAAO4D,EAAM,MAAM9K,CAAC,EAExB,GAAI8e,EAAS5X,EAAK,OAAQpF,CAAM,EAAG,CAC/B,IAAI+I,EAAYoI,EAAS/L,EAAM3D,CAAI,EAEnC,GAAIsH,EAAW,CACXiI,EAAW,KAAKjI,CAAS,EACzB,SAOpB,OAAOiI,CACf,EAWI+L,EAAM,IAAM,SAAS7U,EAAQ+U,EAAYC,EAAUC,EAAU,CACzDA,EAAWA,GAAY,OASvB,QAPIC,EAAW1c,EAAO,MAAMuc,EAAYC,CAAQ,EAC5CG,EAAY3c,EAAO,UAAUA,EAAO,IAAIuc,EAAYC,CAAQ,CAAC,EAC7DI,GAAQJ,EAAS,EAAID,EAAW,GAAK,GACrCM,GAAQL,EAAS,EAAID,EAAW,GAAK,GACrCO,EAAM9O,EAAO,UAAU4O,EAAMC,EAAMF,EAAWF,EAAU,CAAE,MAAOC,EAAU,EAC3EpM,EAAa+L,EAAM,SAASS,EAAKtV,CAAM,EAElC3K,EAAI,EAAGA,EAAIyT,EAAW,OAAQzT,GAAK,EAAG,CAC3C,IAAIwL,EAAYiI,EAAWzT,CAAC,EAC5BwL,EAAU,KAAOA,EAAU,MAAQA,EAAU,MAGjD,OAAOiI,CACf,EAUI+L,EAAM,OAAS,SAAS7U,EAAQlI,EAAQyd,EAAS,CAG7C,QAFIxe,EAAS,CAAA,EAEJ,EAAI,EAAG,EAAIiJ,EAAO,OAAQ,IAAK,CACpC,IAAIzG,EAAOyG,EAAO,CAAC,EACf8U,EAAWld,EAAO,SAAS2B,EAAK,OAAQzB,CAAM,GAC7Cgd,GAAY,CAACS,GAAa,CAACT,GAAYS,IACxCxe,EAAO,KAAKwC,CAAI,EAGxB,OAAOxC,CACf,EASI8d,EAAM,MAAQ,SAAS7U,EAAQ/H,EAAO,CAGlC,QAFIlB,EAAS,CAAA,EAEJ1B,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAEnB,GAAIuC,EAAO,SAAS2B,EAAK,OAAQtB,CAAK,EAClC,QAASjC,EAAIuD,EAAK,MAAM,SAAW,EAAI,EAAI,EAAGvD,EAAIuD,EAAK,MAAM,OAAQvD,IAAK,CACtE,IAAIkH,EAAO3D,EAAK,MAAMvD,CAAC,EAEvB,GAAI4B,EAAO,SAASsF,EAAK,OAAQjF,CAAK,GAC/BoB,EAAS,SAAS6D,EAAK,SAAUjF,CAAK,EAAG,CAC5ClB,EAAO,KAAKwC,CAAI,EAChB,QAMhB,OAAOxC,CACf,CAEA,GAAC,GAKM,SAAS9C,EAAQM,EAASF,EAAqB,CAUtD,IAAImhB,EAAS,CAAA,EAEbvhB,EAAO,QAAUuhB,EAEjB,IAAIlZ,EAAOjI,EAAoB,CAAC,EAC5BW,EAASX,EAAoB,CAAC,EAC9BgL,EAAYhL,EAAoB,CAAC,EACjCuD,EAASvD,EAAoB,CAAC,EAC9BuK,EAASvK,EAAoB,CAAC,EAC9BmE,EAASnE,EAAoB,CAAC,EAC9BwV,EAAQxV,EAAoB,EAAE,GAEjC,UAAW,CAER,IAAIohB,EACAC,EAEA,OAAO,OAAW,MAClBD,EAAyB,OAAO,uBAAyB,OAAO,6BAC/B,OAAO,0BAA4B,OAAO,yBAC1C,SAAS3W,EAAS,CAAE,OAAO,WAAW,UAAW,CAAEA,EAAS9J,EAAO,IAAG,CAAE,CAAE,EAAI,IAAO,EAAE,CAAE,EAE1H0gB,EAAwB,OAAO,sBAAwB,OAAO,yBAC7B,OAAO,4BAA8B,OAAO,wBAGjFF,EAAO,SAAW,GAClBA,EAAO,WAAa,IAAO,GAU3BA,EAAO,OAAS,SAAS/Y,EAAS,CAC9B,IAAIC,EAAW,CACX,OAAQ,KACR,QAAS,KACT,OAAQ,KACR,MAAO,KACP,eAAgB,KAChB,OAAQ,CACJ,YAAa,GACb,MAAO,EACP,aAAc,CAAA,EACd,SAAU,EACV,cAAe,EACf,YAAa,EACb,iBAAkB,EAClB,wBAAyB,CAAA,EACzB,mBAAoB,CAAA,EACpB,qBAAsB,CAAA,EACtB,eAAgB,CAAA,GAEpB,QAAS,CACL,MAAO,IACP,OAAQ,IACR,WAAY,EACZ,WAAY,UACZ,oBAAqB,UACrB,UAAW,CAAC,CAACD,EAAQ,OACrB,QAAS,GACT,WAAY,GACZ,aAAc,GACd,UAAW,GACX,UAAW,GACX,gBAAiB,GACjB,WAAY,GACZ,aAAc,GACd,eAAgB,GAChB,gBAAiB,GACjB,SAAU,GACV,cAAe,GACf,mBAAoB,GACpB,QAAS,GACT,kBAAmB,GACnB,gBAAiB,GACjB,kBAAmB,GACnB,kBAAmB,GAEnC,EAEYwI,EAASjQ,EAAO,OAAO0H,EAAUD,CAAO,EAE5C,OAAIwI,EAAO,SACPA,EAAO,OAAO,MAAQA,EAAO,QAAQ,OAASA,EAAO,OAAO,MAC5DA,EAAO,OAAO,OAASA,EAAO,QAAQ,QAAUA,EAAO,OAAO,QAGlEA,EAAO,MAAQxI,EAAQ,MACvBwI,EAAO,OAASxI,EAAQ,OACxBwI,EAAO,OAASA,EAAO,QAAU0Q,EAAc1Q,EAAO,QAAQ,MAAOA,EAAO,QAAQ,MAAM,EAC1FA,EAAO,QAAUA,EAAO,OAAO,WAAW,IAAI,EAC9CA,EAAO,SAAW,CAAA,EAElBA,EAAO,OAASA,EAAO,QAAU,CAC7B,IAAK,CACD,EAAG,EACH,EAAG,GAEP,IAAK,CACD,EAAGA,EAAO,OAAO,MACjB,EAAGA,EAAO,OAAO,OAEjC,EAGQA,EAAO,WAAauQ,EACpBvQ,EAAO,QAAQ,eAAiB,GAE5BA,EAAO,QAAQ,aAAe,GAC9BuQ,EAAO,cAAcvQ,EAAQA,EAAO,QAAQ,UAAU,EAGtDjQ,EAAO,UAAUiQ,EAAO,OAAO,GAC/BA,EAAO,QAAQ,YAAYA,EAAO,MAAM,EAGrCA,CACf,EAOIuQ,EAAO,IAAM,SAASvQ,EAAQ,EACzB,SAAS2Q,EAAKC,EAAK,CAChB5Q,EAAO,eAAiBwQ,EAAuBG,CAAI,EAEnDE,EAAc7Q,EAAQ4Q,CAAI,EAE1BL,EAAO,MAAMvQ,EAAQ4Q,CAAI,GAErB5Q,EAAO,QAAQ,WAAaA,EAAO,QAAQ,YAC3CuQ,EAAO,MAAMvQ,EAAQA,EAAO,QAAS4Q,CAAI,GAGzC5Q,EAAO,QAAQ,iBAAmBA,EAAO,QAAQ,YACjDuQ,EAAO,YAAYvQ,EAAQA,EAAO,QAAS4Q,CAAI,CAE/D,GAAS,CACT,EAOIL,EAAO,KAAO,SAASvQ,EAAQ,CAC3ByQ,EAAsBzQ,EAAO,cAAc,CACnD,EASIuQ,EAAO,cAAgB,SAASvQ,EAAQiF,EAAY,CAChD,IAAIzN,EAAUwI,EAAO,QACjB8Q,EAAS9Q,EAAO,OAEhBiF,IAAe,SACfA,EAAa8L,EAAeD,CAAM,GAGtCtZ,EAAQ,WAAayN,EACrB6L,EAAO,aAAa,mBAAoB7L,CAAU,EAClD6L,EAAO,MAAQtZ,EAAQ,MAAQyN,EAC/B6L,EAAO,OAAStZ,EAAQ,OAASyN,EACjC6L,EAAO,MAAM,MAAQtZ,EAAQ,MAAQ,KACrCsZ,EAAO,MAAM,OAAStZ,EAAQ,OAAS,IAC/C,EAeI+Y,EAAO,OAAS,SAASvQ,EAAQtF,EAASsW,EAASC,EAAQ,CACvDA,EAAS,OAAOA,EAAW,IAAcA,EAAS,GAClDvW,EAAU3K,EAAO,QAAQ2K,CAAO,EAAIA,EAAU,CAACA,CAAO,EACtDsW,EAAUA,GAAW,CACjB,EAAG,EACH,EAAG,CACf,EAQQ,QALIne,EAAS,CACT,IAAK,CAAE,EAAG,IAAU,EAAG,GAAQ,EAC/B,IAAK,CAAE,EAAG,KAAW,EAAG,IAAS,CAC7C,EAEiBzC,EAAI,EAAGA,EAAIsK,EAAQ,OAAQtK,GAAK,EAAG,CACxC,IAAIP,EAAS6K,EAAQtK,CAAC,EAClBc,EAAMrB,EAAO,OAASA,EAAO,OAAO,IAAOA,EAAO,KAAOA,EAAO,UAAYA,EAC5EsB,EAAMtB,EAAO,OAASA,EAAO,OAAO,IAAOA,EAAO,KAAOA,EAAO,UAAYA,EAE5EqB,GAAOC,IACHD,EAAI,EAAI2B,EAAO,IAAI,IACnBA,EAAO,IAAI,EAAI3B,EAAI,GAEnBC,EAAI,EAAI0B,EAAO,IAAI,IACnBA,EAAO,IAAI,EAAI1B,EAAI,GAEnBD,EAAI,EAAI2B,EAAO,IAAI,IACnBA,EAAO,IAAI,EAAI3B,EAAI,GAEnBC,EAAI,EAAI0B,EAAO,IAAI,IACnBA,EAAO,IAAI,EAAI1B,EAAI,IAK/B,IAAIqQ,EAAS3O,EAAO,IAAI,EAAIA,EAAO,IAAI,EAAK,EAAIme,EAAQ,EACpDvP,EAAU5O,EAAO,IAAI,EAAIA,EAAO,IAAI,EAAK,EAAIme,EAAQ,EACrDE,EAAalR,EAAO,OAAO,OAC3BmR,EAAYnR,EAAO,OAAO,MAC1BoR,EAAaD,EAAYD,EACzBG,EAAa7P,EAAQC,EACrB9L,EAAS,EACTC,EAAS,EAGTyb,EAAaD,EACbxb,EAASyb,EAAaD,EAEtBzb,EAASyb,EAAaC,EAI1BrR,EAAO,QAAQ,UAAY,GAG3BA,EAAO,OAAO,IAAI,EAAInN,EAAO,IAAI,EACjCmN,EAAO,OAAO,IAAI,EAAInN,EAAO,IAAI,EAAI2O,EAAQ7L,EAC7CqK,EAAO,OAAO,IAAI,EAAInN,EAAO,IAAI,EACjCmN,EAAO,OAAO,IAAI,EAAInN,EAAO,IAAI,EAAI4O,EAAS7L,EAG1Cqb,IACAjR,EAAO,OAAO,IAAI,GAAKwB,EAAQ,GAAOA,EAAQ7L,EAAU,GACxDqK,EAAO,OAAO,IAAI,GAAKwB,EAAQ,GAAOA,EAAQ7L,EAAU,GACxDqK,EAAO,OAAO,IAAI,GAAKyB,EAAS,GAAOA,EAAS7L,EAAU,GAC1DoK,EAAO,OAAO,IAAI,GAAKyB,EAAS,GAAOA,EAAS7L,EAAU,IAI9DoK,EAAO,OAAO,IAAI,GAAKgR,EAAQ,EAC/BhR,EAAO,OAAO,IAAI,GAAKgR,EAAQ,EAC/BhR,EAAO,OAAO,IAAI,GAAKgR,EAAQ,EAC/BhR,EAAO,OAAO,IAAI,GAAKgR,EAAQ,EAG3BhR,EAAO,QACP4E,EAAM,SAAS5E,EAAO,MAAO,CACzB,GAAIA,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,GAAKA,EAAO,OAAO,MAC/D,GAAIA,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,GAAKA,EAAO,OAAO,MAC/E,CAAa,EAED4E,EAAM,UAAU5E,EAAO,MAAOA,EAAO,OAAO,GAAG,EAE3D,EAOIuQ,EAAO,mBAAqB,SAASvQ,EAAQ,CACzC,IAAIsR,EAActR,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,EACtDuR,EAAevR,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,EACvDwR,EAAeF,EAActR,EAAO,QAAQ,MAC5CyR,EAAeF,EAAevR,EAAO,QAAQ,OAEjDA,EAAO,QAAQ,aACXA,EAAO,QAAQ,WAAawR,EAAc,EAAG,EAC7CxR,EAAO,QAAQ,WAAayR,EAAc,EAAG,CACzD,EAEQzR,EAAO,QAAQ,UAAU,CAACA,EAAO,OAAO,IAAI,EAAG,CAACA,EAAO,OAAO,IAAI,CAAC,CAC3E,EAOIuQ,EAAO,iBAAmB,SAASvQ,EAAQ,CACvCA,EAAO,QAAQ,aAAaA,EAAO,QAAQ,WAAY,EAAG,EAAGA,EAAO,QAAQ,WAAY,EAAG,CAAC,CACpG,EAQIuQ,EAAO,MAAQ,SAASvQ,EAAQ4Q,EAAM,CAClC,IAAI7J,EAAYhX,EAAO,IAAG,EACtB+W,EAAS9G,EAAO,OAChBgH,EAAQF,EAAO,MACfgK,EAAS9Q,EAAO,OAChB0R,EAAU1R,EAAO,QACjBxI,EAAUwI,EAAO,QACjBiH,EAASjH,EAAO,OAEhBkH,EAAY9M,EAAU,UAAU4M,CAAK,EACrCG,EAAiB/M,EAAU,eAAe4M,CAAK,EAC/C2K,EAAana,EAAQ,WAAaA,EAAQ,oBAAsBA,EAAQ,WACxEuD,EAAS,CAAA,EACTC,EAAc,CAAA,EACd5K,EAEA6J,EAAQ,CACR,UAAW6M,EAAO,OAAO,SACrC,EAeQ,GAbAnN,EAAO,QAAQqG,EAAQ,eAAgB/F,CAAK,EAGxC+F,EAAO,oBAAsB2R,GAC7BC,EAAiB5R,EAAQ2R,CAAU,EAGvCD,EAAQ,yBAA2B,YACnCA,EAAQ,UAAY,cACpBA,EAAQ,SAAS,EAAG,EAAGZ,EAAO,MAAOA,EAAO,MAAM,EAClDY,EAAQ,yBAA2B,cAG/Bla,EAAQ,UAAW,CAEnB,IAAKpH,EAAI,EAAGA,EAAI8W,EAAU,OAAQ9W,IAAK,CACnC,IAAIkE,EAAO4S,EAAU9W,CAAC,EAClBuC,EAAO,SAAS2B,EAAK,OAAQ0L,EAAO,MAAM,GAC1CjF,EAAO,KAAKzG,CAAI,EAIxB,IAAKlE,EAAI,EAAGA,EAAI+W,EAAe,OAAQ/W,IAAK,CACxC,IAAIyK,EAAasM,EAAe/W,CAAC,EAC7ByL,EAAQhB,EAAW,MACnBiB,EAAQjB,EAAW,MACnByF,EAAczF,EAAW,OACzB0F,EAAc1F,EAAW,OAEzBgB,IAAOyE,EAAc/M,EAAO,IAAIsI,EAAM,SAAUhB,EAAW,MAAM,GACjEiB,IAAOyE,EAAchN,EAAO,IAAIuI,EAAM,SAAUjB,EAAW,MAAM,GAEjE,GAACyF,GAAe,CAACC,KAGjB5N,EAAO,SAASqN,EAAO,OAAQM,CAAW,GAAK3N,EAAO,SAASqN,EAAO,OAAQO,CAAW,IACzFvF,EAAY,KAAKH,CAAU,EAInC0V,EAAO,mBAAmBvQ,CAAM,EAG5BA,EAAO,QACP4E,EAAM,SAAS5E,EAAO,MAAO,CACzB,GAAIA,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,GAAKA,EAAO,QAAQ,MAChE,GAAIA,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,GAAKA,EAAO,QAAQ,MACpF,CAAiB,EAED4E,EAAM,UAAU5E,EAAO,MAAOA,EAAO,OAAO,GAAG,EAE/D,MACYhF,EAAcmM,EACdpM,EAASmM,EAELlH,EAAO,QAAQ,aAAe,GAC9BA,EAAO,QAAQ,aAAaA,EAAO,QAAQ,WAAY,EAAG,EAAGA,EAAO,QAAQ,WAAY,EAAG,CAAC,EAIhG,CAACxI,EAAQ,YAAesP,EAAO,gBAAkBtP,EAAQ,aAEzD+Y,EAAO,OAAOvQ,EAAQjF,EAAQ2W,CAAO,GAEjCla,EAAQ,iBACR+Y,EAAO,gBAAgBvQ,EAAQjF,EAAQ2W,CAAO,EAGlDnB,EAAO,eAAevQ,EAAQjF,EAAQ2W,CAAO,GAG7Cla,EAAQ,YACR+Y,EAAO,WAAWvQ,EAAQjF,EAAQ2W,CAAO,GAEzCla,EAAQ,UAAYA,EAAQ,qBAC5B+Y,EAAO,SAASvQ,EAAQjF,EAAQ2W,CAAO,EAEvCla,EAAQ,eACR+Y,EAAO,cAAcvQ,EAAQjF,EAAQ2W,CAAO,EAE5Cla,EAAQ,cACR+Y,EAAO,aAAavQ,EAAQjF,EAAQ2W,CAAO,EAE3Cla,EAAQ,SACR+Y,EAAO,QAAQvQ,EAAQjF,EAAQ2W,CAAO,EAEtCla,EAAQ,iBACR+Y,EAAO,YAAYvQ,EAAQ8G,EAAO,MAAM,KAAM4K,CAAO,EAErDla,EAAQ,gBACR+Y,EAAO,WAAWvQ,EAAQ8G,EAAO,MAAM,KAAM4K,CAAO,EAEpDla,EAAQ,mBACR+Y,EAAO,cAAcvQ,EAAQjF,EAAQ2W,CAAO,EAE5Cla,EAAQ,mBACR+Y,EAAO,cAAcvQ,EAAQA,EAAO,MAAO0R,CAAO,EAEtDnB,EAAO,YAAYvV,EAAa0W,CAAO,EAEnCla,EAAQ,WAER+Y,EAAO,iBAAiBvQ,CAAM,EAGlCrG,EAAO,QAAQqG,EAAQ,cAAe/F,CAAK,EAG3CgN,EAAO,YAAclX,EAAO,IAAG,EAAKgX,CAC5C,EAUIwJ,EAAO,MAAQ,SAASvQ,EAAQ0R,EAASd,EAAM,CAW3C,QAVI9J,EAAS9G,EAAO,OAChBgH,EAAQF,EAAO,MACf/L,EAASX,EAAU,UAAU4M,CAAK,EAClCnW,EAAQ,EACR2Q,EAAQ,GACRC,EAAS,GACTjO,EAAI,EACJC,EAAI,EAGCrD,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,GAAK,EACpCS,GAASkK,EAAO3K,CAAC,EAAE,MAAM,OAI7B,IAAIyhB,EAAW,CACX,KAAQhhB,EACR,KAAQkK,EAAO,OACf,KAAQX,EAAU,eAAe4M,CAAK,EAAE,OACxC,KAAQ5M,EAAU,cAAc4M,CAAK,EAAE,OACvC,KAAQF,EAAO,MAAM,KAAK,MACtC,EAGQ4K,EAAQ,UAAY,UACpBA,EAAQ,SAASle,EAAGC,EAAG+N,EAAQ,IAAKC,CAAM,EAE1CiQ,EAAQ,KAAO,aACfA,EAAQ,aAAe,MACvBA,EAAQ,UAAY,QAGpB,QAAS9hB,KAAOiiB,EAAU,CACtB,IAAIC,EAAUD,EAASjiB,CAAG,EAE1B8hB,EAAQ,UAAY,OACpBA,EAAQ,SAAS9hB,EAAK4D,EAAIgO,EAAO/N,EAAI,CAAC,EAGtCie,EAAQ,UAAY,OACpBA,EAAQ,SAASI,EAASte,EAAIgO,EAAO/N,EAAI,EAAE,EAE3CD,GAAKgO,EAEjB,EASI+O,EAAO,YAAc,SAASvQ,EAAQ0R,EAAS,CAC3C,IAAI5K,EAAS9G,EAAO,OAChBiH,EAASjH,EAAO,OAChB+R,EAAe9K,EAAO,aACtB+K,EAAiB/K,EAAO,eACxBgL,EAA0BhL,EAAO,wBACjCiL,EAAqBjL,EAAO,mBAC5BkL,EAAuBlL,EAAO,qBAC9BmL,EAAkBtL,EAAO,OAAO,UAEhCuL,EAAYC,EAAMP,CAAY,EAC9BQ,EAAcD,EAAMN,CAAc,EAClCQ,EAAkBF,EAAMJ,CAAkB,EAC1CO,EAAoBH,EAAMH,CAAoB,EAC9CO,EAAuBJ,EAAML,CAAuB,EACpDU,EAAYD,EAAuBL,GAAc,EACjDO,EAAO,IAAOP,GAAc,EAE5BQ,EAAc,EACdC,EAAM,GACNtR,EAAQ,GACRC,EAAS,GACTjO,EAAI,GACJC,EAAI,GAGRie,EAAQ,UAAY,UACpBA,EAAQ,SAAS,EAAG,GAAIoB,EAAM,EAAItR,EAAQ,EAAI,GAAIC,CAAM,EAGxD8O,EAAO,OACHmB,EAASle,EAAGC,EAAG+N,EAAOqR,EAAad,EAAa,OAChD,KAAK,MAAMa,CAAG,EAAI,OAClBA,EAAMrC,EAAO,SACb,SAASngB,EAAG,CAAE,OAAQ2hB,EAAa3hB,CAAC,EAAIiiB,EAAa,CAAE,CACnE,EAGQ9B,EAAO,OACHmB,EAASle,EAAIsf,EAAMtR,EAAO/N,EAAG+N,EAAOqR,EAAaX,EAAmB,OACpEE,EAAgB,QAAQ,CAAC,EAAI,MAC7B7B,EAAO,WAAa6B,EACpB,SAAShiB,EAAG,CAAE,OAAQ8hB,EAAmB9hB,CAAC,EAAIoiB,EAAmB,CAAE,CAC/E,EAGQjC,EAAO,OACHmB,EAASle,GAAKsf,EAAMtR,GAAS,EAAG/N,EAAG+N,EAAOqR,EAAaV,EAAqB,OAC5EM,EAAkB,QAAQ,CAAC,EAAI,MAC/B,EAAKA,EAAoBlC,EAAO,SAChC,SAASngB,EAAG,CAAE,OAAQ+hB,EAAqB/hB,CAAC,EAAIqiB,EAAqB,CAAE,CACnF,EAGQlC,EAAO,OACHmB,EAASle,GAAKsf,EAAMtR,GAAS,EAAG/N,EAAG+N,EAAOqR,EAAab,EAAe,OACtEO,EAAY,QAAQ,CAAC,EAAI,MACzB,EAAKA,EAAchC,EAAO,SAC1B,SAASngB,EAAG,CAAE,OAAQ4hB,EAAe5hB,CAAC,EAAImiB,EAAe,CAAE,CACvE,EAGQhC,EAAO,OACHmB,EAASle,GAAKsf,EAAMtR,GAAS,EAAG/N,EAAG+N,EAAOqR,EAAaZ,EAAwB,OAC/EU,EAAS,QAAQ,CAAC,EAAI,KACtBA,EAAWA,EAAWA,EACtB,SAASviB,EAAG,CAAE,OAAU6hB,EAAwB7hB,CAAC,EAAI2hB,EAAa3hB,CAAC,EAAKuiB,GAAa,GAAK,CAAE,CACxG,CACA,EAgBIpC,EAAO,OAAS,SAASmB,EAASle,EAAGC,EAAG+N,EAAOC,EAAQsR,EAAOC,EAAOC,EAAWC,EAAO,CAEnFxB,EAAQ,YAAc,OACtBA,EAAQ,UAAY,OACpBA,EAAQ,UAAY,EACpBA,EAAQ,SAASle,EAAGC,EAAI,EAAG+N,EAAO,CAAC,EAGnCkQ,EAAQ,UAAS,EACjBA,EAAQ,OAAOle,EAAGC,EAAI,EAAIgO,EAAS1R,EAAO,MAAM,GAAMmjB,EAAM,CAAC,EAAG,GAAI,CAAC,CAAC,EACtE,QAAS9iB,EAAI,EAAGA,EAAIoR,EAAOpR,GAAK,EAC5BshB,EAAQ,OAAOle,EAAIpD,EAAGqD,EAAI,GAAKrD,EAAI2iB,EAAQtR,EAAS1R,EAAO,MAAM,GAAMmjB,EAAM9iB,CAAC,EAAG,GAAI,CAAC,EAAI,EAAE,EAEhGshB,EAAQ,OAAM,EAGdA,EAAQ,UAAY,OAAS3hB,EAAO,MAAM,GAAK,GAAKkjB,EAAW,EAAG,GAAG,EAAI,aACzEvB,EAAQ,SAASle,EAAGC,EAAI,EAAG,EAAG,CAAC,EAG/Bie,EAAQ,KAAO,aACfA,EAAQ,aAAe,SACvBA,EAAQ,UAAY,QACpBA,EAAQ,UAAY,OACpBA,EAAQ,SAASsB,EAAOxf,EAAIgO,EAAO/N,EAAI,CAAC,CAChD,EASI8c,EAAO,YAAc,SAASvV,EAAa0W,EAAS,CAGhD,QAFIyB,EAAIzB,EAECthB,EAAI,EAAGA,EAAI4K,EAAY,OAAQ5K,IAAK,CACzC,IAAIyK,EAAaG,EAAY5K,CAAC,EAE9B,GAAI,GAACyK,EAAW,OAAO,SAAW,CAACA,EAAW,QAAU,CAACA,EAAW,QAGpE,KAAIgB,EAAQhB,EAAW,MACnBiB,EAAQjB,EAAW,MACnBiS,EACAnc,EAQJ,GANIkL,EACAiR,EAAQvZ,EAAO,IAAIsI,EAAM,SAAUhB,EAAW,MAAM,EAEpDiS,EAAQjS,EAAW,OAGnBA,EAAW,OAAO,OAAS,MAC3BsY,EAAE,UAAS,EACXA,EAAE,IAAIrG,EAAM,EAAGA,EAAM,EAAG,EAAG,EAAG,EAAI,KAAK,EAAE,EACzCqG,EAAE,UAAS,MACR,CAUH,GATIrX,EACAnL,EAAM4C,EAAO,IAAIuI,EAAM,SAAUjB,EAAW,MAAM,EAElDlK,EAAMkK,EAAW,OAGrBsY,EAAE,UAAS,EACXA,EAAE,OAAOrG,EAAM,EAAGA,EAAM,CAAC,EAErBjS,EAAW,OAAO,OAAS,SAM3B,QALIhF,EAAQtC,EAAO,IAAI5C,EAAKmc,CAAK,EAC7BrQ,EAASlJ,EAAO,KAAKA,EAAO,UAAUsC,CAAK,CAAC,EAC5Cud,EAAQ,KAAK,KAAKrjB,EAAO,MAAM8K,EAAW,OAAS,EAAG,GAAI,EAAE,CAAC,EAC7DpB,EAEK1I,EAAI,EAAGA,EAAIqiB,EAAOriB,GAAK,EAC5B0I,EAAS1I,EAAI,IAAM,EAAI,EAAI,GAE3BoiB,EAAE,OACErG,EAAM,EAAIjX,EAAM,GAAK9E,EAAIqiB,GAAS3W,EAAO,EAAIhD,EAAS,EACtDqT,EAAM,EAAIjX,EAAM,GAAK9E,EAAIqiB,GAAS3W,EAAO,EAAIhD,EAAS,CAClF,EAIgB0Z,EAAE,OAAOxiB,EAAI,EAAGA,EAAI,CAAC,EAGrBkK,EAAW,OAAO,YAClBsY,EAAE,UAAYtY,EAAW,OAAO,UAChCsY,EAAE,YAActY,EAAW,OAAO,YAClCsY,EAAE,OAAM,GAGRtY,EAAW,OAAO,UAClBsY,EAAE,UAAYtY,EAAW,OAAO,YAChCsY,EAAE,UAAS,EACXA,EAAE,IAAIrG,EAAM,EAAGA,EAAM,EAAG,EAAG,EAAG,EAAI,KAAK,EAAE,EACzCqG,EAAE,IAAIxiB,EAAI,EAAGA,EAAI,EAAG,EAAG,EAAG,EAAI,KAAK,EAAE,EACrCwiB,EAAE,UAAS,EACXA,EAAE,KAAI,IAGtB,EAUI5C,EAAO,OAAS,SAASvQ,EAAQjF,EAAQ2W,EAAS,CAC9C,IAAIyB,EAAIzB,EACK1R,EAAO,OAC5B,IAAYxI,EAAUwI,EAAO,QACjBqT,EAAoB7b,EAAQ,mBAAqB,CAACA,EAAQ,WAC1DlD,EACA2D,EACA7H,EACA6G,EAEJ,IAAK7G,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAG3B,GAFAkE,EAAOyG,EAAO3K,CAAC,EAEX,EAACkE,EAAK,OAAO,SAIjB,IAAK2C,EAAI3C,EAAK,MAAM,OAAS,EAAI,EAAI,EAAG2C,EAAI3C,EAAK,MAAM,OAAQ2C,IAG3D,GAFAgB,EAAO3D,EAAK,MAAM2C,CAAC,EAEf,EAACgB,EAAK,OAAO,QASjB,IANIT,EAAQ,cAAgBlD,EAAK,WAC7B6e,EAAE,YAAc,GAAMlb,EAAK,OAAO,QAC3BA,EAAK,OAAO,UAAY,IAC/Bkb,EAAE,YAAclb,EAAK,OAAO,SAG5BA,EAAK,OAAO,QAAUA,EAAK,OAAO,OAAO,SAAW,CAACT,EAAQ,WAAY,CAEzE,IAAI8b,EAASrb,EAAK,OAAO,OACrBsb,EAAUC,EAAYxT,EAAQsT,EAAO,OAAO,EAEhDH,EAAE,UAAUlb,EAAK,SAAS,EAAGA,EAAK,SAAS,CAAC,EAC5Ckb,EAAE,OAAOlb,EAAK,KAAK,EAEnBkb,EAAE,UACEI,EACAA,EAAQ,MAAQ,CAACD,EAAO,QAAUA,EAAO,OACzCC,EAAQ,OAAS,CAACD,EAAO,QAAUA,EAAO,OAC1CC,EAAQ,MAAQD,EAAO,OACvBC,EAAQ,OAASD,EAAO,MAChD,EAGoBH,EAAE,OAAO,CAAClb,EAAK,KAAK,EACpBkb,EAAE,UAAU,CAAClb,EAAK,SAAS,EAAG,CAACA,EAAK,SAAS,CAAC,CAClE,KAAuB,CAEH,GAAIA,EAAK,aACLkb,EAAE,UAAS,EACXA,EAAE,IAAIlb,EAAK,SAAS,EAAGA,EAAK,SAAS,EAAGA,EAAK,aAAc,EAAG,EAAI,KAAK,EAAE,MACtE,CACHkb,EAAE,UAAS,EACXA,EAAE,OAAOlb,EAAK,SAAS,CAAC,EAAE,EAAGA,EAAK,SAAS,CAAC,EAAE,CAAC,EAE/C,QAASlH,EAAI,EAAGA,EAAIkH,EAAK,SAAS,OAAQlH,IAClC,CAACkH,EAAK,SAASlH,EAAI,CAAC,EAAE,YAAcsiB,EACpCF,EAAE,OAAOlb,EAAK,SAASlH,CAAC,EAAE,EAAGkH,EAAK,SAASlH,CAAC,EAAE,CAAC,EAE/CoiB,EAAE,OAAOlb,EAAK,SAASlH,CAAC,EAAE,EAAGkH,EAAK,SAASlH,CAAC,EAAE,CAAC,EAG/CkH,EAAK,SAASlH,CAAC,EAAE,YAAc,CAACsiB,GAChCF,EAAE,OAAOlb,EAAK,UAAUlH,EAAI,GAAKkH,EAAK,SAAS,MAAM,EAAE,EAAGA,EAAK,UAAUlH,EAAI,GAAKkH,EAAK,SAAS,MAAM,EAAE,CAAC,EAIjHkb,EAAE,OAAOlb,EAAK,SAAS,CAAC,EAAE,EAAGA,EAAK,SAAS,CAAC,EAAE,CAAC,EAC/Ckb,EAAE,UAAS,EAGV3b,EAAQ,YAWT2b,EAAE,UAAY,EACdA,EAAE,YAAc,OAChBA,EAAE,OAAM,IAZRA,EAAE,UAAYlb,EAAK,OAAO,UAEtBA,EAAK,OAAO,YACZkb,EAAE,UAAYlb,EAAK,OAAO,UAC1Bkb,EAAE,YAAclb,EAAK,OAAO,YAC5Bkb,EAAE,OAAM,GAGZA,EAAE,KAAI,GAQdA,EAAE,YAAc,GAGhC,EAUI5C,EAAO,eAAiB,SAASvQ,EAAQjF,EAAQ2W,EAAS,CACtD,IAAIyB,EAAIzB,EACJ2B,EAAoBrT,EAAO,QAAQ,kBACnC1L,EACA2D,EACA7H,EACAW,EACAkG,EAKJ,IAHAkc,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAG3B,GAFAkE,EAAOyG,EAAO3K,CAAC,EAEX,EAACkE,EAAK,OAAO,QAIjB,IAAK2C,EAAI3C,EAAK,MAAM,OAAS,EAAI,EAAI,EAAG2C,EAAI3C,EAAK,MAAM,OAAQ2C,IAAK,CAKhE,IAJAgB,EAAO3D,EAAK,MAAM2C,CAAC,EAEnBkc,EAAE,OAAOlb,EAAK,SAAS,CAAC,EAAE,EAAGA,EAAK,SAAS,CAAC,EAAE,CAAC,EAE1ClH,EAAI,EAAGA,EAAIkH,EAAK,SAAS,OAAQlH,IAC9B,CAACkH,EAAK,SAASlH,EAAI,CAAC,EAAE,YAAcsiB,EACpCF,EAAE,OAAOlb,EAAK,SAASlH,CAAC,EAAE,EAAGkH,EAAK,SAASlH,CAAC,EAAE,CAAC,EAE/CoiB,EAAE,OAAOlb,EAAK,SAASlH,CAAC,EAAE,EAAGkH,EAAK,SAASlH,CAAC,EAAE,CAAC,EAG/CkH,EAAK,SAASlH,CAAC,EAAE,YAAc,CAACsiB,GAChCF,EAAE,OAAOlb,EAAK,UAAUlH,EAAI,GAAKkH,EAAK,SAAS,MAAM,EAAE,EAAGA,EAAK,UAAUlH,EAAI,GAAKkH,EAAK,SAAS,MAAM,EAAE,CAAC,EAIjHkb,EAAE,OAAOlb,EAAK,SAAS,CAAC,EAAE,EAAGA,EAAK,SAAS,CAAC,EAAE,CAAC,EAIvDkb,EAAE,UAAY,EACdA,EAAE,YAAc,OAChBA,EAAE,OAAM,CAChB,EAUI5C,EAAO,gBAAkB,SAASvQ,EAAQjF,EAAQ2W,EAAS,CACvD,IAAIyB,EAAIzB,EACJpd,EAEAlE,EACAW,EAMJ,IAHAoiB,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAG3B,GAFAkE,EAAOyG,EAAO3K,CAAC,EAEX,GAACkE,EAAK,OAAO,SAAWA,EAAK,MAAM,SAAW,GAKlD,KAFA6e,EAAE,OAAO7e,EAAK,SAAS,CAAC,EAAE,EAAGA,EAAK,SAAS,CAAC,EAAE,CAAC,EAE1CvD,EAAI,EAAGA,EAAIuD,EAAK,SAAS,OAAQvD,IAClCoiB,EAAE,OAAO7e,EAAK,SAASvD,CAAC,EAAE,EAAGuD,EAAK,SAASvD,CAAC,EAAE,CAAC,EAGnDoiB,EAAE,OAAO7e,EAAK,SAAS,CAAC,EAAE,EAAGA,EAAK,SAAS,CAAC,EAAE,CAAC,EAGnD6e,EAAE,UAAY,EACdA,EAAE,YAAc,wBAChBA,EAAE,OAAM,CAChB,EAUI5C,EAAO,cAAgB,SAASvQ,EAAQjF,EAAQ2W,EAAS,CACrD,IAAIyB,EAAIzB,EACJthB,EACAW,EACAkG,EAEJ,IAAK7G,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CAChC,IAAIS,EAAQkK,EAAO3K,CAAC,EAAE,MACtB,IAAK6G,EAAIpG,EAAM,OAAS,EAAI,EAAI,EAAGoG,EAAIpG,EAAM,OAAQoG,IAAK,CACtD,IAAIgB,EAAOpH,EAAMoG,CAAC,EAClB,IAAKlG,EAAI,EAAGA,EAAIkH,EAAK,SAAS,OAAQlH,IAClCoiB,EAAE,UAAY,wBACdA,EAAE,SAAS/iB,EAAI,IAAMW,EAAGkH,EAAK,SAAS,GAAKA,EAAK,SAASlH,CAAC,EAAE,EAAIkH,EAAK,SAAS,GAAK,GAAKA,EAAK,SAAS,GAAKA,EAAK,SAASlH,CAAC,EAAE,EAAIkH,EAAK,SAAS,GAAK,EAAG,GAI1K,EAUIsY,EAAO,cAAgB,SAASvQ,EAAQ8E,EAAO4M,EAAS,CACpD,IAAIyB,EAAIzB,EACRyB,EAAE,UAAY,wBACdA,EAAE,SAASrO,EAAM,SAAS,EAAI,KAAOA,EAAM,SAAS,EAAGA,EAAM,SAAS,EAAI,EAAGA,EAAM,SAAS,EAAI,CAAC,CACzG,EAUIyL,EAAO,WAAa,SAASvQ,EAAQjF,EAAQ2W,EAAS,CAClD,IAAIyB,EAAIzB,EACK1R,EAAO,OAC5B,IAAYxI,EAAUwI,EAAO,QAErBmT,EAAE,UAAS,EAEX,QAAS/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAEnB,GAAIkE,EAAK,OAAO,QAEZ,QADIzD,EAAQkK,EAAO3K,CAAC,EAAE,MACbW,EAAIF,EAAM,OAAS,EAAI,EAAI,EAAGE,EAAIF,EAAM,OAAQE,IAAK,CAC1D,IAAIkH,EAAOpH,EAAME,CAAC,EAClBoiB,EAAE,KAAKlb,EAAK,OAAO,IAAI,EAAGA,EAAK,OAAO,IAAI,EAAGA,EAAK,OAAO,IAAI,EAAIA,EAAK,OAAO,IAAI,EAAGA,EAAK,OAAO,IAAI,EAAIA,EAAK,OAAO,IAAI,CAAC,GAKjIT,EAAQ,WACR2b,EAAE,YAAc,yBAEhBA,EAAE,YAAc,kBAGpBA,EAAE,UAAY,EACdA,EAAE,OAAM,CAChB,EAUI5C,EAAO,SAAW,SAASvQ,EAAQjF,EAAQ2W,EAAS,CAChD,IAAIyB,EAAIzB,EACK1R,EAAO,OAC5B,IAAYxI,EAAUwI,EAAO,QACjB/H,EACA7H,EACAW,EACAkG,EAIJ,IAFAkc,EAAE,UAAS,EAEN/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CAChC,IAAIkE,EAAOyG,EAAO3K,CAAC,EACfS,EAAQyD,EAAK,MAEjB,GAAKA,EAAK,OAAO,QAGjB,GAAIkD,EAAQ,SAER,IAAKzG,EAAIF,EAAM,OAAS,EAAI,EAAI,EAAGE,EAAIF,EAAM,OAAQE,IAEjD,IADAkH,EAAOpH,EAAME,CAAC,EACTkG,EAAI,EAAGA,EAAIgB,EAAK,KAAK,OAAQhB,IAAK,CACnC,IAAIgH,EAAOhG,EAAK,KAAKhB,CAAC,EACtBkc,EAAE,OAAOlb,EAAK,SAAS,EAAGA,EAAK,SAAS,CAAC,EACzCkb,EAAE,OAAOlb,EAAK,SAAS,EAAIgG,EAAK,EAAI,GAAIhG,EAAK,SAAS,EAAIgG,EAAK,EAAI,EAAE,MAI7E,KAAKlN,EAAIF,EAAM,OAAS,EAAI,EAAI,EAAGE,EAAIF,EAAM,OAAQE,IAEjD,IADAkH,EAAOpH,EAAME,CAAC,EACTkG,EAAI,EAAGA,EAAIgB,EAAK,KAAK,OAAQhB,IAE9Bkc,EAAE,OAAOlb,EAAK,SAAS,EAAGA,EAAK,SAAS,CAAC,EACzCkb,EAAE,QAAQlb,EAAK,SAAS,CAAC,EAAE,EAAIA,EAAK,SAASA,EAAK,SAAS,OAAO,CAAC,EAAE,GAAK,GACrEA,EAAK,SAAS,CAAC,EAAE,EAAIA,EAAK,SAASA,EAAK,SAAS,OAAO,CAAC,EAAE,GAAK,CAAC,EAMlFT,EAAQ,YACR2b,EAAE,YAAc,YAChBA,EAAE,UAAY,IAEdA,EAAE,YAAc,2BAChBA,EAAE,yBAA2B,UAC7BA,EAAE,UAAY,GAGlBA,EAAE,OAAM,EACRA,EAAE,yBAA2B,aACrC,EAUI5C,EAAO,cAAgB,SAASvQ,EAAQjF,EAAQ2W,EAAS,CACrD,IAAIyB,EAAIzB,EACK1R,EAAO,OAC5B,IAAYxI,EAAUwI,EAAO,QACjB1L,EACA2D,EACA7H,EACA6G,EAKJ,IAHAkc,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAG3B,GAFAkE,EAAOyG,EAAO3K,CAAC,EAEX,EAACkE,EAAK,OAAO,QAIjB,IAAK2C,EAAI,EAAGA,EAAI3C,EAAK,MAAM,OAAQ2C,IAC/BgB,EAAO3D,EAAK,MAAM2C,CAAC,EACnBkc,EAAE,IAAIlb,EAAK,SAAS,EAAGA,EAAK,SAAS,EAAG,EAAG,EAAG,EAAI,KAAK,GAAI,EAAK,EAChEkb,EAAE,UAAS,EAcnB,IAVI3b,EAAQ,WACR2b,EAAE,UAAY,YAEdA,EAAE,UAAY,kBAElBA,EAAE,KAAI,EAENA,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAC3BkE,EAAOyG,EAAO3K,CAAC,EACXkE,EAAK,OAAO,UACZ6e,EAAE,IAAI7e,EAAK,aAAa,EAAGA,EAAK,aAAa,EAAG,EAAG,EAAG,EAAI,KAAK,GAAI,EAAK,EACxE6e,EAAE,UAAS,GAInBA,EAAE,UAAY,sBACdA,EAAE,KAAI,CACd,EAUI5C,EAAO,aAAe,SAASvQ,EAAQjF,EAAQ2W,EAAS,CACpD,IAAIyB,EAAIzB,EAERyB,EAAE,UAAS,EAEX,QAAS/iB,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAAK,CACpC,IAAIkE,EAAOyG,EAAO3K,CAAC,EAEnB,GAAKkE,EAAK,OAAO,QAGjB,KAAIxB,EAAWuE,EAAK,YAAY/C,CAAI,EAEpC6e,EAAE,OAAO7e,EAAK,SAAS,EAAGA,EAAK,SAAS,CAAC,EACzC6e,EAAE,OAAO7e,EAAK,SAAS,EAAIxB,EAAS,EAAGwB,EAAK,SAAS,EAAIxB,EAAS,CAAC,GAGvEqgB,EAAE,UAAY,EACdA,EAAE,YAAc,iBAChBA,EAAE,OAAM,CAChB,EAUI5C,EAAO,QAAU,SAASvQ,EAAQjF,EAAQ2W,EAAS,CAC/C,IAAIyB,EAAIzB,EACJthB,EACAW,EAEJ,IAAKX,EAAI,EAAGA,EAAI2K,EAAO,OAAQ3K,IAC3B,GAAK2K,EAAO3K,CAAC,EAAE,OAAO,QAGtB,KAAIS,EAAQkK,EAAO3K,CAAC,EAAE,MACtB,IAAKW,EAAIF,EAAM,OAAS,EAAI,EAAI,EAAGE,EAAIF,EAAM,OAAQE,IAAK,CACtD,IAAIkH,EAAOpH,EAAME,CAAC,EAClBoiB,EAAE,KAAO,aACTA,EAAE,UAAY,wBACdA,EAAE,SAASlb,EAAK,GAAIA,EAAK,SAAS,EAAI,GAAIA,EAAK,SAAS,EAAI,EAAE,GAG9E,EAUIsY,EAAO,WAAa,SAASvQ,EAAQtE,EAAOgW,EAAS,CACjD,IAAIyB,EAAIzB,EACJla,EAAUwI,EAAO,QACjBrE,EACAC,EAIAxL,EACAW,EAKJ,IAHAoiB,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAIsL,EAAM,OAAQtL,IAG1B,GAFAuL,EAAOD,EAAMtL,CAAC,EAEV,EAACuL,EAAK,SAIV,IADAC,EAAYD,EAAK,UACZ5K,EAAI,EAAGA,EAAI4K,EAAK,eAAe,OAAQ5K,IAAK,CAC7C,IAAI2O,EAAU/D,EAAK,eAAe5K,CAAC,EAC/BgC,EAAS2M,EAAQ,OACrByT,EAAE,KAAKpgB,EAAO,EAAI,IAAKA,EAAO,EAAI,IAAK,IAAK,GAAG,EAcvD,IAVIyE,EAAQ,WACR2b,EAAE,UAAY,wBAEdA,EAAE,UAAY,SAElBA,EAAE,KAAI,EAENA,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAIsL,EAAM,OAAQtL,IAG1B,GAFAuL,EAAOD,EAAMtL,CAAC,EAEV,EAACuL,EAAK,WAGVC,EAAYD,EAAK,UAEbA,EAAK,eAAe,OAAS,GAAG,CAChC,IAAI8X,EAAa9X,EAAK,eAAe,CAAC,EAAE,OAAO,EAC3C+X,EAAa/X,EAAK,eAAe,CAAC,EAAE,OAAO,EAE3CA,EAAK,eAAe,SAAW,IAC/B8X,GAAc9X,EAAK,eAAe,CAAC,EAAE,OAAO,EAAIA,EAAK,eAAe,CAAC,EAAE,OAAO,GAAK,EACnF+X,GAAc/X,EAAK,eAAe,CAAC,EAAE,OAAO,EAAIA,EAAK,eAAe,CAAC,EAAE,OAAO,GAAK,GAGnFC,EAAU,QAAUA,EAAU,SAAS,CAAC,EAAE,MAAQA,EAAU,MAAM,WAAa,GAC/EuX,EAAE,OAAOM,EAAa7X,EAAU,OAAO,EAAI,EAAG8X,EAAa9X,EAAU,OAAO,EAAI,CAAC,EAEjFuX,EAAE,OAAOM,EAAa7X,EAAU,OAAO,EAAI,EAAG8X,EAAa9X,EAAU,OAAO,EAAI,CAAC,EAGrFuX,EAAE,OAAOM,EAAYC,CAAU,EAInClc,EAAQ,WACR2b,EAAE,YAAc,sBAEhBA,EAAE,YAAc,SAGpBA,EAAE,UAAY,EACdA,EAAE,OAAM,CAChB,EAUI5C,EAAO,YAAc,SAASvQ,EAAQtE,EAAOgW,EAAS,CAClD,IAAIyB,EAAIzB,EACJla,EAAUwI,EAAO,QACjBrE,EACAC,EAEAC,EACAC,EACA1L,EAMJ,IAHA+iB,EAAE,UAAS,EAGN/iB,EAAI,EAAGA,EAAIsL,EAAM,OAAQtL,IAG1B,GAFAuL,EAAOD,EAAMtL,CAAC,EAEV,EAACuL,EAAK,SAGV,CAAAC,EAAYD,EAAK,UACjBE,EAAQD,EAAU,MAClBE,EAAQF,EAAU,MAElB,IAAI3E,EAAI,EAEJ,CAAC6E,EAAM,UAAY,CAACD,EAAM,WAAU5E,EAAI,IACxC6E,EAAM,WAAU7E,EAAI,GAExBkc,EAAE,OAAOrX,EAAM,SAAS,EAAGA,EAAM,SAAS,CAAC,EAC3CqX,EAAE,OAAOrX,EAAM,SAAS,EAAIF,EAAU,YAAY,EAAI3E,EAAG6E,EAAM,SAAS,EAAIF,EAAU,YAAY,EAAI3E,CAAC,EAEvGA,EAAI,EAEA,CAAC6E,EAAM,UAAY,CAACD,EAAM,WAAU5E,EAAI,IACxC4E,EAAM,WAAU5E,EAAI,GAExBkc,EAAE,OAAOtX,EAAM,SAAS,EAAGA,EAAM,SAAS,CAAC,EAC3CsX,EAAE,OAAOtX,EAAM,SAAS,EAAID,EAAU,YAAY,EAAI3E,EAAG4E,EAAM,SAAS,EAAID,EAAU,YAAY,EAAI3E,CAAC,EAGvGO,EAAQ,WACR2b,EAAE,YAAc,sBAEhBA,EAAE,YAAc,SAEpBA,EAAE,OAAM,CAChB,EASI5C,EAAO,UAAY,SAASoD,EAAWjC,EAAS,CAC/BiC,EAAU,OAC/B,IAAYC,EAAWD,EAAU,SACrB3T,EAAS2T,EAAU,OACnBnc,EAAUwI,EAAO,QACjBnN,EAEJ,GAAI2E,EAAQ,UAAW,CACnB,IAAI8Z,EAActR,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,EACtDuR,EAAevR,EAAO,OAAO,IAAI,EAAIA,EAAO,OAAO,IAAI,EACvDwR,EAAeF,EAActR,EAAO,QAAQ,MAC5CyR,EAAeF,EAAevR,EAAO,QAAQ,OAEjD0R,EAAQ,MAAM,EAAIF,EAAc,EAAIC,CAAY,EAChDC,EAAQ,UAAU,CAAC1R,EAAO,OAAO,IAAI,EAAG,CAACA,EAAO,OAAO,IAAI,CAAC,EAGhE,QAAS5P,EAAI,EAAGA,EAAIwjB,EAAS,OAAQxjB,IAAK,CACtC,IAAIyjB,EAAOD,EAASxjB,CAAC,EAAE,KAOvB,OALAshB,EAAQ,UAAU,GAAK,EAAG,EAC1BA,EAAQ,UAAY,EACpBA,EAAQ,YAAc,sBACtBA,EAAQ,YAAY,CAAC,EAAE,CAAC,CAAC,EAEjBmC,EAAK,KAAI,CAEjB,IAAK,OAGDhhB,EAASghB,EAAK,OACdnC,EAAQ,UAAS,EACjBA,EAAQ,KAAK,KAAK,MAAM7e,EAAO,IAAI,EAAI,CAAC,EAAG,KAAK,MAAMA,EAAO,IAAI,EAAI,CAAC,EAClE,KAAK,MAAMA,EAAO,IAAI,EAAIA,EAAO,IAAI,EAAI,CAAC,EAAG,KAAK,MAAMA,EAAO,IAAI,EAAIA,EAAO,IAAI,EAAI,CAAC,CAAC,EAC5F6e,EAAQ,UAAS,EACjBA,EAAQ,OAAM,EAEd,MAEJ,IAAK,aAGD,IAAI1e,EAAQ6gB,EAAK,OACbA,EAAK,QACL7gB,EAAQ6gB,EAAK,QACjBnC,EAAQ,UAAS,EACjBA,EAAQ,IAAI1e,EAAM,EAAGA,EAAM,EAAG,GAAI,EAAG,EAAI,KAAK,EAAE,EAChD0e,EAAQ,UAAS,EACjBA,EAAQ,OAAM,EAEd,MAIJA,EAAQ,YAAY,EAAE,EACtBA,EAAQ,UAAU,IAAM,GAAI,EAI5BiC,EAAU,cAAgB,OAC1BjC,EAAQ,UAAU,GAAK,EAAG,EAC1BA,EAAQ,UAAY,EACpBA,EAAQ,YAAc,sBACtBA,EAAQ,UAAY,sBACpB7e,EAAS8gB,EAAU,aACnBjC,EAAQ,UAAS,EACjBA,EAAQ,KAAK,KAAK,MAAM7e,EAAO,IAAI,CAAC,EAAG,KAAK,MAAMA,EAAO,IAAI,CAAC,EAC1D,KAAK,MAAMA,EAAO,IAAI,EAAIA,EAAO,IAAI,CAAC,EAAG,KAAK,MAAMA,EAAO,IAAI,EAAIA,EAAO,IAAI,CAAC,CAAC,EACpF6e,EAAQ,UAAS,EACjBA,EAAQ,OAAM,EACdA,EAAQ,KAAI,EACZA,EAAQ,UAAU,IAAM,GAAI,GAG5Bla,EAAQ,WACRka,EAAQ,aAAa,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,CACjD,EASI,IAAIb,EAAgB,SAAS7Q,EAAQ4Q,EAAM,CACvC,IAAI9J,EAAS9G,EAAO,OAChBiH,EAASjH,EAAO,OAChB8T,EAAc7M,EAAO,YACrB/H,EAAY4H,EAAO,OAAO,UAE9BG,EAAO,MAAQ2J,EAAO3J,EAAO,UAAYsJ,EAAO,WAChDtJ,EAAO,SAAW2J,EAElB3J,EAAO,iBAAmB/H,EAAY+H,EAAO,eAAiB,EAC9DA,EAAO,cAAgB/H,EAEvB+H,EAAO,aAAa,QAAQA,EAAO,KAAK,EACxCA,EAAO,aAAa,OAAS,KAAK,IAAIA,EAAO,aAAa,OAAQ6M,CAAW,EAE7E7M,EAAO,mBAAmB,QAAQH,EAAO,OAAO,SAAS,EACzDG,EAAO,mBAAmB,OAAS,KAAK,IAAIA,EAAO,mBAAmB,OAAQ6M,CAAW,EAEzF7M,EAAO,wBAAwB,QAAQA,EAAO,gBAAgB,EAC9DA,EAAO,wBAAwB,OAAS,KAAK,IAAIA,EAAO,wBAAwB,OAAQ6M,CAAW,EAEnG7M,EAAO,qBAAqB,QAAQH,EAAO,OAAO,WAAW,EAC7DG,EAAO,qBAAqB,OAAS,KAAK,IAAIA,EAAO,qBAAqB,OAAQ6M,CAAW,EAE7F7M,EAAO,eAAe,QAAQA,EAAO,WAAW,EAChDA,EAAO,eAAe,OAAS,KAAK,IAAIA,EAAO,eAAe,OAAQ6M,CAAW,CACzF,EASQxB,EAAQ,SAAS9hB,EAAQ,CAEzB,QADIsB,EAAS,EACJ1B,EAAI,EAAGA,EAAII,EAAO,OAAQJ,GAAK,EACpC0B,GAAUtB,EAAOJ,CAAC,EAEtB,OAAQ0B,EAAStB,EAAO,QAAW,CAC3C,EASQkgB,EAAgB,SAASlP,EAAOC,EAAQ,CACxC,IAAIqP,EAAS,SAAS,cAAc,QAAQ,EAC5C,OAAAA,EAAO,MAAQtP,EACfsP,EAAO,OAASrP,EAChBqP,EAAO,cAAgB,UAAW,CAAE,MAAO,EAAM,EACjDA,EAAO,cAAgB,UAAW,CAAE,MAAO,EAAM,EAC1CA,CACf,EASQC,EAAiB,SAASD,EAAQ,CAClC,IAAIY,EAAUZ,EAAO,WAAW,IAAI,EAChCiD,EAAmB,OAAO,kBAAoB,EAC9CC,EAAyBtC,EAAQ,8BAAgCA,EAAQ,2BAC5CA,EAAQ,0BAA4BA,EAAQ,yBAC5CA,EAAQ,wBAA0B,EAEnE,OAAOqC,EAAmBC,CAClC,EAUQR,EAAc,SAASxT,EAAQiU,EAAW,CAC1C,IAAIC,EAAQlU,EAAO,SAASiU,CAAS,EAErC,OAAIC,IAGJA,EAAQlU,EAAO,SAASiU,CAAS,EAAI,IAAI,MACzCC,EAAM,IAAMD,EAELC,EACf,EASQtC,EAAmB,SAAS5R,EAAQ2R,EAAY,CAChD,IAAIwC,EAAgBxC,EAEhB,iBAAiB,KAAKA,CAAU,IAChCwC,EAAgB,OAASxC,EAAa,KAE1C3R,EAAO,OAAO,MAAM,WAAamU,EACjCnU,EAAO,OAAO,MAAM,eAAiB,UACrCA,EAAO,kBAAoB2R,CACnC,CAoVA,GAAC,GAKM,SAAS3iB,EAAQM,EAASF,EAAqB,CActD,IAAIglB,EAAS,CAAA,EAEbplB,EAAO,QAAUolB,EAEjB,IAAIza,EAASvK,EAAoB,CAAC,EAC9BuX,EAASvX,EAAoB,EAAE,EAC/BW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAER,IAAIohB,EACAC,EAUJ,GARI,OAAO,OAAW,MAClBD,EAAyB,OAAO,uBAAyB,OAAO,6BAC/B,OAAO,0BAA4B,OAAO,wBAE3EC,EAAwB,OAAO,sBAAwB,OAAO,yBAC7B,OAAO,4BAA8B,OAAO,wBAG7E,CAACD,EAAwB,CACzB,IAAI6D,EAEJ7D,EAAyB,SAAS3W,EAAS,CACvCwa,EAAgB,WAAW,UAAW,CAClCxa,EAAS9J,EAAO,IAAG,CAAE,CACrC,EAAe,IAAO,EAAE,CACxB,EAEQ0gB,EAAwB,UAAW,CAC/B,aAAa4D,CAAa,CACtC,EAQID,EAAO,OAAS,SAAS5c,EAAS,CAC9B,IAAIC,EAAW,CACX,IAAK,GACL,gBAAiB,GACjB,iBAAkB,EAClB,aAAc,EACd,aAAc,CAAA,EACd,SAAU,KACV,eAAgB,KAChB,QAAS,GACT,QAAS,EACrB,EAEY6c,EAASvkB,EAAO,OAAO0H,EAAUD,CAAO,EAE5C,OAAA8c,EAAO,MAAQA,EAAO,OAAS,IAAOA,EAAO,IAC7CA,EAAO,SAAWA,EAAO,UAAY,IAAOA,EAAO,IACnDA,EAAO,SAAWA,EAAO,UAAY,KAAQA,EAAO,IAAM,IAC1DA,EAAO,IAAM,IAAOA,EAAO,MAEpBA,CACf,EAOIF,EAAO,IAAM,SAASE,EAAQxN,EAAQ,CAElC,OAAI,OAAOwN,EAAO,mBAAuB,MACrCxN,EAASwN,EACTA,EAASF,EAAO,OAAM,GAGzB,SAASG,EAAI3D,EAAK,CACf0D,EAAO,eAAiB9D,EAAuB+D,CAAG,EAE9C3D,GAAQ0D,EAAO,SACfF,EAAO,KAAKE,EAAQxN,EAAQ8J,CAAI,CAEhD,EAAS,EAEM0D,CACf,EAWIF,EAAO,KAAO,SAASE,EAAQxN,EAAQ8J,EAAM,CACzC,IAAI3J,EAASH,EAAO,OAChBjR,EAEAye,EAAO,QAEPze,EAAQye,EAAO,OAGfze,EAAS+a,EAAO0D,EAAO,UAAaA,EAAO,MAC3CA,EAAO,SAAW1D,EAGlB0D,EAAO,aAAa,KAAKze,CAAK,EAC9Bye,EAAO,aAAeA,EAAO,aAAa,MAAM,CAACA,EAAO,eAAe,EACvEze,EAAQ,KAAK,IAAI,MAAM,KAAMye,EAAO,YAAY,EAGhDze,EAAQA,EAAQye,EAAO,SAAWA,EAAO,SAAWze,EACpDA,EAAQA,EAAQye,EAAO,SAAWA,EAAO,SAAWze,EAGpDye,EAAO,MAAQze,GAInB,IAAIoE,EAAQ,CACR,UAAWgN,EAAO,SAC9B,EAEQtN,EAAO,QAAQ2a,EAAQ,aAAcra,CAAK,EAG1Cqa,EAAO,cAAgB,EACnB1D,EAAO0D,EAAO,kBAAoB,MAClCA,EAAO,IAAMA,EAAO,eAAiB1D,EAAO0D,EAAO,kBAAoB,KACvEA,EAAO,iBAAmB1D,EAC1B0D,EAAO,aAAe,GAG1B3a,EAAO,QAAQ2a,EAAQ,OAAQra,CAAK,EAGpCN,EAAO,QAAQ2a,EAAQ,eAAgBra,CAAK,EAE5C0M,EAAO,OAAOG,EAAQjR,CAAK,EAE3B8D,EAAO,QAAQ2a,EAAQ,cAAera,CAAK,EAE3CN,EAAO,QAAQ2a,EAAQ,YAAara,CAAK,CACjD,EAQIma,EAAO,KAAO,SAASE,EAAQ,CAC3B7D,EAAsB6D,EAAO,cAAc,CACnD,EAQIF,EAAO,MAAQ,SAASE,EAAQxN,EAAQ,CACpCsN,EAAO,IAAIE,EAAQxN,CAAM,CACjC,CA4FA,GAAC,GAKM,SAAS9X,EAAQM,EAASF,EAAqB,CActD,IAAIolB,EAAM,CAAA,EAEVxlB,EAAO,QAAUwlB,EAEjB,IAAIrY,EAAY/M,EAAoB,CAAC,EACjCW,EAASX,EAAoB,CAAC,EAC9Bic,EAAatb,EAAO,YAEvB,UAAW,CAURykB,EAAI,SAAW,SAAS3Y,EAAOC,EAAO,CAClC,OAAOK,EAAU,SAASN,EAAOC,CAAK,CAC9C,EAEIuP,EAAWmJ,EAAK,WAAY,+CAA+C,CAE/E,GAAC,GAKM,SAASxlB,EAAQM,EAASF,EAAqB,CAYtD,IAAIqlB,EAAM,CAAA,EAEVzlB,EAAO,QAAUylB,EAEJrlB,EAAoB,CAAC,EAClC,IAAIW,EAASX,EAAoB,CAAC,GAEjC,UAAW,CAaRqlB,EAAI,eAAiB,SAAShkB,EAAMikB,EAAc,CAC1C,OAAO,OAAW,KAAe,EAAE,eAAgB,SACnD3kB,EAAO,KAAK,qEAAqE,EAIrF,IAAIK,EAAGukB,EAAInc,EAAOxF,EAAO4hB,EAASC,EAC9BC,EAAeC,EACfC,EAAWC,EAAc5gB,EAAS,CAAA,EAClC6gB,EAAIC,EAAIpV,EAAS,EAAGvM,EAAI,EAAGC,EAAI,EAEnCihB,EAAeA,GAAgB,GAE/B,IAAIU,EAAW,SAASC,EAAIC,EAAIC,EAAa,CAEzC,IAAIC,EAAaD,EAAc,IAAM,GAAKA,EAAc,EAGxD,GAAI,CAACP,GAAaK,GAAML,EAAU,GAAKM,GAAMN,EAAU,EAAG,CAClDA,GAAaQ,GACbN,EAAKF,EAAU,EACfG,EAAKH,EAAU,IAEfE,EAAK,EACLC,EAAK,GAGT,IAAIniB,EAAQ,CACR,EAAGkiB,EAAKG,EACR,EAAGF,EAAKG,CAC5B,GAGoBE,GAAc,CAACR,KACfA,EAAYhiB,GAGhBqB,EAAO,KAAKrB,CAAK,EAEjBQ,EAAI0hB,EAAKG,EACT5hB,EAAI0hB,EAAKG,EAEzB,EAEYG,EAAkB,SAASb,EAAS,CACpC,IAAIc,EAAUd,EAAQ,oBAAoB,YAAW,EAGrD,GAAIc,IAAY,IAIhB,QAAQA,EAAO,CAEf,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACDliB,EAAIohB,EAAQ,EACZnhB,EAAImhB,EAAQ,EACZ,MACJ,IAAK,IACDphB,EAAIohB,EAAQ,EACZ,MACJ,IAAK,IACDnhB,EAAImhB,EAAQ,EACZ,MAGJQ,EAAS5hB,EAAGC,EAAGmhB,EAAQ,WAAW,EAC9C,EAUQ,IAPAH,EAAI,mBAAmBhkB,CAAI,EAG3B+H,EAAQ/H,EAAK,eAAc,EAG3BokB,EAAW,CAAA,EACNzkB,EAAI,EAAGA,EAAIK,EAAK,YAAY,cAAeL,GAAK,EACjDykB,EAAS,KAAKpkB,EAAK,YAAY,QAAQL,CAAC,CAAC,EAK7C,IAHA0kB,EAAgBD,EAAS,OAAM,EAGxB9U,EAASvH,GAAO,CAMnB,GAJAyc,EAAexkB,EAAK,mBAAmBsP,CAAM,EAC7C6U,EAAUC,EAASI,CAAY,EAG3BL,GAAWG,EAAa,CACxB,KAAOD,EAAc,QAAUA,EAAc,CAAC,GAAKF,GAC/Ca,EAAgBX,EAAc,OAAO,EAEzCC,EAAcH,EAKlB,OAAQA,EAAQ,oBAAoB,YAAW,EAAE,CAEjD,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACD5hB,EAAQvC,EAAK,iBAAiBsP,CAAM,EACpCqV,EAASpiB,EAAM,EAAGA,EAAM,EAAG,CAAC,EAC5B,MAKJ+M,GAAU2U,EAId,IAAKtkB,EAAI,EAAGukB,EAAKG,EAAc,OAAQ1kB,EAAIukB,EAAI,EAAEvkB,EAC7CqlB,EAAgBX,EAAc1kB,CAAC,CAAC,EAEpC,OAAOiE,CACf,EAEIogB,EAAI,mBAAqB,SAAShkB,EAAM,CAQpC,QAHIklB,EAAIC,EAAI9T,EAAI+T,EAAI9T,EAAI+T,EAAIC,EAAOtlB,EAAK,YACpC+C,EAAI,EAAGC,EAAI,EAAGuiB,EAAMD,EAAK,cAEpB3lB,EAAI,EAAGA,EAAI4lB,EAAK,EAAE5lB,EAAG,CAC1B,IAAI6lB,EAAMF,EAAK,QAAQ3lB,CAAC,EACpBslB,EAAUO,EAAI,oBAElB,GAAI,cAAc,KAAKP,CAAO,EACtB,MAAOO,IAAKziB,EAAIyiB,EAAI,GACpB,MAAOA,IAAKxiB,EAAIwiB,EAAI,OASxB,QAPI,OAAQA,IAAKnU,EAAKtO,EAAIyiB,EAAI,IAC1B,OAAQA,IAAKlU,EAAKvO,EAAIyiB,EAAI,IAC1B,OAAQA,IAAKJ,EAAKpiB,EAAIwiB,EAAI,IAC1B,OAAQA,IAAKH,EAAKriB,EAAIwiB,EAAI,IAC1B,MAAOA,IAAKziB,GAAKyiB,EAAI,GACrB,MAAOA,IAAKxiB,GAAKwiB,EAAI,GAEjBP,EAAO,CAEf,IAAK,IACDK,EAAK,YAAYtlB,EAAK,0BAA0B+C,EAAGC,CAAC,EAAGrD,CAAC,EACxD,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,0BAA0B+C,EAAGC,CAAC,EAAGrD,CAAC,EACxD,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,oCAAoC+C,CAAC,EAAGpD,CAAC,EAC/D,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,kCAAkCgD,CAAC,EAAGrD,CAAC,EAC7D,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,gCAAgC+C,EAAGC,EAAGqO,EAAI+T,EAAI9T,EAAI+T,CAAE,EAAG1lB,CAAC,EAC9E,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,sCAAsC+C,EAAGC,EAAGsO,EAAI+T,CAAE,EAAG1lB,CAAC,EAC5E,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,oCAAoC+C,EAAGC,EAAGqO,EAAI+T,CAAE,EAAGzlB,CAAC,EAC1E,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,0CAA0C+C,EAAGC,CAAC,EAAGrD,CAAC,EACxE,MACJ,IAAK,IACD2lB,EAAK,YAAYtlB,EAAK,uBAAuB+C,EAAGC,EAAGwiB,EAAI,GAAIA,EAAI,GAAIA,EAAI,MAAOA,EAAI,aAAcA,EAAI,SAAS,EAAG7lB,CAAC,EACjH,MACJ,IAAK,IACL,IAAK,IACDoD,EAAImiB,EACJliB,EAAImiB,EACJ,OAKJF,GAAW,KAAOA,GAAW,OAC7BC,EAAKniB,EACLoiB,EAAKniB,GAGrB,CAEA,GAAC,GAIM,SAASzE,EAAQM,EAASF,EAAqB,CAgBtD,IAAI8mB,EAAQ,CAAA,EAEZlnB,EAAO,QAAUknB,EAEjB,IAAI9b,EAAYhL,EAAoB,CAAC,EACxBA,EAAoB,CAAC,EAEjC,UAAW,CAKR8mB,EAAM,OAAS9b,EAAU,OACzB8b,EAAM,IAAM9b,EAAU,IACtB8b,EAAM,OAAS9b,EAAU,OACzB8b,EAAM,MAAQ9b,EAAU,MACxB8b,EAAM,aAAe9b,EAAU,aAC/B8b,EAAM,QAAU9b,EAAU,QAC1B8b,EAAM,cAAgB9b,EAAU,aAEpC,EAAC,EAID,CAAU,CACV,CAAC", "x_google_ignoreList": [0]}