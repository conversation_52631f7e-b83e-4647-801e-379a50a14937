const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./browserAll-C_WFDGnb.js","./webworkerAll-B49PaXXS.js"])))=>i.map(i=>d[i]);
var Eg=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Po(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}const Al="modulepreload",Bl=function(s,e){return new URL(s,e).href},Hi={},wr=function(e,t,r){let i=Promise.resolve();if(t&&t.length>0){const o=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),h=a?.nonce||a?.getAttribute("nonce");i=Promise.allSettled(t.map(l=>{if(l=Bl(l,r),l in Hi)return;Hi[l]=!0;const c=l.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(!!r)for(let p=o.length-1;p>=0;p--){const g=o[p];if(g.href===l&&(!c||g.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${l}"]${u}`))return;const d=document.createElement("link");if(d.rel=c?"stylesheet":Al,c||(d.as="script"),d.crossOrigin="",d.href=l,h&&d.setAttribute("nonce",h),document.head.appendChild(d),c)return new Promise((p,g)=>{d.addEventListener("load",p),d.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${l}`)))})}))}function n(o){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=o,window.dispatchEvent(a),!a.defaultPrevented)throw o}return i.then(o=>{for(const a of o||[])a.status==="rejected"&&n(a.reason);return e().catch(n)})};var x=(s=>(s.Application="application",s.WebGLPipes="webgl-pipes",s.WebGLPipesAdaptor="webgl-pipes-adaptor",s.WebGLSystem="webgl-system",s.WebGPUPipes="webgpu-pipes",s.WebGPUPipesAdaptor="webgpu-pipes-adaptor",s.WebGPUSystem="webgpu-system",s.CanvasSystem="canvas-system",s.CanvasPipesAdaptor="canvas-pipes-adaptor",s.CanvasPipes="canvas-pipes",s.Asset="asset",s.LoadParser="load-parser",s.ResolveParser="resolve-parser",s.CacheParser="cache-parser",s.DetectionParser="detection-parser",s.MaskEffect="mask-effect",s.BlendMode="blend-mode",s.TextureSource="texture-source",s.Environment="environment",s.ShapeBuilder="shape-builder",s.Batcher="batcher",s))(x||{});const Cs=s=>{if(typeof s=="function"||typeof s=="object"&&s.extension){if(!s.extension)throw new Error("Extension class must have an extension object");s={...typeof s.extension!="object"?{type:s.extension}:s.extension,ref:s}}if(typeof s=="object")s={...s};else throw new Error("Invalid extension type");return typeof s.type=="string"&&(s.type=[s.type]),s},tr=(s,e)=>Cs(s).priority??e,q={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...s){return s.map(Cs).forEach(e=>{e.type.forEach(t=>this._removeHandlers[t]?.(e))}),this},add(...s){return s.map(Cs).forEach(e=>{e.type.forEach(t=>{const r=this._addHandlers,i=this._queue;r[t]?r[t]?.(e):(i[t]=i[t]||[],i[t]?.push(e))})}),this},handle(s,e,t){const r=this._addHandlers,i=this._removeHandlers;if(r[s]||i[s])throw new Error(`Extension type ${s} already has a handler`);r[s]=e,i[s]=t;const n=this._queue;return n[s]&&(n[s]?.forEach(o=>e(o)),delete n[s]),this},handleByMap(s,e){return this.handle(s,t=>{t.name&&(e[t.name]=t.ref)},t=>{t.name&&delete e[t.name]})},handleByNamedList(s,e,t=-1){return this.handle(s,r=>{e.findIndex(n=>n.name===r.name)>=0||(e.push({name:r.name,value:r.ref}),e.sort((n,o)=>tr(o.value,t)-tr(n.value,t)))},r=>{const i=e.findIndex(n=>n.name===r.name);i!==-1&&e.splice(i,1)})},handleByList(s,e,t=-1){return this.handle(s,r=>{e.includes(r.ref)||(e.push(r.ref),e.sort((i,n)=>tr(n,t)-tr(i,t)))},r=>{const i=e.indexOf(r.ref);i!==-1&&e.splice(i,1)})},mixin(s,...e){for(const t of e)Object.defineProperties(s.prototype,Object.getOwnPropertyDescriptors(t))}},Rl={extension:{type:x.Environment,name:"browser",priority:-1},test:()=>!0,load:async()=>{await wr(()=>import("./browserAll-C_WFDGnb.js"),__vite__mapDeps([0,1]),import.meta.url)}},kl={extension:{type:x.Environment,name:"webworker",priority:0},test:()=>typeof self<"u"&&self.WorkerGlobalScope!==void 0,load:async()=>{await wr(()=>import("./webworkerAll-B49PaXXS.js"),[],import.meta.url)}};class le{constructor(e,t,r){this._x=t||0,this._y=r||0,this._observer=e}clone(e){return new le(e??this._observer,this._x,this._y)}set(e=0,t=e){return(this._x!==e||this._y!==t)&&(this._x=e,this._y=t,this._observer._onUpdate(this)),this}copyFrom(e){return(this._x!==e.x||this._y!==e.y)&&(this._x=e.x,this._y=e.y,this._observer._onUpdate(this)),this}copyTo(e){return e.set(this._x,this._y),e}equals(e){return e.x===this._x&&e.y===this._y}toString(){return`[pixi.js/math:ObservablePoint x=${this._x} y=${this._y} scope=${this._observer}]`}get x(){return this._x}set x(e){this._x!==e&&(this._x=e,this._observer._onUpdate(this))}get y(){return this._y}set y(e){this._y!==e&&(this._y=e,this._observer._onUpdate(this))}}var Co={exports:{}};(function(s){var e=Object.prototype.hasOwnProperty,t="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(t=!1));function i(h,l,c){this.fn=h,this.context=l,this.once=c||!1}function n(h,l,c,u,f){if(typeof c!="function")throw new TypeError("The listener must be a function");var d=new i(c,u||h,f),p=t?t+l:l;return h._events[p]?h._events[p].fn?h._events[p]=[h._events[p],d]:h._events[p].push(d):(h._events[p]=d,h._eventsCount++),h}function o(h,l){--h._eventsCount===0?h._events=new r:delete h._events[l]}function a(){this._events=new r,this._eventsCount=0}a.prototype.eventNames=function(){var l=[],c,u;if(this._eventsCount===0)return l;for(u in c=this._events)e.call(c,u)&&l.push(t?u.slice(1):u);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(c)):l},a.prototype.listeners=function(l){var c=t?t+l:l,u=this._events[c];if(!u)return[];if(u.fn)return[u.fn];for(var f=0,d=u.length,p=new Array(d);f<d;f++)p[f]=u[f].fn;return p},a.prototype.listenerCount=function(l){var c=t?t+l:l,u=this._events[c];return u?u.fn?1:u.length:0},a.prototype.emit=function(l,c,u,f,d,p){var g=t?t+l:l;if(!this._events[g])return!1;var m=this._events[g],_=arguments.length,y,b;if(m.fn){switch(m.once&&this.removeListener(l,m.fn,void 0,!0),_){case 1:return m.fn.call(m.context),!0;case 2:return m.fn.call(m.context,c),!0;case 3:return m.fn.call(m.context,c,u),!0;case 4:return m.fn.call(m.context,c,u,f),!0;case 5:return m.fn.call(m.context,c,u,f,d),!0;case 6:return m.fn.call(m.context,c,u,f,d,p),!0}for(b=1,y=new Array(_-1);b<_;b++)y[b-1]=arguments[b];m.fn.apply(m.context,y)}else{var v=m.length,w;for(b=0;b<v;b++)switch(m[b].once&&this.removeListener(l,m[b].fn,void 0,!0),_){case 1:m[b].fn.call(m[b].context);break;case 2:m[b].fn.call(m[b].context,c);break;case 3:m[b].fn.call(m[b].context,c,u);break;case 4:m[b].fn.call(m[b].context,c,u,f);break;default:if(!y)for(w=1,y=new Array(_-1);w<_;w++)y[w-1]=arguments[w];m[b].fn.apply(m[b].context,y)}}return!0},a.prototype.on=function(l,c,u){return n(this,l,c,u,!1)},a.prototype.once=function(l,c,u){return n(this,l,c,u,!0)},a.prototype.removeListener=function(l,c,u,f){var d=t?t+l:l;if(!this._events[d])return this;if(!c)return o(this,d),this;var p=this._events[d];if(p.fn)p.fn===c&&(!f||p.once)&&(!u||p.context===u)&&o(this,d);else{for(var g=0,m=[],_=p.length;g<_;g++)(p[g].fn!==c||f&&!p[g].once||u&&p[g].context!==u)&&m.push(p[g]);m.length?this._events[d]=m.length===1?m[0]:m:o(this,d)}return this},a.prototype.removeAllListeners=function(l){var c;return l?(c=t?t+l:l,this._events[c]&&o(this,c)):(this._events=new r,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=t,a.EventEmitter=a,s.exports=a})(Co);var Gl=Co.exports;const de=Po(Gl),Il=Math.PI*2,Fl=180/Math.PI,Ul=Math.PI/180;class H{constructor(e=0,t=0){this.x=0,this.y=0,this.x=e,this.y=t}clone(){return new H(this.x,this.y)}copyFrom(e){return this.set(e.x,e.y),this}copyTo(e){return e.set(this.x,this.y),e}equals(e){return e.x===this.x&&e.y===this.y}set(e=0,t=e){return this.x=e,this.y=t,this}toString(){return`[pixi.js/math:Point x=${this.x} y=${this.y}]`}static get shared(){return Nr.x=0,Nr.y=0,Nr}}const Nr=new H;class B{constructor(e=1,t=0,r=0,i=1,n=0,o=0){this.array=null,this.a=e,this.b=t,this.c=r,this.d=i,this.tx=n,this.ty=o}fromArray(e){this.a=e[0],this.b=e[1],this.c=e[3],this.d=e[4],this.tx=e[2],this.ty=e[5]}set(e,t,r,i,n,o){return this.a=e,this.b=t,this.c=r,this.d=i,this.tx=n,this.ty=o,this}toArray(e,t){this.array||(this.array=new Float32Array(9));const r=t||this.array;return e?(r[0]=this.a,r[1]=this.b,r[2]=0,r[3]=this.c,r[4]=this.d,r[5]=0,r[6]=this.tx,r[7]=this.ty,r[8]=1):(r[0]=this.a,r[1]=this.c,r[2]=this.tx,r[3]=this.b,r[4]=this.d,r[5]=this.ty,r[6]=0,r[7]=0,r[8]=1),r}apply(e,t){t=t||new H;const r=e.x,i=e.y;return t.x=this.a*r+this.c*i+this.tx,t.y=this.b*r+this.d*i+this.ty,t}applyInverse(e,t){t=t||new H;const r=this.a,i=this.b,n=this.c,o=this.d,a=this.tx,h=this.ty,l=1/(r*o+n*-i),c=e.x,u=e.y;return t.x=o*l*c+-n*l*u+(h*n-a*o)*l,t.y=r*l*u+-i*l*c+(-h*r+a*i)*l,t}translate(e,t){return this.tx+=e,this.ty+=t,this}scale(e,t){return this.a*=e,this.d*=t,this.c*=e,this.b*=t,this.tx*=e,this.ty*=t,this}rotate(e){const t=Math.cos(e),r=Math.sin(e),i=this.a,n=this.c,o=this.tx;return this.a=i*t-this.b*r,this.b=i*r+this.b*t,this.c=n*t-this.d*r,this.d=n*r+this.d*t,this.tx=o*t-this.ty*r,this.ty=o*r+this.ty*t,this}append(e){const t=this.a,r=this.b,i=this.c,n=this.d;return this.a=e.a*t+e.b*i,this.b=e.a*r+e.b*n,this.c=e.c*t+e.d*i,this.d=e.c*r+e.d*n,this.tx=e.tx*t+e.ty*i+this.tx,this.ty=e.tx*r+e.ty*n+this.ty,this}appendFrom(e,t){const r=e.a,i=e.b,n=e.c,o=e.d,a=e.tx,h=e.ty,l=t.a,c=t.b,u=t.c,f=t.d;return this.a=r*l+i*u,this.b=r*c+i*f,this.c=n*l+o*u,this.d=n*c+o*f,this.tx=a*l+h*u+t.tx,this.ty=a*c+h*f+t.ty,this}setTransform(e,t,r,i,n,o,a,h,l){return this.a=Math.cos(a+l)*n,this.b=Math.sin(a+l)*n,this.c=-Math.sin(a-h)*o,this.d=Math.cos(a-h)*o,this.tx=e-(r*this.a+i*this.c),this.ty=t-(r*this.b+i*this.d),this}prepend(e){const t=this.tx;if(e.a!==1||e.b!==0||e.c!==0||e.d!==1){const r=this.a,i=this.c;this.a=r*e.a+this.b*e.c,this.b=r*e.b+this.b*e.d,this.c=i*e.a+this.d*e.c,this.d=i*e.b+this.d*e.d}return this.tx=t*e.a+this.ty*e.c+e.tx,this.ty=t*e.b+this.ty*e.d+e.ty,this}decompose(e){const t=this.a,r=this.b,i=this.c,n=this.d,o=e.pivot,a=-Math.atan2(-i,n),h=Math.atan2(r,t),l=Math.abs(a+h);return l<1e-5||Math.abs(Il-l)<1e-5?(e.rotation=h,e.skew.x=e.skew.y=0):(e.rotation=0,e.skew.x=a,e.skew.y=h),e.scale.x=Math.sqrt(t*t+r*r),e.scale.y=Math.sqrt(i*i+n*n),e.position.x=this.tx+(o.x*t+o.y*i),e.position.y=this.ty+(o.x*r+o.y*n),e}invert(){const e=this.a,t=this.b,r=this.c,i=this.d,n=this.tx,o=e*i-t*r;return this.a=i/o,this.b=-t/o,this.c=-r/o,this.d=e/o,this.tx=(r*this.ty-i*n)/o,this.ty=-(e*this.ty-t*n)/o,this}isIdentity(){return this.a===1&&this.b===0&&this.c===0&&this.d===1&&this.tx===0&&this.ty===0}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const e=new B;return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyTo(e){return e.a=this.a,e.b=this.b,e.c=this.c,e.d=this.d,e.tx=this.tx,e.ty=this.ty,e}copyFrom(e){return this.a=e.a,this.b=e.b,this.c=e.c,this.d=e.d,this.tx=e.tx,this.ty=e.ty,this}equals(e){return e.a===this.a&&e.b===this.b&&e.c===this.c&&e.d===this.d&&e.tx===this.tx&&e.ty===this.ty}toString(){return`[pixi.js:Matrix a=${this.a} b=${this.b} c=${this.c} d=${this.d} tx=${this.tx} ty=${this.ty}]`}static get IDENTITY(){return Ol.identity()}static get shared(){return Dl.identity()}}const Dl=new B,Ol=new B,Ye=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],je=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],qe=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],Ke=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],Es=[],Eo=[],rr=Math.sign;function Ll(){for(let s=0;s<16;s++){const e=[];Es.push(e);for(let t=0;t<16;t++){const r=rr(Ye[s]*Ye[t]+qe[s]*je[t]),i=rr(je[s]*Ye[t]+Ke[s]*je[t]),n=rr(Ye[s]*qe[t]+qe[s]*Ke[t]),o=rr(je[s]*qe[t]+Ke[s]*Ke[t]);for(let a=0;a<16;a++)if(Ye[a]===r&&je[a]===i&&qe[a]===n&&Ke[a]===o){e.push(a);break}}}for(let s=0;s<16;s++){const e=new B;e.set(Ye[s],je[s],qe[s],Ke[s],0,0),Eo.push(e)}}Ll();const Y={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:s=>Ye[s],uY:s=>je[s],vX:s=>qe[s],vY:s=>Ke[s],inv:s=>s&8?s&15:-s&7,add:(s,e)=>Es[s][e],sub:(s,e)=>Es[s][Y.inv(e)],rotate180:s=>s^4,isVertical:s=>(s&3)===2,byDirection:(s,e)=>Math.abs(s)*2<=Math.abs(e)?e>=0?Y.S:Y.N:Math.abs(e)*2<=Math.abs(s)?s>0?Y.E:Y.W:e>0?s>0?Y.SE:Y.SW:s>0?Y.NE:Y.NW,matrixAppendRotationInv:(s,e,t=0,r=0)=>{const i=Eo[Y.inv(e)];i.tx=t,i.ty=r,s.append(i)}},sr=[new H,new H,new H,new H];class N{constructor(e=0,t=0,r=0,i=0){this.type="rectangle",this.x=Number(e),this.y=Number(t),this.width=Number(r),this.height=Number(i)}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}isEmpty(){return this.left===this.right||this.top===this.bottom}static get EMPTY(){return new N(0,0,0,0)}clone(){return new N(this.x,this.y,this.width,this.height)}copyFromBounds(e){return this.x=e.minX,this.y=e.minY,this.width=e.maxX-e.minX,this.height=e.maxY-e.minY,this}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){return this.width<=0||this.height<=0?!1:e>=this.x&&e<this.x+this.width&&t>=this.y&&t<this.y+this.height}strokeContains(e,t,r,i=.5){const{width:n,height:o}=this;if(n<=0||o<=0)return!1;const a=this.x,h=this.y,l=r*(1-i),c=r-l,u=a-l,f=a+n+l,d=h-l,p=h+o+l,g=a+c,m=a+n-c,_=h+c,y=h+o-c;return e>=u&&e<=f&&t>=d&&t<=p&&!(e>g&&e<m&&t>_&&t<y)}intersects(e,t){if(!t){const k=this.x<e.x?e.x:this.x;if((this.right>e.right?e.right:this.right)<=k)return!1;const P=this.y<e.y?e.y:this.y;return(this.bottom>e.bottom?e.bottom:this.bottom)>P}const r=this.left,i=this.right,n=this.top,o=this.bottom;if(i<=r||o<=n)return!1;const a=sr[0].set(e.left,e.top),h=sr[1].set(e.left,e.bottom),l=sr[2].set(e.right,e.top),c=sr[3].set(e.right,e.bottom);if(l.x<=a.x||h.y<=a.y)return!1;const u=Math.sign(t.a*t.d-t.b*t.c);if(u===0||(t.apply(a,a),t.apply(h,h),t.apply(l,l),t.apply(c,c),Math.max(a.x,h.x,l.x,c.x)<=r||Math.min(a.x,h.x,l.x,c.x)>=i||Math.max(a.y,h.y,l.y,c.y)<=n||Math.min(a.y,h.y,l.y,c.y)>=o))return!1;const f=u*(h.y-a.y),d=u*(a.x-h.x),p=f*r+d*n,g=f*i+d*n,m=f*r+d*o,_=f*i+d*o;if(Math.max(p,g,m,_)<=f*a.x+d*a.y||Math.min(p,g,m,_)>=f*c.x+d*c.y)return!1;const y=u*(a.y-l.y),b=u*(l.x-a.x),v=y*r+b*n,w=y*i+b*n,S=y*r+b*o,T=y*i+b*o;return!(Math.max(v,w,S,T)<=y*a.x+b*a.y||Math.min(v,w,S,T)>=y*c.x+b*c.y)}pad(e=0,t=e){return this.x-=e,this.y-=t,this.width+=e*2,this.height+=t*2,this}fit(e){const t=Math.max(this.x,e.x),r=Math.min(this.x+this.width,e.x+e.width),i=Math.max(this.y,e.y),n=Math.min(this.y+this.height,e.y+e.height);return this.x=t,this.width=Math.max(r-t,0),this.y=i,this.height=Math.max(n-i,0),this}ceil(e=1,t=.001){const r=Math.ceil((this.x+this.width-t)*e)/e,i=Math.ceil((this.y+this.height-t)*e)/e;return this.x=Math.floor((this.x+t)*e)/e,this.y=Math.floor((this.y+t)*e)/e,this.width=r-this.x,this.height=i-this.y,this}enlarge(e){const t=Math.min(this.x,e.x),r=Math.max(this.x+this.width,e.x+e.width),i=Math.min(this.y,e.y),n=Math.max(this.y+this.height,e.y+e.height);return this.x=t,this.width=r-t,this.y=i,this.height=n-i,this}getBounds(e){return e||(e=new N),e.copyFrom(this),e}containsRect(e){if(this.width<=0||this.height<=0)return!1;const t=e.x,r=e.y,i=e.x+e.width,n=e.y+e.height;return t>=this.x&&t<this.x+this.width&&r>=this.y&&r<this.y+this.height&&i>=this.x&&i<this.x+this.width&&n>=this.y&&n<this.y+this.height}set(e,t,r,i){return this.x=e,this.y=t,this.width=r,this.height=i,this}toString(){return`[pixi.js/math:Rectangle x=${this.x} y=${this.y} width=${this.width} height=${this.height}]`}}const Hr={default:-1};function V(s="default"){return Hr[s]===void 0&&(Hr[s]=-1),++Hr[s]}const zi={},O="8.0.0",Nl="8.3.4";function I(s,e,t=3){if(zi[e])return;let r=new Error().stack;typeof r>"u"?console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`):(r=r.split(`
`).splice(t).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${e}
Deprecated since v${s}`),console.warn(r),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${e}
Deprecated since v${s}`),console.warn(r))),zi[e]=!0}const Mo=()=>{};function ht(s){return s+=s===0?1:0,--s,s|=s>>>1,s|=s>>>2,s|=s>>>4,s|=s>>>8,s|=s>>>16,s+1}function Wi(s){return!(s&s-1)&&!!s}function Ao(s){const e={};for(const t in s)s[t]!==void 0&&(e[t]=s[t]);return e}const Vi=Object.create(null);function Hl(s){const e=Vi[s];return e===void 0&&(Vi[s]=V("resource")),e}const Bo=class Ro extends de{constructor(e={}){super(),this._resourceType="textureSampler",this._touched=0,this._maxAnisotropy=1,this.destroyed=!1,e={...Ro.defaultOptions,...e},this.addressMode=e.addressMode,this.addressModeU=e.addressModeU??this.addressModeU,this.addressModeV=e.addressModeV??this.addressModeV,this.addressModeW=e.addressModeW??this.addressModeW,this.scaleMode=e.scaleMode,this.magFilter=e.magFilter??this.magFilter,this.minFilter=e.minFilter??this.minFilter,this.mipmapFilter=e.mipmapFilter??this.mipmapFilter,this.lodMinClamp=e.lodMinClamp,this.lodMaxClamp=e.lodMaxClamp,this.compare=e.compare,this.maxAnisotropy=e.maxAnisotropy??1}set addressMode(e){this.addressModeU=e,this.addressModeV=e,this.addressModeW=e}get addressMode(){return this.addressModeU}set wrapMode(e){I(O,"TextureStyle.wrapMode is now TextureStyle.addressMode"),this.addressMode=e}get wrapMode(){return this.addressMode}set scaleMode(e){this.magFilter=e,this.minFilter=e,this.mipmapFilter=e}get scaleMode(){return this.magFilter}set maxAnisotropy(e){this._maxAnisotropy=Math.min(e,16),this._maxAnisotropy>1&&(this.scaleMode="linear")}get maxAnisotropy(){return this._maxAnisotropy}get _resourceId(){return this._sharedResourceId||this._generateResourceId()}update(){this.emit("change",this),this._sharedResourceId=null}_generateResourceId(){const e=`${this.addressModeU}-${this.addressModeV}-${this.addressModeW}-${this.magFilter}-${this.minFilter}-${this.mipmapFilter}-${this.lodMinClamp}-${this.lodMaxClamp}-${this.compare}-${this._maxAnisotropy}`;return this._sharedResourceId=Hl(e),this._resourceId}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this.removeAllListeners()}};Bo.defaultOptions={addressMode:"clamp-to-edge",scaleMode:"linear"};let Je=Bo;const ko=class Go extends de{constructor(e={}){super(),this.options=e,this.uid=V("textureSource"),this._resourceType="textureSource",this._resourceId=V("resource"),this.uploadMethodId="unknown",this._resolution=1,this.pixelWidth=1,this.pixelHeight=1,this.width=1,this.height=1,this.sampleCount=1,this.mipLevelCount=1,this.autoGenerateMipmaps=!1,this.format="rgba8unorm",this.dimension="2d",this.antialias=!1,this._touched=0,this._batchTick=-1,this._textureBindLocation=-1,e={...Go.defaultOptions,...e},this.label=e.label??"",this.resource=e.resource,this.autoGarbageCollect=e.autoGarbageCollect,this._resolution=e.resolution,e.width?this.pixelWidth=e.width*this._resolution:this.pixelWidth=this.resource?this.resourceWidth??1:1,e.height?this.pixelHeight=e.height*this._resolution:this.pixelHeight=this.resource?this.resourceHeight??1:1,this.width=this.pixelWidth/this._resolution,this.height=this.pixelHeight/this._resolution,this.format=e.format,this.dimension=e.dimensions,this.mipLevelCount=e.mipLevelCount,this.autoGenerateMipmaps=e.autoGenerateMipmaps,this.sampleCount=e.sampleCount,this.antialias=e.antialias,this.alphaMode=e.alphaMode,this.style=new Je(Ao(e)),this.destroyed=!1,this._refreshPOT()}get source(){return this}get style(){return this._style}set style(e){this.style!==e&&(this._style?.off("change",this._onStyleChange,this),this._style=e,this._style?.on("change",this._onStyleChange,this),this._onStyleChange())}get addressMode(){return this._style.addressMode}set addressMode(e){this._style.addressMode=e}get repeatMode(){return this._style.addressMode}set repeatMode(e){this._style.addressMode=e}get magFilter(){return this._style.magFilter}set magFilter(e){this._style.magFilter=e}get minFilter(){return this._style.minFilter}set minFilter(e){this._style.minFilter=e}get mipmapFilter(){return this._style.mipmapFilter}set mipmapFilter(e){this._style.mipmapFilter=e}get lodMinClamp(){return this._style.lodMinClamp}set lodMinClamp(e){this._style.lodMinClamp=e}get lodMaxClamp(){return this._style.lodMaxClamp}set lodMaxClamp(e){this._style.lodMaxClamp=e}_onStyleChange(){this.emit("styleChange",this)}update(){if(this.resource){const e=this._resolution;if(this.resize(this.resourceWidth/e,this.resourceHeight/e))return}this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._style&&(this._style.destroy(),this._style=null),this.uploadMethodId=null,this.resource=null,this.removeAllListeners()}unload(){this._resourceId=V("resource"),this.emit("change",this),this.emit("unload",this)}get resourceWidth(){const{resource:e}=this;return e.naturalWidth||e.videoWidth||e.displayWidth||e.width}get resourceHeight(){const{resource:e}=this;return e.naturalHeight||e.videoHeight||e.displayHeight||e.height}get resolution(){return this._resolution}set resolution(e){this._resolution!==e&&(this._resolution=e,this.width=this.pixelWidth/e,this.height=this.pixelHeight/e)}resize(e,t,r){r||(r=this._resolution),e||(e=this.width),t||(t=this.height);const i=Math.round(e*r),n=Math.round(t*r);return this.width=i/r,this.height=n/r,this._resolution=r,this.pixelWidth===i&&this.pixelHeight===n?!1:(this._refreshPOT(),this.pixelWidth=i,this.pixelHeight=n,this.emit("resize",this),this._resourceId=V("resource"),this.emit("change",this),!0)}updateMipmaps(){this.autoGenerateMipmaps&&this.mipLevelCount>1&&this.emit("updateMipmaps",this)}set wrapMode(e){this._style.wrapMode=e}get wrapMode(){return this._style.wrapMode}set scaleMode(e){this._style.scaleMode=e}get scaleMode(){return this._style.scaleMode}_refreshPOT(){this.isPowerOfTwo=Wi(this.pixelWidth)&&Wi(this.pixelHeight)}static test(e){throw new Error("Unimplemented")}};ko.defaultOptions={resolution:1,format:"bgra8unorm",alphaMode:"premultiply-alpha-on-upload",dimensions:"2d",mipLevelCount:1,autoGenerateMipmaps:!1,sampleCount:1,antialias:!1,autoGarbageCollect:!1};let Q=ko;class Qs extends Q{constructor(e){const t=e.resource||new Float32Array(e.width*e.height*4);let r=e.format;r||(t instanceof Float32Array?r="rgba32float":t instanceof Int32Array||t instanceof Uint32Array?r="rgba32uint":t instanceof Int16Array||t instanceof Uint16Array?r="rgba16uint":(t instanceof Int8Array,r="bgra8unorm")),super({...e,resource:t,format:r}),this.uploadMethodId="buffer"}static test(e){return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array}}Qs.extension=x.TextureSource;const $i=new B;class Io{constructor(e,t){this.mapCoord=new B,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,typeof t>"u"?this.clampMargin=e.width<10?0:.5:this.clampMargin=t,this.isSimple=!1,this.texture=e}get texture(){return this._texture}set texture(e){this.texture!==e&&(this._texture?.removeListener("update",this.update,this),this._texture=e,this._texture.addListener("update",this.update,this),this.update())}multiplyUvs(e,t){t===void 0&&(t=e);const r=this.mapCoord;for(let i=0;i<e.length;i+=2){const n=e[i],o=e[i+1];t[i]=n*r.a+o*r.c+r.tx,t[i+1]=n*r.b+o*r.d+r.ty}return t}update(){const e=this._texture;this._updateID++;const t=e.uvs;this.mapCoord.set(t.x1-t.x0,t.y1-t.y0,t.x3-t.x0,t.y3-t.y0,t.x0,t.y0);const r=e.orig,i=e.trim;i&&($i.set(r.width/i.width,0,0,r.height/i.height,-i.x/i.width,-i.y/i.height),this.mapCoord.append($i));const n=e.source,o=this.uClampFrame,a=this.clampMargin/n._resolution,h=this.clampOffset/n._resolution;return o[0]=(e.frame.x+a+h)/n.width,o[1]=(e.frame.y+a+h)/n.height,o[2]=(e.frame.x+e.frame.width-a+h)/n.width,o[3]=(e.frame.y+e.frame.height-a+h)/n.height,this.uClampOffset[0]=this.clampOffset/n.pixelWidth,this.uClampOffset[1]=this.clampOffset/n.pixelHeight,this.isSimple=e.frame.width===n.width&&e.frame.height===n.height&&e.rotate===0,!0}}class A extends de{constructor({source:e,label:t,frame:r,orig:i,trim:n,defaultAnchor:o,defaultBorders:a,rotate:h,dynamic:l}={}){if(super(),this.uid=V("texture"),this.uvs={x0:0,y0:0,x1:0,y1:0,x2:0,y2:0,x3:0,y3:0},this.frame=new N,this.noFrame=!1,this.dynamic=!1,this.isTexture=!0,this.label=t,this.source=e?.source??new Q,this.noFrame=!r,r)this.frame.copyFrom(r);else{const{width:c,height:u}=this._source;this.frame.width=c,this.frame.height=u}this.orig=i||this.frame,this.trim=n,this.rotate=h??0,this.defaultAnchor=o,this.defaultBorders=a,this.destroyed=!1,this.dynamic=l||!1,this.updateUvs()}set source(e){this._source&&this._source.off("resize",this.update,this),this._source=e,e.on("resize",this.update,this),this.emit("update",this)}get source(){return this._source}get textureMatrix(){return this._textureMatrix||(this._textureMatrix=new Io(this)),this._textureMatrix}get width(){return this.orig.width}get height(){return this.orig.height}updateUvs(){const{uvs:e,frame:t}=this,{width:r,height:i}=this._source,n=t.x/r,o=t.y/i,a=t.width/r,h=t.height/i;let l=this.rotate;if(l){const c=a/2,u=h/2,f=n+c,d=o+u;l=Y.add(l,Y.NW),e.x0=f+c*Y.uX(l),e.y0=d+u*Y.uY(l),l=Y.add(l,2),e.x1=f+c*Y.uX(l),e.y1=d+u*Y.uY(l),l=Y.add(l,2),e.x2=f+c*Y.uX(l),e.y2=d+u*Y.uY(l),l=Y.add(l,2),e.x3=f+c*Y.uX(l),e.y3=d+u*Y.uY(l)}else e.x0=n,e.y0=o,e.x1=n+a,e.y1=o,e.x2=n+a,e.y2=o+h,e.x3=n,e.y3=o+h}destroy(e=!1){this._source&&e&&(this._source.destroy(),this._source=null),this._textureMatrix=null,this.destroyed=!0,this.emit("destroy",this),this.removeAllListeners()}update(){this.noFrame&&(this.frame.width=this._source.width,this.frame.height=this._source.height),this.updateUvs(),this.emit("update",this)}get baseTexture(){return I(O,"Texture.baseTexture is now Texture.source"),this._source}}A.EMPTY=new A({label:"EMPTY",source:new Q({label:"EMPTY"})});A.EMPTY.destroy=Mo;A.WHITE=new A({source:new Qs({resource:new Uint8Array([255,255,255,255]),width:1,height:1,alphaMode:"premultiply-alpha-on-upload",label:"WHITE"}),label:"WHITE"});A.WHITE.destroy=Mo;function Fo(s,e,t){const{width:r,height:i}=t.orig,n=t.trim;if(n){const o=n.width,a=n.height;s.minX=n.x-e._x*r,s.maxX=s.minX+o,s.minY=n.y-e._y*i,s.maxY=s.minY+a}else s.minX=-e._x*r,s.maxX=s.minX+r,s.minY=-e._y*i,s.maxY=s.minY+i}const Xi=new B;class se{constructor(e=1/0,t=1/0,r=-1/0,i=-1/0){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=Xi,this.minX=e,this.minY=t,this.maxX=r,this.maxY=i}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}get rectangle(){this._rectangle||(this._rectangle=new N);const e=this._rectangle;return this.minX>this.maxX||this.minY>this.maxY?(e.x=0,e.y=0,e.width=0,e.height=0):e.copyFromBounds(this),e}clear(){return this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.matrix=Xi,this}set(e,t,r,i){this.minX=e,this.minY=t,this.maxX=r,this.maxY=i}addFrame(e,t,r,i,n){n||(n=this.matrix);const o=n.a,a=n.b,h=n.c,l=n.d,c=n.tx,u=n.ty;let f=this.minX,d=this.minY,p=this.maxX,g=this.maxY,m=o*e+h*t+c,_=a*e+l*t+u;m<f&&(f=m),_<d&&(d=_),m>p&&(p=m),_>g&&(g=_),m=o*r+h*t+c,_=a*r+l*t+u,m<f&&(f=m),_<d&&(d=_),m>p&&(p=m),_>g&&(g=_),m=o*e+h*i+c,_=a*e+l*i+u,m<f&&(f=m),_<d&&(d=_),m>p&&(p=m),_>g&&(g=_),m=o*r+h*i+c,_=a*r+l*i+u,m<f&&(f=m),_<d&&(d=_),m>p&&(p=m),_>g&&(g=_),this.minX=f,this.minY=d,this.maxX=p,this.maxY=g}addRect(e,t){this.addFrame(e.x,e.y,e.x+e.width,e.y+e.height,t)}addBounds(e,t){this.addFrame(e.minX,e.minY,e.maxX,e.maxY,t)}addBoundsMask(e){this.minX=this.minX>e.minX?this.minX:e.minX,this.minY=this.minY>e.minY?this.minY:e.minY,this.maxX=this.maxX<e.maxX?this.maxX:e.maxX,this.maxY=this.maxY<e.maxY?this.maxY:e.maxY}applyMatrix(e){const t=this.minX,r=this.minY,i=this.maxX,n=this.maxY,{a:o,b:a,c:h,d:l,tx:c,ty:u}=e;let f=o*t+h*r+c,d=a*t+l*r+u;this.minX=f,this.minY=d,this.maxX=f,this.maxY=d,f=o*i+h*r+c,d=a*i+l*r+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY,f=o*t+h*n+c,d=a*t+l*n+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY,f=o*i+h*n+c,d=a*i+l*n+u,this.minX=f<this.minX?f:this.minX,this.minY=d<this.minY?d:this.minY,this.maxX=f>this.maxX?f:this.maxX,this.maxY=d>this.maxY?d:this.maxY}fit(e){return this.minX<e.left&&(this.minX=e.left),this.maxX>e.right&&(this.maxX=e.right),this.minY<e.top&&(this.minY=e.top),this.maxY>e.bottom&&(this.maxY=e.bottom),this}fitBounds(e,t,r,i){return this.minX<e&&(this.minX=e),this.maxX>t&&(this.maxX=t),this.minY<r&&(this.minY=r),this.maxY>i&&(this.maxY=i),this}pad(e,t=e){return this.minX-=e,this.maxX+=e,this.minY-=t,this.maxY+=t,this}ceil(){return this.minX=Math.floor(this.minX),this.minY=Math.floor(this.minY),this.maxX=Math.ceil(this.maxX),this.maxY=Math.ceil(this.maxY),this}clone(){return new se(this.minX,this.minY,this.maxX,this.maxY)}scale(e,t=e){return this.minX*=e,this.minY*=t,this.maxX*=e,this.maxY*=t,this}get x(){return this.minX}set x(e){const t=this.maxX-this.minX;this.minX=e,this.maxX=e+t}get y(){return this.minY}set y(e){const t=this.maxY-this.minY;this.minY=e,this.maxY=e+t}get width(){return this.maxX-this.minX}set width(e){this.maxX=this.minX+e}get height(){return this.maxY-this.minY}set height(e){this.maxY=this.minY+e}get left(){return this.minX}get right(){return this.maxX}get top(){return this.minY}get bottom(){return this.maxY}get isPositive(){return this.maxX-this.minX>0&&this.maxY-this.minY>0}get isValid(){return this.minX+this.minY!==1/0}addVertexData(e,t,r,i){let n=this.minX,o=this.minY,a=this.maxX,h=this.maxY;i||(i=this.matrix);const l=i.a,c=i.b,u=i.c,f=i.d,d=i.tx,p=i.ty;for(let g=t;g<r;g+=2){const m=e[g],_=e[g+1],y=l*m+u*_+d,b=c*m+f*_+p;n=y<n?y:n,o=b<o?b:o,a=y>a?y:a,h=b>h?b:h}this.minX=n,this.minY=o,this.maxX=a,this.maxY=h}containsPoint(e,t){return this.minX<=e&&this.minY<=t&&this.maxX>=e&&this.maxY>=t}toString(){return`[pixi.js:Bounds minX=${this.minX} minY=${this.minY} maxX=${this.maxX} maxY=${this.maxY} width=${this.width} height=${this.height}]`}copyFrom(e){return this.minX=e.minX,this.minY=e.minY,this.maxX=e.maxX,this.maxY=e.maxY,this}}var zl={grad:.9,turn:360,rad:360/(2*Math.PI)},Ce=function(s){return typeof s=="string"?s.length>0:typeof s=="number"},J=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=Math.pow(10,e)),Math.round(t*s)/t+0},fe=function(s,e,t){return e===void 0&&(e=0),t===void 0&&(t=1),s>t?t:s>e?s:e},Uo=function(s){return(s=isFinite(s)?s%360:0)>0?s:s+360},Yi=function(s){return{r:fe(s.r,0,255),g:fe(s.g,0,255),b:fe(s.b,0,255),a:fe(s.a)}},zr=function(s){return{r:J(s.r),g:J(s.g),b:J(s.b),a:J(s.a,3)}},Wl=/^#([0-9a-f]{3,8})$/i,ir=function(s){var e=s.toString(16);return e.length<2?"0"+e:e},Do=function(s){var e=s.r,t=s.g,r=s.b,i=s.a,n=Math.max(e,t,r),o=n-Math.min(e,t,r),a=o?n===e?(t-r)/o:n===t?2+(r-e)/o:4+(e-t)/o:0;return{h:60*(a<0?a+6:a),s:n?o/n*100:0,v:n/255*100,a:i}},Oo=function(s){var e=s.h,t=s.s,r=s.v,i=s.a;e=e/360*6,t/=100,r/=100;var n=Math.floor(e),o=r*(1-t),a=r*(1-(e-n)*t),h=r*(1-(1-e+n)*t),l=n%6;return{r:255*[r,a,o,o,h,r][l],g:255*[h,r,r,a,o,o][l],b:255*[o,o,h,r,r,a][l],a:i}},ji=function(s){return{h:Uo(s.h),s:fe(s.s,0,100),l:fe(s.l,0,100),a:fe(s.a)}},qi=function(s){return{h:J(s.h),s:J(s.s),l:J(s.l),a:J(s.a,3)}},Ki=function(s){return Oo((t=(e=s).s,{h:e.h,s:(t*=((r=e.l)<50?r:100-r)/100)>0?2*t/(r+t)*100:0,v:r+t,a:e.a}));var e,t,r},Bt=function(s){return{h:(e=Do(s)).h,s:(i=(200-(t=e.s))*(r=e.v)/100)>0&&i<200?t*r/100/(i<=100?i:200-i)*100:0,l:i/2,a:e.a};var e,t,r,i},Vl=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,$l=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Xl=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Yl=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Ms={string:[[function(s){var e=Wl.exec(s);return e?(s=e[1]).length<=4?{r:parseInt(s[0]+s[0],16),g:parseInt(s[1]+s[1],16),b:parseInt(s[2]+s[2],16),a:s.length===4?J(parseInt(s[3]+s[3],16)/255,2):1}:s.length===6||s.length===8?{r:parseInt(s.substr(0,2),16),g:parseInt(s.substr(2,2),16),b:parseInt(s.substr(4,2),16),a:s.length===8?J(parseInt(s.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(s){var e=Xl.exec(s)||Yl.exec(s);return e?e[2]!==e[4]||e[4]!==e[6]?null:Yi({r:Number(e[1])/(e[2]?100/255:1),g:Number(e[3])/(e[4]?100/255:1),b:Number(e[5])/(e[6]?100/255:1),a:e[7]===void 0?1:Number(e[7])/(e[8]?100:1)}):null},"rgb"],[function(s){var e=Vl.exec(s)||$l.exec(s);if(!e)return null;var t,r,i=ji({h:(t=e[1],r=e[2],r===void 0&&(r="deg"),Number(t)*(zl[r]||1)),s:Number(e[3]),l:Number(e[4]),a:e[5]===void 0?1:Number(e[5])/(e[6]?100:1)});return Ki(i)},"hsl"]],object:[[function(s){var e=s.r,t=s.g,r=s.b,i=s.a,n=i===void 0?1:i;return Ce(e)&&Ce(t)&&Ce(r)?Yi({r:Number(e),g:Number(t),b:Number(r),a:Number(n)}):null},"rgb"],[function(s){var e=s.h,t=s.s,r=s.l,i=s.a,n=i===void 0?1:i;if(!Ce(e)||!Ce(t)||!Ce(r))return null;var o=ji({h:Number(e),s:Number(t),l:Number(r),a:Number(n)});return Ki(o)},"hsl"],[function(s){var e=s.h,t=s.s,r=s.v,i=s.a,n=i===void 0?1:i;if(!Ce(e)||!Ce(t)||!Ce(r))return null;var o=function(a){return{h:Uo(a.h),s:fe(a.s,0,100),v:fe(a.v,0,100),a:fe(a.a)}}({h:Number(e),s:Number(t),v:Number(r),a:Number(n)});return Oo(o)},"hsv"]]},Zi=function(s,e){for(var t=0;t<e.length;t++){var r=e[t][0](s);if(r)return[r,e[t][1]]}return[null,void 0]},jl=function(s){return typeof s=="string"?Zi(s.trim(),Ms.string):typeof s=="object"&&s!==null?Zi(s,Ms.object):[null,void 0]},Wr=function(s,e){var t=Bt(s);return{h:t.h,s:fe(t.s+100*e,0,100),l:t.l,a:t.a}},Vr=function(s){return(299*s.r+587*s.g+114*s.b)/1e3/255},Qi=function(s,e){var t=Bt(s);return{h:t.h,s:t.s,l:fe(t.l+100*e,0,100),a:t.a}},As=function(){function s(e){this.parsed=jl(e)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return s.prototype.isValid=function(){return this.parsed!==null},s.prototype.brightness=function(){return J(Vr(this.rgba),2)},s.prototype.isDark=function(){return Vr(this.rgba)<.5},s.prototype.isLight=function(){return Vr(this.rgba)>=.5},s.prototype.toHex=function(){return e=zr(this.rgba),t=e.r,r=e.g,i=e.b,o=(n=e.a)<1?ir(J(255*n)):"","#"+ir(t)+ir(r)+ir(i)+o;var e,t,r,i,n,o},s.prototype.toRgb=function(){return zr(this.rgba)},s.prototype.toRgbString=function(){return e=zr(this.rgba),t=e.r,r=e.g,i=e.b,(n=e.a)<1?"rgba("+t+", "+r+", "+i+", "+n+")":"rgb("+t+", "+r+", "+i+")";var e,t,r,i,n},s.prototype.toHsl=function(){return qi(Bt(this.rgba))},s.prototype.toHslString=function(){return e=qi(Bt(this.rgba)),t=e.h,r=e.s,i=e.l,(n=e.a)<1?"hsla("+t+", "+r+"%, "+i+"%, "+n+")":"hsl("+t+", "+r+"%, "+i+"%)";var e,t,r,i,n},s.prototype.toHsv=function(){return e=Do(this.rgba),{h:J(e.h),s:J(e.s),v:J(e.v),a:J(e.a,3)};var e},s.prototype.invert=function(){return ve({r:255-(e=this.rgba).r,g:255-e.g,b:255-e.b,a:e.a});var e},s.prototype.saturate=function(e){return e===void 0&&(e=.1),ve(Wr(this.rgba,e))},s.prototype.desaturate=function(e){return e===void 0&&(e=.1),ve(Wr(this.rgba,-e))},s.prototype.grayscale=function(){return ve(Wr(this.rgba,-1))},s.prototype.lighten=function(e){return e===void 0&&(e=.1),ve(Qi(this.rgba,e))},s.prototype.darken=function(e){return e===void 0&&(e=.1),ve(Qi(this.rgba,-e))},s.prototype.rotate=function(e){return e===void 0&&(e=15),this.hue(this.hue()+e)},s.prototype.alpha=function(e){return typeof e=="number"?ve({r:(t=this.rgba).r,g:t.g,b:t.b,a:e}):J(this.rgba.a,3);var t},s.prototype.hue=function(e){var t=Bt(this.rgba);return typeof e=="number"?ve({h:e,s:t.s,l:t.l,a:t.a}):J(t.h)},s.prototype.isEqual=function(e){return this.toHex()===ve(e).toHex()},s}(),ve=function(s){return s instanceof As?s:new As(s)},Ji=[],ql=function(s){s.forEach(function(e){Ji.indexOf(e)<0&&(e(As,Ms),Ji.push(e))})};function Kl(s,e){var t={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},r={};for(var i in t)r[t[i]]=i;var n={};s.prototype.toName=function(o){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var a,h,l=r[this.toHex()];if(l)return l;if(o?.closest){var c=this.toRgb(),u=1/0,f="black";if(!n.length)for(var d in t)n[d]=new s(t[d]).toRgb();for(var p in t){var g=(a=c,h=n[p],Math.pow(a.r-h.r,2)+Math.pow(a.g-h.g,2)+Math.pow(a.b-h.b,2));g<u&&(u=g,f=p)}return f}},e.string.push([function(o){var a=o.toLowerCase(),h=a==="transparent"?"#0000":t[a];return h?new s(h).toRgb():null},"name"])}ql([Kl]);const lt=class Ct{constructor(e=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=e}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(e){return this.value=e,this}set value(e){if(e instanceof Ct)this._value=this._cloneSource(e._value),this._int=e._int,this._components.set(e._components);else{if(e===null)throw new Error("Cannot set Color#value to null");(this._value===null||!this._isSourceEqual(this._value,e))&&(this._value=this._cloneSource(e),this._normalize(this._value))}}get value(){return this._value}_cloneSource(e){return typeof e=="string"||typeof e=="number"||e instanceof Number||e===null?e:Array.isArray(e)||ArrayBuffer.isView(e)?e.slice(0):typeof e=="object"&&e!==null?{...e}:e}_isSourceEqual(e,t){const r=typeof e;if(r!==typeof t)return!1;if(r==="number"||r==="string"||e instanceof Number)return e===t;if(Array.isArray(e)&&Array.isArray(t)||ArrayBuffer.isView(e)&&ArrayBuffer.isView(t))return e.length!==t.length?!1:e.every((n,o)=>n===t[o]);if(e!==null&&t!==null){const n=Object.keys(e),o=Object.keys(t);return n.length!==o.length?!1:n.every(a=>e[a]===t[a])}return e===t}toRgba(){const[e,t,r,i]=this._components;return{r:e,g:t,b:r,a:i}}toRgb(){const[e,t,r]=this._components;return{r:e,g:t,b:r}}toRgbaString(){const[e,t,r]=this.toUint8RgbArray();return`rgba(${e},${t},${r},${this.alpha})`}toUint8RgbArray(e){const[t,r,i]=this._components;return this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb),e[0]=Math.round(t*255),e[1]=Math.round(r*255),e[2]=Math.round(i*255),e}toArray(e){this._arrayRgba||(this._arrayRgba=[]),e||(e=this._arrayRgba);const[t,r,i,n]=this._components;return e[0]=t,e[1]=r,e[2]=i,e[3]=n,e}toRgbArray(e){this._arrayRgb||(this._arrayRgb=[]),e||(e=this._arrayRgb);const[t,r,i]=this._components;return e[0]=t,e[1]=r,e[2]=i,e}toNumber(){return this._int}toBgrNumber(){const[e,t,r]=this.toUint8RgbArray();return(r<<16)+(t<<8)+e}toLittleEndianNumber(){const e=this._int;return(e>>16)+(e&65280)+((e&255)<<16)}multiply(e){const[t,r,i,n]=Ct._temp.setValue(e)._components;return this._components[0]*=t,this._components[1]*=r,this._components[2]*=i,this._components[3]*=n,this._refreshInt(),this._value=null,this}premultiply(e,t=!0){return t&&(this._components[0]*=e,this._components[1]*=e,this._components[2]*=e),this._components[3]=e,this._refreshInt(),this._value=null,this}toPremultiplied(e,t=!0){if(e===1)return(255<<24)+this._int;if(e===0)return t?0:this._int;let r=this._int>>16&255,i=this._int>>8&255,n=this._int&255;return t&&(r=r*e+.5|0,i=i*e+.5|0,n=n*e+.5|0),(e*255<<24)+(r<<16)+(i<<8)+n}toHex(){const e=this._int.toString(16);return`#${"000000".substring(0,6-e.length)+e}`}toHexa(){const t=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-t.length)+t}setAlpha(e){return this._components[3]=this._clamp(e),this}_normalize(e){let t,r,i,n;if((typeof e=="number"||e instanceof Number)&&e>=0&&e<=16777215){const o=e;t=(o>>16&255)/255,r=(o>>8&255)/255,i=(o&255)/255,n=1}else if((Array.isArray(e)||e instanceof Float32Array)&&e.length>=3&&e.length<=4)e=this._clamp(e),[t,r,i,n=1]=e;else if((e instanceof Uint8Array||e instanceof Uint8ClampedArray)&&e.length>=3&&e.length<=4)e=this._clamp(e,0,255),[t,r,i,n=255]=e,t/=255,r/=255,i/=255,n/=255;else if(typeof e=="string"||typeof e=="object"){if(typeof e=="string"){const a=Ct.HEX_PATTERN.exec(e);a&&(e=`#${a[2]}`)}const o=ve(e);o.isValid()&&({r:t,g:r,b:i,a:n}=o.rgba,t/=255,r/=255,i/=255)}if(t!==void 0)this._components[0]=t,this._components[1]=r,this._components[2]=i,this._components[3]=n,this._refreshInt();else throw new Error(`Unable to convert color ${e}`)}_refreshInt(){this._clamp(this._components);const[e,t,r]=this._components;this._int=(e*255<<16)+(t*255<<8)+(r*255|0)}_clamp(e,t=0,r=1){return typeof e=="number"?Math.min(Math.max(e,t),r):(e.forEach((i,n)=>{e[n]=Math.min(Math.max(i,t),r)}),e)}static isColorLike(e){return typeof e=="number"||typeof e=="string"||e instanceof Number||e instanceof Ct||Array.isArray(e)||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Float32Array||e.r!==void 0&&e.g!==void 0&&e.b!==void 0||e.r!==void 0&&e.g!==void 0&&e.b!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0||e.h!==void 0&&e.s!==void 0&&e.l!==void 0&&e.a!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0||e.h!==void 0&&e.s!==void 0&&e.v!==void 0&&e.a!==void 0}};lt.shared=new lt;lt._temp=new lt;lt.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let L=lt;const Zl={cullArea:null,cullable:!1,cullableChildren:!0};class Js{constructor(e,t){this._pool=[],this._count=0,this._index=0,this._classType=e,t&&this.prepopulate(t)}prepopulate(e){for(let t=0;t<e;t++)this._pool[this._index++]=new this._classType;this._count+=e}get(e){let t;return this._index>0?t=this._pool[--this._index]:t=new this._classType,t.init?.(e),t}return(e){e.reset?.(),this._pool[this._index++]=e}get totalSize(){return this._count}get totalFree(){return this._index}get totalUsed(){return this._count-this._index}clear(){this._pool.length=0,this._index=0}}class Ql{constructor(){this._poolsByClass=new Map}prepopulate(e,t){this.getPool(e).prepopulate(t)}get(e,t){return this.getPool(e).get(t)}return(e){this.getPool(e.constructor).return(e)}getPool(e){return this._poolsByClass.has(e)||this._poolsByClass.set(e,new Js(e)),this._poolsByClass.get(e)}stats(){const e={};return this._poolsByClass.forEach(t=>{const r=e[t._classType.name]?t._classType.name+t._classType.ID:t._classType.name;e[r]={free:t.totalFree,used:t.totalUsed,size:t.totalSize}}),e}}const ee=new Ql,Jl={get isCachedAsTexture(){return!!this.renderGroup?.isCachedAsTexture},cacheAsTexture(s){typeof s=="boolean"&&s===!1?this.disableRenderGroup():(this.enableRenderGroup(),this.renderGroup.enableCacheAsTexture(s===!0?{}:s))},updateCacheTexture(){this.renderGroup?.updateCacheTexture()},get cacheAsBitmap(){return this.isCachedAsTexture},set cacheAsBitmap(s){I("v8.6.0","cacheAsBitmap is deprecated, use cacheAsTexture instead."),this.cacheAsTexture(s)}};function Lo(s,e,t){const r=s.length;let i;if(e>=r||t===0)return;t=e+t>r?r-e:t;const n=r-t;for(i=e;i<n;++i)s[i]=s[i+t];s.length=n}const ec={allowChildren:!0,removeChildren(s=0,e){const t=e??this.children.length,r=t-s,i=[];if(r>0&&r<=t){for(let o=t-1;o>=s;o--){const a=this.children[o];a&&(i.push(a),a.parent=null)}Lo(this.children,s,t);const n=this.renderGroup||this.parentRenderGroup;n&&n.removeChildren(i);for(let o=0;o<i.length;++o){const a=i[o];a.parentRenderLayer?.detach(a),this.emit("childRemoved",a,this,o),i[o].emit("removed",this)}return i.length>0&&this._didViewChangeTick++,i}else if(r===0&&this.children.length===0)return i;throw new RangeError("removeChildren: numeric values are outside the acceptable range.")},removeChildAt(s){const e=this.getChildAt(s);return this.removeChild(e)},getChildAt(s){if(s<0||s>=this.children.length)throw new Error(`getChildAt: Index (${s}) does not exist.`);return this.children[s]},setChildIndex(s,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);this.getChildIndex(s),this.addChildAt(s,e)},getChildIndex(s){const e=this.children.indexOf(s);if(e===-1)throw new Error("The supplied Container must be a child of the caller");return e},addChildAt(s,e){this.allowChildren||I(O,"addChildAt: Only Containers will be allowed to add children in v8.0.0");const{children:t}=this;if(e<0||e>t.length)throw new Error(`${s}addChildAt: The index ${e} supplied is out of bounds ${t.length}`);if(s.parent){const i=s.parent.children.indexOf(s);if(s.parent===this&&i===e)return s;i!==-1&&s.parent.children.splice(i,1)}e===t.length?t.push(s):t.splice(e,0,s),s.parent=this,s.didChange=!0,s._updateFlags=15;const r=this.renderGroup||this.parentRenderGroup;return r&&r.addChild(s),this.sortableChildren&&(this.sortDirty=!0),this.emit("childAdded",s,this,e),s.emit("added",this),s},swapChildren(s,e){if(s===e)return;const t=this.getChildIndex(s),r=this.getChildIndex(e);this.children[t]=e,this.children[r]=s;const i=this.renderGroup||this.parentRenderGroup;i&&(i.structureDidChange=!0),this._didContainerChangeTick++},removeFromParent(){this.parent?.removeChild(this)},reparentChild(...s){return s.length===1?this.reparentChildAt(s[0],this.children.length):(s.forEach(e=>this.reparentChildAt(e,this.children.length)),s[0])},reparentChildAt(s,e){if(s.parent===this)return this.setChildIndex(s,e),s;const t=s.worldTransform.clone();s.removeFromParent(),this.addChildAt(s,e);const r=this.worldTransform.clone();return r.invert(),t.prepend(r),s.setFromMatrix(t),s}},tc={collectRenderables(s,e,t){this.parentRenderLayer&&this.parentRenderLayer!==t||this.globalDisplayStatus<7||!this.includeInBuild||(this.sortableChildren&&this.sortChildren(),this.isSimple?this.collectRenderablesSimple(s,e,t):this.renderGroup?e.renderPipes.renderGroup.addRenderGroup(this.renderGroup,s):this.collectRenderablesWithEffects(s,e,t))},collectRenderablesSimple(s,e,t){const r=this.children,i=r.length;for(let n=0;n<i;n++)r[n].collectRenderables(s,e,t)},collectRenderablesWithEffects(s,e,t){const{renderPipes:r}=e;for(let i=0;i<this.effects.length;i++){const n=this.effects[i];r[n.pipe].push(n,this,s)}this.collectRenderablesSimple(s,e,t);for(let i=this.effects.length-1;i>=0;i--){const n=this.effects[i];r[n.pipe].pop(n,this,s)}}};class Pr{constructor(){this.pipe="filter",this.priority=1}destroy(){for(let e=0;e<this.filters.length;e++)this.filters[e].destroy();this.filters=null,this.filterArea=null}}class rc{constructor(){this._effectClasses=[],this._tests=[],this._initialized=!1}init(){this._initialized||(this._initialized=!0,this._effectClasses.forEach(e=>{this.add({test:e.test,maskClass:e})}))}add(e){this._tests.push(e)}getMaskEffect(e){this._initialized||this.init();for(let t=0;t<this._tests.length;t++){const r=this._tests[t];if(r.test(e))return ee.get(r.maskClass,e)}return e}returnMaskEffect(e){ee.return(e)}}const Bs=new rc;q.handleByList(x.MaskEffect,Bs._effectClasses);const sc={_maskEffect:null,_maskOptions:{inverse:!1},_filterEffect:null,effects:[],_markStructureAsChanged(){const s=this.renderGroup||this.parentRenderGroup;s&&(s.structureDidChange=!0)},addEffect(s){this.effects.indexOf(s)===-1&&(this.effects.push(s),this.effects.sort((t,r)=>t.priority-r.priority),this._markStructureAsChanged(),this._updateIsSimple())},removeEffect(s){const e=this.effects.indexOf(s);e!==-1&&(this.effects.splice(e,1),this._markStructureAsChanged(),this._updateIsSimple())},set mask(s){const e=this._maskEffect;e?.mask!==s&&(e&&(this.removeEffect(e),Bs.returnMaskEffect(e),this._maskEffect=null),s!=null&&(this._maskEffect=Bs.getMaskEffect(s),this.addEffect(this._maskEffect)))},get mask(){return this._maskEffect?.mask},setMask(s){this._maskOptions={...this._maskOptions,...s},s.mask&&(this.mask=s.mask),this._markStructureAsChanged()},set filters(s){!Array.isArray(s)&&s&&(s=[s]);const e=this._filterEffect||(this._filterEffect=new Pr);s=s;const t=s?.length>0,r=e.filters?.length>0,i=t!==r;s=Array.isArray(s)?s.slice(0):s,e.filters=Object.freeze(s),i&&(t?this.addEffect(e):(this.removeEffect(e),e.filters=s??null))},get filters(){return this._filterEffect?.filters},set filterArea(s){this._filterEffect||(this._filterEffect=new Pr),this._filterEffect.filterArea=s},get filterArea(){return this._filterEffect?.filterArea}},ic={label:null,get name(){return I(O,"Container.name property has been removed, use Container.label instead"),this.label},set name(s){I(O,"Container.name property has been removed, use Container.label instead"),this.label=s},getChildByName(s,e=!1){return this.getChildByLabel(s,e)},getChildByLabel(s,e=!1){const t=this.children;for(let r=0;r<t.length;r++){const i=t[r];if(i.label===s||s instanceof RegExp&&s.test(i.label))return i}if(e)for(let r=0;r<t.length;r++){const n=t[r].getChildByLabel(s,!0);if(n)return n}return null},getChildrenByLabel(s,e=!1,t=[]){const r=this.children;for(let i=0;i<r.length;i++){const n=r[i];(n.label===s||s instanceof RegExp&&s.test(n.label))&&t.push(n)}if(e)for(let i=0;i<r.length;i++)r[i].getChildrenByLabel(s,!0,t);return t}},ie=new Js(B),Ae=new Js(se),nc=new B,oc={getFastGlobalBounds(s,e){e||(e=new se),e.clear(),this._getGlobalBoundsRecursive(!!s,e,this.parentRenderLayer),e.isValid||e.set(0,0,0,0);const t=this.renderGroup||this.parentRenderGroup;return e.applyMatrix(t.worldTransform),e},_getGlobalBoundsRecursive(s,e,t){let r=e;if(s&&this.parentRenderLayer&&this.parentRenderLayer!==t||this.localDisplayStatus!==7||!this.measurable)return;const i=!!this.effects.length;if((this.renderGroup||i)&&(r=Ae.get().clear()),this.boundsArea)e.addRect(this.boundsArea,this.worldTransform);else{if(this.renderPipeId){const o=this.bounds;r.addFrame(o.minX,o.minY,o.maxX,o.maxY,this.groupTransform)}const n=this.children;for(let o=0;o<n.length;o++)n[o]._getGlobalBoundsRecursive(s,r,t)}if(i){let n=!1;const o=this.renderGroup||this.parentRenderGroup;for(let a=0;a<this.effects.length;a++)this.effects[a].addBounds&&(n||(n=!0,r.applyMatrix(o.worldTransform)),this.effects[a].addBounds(r,!0));n&&(r.applyMatrix(o.worldTransform.copyTo(nc).invert()),e.addBounds(r,this.relativeGroupTransform)),e.addBounds(r),Ae.return(r)}else this.renderGroup&&(e.addBounds(r,this.relativeGroupTransform),Ae.return(r))}};function ei(s,e,t){t.clear();let r,i;return s.parent?e?r=s.parent.worldTransform:(i=ie.get().identity(),r=ti(s,i)):r=B.IDENTITY,No(s,t,r,e),i&&ie.return(i),t.isValid||t.set(0,0,0,0),t}function No(s,e,t,r){if(!s.visible||!s.measurable)return;let i;r?i=s.worldTransform:(s.updateLocalTransform(),i=ie.get(),i.appendFrom(s.localTransform,t));const n=e,o=!!s.effects.length;if(o&&(e=Ae.get().clear()),s.boundsArea)e.addRect(s.boundsArea,i);else{s.bounds&&(e.matrix=i,e.addBounds(s.bounds));for(let a=0;a<s.children.length;a++)No(s.children[a],e,i,r)}if(o){for(let a=0;a<s.effects.length;a++)s.effects[a].addBounds?.(e);n.addBounds(e,B.IDENTITY),Ae.return(e)}r||ie.return(i)}function ti(s,e){const t=s.parent;return t&&(ti(t,e),t.updateLocalTransform(),e.append(t.localTransform)),e}function Ho(s,e){if(s===16777215||!e)return e;if(e===16777215||!s)return s;const t=s>>16&255,r=s>>8&255,i=s&255,n=e>>16&255,o=e>>8&255,a=e&255,h=t*n/255|0,l=r*o/255|0,c=i*a/255|0;return(h<<16)+(l<<8)+c}const en=16777215;function Cr(s,e){return s===en?e:e===en?s:Ho(s,e)}function br(s){return((s&255)<<16)+(s&65280)+(s>>16&255)}const ac={getGlobalAlpha(s){if(s)return this.renderGroup?this.renderGroup.worldAlpha:this.parentRenderGroup?this.parentRenderGroup.worldAlpha*this.alpha:this.alpha;let e=this.alpha,t=this.parent;for(;t;)e*=t.alpha,t=t.parent;return e},getGlobalTransform(s,e){if(e)return s.copyFrom(this.worldTransform);this.updateLocalTransform();const t=ti(this,ie.get().identity());return s.appendFrom(this.localTransform,t),ie.return(t),s},getGlobalTint(s){if(s)return this.renderGroup?br(this.renderGroup.worldColor):this.parentRenderGroup?br(Cr(this.localColor,this.parentRenderGroup.worldColor)):this.tint;let e=this.localColor,t=this.parent;for(;t;)e=Cr(e,t.localColor),t=t.parent;return br(e)}};function ri(s,e,t){return e.clear(),t||(t=B.IDENTITY),zo(s,e,t,s,!0),e.isValid||e.set(0,0,0,0),e}function zo(s,e,t,r,i){let n;if(i)n=ie.get(),n=t.copyTo(n);else{if(!s.visible||!s.measurable)return;s.updateLocalTransform();const h=s.localTransform;n=ie.get(),n.appendFrom(h,t)}const o=e,a=!!s.effects.length;if(a&&(e=Ae.get().clear()),s.boundsArea)e.addRect(s.boundsArea,n);else{s.renderPipeId&&(e.matrix=n,e.addBounds(s.bounds));const h=s.children;for(let l=0;l<h.length;l++)zo(h[l],e,n,r,!1)}if(a){for(let h=0;h<s.effects.length;h++)s.effects[h].addLocalBounds?.(e,r);o.addBounds(e,B.IDENTITY),Ae.return(e)}ie.return(n)}function Wo(s,e){const t=s.children;for(let r=0;r<t.length;r++){const i=t[r],n=i.uid,o=(i._didViewChangeTick&65535)<<16|i._didContainerChangeTick&65535,a=e.index;(e.data[a]!==n||e.data[a+1]!==o)&&(e.data[e.index]=n,e.data[e.index+1]=o,e.didChange=!0),e.index=a+2,i.children.length&&Wo(i,e)}return e.didChange}const hc=new B,lc={_localBoundsCacheId:-1,_localBoundsCacheData:null,_setWidth(s,e){const t=Math.sign(this.scale.x)||1;e!==0?this.scale.x=s/e*t:this.scale.x=t},_setHeight(s,e){const t=Math.sign(this.scale.y)||1;e!==0?this.scale.y=s/e*t:this.scale.y=t},getLocalBounds(){this._localBoundsCacheData||(this._localBoundsCacheData={data:[],index:1,didChange:!1,localBounds:new se});const s=this._localBoundsCacheData;return s.index=1,s.didChange=!1,s.data[0]!==this._didViewChangeTick&&(s.didChange=!0,s.data[0]=this._didViewChangeTick),Wo(this,s),s.didChange&&ri(this,s.localBounds,hc),s.localBounds},getBounds(s,e){return ei(this,s,e||new se)}},cc={_onRender:null,set onRender(s){const e=this.renderGroup||this.parentRenderGroup;if(!s){this._onRender&&e?.removeOnRender(this),this._onRender=null;return}this._onRender||e?.addOnRender(this),this._onRender=s},get onRender(){return this._onRender}},uc={_zIndex:0,sortDirty:!1,sortableChildren:!1,get zIndex(){return this._zIndex},set zIndex(s){this._zIndex!==s&&(this._zIndex=s,this.depthOfChildModified())},depthOfChildModified(){this.parent&&(this.parent.sortableChildren=!0,this.parent.sortDirty=!0),this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0)},sortChildren(){this.sortDirty&&(this.sortDirty=!1,this.children.sort(dc))}};function dc(s,e){return s._zIndex-e._zIndex}const fc={getGlobalPosition(s=new H,e=!1){return this.parent?this.parent.toGlobal(this._position,s,e):(s.x=this._position.x,s.y=this._position.y),s},toGlobal(s,e,t=!1){const r=this.getGlobalTransform(ie.get(),t);return e=r.apply(s,e),ie.return(r),e},toLocal(s,e,t,r){e&&(s=e.toGlobal(s,t,r));const i=this.getGlobalTransform(ie.get(),r);return t=i.applyInverse(s,t),ie.return(i),t}};class Vo{constructor(){this.uid=V("instructionSet"),this.instructions=[],this.instructionSize=0,this.renderables=[],this.gcTick=0}reset(){this.instructionSize=0}add(e){this.instructions[this.instructionSize++]=e}log(){this.instructions.length=this.instructionSize,console.table(this.instructions,["type","action"])}}let pc=0;class mc{constructor(e){this._poolKeyHash=Object.create(null),this._texturePool={},this.textureOptions=e||{},this.enableFullScreen=!1,this.textureStyle=new Je(this.textureOptions)}createTexture(e,t,r){const i=new Q({...this.textureOptions,width:e,height:t,resolution:1,antialias:r,autoGarbageCollect:!1});return new A({source:i,label:`texturePool_${pc++}`})}getOptimalTexture(e,t,r=1,i){let n=Math.ceil(e*r-1e-6),o=Math.ceil(t*r-1e-6);n=ht(n),o=ht(o);const a=(n<<17)+(o<<1)+(i?1:0);this._texturePool[a]||(this._texturePool[a]=[]);let h=this._texturePool[a].pop();return h||(h=this.createTexture(n,o,i)),h.source._resolution=r,h.source.width=n/r,h.source.height=o/r,h.source.pixelWidth=n,h.source.pixelHeight=o,h.frame.x=0,h.frame.y=0,h.frame.width=e,h.frame.height=t,h.updateUvs(),this._poolKeyHash[h.uid]=a,h}getSameSizeTexture(e,t=!1){const r=e.source;return this.getOptimalTexture(e.width,e.height,r._resolution,t)}returnTexture(e,t=!1){const r=this._poolKeyHash[e.uid];t&&(e.source.style=this.textureStyle),this._texturePool[r].push(e)}clear(e){if(e=e!==!1,e)for(const t in this._texturePool){const r=this._texturePool[t];if(r)for(let i=0;i<r.length;i++)r[i].destroy(!0)}this._texturePool={}}}const te=new mc;class gc{constructor(){this.renderPipeId="renderGroup",this.root=null,this.canBundle=!1,this.renderGroupParent=null,this.renderGroupChildren=[],this.worldTransform=new B,this.worldColorAlpha=4294967295,this.worldColor=16777215,this.worldAlpha=1,this.childrenToUpdate=Object.create(null),this.updateTick=0,this.gcTick=0,this.childrenRenderablesToUpdate={list:[],index:0},this.structureDidChange=!0,this.instructionSet=new Vo,this._onRenderContainers=[],this.textureNeedsUpdate=!0,this.isCachedAsTexture=!1,this._matrixDirty=7}init(e){this.root=e,e._onRender&&this.addOnRender(e),e.didChange=!0;const t=e.children;for(let r=0;r<t.length;r++){const i=t[r];i._updateFlags=15,this.addChild(i)}}enableCacheAsTexture(e={}){this.textureOptions=e,this.isCachedAsTexture=!0,this.textureNeedsUpdate=!0}disableCacheAsTexture(){this.isCachedAsTexture=!1,this.texture&&(te.returnTexture(this.texture),this.texture=null)}updateCacheTexture(){this.textureNeedsUpdate=!0}reset(){this.renderGroupChildren.length=0;for(const e in this.childrenToUpdate){const t=this.childrenToUpdate[e];t.list.fill(null),t.index=0}this.childrenRenderablesToUpdate.index=0,this.childrenRenderablesToUpdate.list.fill(null),this.root=null,this.updateTick=0,this.structureDidChange=!0,this._onRenderContainers.length=0,this.renderGroupParent=null,this.disableCacheAsTexture()}get localTransform(){return this.root.localTransform}addRenderGroupChild(e){e.renderGroupParent&&e.renderGroupParent._removeRenderGroupChild(e),e.renderGroupParent=this,this.renderGroupChildren.push(e)}_removeRenderGroupChild(e){const t=this.renderGroupChildren.indexOf(e);t>-1&&this.renderGroupChildren.splice(t,1),e.renderGroupParent=null}addChild(e){if(this.structureDidChange=!0,e.parentRenderGroup=this,e.updateTick=-1,e.parent===this.root?e.relativeRenderGroupDepth=1:e.relativeRenderGroupDepth=e.parent.relativeRenderGroupDepth+1,e.didChange=!0,this.onChildUpdate(e),e.renderGroup){this.addRenderGroupChild(e.renderGroup);return}e._onRender&&this.addOnRender(e);const t=e.children;for(let r=0;r<t.length;r++)this.addChild(t[r])}removeChild(e){if(this.structureDidChange=!0,e._onRender&&(e.renderGroup||this.removeOnRender(e)),e.parentRenderGroup=null,e.renderGroup){this._removeRenderGroupChild(e.renderGroup);return}const t=e.children;for(let r=0;r<t.length;r++)this.removeChild(t[r])}removeChildren(e){for(let t=0;t<e.length;t++)this.removeChild(e[t])}onChildUpdate(e){let t=this.childrenToUpdate[e.relativeRenderGroupDepth];t||(t=this.childrenToUpdate[e.relativeRenderGroupDepth]={index:0,list:[]}),t.list[t.index++]=e}updateRenderable(e){e.globalDisplayStatus<7||(this.instructionSet.renderPipes[e.renderPipeId].updateRenderable(e),e.didViewUpdate=!1)}onChildViewUpdate(e){this.childrenRenderablesToUpdate.list[this.childrenRenderablesToUpdate.index++]=e}get isRenderable(){return this.root.localDisplayStatus===7&&this.worldAlpha>0}addOnRender(e){this._onRenderContainers.push(e)}removeOnRender(e){this._onRenderContainers.splice(this._onRenderContainers.indexOf(e),1)}runOnRender(e){for(let t=0;t<this._onRenderContainers.length;t++)this._onRenderContainers[t]._onRender(e)}destroy(){this.disableCacheAsTexture(),this.renderGroupParent=null,this.root=null,this.childrenRenderablesToUpdate=null,this.childrenToUpdate=null,this.renderGroupChildren=null,this._onRenderContainers=null,this.instructionSet=null}getChildren(e=[]){const t=this.root.children;for(let r=0;r<t.length;r++)this._getChildren(t[r],e);return e}_getChildren(e,t=[]){if(t.push(e),e.renderGroup)return t;const r=e.children;for(let i=0;i<r.length;i++)this._getChildren(r[i],t);return t}invalidateMatrices(){this._matrixDirty=7}get inverseWorldTransform(){return this._matrixDirty&1?(this._matrixDirty&=-2,this._inverseWorldTransform||(this._inverseWorldTransform=new B),this._inverseWorldTransform.copyFrom(this.worldTransform).invert()):this._inverseWorldTransform}get textureOffsetInverseTransform(){return this._matrixDirty&2?(this._matrixDirty&=-3,this._textureOffsetInverseTransform||(this._textureOffsetInverseTransform=new B),this._textureOffsetInverseTransform.copyFrom(this.inverseWorldTransform).translate(-this._textureBounds.x,-this._textureBounds.y)):this._textureOffsetInverseTransform}get inverseParentTextureTransform(){if(!(this._matrixDirty&4))return this._inverseParentTextureTransform;this._matrixDirty&=-5;const e=this._parentCacheAsTextureRenderGroup;return e?(this._inverseParentTextureTransform||(this._inverseParentTextureTransform=new B),this._inverseParentTextureTransform.copyFrom(this.worldTransform).prepend(e.inverseWorldTransform).translate(-e._textureBounds.x,-e._textureBounds.y)):this.worldTransform}get cacheToLocalTransform(){return this._parentCacheAsTextureRenderGroup?this._parentCacheAsTextureRenderGroup.textureOffsetInverseTransform:null}}function _c(s,e,t={}){for(const r in e)!t[r]&&e[r]!==void 0&&(s[r]=e[r])}const $r=new le(null),Xr=new le(null),Yr=new le(null,1,1),Er=1,si=2,Rt=4;class xe extends de{constructor(e={}){super(),this.uid=V("renderable"),this._updateFlags=15,this.renderGroup=null,this.parentRenderGroup=null,this.parentRenderGroupIndex=0,this.didChange=!1,this.didViewUpdate=!1,this.relativeRenderGroupDepth=0,this.children=[],this.parent=null,this.includeInBuild=!0,this.measurable=!0,this.isSimple=!0,this.updateTick=-1,this.localTransform=new B,this.relativeGroupTransform=new B,this.groupTransform=this.relativeGroupTransform,this.destroyed=!1,this._position=new le(this,0,0),this._scale=Yr,this._pivot=Xr,this._skew=$r,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._rotation=0,this.localColor=16777215,this.localAlpha=1,this.groupAlpha=1,this.groupColor=16777215,this.groupColorAlpha=4294967295,this.localBlendMode="inherit",this.groupBlendMode="normal",this.localDisplayStatus=7,this.globalDisplayStatus=7,this._didContainerChangeTick=0,this._didViewChangeTick=0,this._didLocalTransformChangeId=-1,this.effects=[],_c(this,e,{children:!0,parent:!0,effects:!0}),e.children?.forEach(t=>this.addChild(t)),e.parent?.addChild(this)}static mixin(e){I("8.8.0","Container.mixin is deprecated, please use extensions.mixin instead."),q.mixin(xe,e)}set _didChangeId(e){this._didViewChangeTick=e>>12&4095,this._didContainerChangeTick=e&4095}get _didChangeId(){return this._didContainerChangeTick&4095|(this._didViewChangeTick&4095)<<12}addChild(...e){if(this.allowChildren||I(O,"addChild: Only Containers will be allowed to add children in v8.0.0"),e.length>1){for(let i=0;i<e.length;i++)this.addChild(e[i]);return e[0]}const t=e[0],r=this.renderGroup||this.parentRenderGroup;return t.parent===this?(this.children.splice(this.children.indexOf(t),1),this.children.push(t),r&&(r.structureDidChange=!0),t):(t.parent&&t.parent.removeChild(t),this.children.push(t),this.sortableChildren&&(this.sortDirty=!0),t.parent=this,t.didChange=!0,t._updateFlags=15,r&&r.addChild(t),this.emit("childAdded",t,this,this.children.length-1),t.emit("added",this),this._didViewChangeTick++,t._zIndex!==0&&t.depthOfChildModified(),t)}removeChild(...e){if(e.length>1){for(let i=0;i<e.length;i++)this.removeChild(e[i]);return e[0]}const t=e[0],r=this.children.indexOf(t);return r>-1&&(this._didViewChangeTick++,this.children.splice(r,1),this.renderGroup?this.renderGroup.removeChild(t):this.parentRenderGroup&&this.parentRenderGroup.removeChild(t),t.parentRenderLayer&&t.parentRenderLayer.detach(t),t.parent=null,this.emit("childRemoved",t,this,r),t.emit("removed",this)),t}_onUpdate(e){e&&e===this._skew&&this._updateSkew(),this._didContainerChangeTick++,!this.didChange&&(this.didChange=!0,this.parentRenderGroup&&this.parentRenderGroup.onChildUpdate(this))}set isRenderGroup(e){!!this.renderGroup!==e&&(e?this.enableRenderGroup():this.disableRenderGroup())}get isRenderGroup(){return!!this.renderGroup}enableRenderGroup(){if(this.renderGroup)return;const e=this.parentRenderGroup;e?.removeChild(this),this.renderGroup=ee.get(gc,this),this.groupTransform=B.IDENTITY,e?.addChild(this),this._updateIsSimple()}disableRenderGroup(){if(!this.renderGroup)return;const e=this.parentRenderGroup;e?.removeChild(this),ee.return(this.renderGroup),this.renderGroup=null,this.groupTransform=this.relativeGroupTransform,e?.addChild(this),this._updateIsSimple()}_updateIsSimple(){this.isSimple=!this.renderGroup&&this.effects.length===0}get worldTransform(){return this._worldTransform||(this._worldTransform=new B),this.renderGroup?this._worldTransform.copyFrom(this.renderGroup.worldTransform):this.parentRenderGroup&&this._worldTransform.appendFrom(this.relativeGroupTransform,this.parentRenderGroup.worldTransform),this._worldTransform}get x(){return this._position.x}set x(e){this._position.x=e}get y(){return this._position.y}set y(e){this._position.y=e}get position(){return this._position}set position(e){this._position.copyFrom(e)}get rotation(){return this._rotation}set rotation(e){this._rotation!==e&&(this._rotation=e,this._onUpdate(this._skew))}get angle(){return this.rotation*Fl}set angle(e){this.rotation=e*Ul}get pivot(){return this._pivot===Xr&&(this._pivot=new le(this,0,0)),this._pivot}set pivot(e){this._pivot===Xr&&(this._pivot=new le(this,0,0)),typeof e=="number"?this._pivot.set(e):this._pivot.copyFrom(e)}get skew(){return this._skew===$r&&(this._skew=new le(this,0,0)),this._skew}set skew(e){this._skew===$r&&(this._skew=new le(this,0,0)),this._skew.copyFrom(e)}get scale(){return this._scale===Yr&&(this._scale=new le(this,1,1)),this._scale}set scale(e){this._scale===Yr&&(this._scale=new le(this,0,0)),typeof e=="number"?this._scale.set(e):this._scale.copyFrom(e)}get width(){return Math.abs(this.scale.x*this.getLocalBounds().width)}set width(e){const t=this.getLocalBounds().width;this._setWidth(e,t)}get height(){return Math.abs(this.scale.y*this.getLocalBounds().height)}set height(e){const t=this.getLocalBounds().height;this._setHeight(e,t)}getSize(e){e||(e={});const t=this.getLocalBounds();return e.width=Math.abs(this.scale.x*t.width),e.height=Math.abs(this.scale.y*t.height),e}setSize(e,t){const r=this.getLocalBounds();typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,r.width),t!==void 0&&this._setHeight(t,r.height)}_updateSkew(){const e=this._rotation,t=this._skew;this._cx=Math.cos(e+t._y),this._sx=Math.sin(e+t._y),this._cy=-Math.sin(e-t._x),this._sy=Math.cos(e-t._x)}updateTransform(e){return this.position.set(typeof e.x=="number"?e.x:this.position.x,typeof e.y=="number"?e.y:this.position.y),this.scale.set(typeof e.scaleX=="number"?e.scaleX||1:this.scale.x,typeof e.scaleY=="number"?e.scaleY||1:this.scale.y),this.rotation=typeof e.rotation=="number"?e.rotation:this.rotation,this.skew.set(typeof e.skewX=="number"?e.skewX:this.skew.x,typeof e.skewY=="number"?e.skewY:this.skew.y),this.pivot.set(typeof e.pivotX=="number"?e.pivotX:this.pivot.x,typeof e.pivotY=="number"?e.pivotY:this.pivot.y),this}setFromMatrix(e){e.decompose(this)}updateLocalTransform(){const e=this._didContainerChangeTick;if(this._didLocalTransformChangeId===e)return;this._didLocalTransformChangeId=e;const t=this.localTransform,r=this._scale,i=this._pivot,n=this._position,o=r._x,a=r._y,h=i._x,l=i._y;t.a=this._cx*o,t.b=this._sx*o,t.c=this._cy*a,t.d=this._sy*a,t.tx=n._x-(h*t.a+l*t.c),t.ty=n._y-(h*t.b+l*t.d)}set alpha(e){e!==this.localAlpha&&(this.localAlpha=e,this._updateFlags|=Er,this._onUpdate())}get alpha(){return this.localAlpha}set tint(e){const r=L.shared.setValue(e??16777215).toBgrNumber();r!==this.localColor&&(this.localColor=r,this._updateFlags|=Er,this._onUpdate())}get tint(){return br(this.localColor)}set blendMode(e){this.localBlendMode!==e&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=si,this.localBlendMode=e,this._onUpdate())}get blendMode(){return this.localBlendMode}get visible(){return!!(this.localDisplayStatus&2)}set visible(e){const t=e?2:0;(this.localDisplayStatus&2)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Rt,this.localDisplayStatus^=2,this._onUpdate())}get culled(){return!(this.localDisplayStatus&4)}set culled(e){const t=e?0:4;(this.localDisplayStatus&4)!==t&&(this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._updateFlags|=Rt,this.localDisplayStatus^=4,this._onUpdate())}get renderable(){return!!(this.localDisplayStatus&1)}set renderable(e){const t=e?1:0;(this.localDisplayStatus&1)!==t&&(this._updateFlags|=Rt,this.localDisplayStatus^=1,this.parentRenderGroup&&(this.parentRenderGroup.structureDidChange=!0),this._onUpdate())}get isRenderable(){return this.localDisplayStatus===7&&this.groupAlpha>0}destroy(e=!1){if(this.destroyed)return;this.destroyed=!0;let t;if(this.children.length&&(t=this.removeChildren(0,this.children.length)),this.removeFromParent(),this.parent=null,this._maskEffect=null,this._filterEffect=null,this.effects=null,this._position=null,this._scale=null,this._pivot=null,this._skew=null,this.emit("destroyed",this),this.removeAllListeners(),(typeof e=="boolean"?e:e?.children)&&t)for(let i=0;i<t.length;++i)t[i].destroy(e);this.renderGroup?.destroy(),this.renderGroup=null}}q.mixin(xe,ec,oc,fc,cc,lc,sc,ic,uc,Zl,Jl,ac,tc);class $o extends xe{constructor(e){super(e),this.canBundle=!0,this.allowChildren=!1,this._roundPixels=0,this._lastUsed=-1,this._gpuData=Object.create(null),this._bounds=new se(0,1,0,0),this._boundsDirty=!0}get bounds(){return this._boundsDirty?(this.updateBounds(),this._boundsDirty=!1,this._bounds):this._bounds}get roundPixels(){return!!this._roundPixels}set roundPixels(e){this._roundPixels=e?1:0}containsPoint(e){const t=this.bounds,{x:r,y:i}=e;return r>=t.minX&&r<=t.maxX&&i>=t.minY&&i<=t.maxY}onViewUpdate(){if(this._didViewChangeTick++,this._boundsDirty=!0,this.didViewUpdate)return;this.didViewUpdate=!0;const e=this.renderGroup||this.parentRenderGroup;e&&e.onChildViewUpdate(this)}destroy(e){super.destroy(e),this._bounds=null;for(const t in this._gpuData)this._gpuData[t].destroy?.();this._gpuData=null}collectRenderablesSimple(e,t,r){const{renderPipes:i}=t;i.blendMode.setBlendMode(this,this.groupBlendMode,e),i[this.renderPipeId].addRenderable(this,e),this.didViewUpdate=!1;const o=this.children,a=o.length;for(let h=0;h<a;h++)o[h].collectRenderables(e,t,r)}}class ct extends $o{constructor(e=A.EMPTY){e instanceof A&&(e={texture:e});const{texture:t=A.EMPTY,anchor:r,roundPixels:i,width:n,height:o,...a}=e;super({label:"Sprite",...a}),this.renderPipeId="sprite",this.batched=!0,this._visualBounds={minX:0,maxX:1,minY:0,maxY:0},this._anchor=new le({_onUpdate:()=>{this.onViewUpdate()}}),r?this.anchor=r:t.defaultAnchor&&(this.anchor=t.defaultAnchor),this.texture=t,this.allowChildren=!1,this.roundPixels=i??!1,n!==void 0&&(this.width=n),o!==void 0&&(this.height=o)}static from(e,t=!1){return e instanceof A?new ct(e):new ct(A.from(e,t))}set texture(e){e||(e=A.EMPTY);const t=this._texture;t!==e&&(t&&t.dynamic&&t.off("update",this.onViewUpdate,this),e.dynamic&&e.on("update",this.onViewUpdate,this),this._texture=e,this._width&&this._setWidth(this._width,this._texture.orig.width),this._height&&this._setHeight(this._height,this._texture.orig.height),this.onViewUpdate())}get texture(){return this._texture}get visualBounds(){return Fo(this._visualBounds,this._anchor,this._texture),this._visualBounds}get sourceBounds(){return I("8.6.1","Sprite.sourceBounds is deprecated, use visualBounds instead."),this.visualBounds}updateBounds(){const e=this._anchor,t=this._texture,r=this._bounds,{width:i,height:n}=t.orig;r.minX=-e._x*i,r.maxX=r.minX+i,r.minY=-e._y*n,r.maxY=r.minY+n}destroy(e=!1){if(super.destroy(e),typeof e=="boolean"?e:e?.texture){const r=typeof e=="boolean"?e:e?.textureSource;this._texture.destroy(r)}this._texture=null,this._visualBounds=null,this._bounds=null,this._anchor=null,this._gpuData=null}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(e){this._setWidth(e,this._texture.orig.width),this._width=e}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(e){this._setHeight(e,this._texture.orig.height),this._height=e}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this._texture.orig.width,e.height=Math.abs(this.scale.y)*this._texture.orig.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this._texture.orig.width),t!==void 0&&this._setHeight(t,this._texture.orig.height)}}const xc=new se;function Xo(s,e,t){const r=xc;s.measurable=!0,ei(s,t,r),e.addBoundsMask(r),s.measurable=!1}let jr=0;const tn=500;function F(...s){jr!==tn&&(jr++,jr===tn?console.warn("PixiJS Warning: too many warnings, no more warnings will be reported to the console by PixiJS."):console.warn("PixiJS Warning: ",...s))}function Yo(s,e,t){const r=Ae.get();s.measurable=!0;const i=ie.get().identity(),n=jo(s,t,i);ri(s,r,n),s.measurable=!1,e.addBoundsMask(r),ie.return(i),Ae.return(r)}function jo(s,e,t){return s?(s!==e&&(jo(s.parent,e,t),s.updateLocalTransform(),t.append(s.localTransform)),t):(F("Mask bounds, renderable is not inside the root container"),t)}class qo{constructor(e){this.priority=0,this.inverse=!1,this.pipe="alphaMask",e?.mask&&this.init(e.mask)}init(e){this.mask=e,this.renderMaskToTexture=!(e instanceof ct),this.mask.renderable=this.renderMaskToTexture,this.mask.includeInBuild=!this.renderMaskToTexture,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask=null}addBounds(e,t){this.inverse||Xo(this.mask,e,t)}addLocalBounds(e,t){Yo(this.mask,e,t)}containsPoint(e,t){const r=this.mask;return t(r,e)}destroy(){this.reset()}static test(e){return e instanceof ct}}qo.extension=x.MaskEffect;class Ko{constructor(e){this.priority=0,this.pipe="colorMask",e?.mask&&this.init(e.mask)}init(e){this.mask=e}destroy(){}static test(e){return typeof e=="number"}}Ko.extension=x.MaskEffect;class Zo{constructor(e){this.priority=0,this.pipe="stencilMask",e?.mask&&this.init(e.mask)}init(e){this.mask=e,this.mask.includeInBuild=!1,this.mask.measurable=!1}reset(){this.mask.measurable=!0,this.mask.includeInBuild=!0,this.mask=null}addBounds(e,t){Xo(this.mask,e,t)}addLocalBounds(e,t){Yo(this.mask,e,t)}containsPoint(e,t){const r=this.mask;return t(r,e)}destroy(){this.reset()}static test(e){return e instanceof xe}}Zo.extension=x.MaskEffect;const yc={createCanvas:(s,e)=>{const t=document.createElement("canvas");return t.width=s,t.height=e,t},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>document.baseURI??window.location.href,getFontFaceSet:()=>document.fonts,fetch:(s,e)=>fetch(s,e),parseXML:s=>new DOMParser().parseFromString(s,"text/xml")};let rn=yc;const $={get(){return rn},set(s){rn=s}};class Be extends Q{constructor(e){e.resource||(e.resource=$.get().createCanvas()),e.width||(e.width=e.resource.width,e.autoDensity||(e.width/=e.resolution)),e.height||(e.height=e.resource.height,e.autoDensity||(e.height/=e.resolution)),super(e),this.uploadMethodId="image",this.autoDensity=e.autoDensity,this.resizeCanvas(),this.transparent=!!e.transparent}resizeCanvas(){this.autoDensity&&"style"in this.resource&&(this.resource.style.width=`${this.width}px`,this.resource.style.height=`${this.height}px`),(this.resource.width!==this.pixelWidth||this.resource.height!==this.pixelHeight)&&(this.resource.width=this.pixelWidth,this.resource.height=this.pixelHeight)}resize(e=this.width,t=this.height,r=this._resolution){const i=super.resize(e,t,r);return i&&this.resizeCanvas(),i}static test(e){return globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement||globalThis.OffscreenCanvas&&e instanceof OffscreenCanvas}get context2D(){return this._context2D||(this._context2D=this.resource.getContext("2d"))}}Be.extension=x.TextureSource;class Ut extends Q{constructor(e){super(e),this.uploadMethodId="image",this.autoGarbageCollect=!0}static test(e){return globalThis.HTMLImageElement&&e instanceof HTMLImageElement||typeof ImageBitmap<"u"&&e instanceof ImageBitmap||globalThis.VideoFrame&&e instanceof VideoFrame}}Ut.extension=x.TextureSource;var Dt=(s=>(s[s.INTERACTION=50]="INTERACTION",s[s.HIGH=25]="HIGH",s[s.NORMAL=0]="NORMAL",s[s.LOW=-25]="LOW",s[s.UTILITY=-50]="UTILITY",s))(Dt||{});class qr{constructor(e,t=null,r=0,i=!1){this.next=null,this.previous=null,this._destroyed=!1,this._fn=e,this._context=t,this.priority=r,this._once=i}match(e,t=null){return this._fn===e&&this._context===t}emit(e){this._fn&&(this._context?this._fn.call(this._context,e):this._fn(e));const t=this.next;return this._once&&this.destroy(!0),this._destroyed&&(this.next=null),t}connect(e){this.previous=e,e.next&&(e.next.previous=this),this.next=e.next,e.next=this}destroy(e=!1){this._destroyed=!0,this._fn=null,this._context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const t=this.next;return this.next=e?null:t,this.previous=null,t}}const Qo=class he{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new qr(null,null,1/0),this.deltaMS=1/he.targetFPMS,this.elapsedMS=1/he.targetFPMS,this._tick=e=>{this._requestId=null,this.started&&(this.update(e),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(e,t,r=Dt.NORMAL){return this._addListener(new qr(e,t,r))}addOnce(e,t,r=Dt.NORMAL){return this._addListener(new qr(e,t,r,!0))}_addListener(e){let t=this._head.next,r=this._head;if(!t)e.connect(r);else{for(;t;){if(e.priority>t.priority){e.connect(r);break}r=t,t=t.next}e.previous||e.connect(r)}return this._startIfPossible(),this}remove(e,t){let r=this._head.next;for(;r;)r.match(e,t)?r=r.destroy():r=r.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let e=0,t=this._head;for(;t=t.next;)e++;return e}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let e=this._head.next;for(;e;)e=e.destroy(!0);this._head.destroy(),this._head=null}}update(e=performance.now()){let t;if(e>this.lastTime){if(t=this.elapsedMS=e-this.lastTime,t>this._maxElapsedMS&&(t=this._maxElapsedMS),t*=this.speed,this._minElapsedMS){const n=e-this._lastFrame|0;if(n<this._minElapsedMS)return;this._lastFrame=e-n%this._minElapsedMS}this.deltaMS=t,this.deltaTime=this.deltaMS*he.targetFPMS;const r=this._head;let i=r.next;for(;i;)i=i.emit(this);r.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=e}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(e){const t=Math.min(this.maxFPS,e),r=Math.min(Math.max(0,t)/1e3,he.targetFPMS);this._maxElapsedMS=1/r}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(e){if(e===0)this._minElapsedMS=0;else{const t=Math.max(this.minFPS,e);this._minElapsedMS=1/(t/1e3)}}static get shared(){if(!he._shared){const e=he._shared=new he;e.autoStart=!0,e._protected=!0}return he._shared}static get system(){if(!he._system){const e=he._system=new he;e.autoStart=!0,e._protected=!0}return he._system}};Qo.targetFPMS=.06;let we=Qo,Kr;async function bc(){return Kr??(Kr=(async()=>{const e=document.createElement("canvas").getContext("webgl");if(!e)return"premultiply-alpha-on-upload";const t=await new Promise(o=>{const a=document.createElement("video");a.onloadeddata=()=>o(a),a.onerror=()=>o(null),a.autoplay=!1,a.crossOrigin="anonymous",a.preload="auto",a.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",a.load()});if(!t)return"premultiply-alpha-on-upload";const r=e.createTexture();e.bindTexture(e.TEXTURE_2D,r);const i=e.createFramebuffer();e.bindFramebuffer(e.FRAMEBUFFER,i),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,r,0),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.pixelStorei(e.UNPACK_COLORSPACE_CONVERSION_WEBGL,e.NONE),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,t);const n=new Uint8Array(4);return e.readPixels(0,0,1,1,e.RGBA,e.UNSIGNED_BYTE,n),e.deleteFramebuffer(i),e.deleteTexture(r),e.getExtension("WEBGL_lose_context")?.loseContext(),n[0]<=n[3]?"premultiplied-alpha":"premultiply-alpha-on-upload"})()),Kr}const Ir=class Jo extends Q{constructor(e){super(e),this.isReady=!1,this.uploadMethodId="video",e={...Jo.defaultOptions,...e},this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=e.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=e.autoPlay!==!1,this.alphaMode=e.alphaMode??"premultiply-alpha-on-upload",this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onCanPlayThrough=this._onCanPlayThrough.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),e.autoLoad!==!1&&this.load()}updateFrame(){if(!this.destroyed){if(this._updateFPS){const e=we.shared.elapsedMS*this.resource.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-e)}(!this._updateFPS||this._msToNextUpdate<=0)&&(this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0),this.isValid&&this.update()}}_videoFrameRequestCallback(){this.updateFrame(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback)}get isValid(){return!!this.resource.videoWidth&&!!this.resource.videoHeight}async load(){if(this._load)return this._load;const e=this.resource,t=this.options;return(e.readyState===e.HAVE_ENOUGH_DATA||e.readyState===e.HAVE_FUTURE_DATA)&&e.width&&e.height&&(e.complete=!0),e.addEventListener("play",this._onPlayStart),e.addEventListener("pause",this._onPlayStop),e.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._mediaReady():(t.preload||e.addEventListener("canplay",this._onCanPlay),e.addEventListener("canplaythrough",this._onCanPlayThrough),e.addEventListener("error",this._onError,!0)),this.alphaMode=await bc(),this._load=new Promise((r,i)=>{this.isValid?r(this):(this._resolve=r,this._reject=i,t.preloadTimeoutMs!==void 0&&(this._preloadTimeout=setTimeout(()=>{this._onError(new ErrorEvent(`Preload exceeded timeout of ${t.preloadTimeoutMs}ms`))})),e.load())}),this._load}_onError(e){this.resource.removeEventListener("error",this._onError,!0),this.emit("error",e),this._reject&&(this._reject(e),this._reject=null,this._resolve=null)}_isSourcePlaying(){const e=this.resource;return!e.paused&&!e.ended}_isSourceReady(){return this.resource.readyState>2}_onPlayStart(){this.isValid||this._mediaReady(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0)}_onCanPlay(){this.resource.removeEventListener("canplay",this._onCanPlay),this._mediaReady()}_onCanPlayThrough(){this.resource.removeEventListener("canplaythrough",this._onCanPlay),this._preloadTimeout&&(clearTimeout(this._preloadTimeout),this._preloadTimeout=void 0),this._mediaReady()}_mediaReady(){const e=this.resource;this.isValid&&(this.isReady=!0,this.resize(e.videoWidth,e.videoHeight)),this._msToNextUpdate=0,this.updateFrame(),this._msToNextUpdate=0,this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&this.resource.play()}destroy(){this._configureAutoUpdate();const e=this.resource;e&&(e.removeEventListener("play",this._onPlayStart),e.removeEventListener("pause",this._onPlayStop),e.removeEventListener("seeked",this._onSeeked),e.removeEventListener("canplay",this._onCanPlay),e.removeEventListener("canplaythrough",this._onCanPlayThrough),e.removeEventListener("error",this._onError,!0),e.pause(),e.src="",e.load()),super.destroy()}get autoUpdate(){return this._autoUpdate}set autoUpdate(e){e!==this._autoUpdate&&(this._autoUpdate=e,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(e){e!==this._updateFPS&&(this._updateFPS=e,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.resource.requestVideoFrameCallback?(this._isConnectedToTicker&&(we.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.resource.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(we.shared.add(this.updateFrame,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.resource.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(we.shared.remove(this.updateFrame,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(e){return globalThis.HTMLVideoElement&&e instanceof HTMLVideoElement}};Ir.extension=x.TextureSource;Ir.defaultOptions={...Q.defaultOptions,autoLoad:!0,autoPlay:!0,updateFPS:0,crossorigin:!0,loop:!1,muted:!0,playsinline:!0,preload:!1};Ir.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let vc=Ir;const nt=(s,e,t=!1)=>(Array.isArray(s)||(s=[s]),e?s.map(r=>typeof r=="string"||t?e(r):r):s);class Tc{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(e){return this._cache.has(e)}get(e){const t=this._cache.get(e);return t||F(`[Assets] Asset id ${e} was not found in the Cache`),t}set(e,t){const r=nt(e);let i;for(let h=0;h<this.parsers.length;h++){const l=this.parsers[h];if(l.test(t)){i=l.getCacheableAssets(r,t);break}}const n=new Map(Object.entries(i||{}));i||r.forEach(h=>{n.set(h,t)});const o=[...n.keys()],a={cacheKeys:o,keys:r};r.forEach(h=>{this._cacheMap.set(h,a)}),o.forEach(h=>{const l=i?i[h]:t;this._cache.has(h)&&this._cache.get(h)!==l&&F("[Cache] already has key:",h),this._cache.set(h,n.get(h))})}remove(e){if(!this._cacheMap.has(e)){F(`[Assets] Asset id ${e} was not found in the Cache`);return}const t=this._cacheMap.get(e);t.cacheKeys.forEach(i=>{this._cache.delete(i)}),t.keys.forEach(i=>{this._cacheMap.delete(i)})}get parsers(){return this._parsers}}const re=new Tc,Rs=[];q.handleByList(x.TextureSource,Rs);function ea(s={}){const e=s&&s.resource,t=e?s.resource:s,r=e?s:{resource:s};for(let i=0;i<Rs.length;i++){const n=Rs[i];if(n.test(t))return new n(r)}throw new Error(`Could not find a source type for resource: ${r.resource}`)}function Sc(s={},e=!1){const t=s&&s.resource,r=t?s.resource:s,i=t?s:{resource:s};if(!e&&re.has(r))return re.get(r);const n=new A({source:ea(i)});return n.on("destroy",()=>{re.has(r)&&re.remove(r)}),e||re.set(r,n),n}function wc(s,e=!1){return typeof s=="string"?re.get(s):s instanceof Q?new A({source:s}):Sc(s,e)}A.from=wc;Q.from=ea;q.add(qo,Ko,Zo,vc,Ut,Be,Qs);var ta=(s=>(s[s.Low=0]="Low",s[s.Normal=1]="Normal",s[s.High=2]="High",s))(ta||{});function me(s){if(typeof s!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(s)}`)}function bt(s){return s.split("?")[0].split("#")[0]}function Pc(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Cc(s,e,t){return s.replace(new RegExp(Pc(e),"g"),t)}function Ec(s,e){let t="",r=0,i=-1,n=0,o=-1;for(let a=0;a<=s.length;++a){if(a<s.length)o=s.charCodeAt(a);else{if(o===47)break;o=47}if(o===47){if(!(i===a-1||n===1))if(i!==a-1&&n===2){if(t.length<2||r!==2||t.charCodeAt(t.length-1)!==46||t.charCodeAt(t.length-2)!==46){if(t.length>2){const h=t.lastIndexOf("/");if(h!==t.length-1){h===-1?(t="",r=0):(t=t.slice(0,h),r=t.length-1-t.lastIndexOf("/")),i=a,n=0;continue}}else if(t.length===2||t.length===1){t="",r=0,i=a,n=0;continue}}}else t.length>0?t+=`/${s.slice(i+1,a)}`:t=s.slice(i+1,a),r=a-i-1;i=a,n=0}else o===46&&n!==-1?++n:n=-1}return t}const Ot={toPosix(s){return Cc(s,"\\","/")},isUrl(s){return/^https?:/.test(this.toPosix(s))},isDataUrl(s){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(s)},isBlobUrl(s){return s.startsWith("blob:")},hasProtocol(s){return/^[^/:]+:/.test(this.toPosix(s))},getProtocol(s){me(s),s=this.toPosix(s);const e=/^file:\/\/\//.exec(s);if(e)return e[0];const t=/^[^/:]+:\/{0,2}/.exec(s);return t?t[0]:""},toAbsolute(s,e,t){if(me(s),this.isDataUrl(s)||this.isBlobUrl(s))return s;const r=bt(this.toPosix(e??$.get().getBaseUrl())),i=bt(this.toPosix(t??this.rootname(r)));return s=this.toPosix(s),s.startsWith("/")?Ot.join(i,s.slice(1)):this.isAbsolute(s)?s:this.join(r,s)},normalize(s){if(me(s),s.length===0)return".";if(this.isDataUrl(s)||this.isBlobUrl(s))return s;s=this.toPosix(s);let e="";const t=s.startsWith("/");this.hasProtocol(s)&&(e=this.rootname(s),s=s.slice(e.length));const r=s.endsWith("/");return s=Ec(s),s.length>0&&r&&(s+="/"),t?`/${s}`:e+s},isAbsolute(s){return me(s),s=this.toPosix(s),this.hasProtocol(s)?!0:s.startsWith("/")},join(...s){if(s.length===0)return".";let e;for(let t=0;t<s.length;++t){const r=s[t];if(me(r),r.length>0)if(e===void 0)e=r;else{const i=s[t-1]??"";this.joinExtensions.includes(this.extname(i).toLowerCase())?e+=`/../${r}`:e+=`/${r}`}}return e===void 0?".":this.normalize(e)},dirname(s){if(me(s),s.length===0)return".";s=this.toPosix(s);let e=s.charCodeAt(0);const t=e===47;let r=-1,i=!0;const n=this.getProtocol(s),o=s;s=s.slice(n.length);for(let a=s.length-1;a>=1;--a)if(e=s.charCodeAt(a),e===47){if(!i){r=a;break}}else i=!1;return r===-1?t?"/":this.isUrl(o)?n+s:n:t&&r===1?"//":n+s.slice(0,r)},rootname(s){me(s),s=this.toPosix(s);let e="";if(s.startsWith("/")?e="/":e=this.getProtocol(s),this.isUrl(s)){const t=s.indexOf("/",e.length);t!==-1?e=s.slice(0,t):e=s,e.endsWith("/")||(e+="/")}return e},basename(s,e){me(s),e&&me(e),s=bt(this.toPosix(s));let t=0,r=-1,i=!0,n;if(e!==void 0&&e.length>0&&e.length<=s.length){if(e.length===s.length&&e===s)return"";let o=e.length-1,a=-1;for(n=s.length-1;n>=0;--n){const h=s.charCodeAt(n);if(h===47){if(!i){t=n+1;break}}else a===-1&&(i=!1,a=n+1),o>=0&&(h===e.charCodeAt(o)?--o===-1&&(r=n):(o=-1,r=a))}return t===r?r=a:r===-1&&(r=s.length),s.slice(t,r)}for(n=s.length-1;n>=0;--n)if(s.charCodeAt(n)===47){if(!i){t=n+1;break}}else r===-1&&(i=!1,r=n+1);return r===-1?"":s.slice(t,r)},extname(s){me(s),s=bt(this.toPosix(s));let e=-1,t=0,r=-1,i=!0,n=0;for(let o=s.length-1;o>=0;--o){const a=s.charCodeAt(o);if(a===47){if(!i){t=o+1;break}continue}r===-1&&(i=!1,r=o+1),a===46?e===-1?e=o:n!==1&&(n=1):e!==-1&&(n=-1)}return e===-1||r===-1||n===0||n===1&&e===r-1&&e===t+1?"":s.slice(e,r)},parse(s){me(s);const e={root:"",dir:"",base:"",ext:"",name:""};if(s.length===0)return e;s=bt(this.toPosix(s));let t=s.charCodeAt(0);const r=this.isAbsolute(s);let i;e.root=this.rootname(s),r||this.hasProtocol(s)?i=1:i=0;let n=-1,o=0,a=-1,h=!0,l=s.length-1,c=0;for(;l>=i;--l){if(t=s.charCodeAt(l),t===47){if(!h){o=l+1;break}continue}a===-1&&(h=!1,a=l+1),t===46?n===-1?n=l:c!==1&&(c=1):n!==-1&&(c=-1)}return n===-1||a===-1||c===0||c===1&&n===a-1&&n===o+1?a!==-1&&(o===0&&r?e.base=e.name=s.slice(1,a):e.base=e.name=s.slice(o,a)):(o===0&&r?(e.name=s.slice(1,n),e.base=s.slice(1,a)):(e.name=s.slice(o,n),e.base=s.slice(o,a)),e.ext=s.slice(n,a)),e.dir=this.dirname(s),e},sep:"/",delimiter:":",joinExtensions:[".html"]};function ra(s,e,t,r,i){const n=e[t];for(let o=0;o<n.length;o++){const a=n[o];t<e.length-1?ra(s.replace(r[t],a),e,t+1,r,i):i.push(s.replace(r[t],a))}}function Mc(s){const e=/\{(.*?)\}/g,t=s.match(e),r=[];if(t){const i=[];t.forEach(n=>{const o=n.substring(1,n.length-1).split(",");i.push(o)}),ra(s,i,0,t,r)}else r.push(s);return r}const sn=s=>!Array.isArray(s);class sa{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(e,t)=>`${e}${this._bundleIdConnector}${t}`,extractAssetIdFromBundle:(e,t)=>t.replace(`${e}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(e){if(this._bundleIdConnector=e.connector??this._bundleIdConnector,this._createBundleAssetId=e.createBundleAssetId??this._createBundleAssetId,this._extractAssetIdFromBundle=e.extractAssetIdFromBundle??this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...e){e.forEach(t=>{this._preferredOrder.push(t),t.priority||(t.priority=Object.keys(t.params))}),this._resolverHash={}}set basePath(e){this._basePath=e}get basePath(){return this._basePath}set rootPath(e){this._rootPath=e}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(e){if(typeof e=="string")this._defaultSearchParams=e;else{const t=e;this._defaultSearchParams=Object.keys(t).map(r=>`${encodeURIComponent(r)}=${encodeURIComponent(t[r])}`).join("&")}}getAlias(e){const{alias:t,src:r}=e;return nt(t||r,n=>typeof n=="string"?n:Array.isArray(n)?n.map(o=>o?.src??o):n?.src?n.src:n,!0)}addManifest(e){this._manifest&&F("[Resolver] Manifest already exists, this will be overwritten"),this._manifest=e,e.bundles.forEach(t=>{this.addBundle(t.name,t.assets)})}addBundle(e,t){const r=[];let i=t;Array.isArray(t)||(i=Object.entries(t).map(([n,o])=>typeof o=="string"||Array.isArray(o)?{alias:n,src:o}:{alias:n,...o})),i.forEach(n=>{const o=n.src,a=n.alias;let h;if(typeof a=="string"){const l=this._createBundleAssetId(e,a);r.push(l),h=[a,l]}else{const l=a.map(c=>this._createBundleAssetId(e,c));r.push(...l),h=[...a,...l]}this.add({...n,alias:h,src:o})}),this._bundles[e]=r}add(e){const t=[];Array.isArray(e)?t.push(...e):t.push(e);let r;r=n=>{this.hasKey(n)&&F(`[Resolver] already has key: ${n} overwriting`)},nt(t).forEach(n=>{const{src:o}=n;let{data:a,format:h,loadParser:l}=n;const c=nt(o).map(d=>typeof d=="string"?Mc(d):Array.isArray(d)?d:[d]),u=this.getAlias(n);Array.isArray(u)?u.forEach(r):r(u);const f=[];c.forEach(d=>{d.forEach(p=>{let g={};if(typeof p!="object"){g.src=p;for(let m=0;m<this._parsers.length;m++){const _=this._parsers[m];if(_.test(p)){g=_.parse(p);break}}}else a=p.data??a,h=p.format??h,l=p.loadParser??l,g={...g,...p};if(!u)throw new Error(`[Resolver] alias is undefined for this asset: ${g.src}`);g=this._buildResolvedAsset(g,{aliases:u,data:a,format:h,loadParser:l}),f.push(g)})}),u.forEach(d=>{this._assetMap[d]=f})})}resolveBundle(e){const t=sn(e);e=nt(e);const r={};return e.forEach(i=>{const n=this._bundles[i];if(n){const o=this.resolve(n),a={};for(const h in o){const l=o[h];a[this._extractAssetIdFromBundle(i,h)]=l}r[i]=a}}),t?r[e[0]]:r}resolveUrl(e){const t=this.resolve(e);if(typeof e!="string"){const r={};for(const i in t)r[i]=t[i].src;return r}return t.src}resolve(e){const t=sn(e);e=nt(e);const r={};return e.forEach(i=>{if(!this._resolverHash[i])if(this._assetMap[i]){let n=this._assetMap[i];const o=this._getPreferredOrder(n);o?.priority.forEach(a=>{o.params[a].forEach(h=>{const l=n.filter(c=>c[a]?c[a]===h:!1);l.length&&(n=l)})}),this._resolverHash[i]=n[0]}else this._resolverHash[i]=this._buildResolvedAsset({alias:[i],src:i},{});r[i]=this._resolverHash[i]}),t?r[e[0]]:r}hasKey(e){return!!this._assetMap[e]}hasBundle(e){return!!this._bundles[e]}_getPreferredOrder(e){for(let t=0;t<e.length;t++){const r=e[t],i=this._preferredOrder.find(n=>n.params.format.includes(r.format));if(i)return i}return this._preferredOrder[0]}_appendDefaultSearchParams(e){if(!this._defaultSearchParams)return e;const t=/\?/.test(e)?"&":"?";return`${e}${t}${this._defaultSearchParams}`}_buildResolvedAsset(e,t){const{aliases:r,data:i,loadParser:n,format:o}=t;return(this._basePath||this._rootPath)&&(e.src=Ot.toAbsolute(e.src,this._basePath,this._rootPath)),e.alias=r??e.alias??[e.src],e.src=this._appendDefaultSearchParams(e.src),e.data={...i||{},...e.data},e.loadParser=n??e.loadParser,e.format=o??e.format??Ac(e.src),e}}sa.RETINA_PREFIX=/@([0-9\.]+)x/;function Ac(s){return s.split(".").pop().split("?").shift().split("#").shift()}const nn=(s,e)=>{const t=e.split("?")[1];return t&&(s+=`?${t}`),s},ia=class Et{constructor(e,t){this.linkedSheets=[];let r=e;e?.source instanceof Q&&(r={texture:e,data:t});const{texture:i,data:n,cachePrefix:o=""}=r;this.cachePrefix=o,this._texture=i instanceof A?i:null,this.textureSource=i.source,this.textures={},this.animations={},this.data=n;const a=parseFloat(n.meta.scale);a?(this.resolution=a,i.source.resolution=this.resolution):this.resolution=i.source._resolution,this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}parse(){return new Promise(e=>{this._callback=e,this._batchIndex=0,this._frameKeys.length<=Et.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(e){let t=e;const r=Et.BATCH_SIZE;for(;t-e<r&&t<this._frameKeys.length;){const i=this._frameKeys[t],n=this._frames[i],o=n.frame;if(o){let a=null,h=null;const l=n.trimmed!==!1&&n.sourceSize?n.sourceSize:n.frame,c=new N(0,0,Math.floor(l.w)/this.resolution,Math.floor(l.h)/this.resolution);n.rotated?a=new N(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.h)/this.resolution,Math.floor(o.w)/this.resolution):a=new N(Math.floor(o.x)/this.resolution,Math.floor(o.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution),n.trimmed!==!1&&n.spriteSourceSize&&(h=new N(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(o.w)/this.resolution,Math.floor(o.h)/this.resolution)),this.textures[i]=new A({source:this.textureSource,frame:a,orig:c,trim:h,rotate:n.rotated?2:0,defaultAnchor:n.anchor,defaultBorders:n.borders,label:i.toString()})}t++}}_processAnimations(){const e=this.data.animations||{};for(const t in e){this.animations[t]=[];for(let r=0;r<e[t].length;r++){const i=e[t][r];this.animations[t].push(this.textures[i])}}}_parseComplete(){const e=this._callback;this._callback=null,this._batchIndex=0,e.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*Et.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*Et.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(e=!1){for(const t in this.textures)this.textures[t].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,e&&(this._texture?.destroy(),this.textureSource.destroy()),this._texture=null,this.textureSource=null,this.linkedSheets=[]}};ia.BATCH_SIZE=1e3;let on=ia;const Bc=["jpg","png","jpeg","avif","webp","basis","etc2","bc7","bc6h","bc5","bc4","bc3","bc2","bc1","eac","astc"];function na(s,e,t){const r={};if(s.forEach(i=>{r[i]=e}),Object.keys(e.textures).forEach(i=>{r[`${e.cachePrefix}${i}`]=e.textures[i]}),!t){const i=Ot.dirname(s[0]);e.linkedSheets.forEach((n,o)=>{const a=na([`${i}/${e.data.meta.related_multi_packs[o]}`],n,!0);Object.assign(r,a)})}return r}const Rc={extension:x.Asset,cache:{test:s=>s instanceof on,getCacheableAssets:(s,e)=>na(s,e,!1)},resolver:{extension:{type:x.ResolveParser,name:"resolveSpritesheet"},test:s=>{const t=s.split("?")[0].split("."),r=t.pop(),i=t.pop();return r==="json"&&Bc.includes(i)},parse:s=>{const e=s.split(".");return{resolution:parseFloat(sa.RETINA_PREFIX.exec(s)?.[1]??"1"),format:e[e.length-2],src:s}}},loader:{name:"spritesheetLoader",extension:{type:x.LoadParser,priority:ta.Normal,name:"spritesheetLoader"},async testParse(s,e){return Ot.extname(e.src).toLowerCase()===".json"&&!!s.frames},async parse(s,e,t){const{texture:r,imageFilename:i,textureOptions:n,cachePrefix:o}=e?.data??{};let a=Ot.dirname(e.src);a&&a.lastIndexOf("/")!==a.length-1&&(a+="/");let h;if(r instanceof A)h=r;else{const u=nn(a+(i??s.meta.image),e.src);h=(await t.load([{src:u,data:n}]))[u]}const l=new on({texture:h.source,data:s,cachePrefix:o});await l.parse();const c=s?.meta?.related_multi_packs;if(Array.isArray(c)){const u=[];for(const d of c){if(typeof d!="string")continue;let p=a+d;e.data?.ignoreMultiPack||(p=nn(p,e.src),u.push(t.load({src:p,data:{textureOptions:n,ignoreMultiPack:!0}})))}const f=await Promise.all(u);l.linkedSheets=f,f.forEach(d=>{d.linkedSheets=[l].concat(l.linkedSheets.filter(p=>p!==d))})}return l},async unload(s,e,t){await t.unload(s.textureSource._sourceOrigin),s.destroy(!1)}}};q.add(Rc);class $t{constructor(e){this.bubbles=!0,this.cancelBubble=!0,this.cancelable=!1,this.composed=!1,this.defaultPrevented=!1,this.eventPhase=$t.prototype.NONE,this.propagationStopped=!1,this.propagationImmediatelyStopped=!1,this.layer=new H,this.page=new H,this.NONE=0,this.CAPTURING_PHASE=1,this.AT_TARGET=2,this.BUBBLING_PHASE=3,this.manager=e}get layerX(){return this.layer.x}get layerY(){return this.layer.y}get pageX(){return this.page.x}get pageY(){return this.page.y}get data(){return this}composedPath(){return this.manager&&(!this.path||this.path[this.path.length-1]!==this.target)&&(this.path=this.target?this.manager.propagationPath(this.target):[]),this.path}initEvent(e,t,r){throw new Error("initEvent() is a legacy DOM API. It is not implemented in the Federated Events API.")}initUIEvent(e,t,r,i,n){throw new Error("initUIEvent() is a legacy DOM API. It is not implemented in the Federated Events API.")}preventDefault(){this.nativeEvent instanceof Event&&this.nativeEvent.cancelable&&this.nativeEvent.preventDefault(),this.defaultPrevented=!0}stopImmediatePropagation(){this.propagationImmediatelyStopped=!0}stopPropagation(){this.propagationStopped=!0}}var Zr=/iPhone/i,an=/iPod/i,hn=/iPad/i,ln=/\biOS-universal(?:.+)Mac\b/i,Qr=/\bAndroid(?:.+)Mobile\b/i,cn=/Android/i,rt=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,nr=/Silk/i,Ee=/Windows Phone/i,un=/\bWindows(?:.+)ARM\b/i,dn=/BlackBerry/i,fn=/BB10/i,pn=/Opera Mini/i,mn=/\b(CriOS|Chrome)(?:.+)Mobile/i,gn=/Mobile(?:.+)Firefox\b/i,_n=function(s){return typeof s<"u"&&s.platform==="MacIntel"&&typeof s.maxTouchPoints=="number"&&s.maxTouchPoints>1&&typeof MSStream>"u"};function kc(s){return function(e){return e.test(s)}}function xn(s){var e={userAgent:"",platform:"",maxTouchPoints:0};!s&&typeof navigator<"u"?e={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof s=="string"?e.userAgent=s:s&&s.userAgent&&(e={userAgent:s.userAgent,platform:s.platform,maxTouchPoints:s.maxTouchPoints||0});var t=e.userAgent,r=t.split("[FBAN");typeof r[1]<"u"&&(t=r[0]),r=t.split("Twitter"),typeof r[1]<"u"&&(t=r[0]);var i=kc(t),n={apple:{phone:i(Zr)&&!i(Ee),ipod:i(an),tablet:!i(Zr)&&(i(hn)||_n(e))&&!i(Ee),universal:i(ln),device:(i(Zr)||i(an)||i(hn)||i(ln)||_n(e))&&!i(Ee)},amazon:{phone:i(rt),tablet:!i(rt)&&i(nr),device:i(rt)||i(nr)},android:{phone:!i(Ee)&&i(rt)||!i(Ee)&&i(Qr),tablet:!i(Ee)&&!i(rt)&&!i(Qr)&&(i(nr)||i(cn)),device:!i(Ee)&&(i(rt)||i(nr)||i(Qr)||i(cn))||i(/\bokhttp\b/i)},windows:{phone:i(Ee),tablet:i(un),device:i(Ee)||i(un)},other:{blackberry:i(dn),blackberry10:i(fn),opera:i(pn),firefox:i(gn),chrome:i(mn),device:i(dn)||i(fn)||i(pn)||i(gn)||i(mn)},any:!1,phone:!1,tablet:!1};return n.any=n.apple.device||n.android.device||n.windows.device||n.other.device,n.phone=n.apple.phone||n.android.phone||n.windows.phone,n.tablet=n.apple.tablet||n.android.tablet||n.windows.tablet,n}const Gc=xn.default??xn,Ic=Gc(globalThis.navigator),Fc=9,or=100,Uc=0,Dc=0,yn=2,bn=1,Oc=-1e3,Lc=-1e3,Nc=2,ii=class oa{constructor(e,t=Ic){this._mobileInfo=t,this.debug=!1,this._activateOnTab=!0,this._deactivateOnMouseMove=!0,this._isActive=!1,this._isMobileAccessibility=!1,this._div=null,this._pool=[],this._renderId=0,this._children=[],this._androidUpdateCount=0,this._androidUpdateFrequency=500,this._hookDiv=null,(t.tablet||t.phone)&&this._createTouchHook(),this._renderer=e}get isActive(){return this._isActive}get isMobileAccessibility(){return this._isMobileAccessibility}get hookDiv(){return this._hookDiv}_createTouchHook(){const e=document.createElement("button");e.style.width=`${bn}px`,e.style.height=`${bn}px`,e.style.position="absolute",e.style.top=`${Oc}px`,e.style.left=`${Lc}px`,e.style.zIndex=Nc.toString(),e.style.backgroundColor="#FF0000",e.title="select to enable accessibility for this content",e.addEventListener("focus",()=>{this._isMobileAccessibility=!0,this._activate(),this._destroyTouchHook()}),document.body.appendChild(e),this._hookDiv=e}_destroyTouchHook(){this._hookDiv&&(document.body.removeChild(this._hookDiv),this._hookDiv=null)}_activate(){if(this._isActive)return;this._isActive=!0,this._div||(this._div=document.createElement("div"),this._div.style.width=`${or}px`,this._div.style.height=`${or}px`,this._div.style.position="absolute",this._div.style.top=`${Uc}px`,this._div.style.left=`${Dc}px`,this._div.style.zIndex=yn.toString(),this._div.style.pointerEvents="none"),this._activateOnTab&&(this._onKeyDown=this._onKeyDown.bind(this),globalThis.addEventListener("keydown",this._onKeyDown,!1)),this._deactivateOnMouseMove&&(this._onMouseMove=this._onMouseMove.bind(this),globalThis.document.addEventListener("mousemove",this._onMouseMove,!0));const e=this._renderer.view.canvas;if(e.parentNode)e.parentNode.appendChild(this._div),this._initAccessibilitySetup();else{const t=new MutationObserver(()=>{e.parentNode&&(e.parentNode.appendChild(this._div),t.disconnect(),this._initAccessibilitySetup())});t.observe(document.body,{childList:!0,subtree:!0})}}_initAccessibilitySetup(){this._renderer.runners.postrender.add(this),this._renderer.lastObjectRendered&&this._updateAccessibleObjects(this._renderer.lastObjectRendered)}_deactivate(){if(!(!this._isActive||this._isMobileAccessibility)){this._isActive=!1,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),this._activateOnTab&&globalThis.addEventListener("keydown",this._onKeyDown,!1),this._renderer.runners.postrender.remove(this);for(const e of this._children)e._accessibleDiv&&e._accessibleDiv.parentNode&&(e._accessibleDiv.parentNode.removeChild(e._accessibleDiv),e._accessibleDiv=null),e._accessibleActive=!1;this._pool.forEach(e=>{e.parentNode&&e.parentNode.removeChild(e)}),this._div&&this._div.parentNode&&this._div.parentNode.removeChild(this._div),this._pool=[],this._children=[]}}_updateAccessibleObjects(e){if(!e.visible||!e.accessibleChildren)return;e.accessible&&(e._accessibleActive||this._addChild(e),e._renderId=this._renderId);const t=e.children;if(t)for(let r=0;r<t.length;r++)this._updateAccessibleObjects(t[r])}init(e){const r={accessibilityOptions:{...oa.defaultOptions,...e?.accessibilityOptions||{}}};this.debug=r.accessibilityOptions.debug,this._activateOnTab=r.accessibilityOptions.activateOnTab,this._deactivateOnMouseMove=r.accessibilityOptions.deactivateOnMouseMove,r.accessibilityOptions.enabledByDefault?this._activate():this._activateOnTab&&(this._onKeyDown=this._onKeyDown.bind(this),globalThis.addEventListener("keydown",this._onKeyDown,!1)),this._renderer.runners.postrender.remove(this)}postrender(){const e=performance.now();if(this._mobileInfo.android.device&&e<this._androidUpdateCount||(this._androidUpdateCount=e+this._androidUpdateFrequency,!this._renderer.renderingToScreen||!this._renderer.view.canvas))return;const t=new Set;if(this._renderer.lastObjectRendered){this._updateAccessibleObjects(this._renderer.lastObjectRendered);for(const r of this._children)r._renderId===this._renderId&&t.add(this._children.indexOf(r))}for(let r=this._children.length-1;r>=0;r--){const i=this._children[r];t.has(r)||(i._accessibleDiv&&i._accessibleDiv.parentNode&&(i._accessibleDiv.parentNode.removeChild(i._accessibleDiv),this._pool.push(i._accessibleDiv),i._accessibleDiv=null),i._accessibleActive=!1,Lo(this._children,r,1))}if(this._renderer.renderingToScreen){const{x:r,y:i,width:n,height:o}=this._renderer.screen,a=this._div;a.style.left=`${r}px`,a.style.top=`${i}px`,a.style.width=`${n}px`,a.style.height=`${o}px`}for(let r=0;r<this._children.length;r++){const i=this._children[r];if(!i._accessibleActive||!i._accessibleDiv)continue;const n=i._accessibleDiv,o=i.hitArea||i.getBounds().rectangle;if(i.hitArea){const a=i.worldTransform,h=this._renderer.resolution,l=this._renderer.resolution;n.style.left=`${(a.tx+o.x*a.a)*h}px`,n.style.top=`${(a.ty+o.y*a.d)*l}px`,n.style.width=`${o.width*a.a*h}px`,n.style.height=`${o.height*a.d*l}px`}else{this._capHitArea(o);const a=this._renderer.resolution,h=this._renderer.resolution;n.style.left=`${o.x*a}px`,n.style.top=`${o.y*h}px`,n.style.width=`${o.width*a}px`,n.style.height=`${o.height*h}px`}}this._renderId++}_updateDebugHTML(e){e.innerHTML=`type: ${e.type}</br> title : ${e.title}</br> tabIndex: ${e.tabIndex}`}_capHitArea(e){e.x<0&&(e.width+=e.x,e.x=0),e.y<0&&(e.height+=e.y,e.y=0);const{width:t,height:r}=this._renderer;e.x+e.width>t&&(e.width=t-e.x),e.y+e.height>r&&(e.height=r-e.y)}_addChild(e){let t=this._pool.pop();t||(e.accessibleType==="button"?t=document.createElement("button"):(t=document.createElement(e.accessibleType),t.style.cssText=`
                        color: transparent;
                        pointer-events: none;
                        padding: 0;
                        margin: 0;
                        border: 0;
                        outline: 0;
                        background: transparent;
                        box-sizing: border-box;
                        user-select: none;
                        -webkit-user-select: none;
                        -moz-user-select: none;
                        -ms-user-select: none;
                    `,e.accessibleText&&(t.innerText=e.accessibleText)),t.style.width=`${or}px`,t.style.height=`${or}px`,t.style.backgroundColor=this.debug?"rgba(255,255,255,0.5)":"transparent",t.style.position="absolute",t.style.zIndex=yn.toString(),t.style.borderStyle="none",navigator.userAgent.toLowerCase().includes("chrome")?t.setAttribute("aria-live","off"):t.setAttribute("aria-live","polite"),navigator.userAgent.match(/rv:.*Gecko\//)?t.setAttribute("aria-relevant","additions"):t.setAttribute("aria-relevant","text"),t.addEventListener("click",this._onClick.bind(this)),t.addEventListener("focus",this._onFocus.bind(this)),t.addEventListener("focusout",this._onFocusOut.bind(this))),t.style.pointerEvents=e.accessiblePointerEvents,t.type=e.accessibleType,e.accessibleTitle&&e.accessibleTitle!==null?t.title=e.accessibleTitle:(!e.accessibleHint||e.accessibleHint===null)&&(t.title=`container ${e.tabIndex}`),e.accessibleHint&&e.accessibleHint!==null&&t.setAttribute("aria-label",e.accessibleHint),this.debug&&this._updateDebugHTML(t),e._accessibleActive=!0,e._accessibleDiv=t,t.container=e,this._children.push(e),this._div.appendChild(e._accessibleDiv),e.interactive&&(e._accessibleDiv.tabIndex=e.tabIndex)}_dispatchEvent(e,t){const{container:r}=e.target,i=this._renderer.events.rootBoundary,n=Object.assign(new $t(i),{target:r});i.rootTarget=this._renderer.lastObjectRendered,t.forEach(o=>i.dispatchEvent(n,o))}_onClick(e){this._dispatchEvent(e,["click","pointertap","tap"])}_onFocus(e){e.target.getAttribute("aria-live")||e.target.setAttribute("aria-live","assertive"),this._dispatchEvent(e,["mouseover"])}_onFocusOut(e){e.target.getAttribute("aria-live")||e.target.setAttribute("aria-live","polite"),this._dispatchEvent(e,["mouseout"])}_onKeyDown(e){e.keyCode!==Fc||!this._activateOnTab||this._activate()}_onMouseMove(e){e.movementX===0&&e.movementY===0||this._deactivate()}destroy(){this._deactivate(),this._destroyTouchHook(),this._div=null,this._pool=null,this._children=null,this._renderer=null,this._activateOnTab&&globalThis.removeEventListener("keydown",this._onKeyDown)}setAccessibilityEnabled(e){e?this._activate():this._deactivate()}};ii.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"accessibility"};ii.defaultOptions={enabledByDefault:!1,debug:!1,activateOnTab:!0,deactivateOnMouseMove:!0};let Mg=ii;const Ag={accessible:!1,accessibleTitle:null,accessibleHint:null,tabIndex:0,accessibleType:"button",accessibleText:null,accessiblePointerEvents:"auto",accessibleChildren:!0,_accessibleActive:!1,_accessibleDiv:null,_renderId:-1},Jr=Object.create(null),vn=Object.create(null);function Lt(s,e){let t=vn[s];return t===void 0&&(Jr[e]===void 0&&(Jr[e]=1),vn[s]=t=Jr[e]++),t}let ar;function aa(){return(!ar||ar?.isContextLost())&&(ar=$.get().createCanvas().getContext("webgl",{})),ar}let hr;function Hc(){if(!hr){hr="mediump";const s=aa();s&&s.getShaderPrecisionFormat&&(hr=s.getShaderPrecisionFormat(s.FRAGMENT_SHADER,s.HIGH_FLOAT).precision?"highp":"mediump")}return hr}function zc(s,e,t){return e?s:t?(s=s.replace("out vec4 finalColor;",""),`

        #ifdef GL_ES // This checks if it is WebGL1
        #define in varying
        #define finalColor gl_FragColor
        #define texture texture2D
        #endif
        ${s}
        `):`

        #ifdef GL_ES // This checks if it is WebGL1
        #define in attribute
        #define out varying
        #endif
        ${s}
        `}function Wc(s,e,t){const r=t?e.maxSupportedFragmentPrecision:e.maxSupportedVertexPrecision;if(s.substring(0,9)!=="precision"){let i=t?e.requestedFragmentPrecision:e.requestedVertexPrecision;return i==="highp"&&r!=="highp"&&(i="mediump"),`precision ${i} float;
${s}`}else if(r!=="highp"&&s.substring(0,15)==="precision highp")return s.replace("precision highp","precision mediump");return s}function Vc(s,e){return e?`#version 300 es
${s}`:s}const $c={},Xc={};function Yc(s,{name:e="pixi-program"},t=!0){e=e.replace(/\s+/g,"-"),e+=t?"-fragment":"-vertex";const r=t?$c:Xc;return r[e]?(r[e]++,e+=`-${r[e]}`):r[e]=1,s.indexOf("#define SHADER_NAME")!==-1?s:`${`#define SHADER_NAME ${e}`}
${s}`}function jc(s,e){return e?s.replace("#version 300 es",""):s}const es={stripVersion:jc,ensurePrecision:Wc,addProgramDefines:zc,setProgramName:Yc,insertVersion:Vc},ts=Object.create(null),ha=class ks{constructor(e){e={...ks.defaultOptions,...e};const t=e.fragment.indexOf("#version 300 es")!==-1,r={stripVersion:t,ensurePrecision:{requestedFragmentPrecision:e.preferredFragmentPrecision,requestedVertexPrecision:e.preferredVertexPrecision,maxSupportedVertexPrecision:"highp",maxSupportedFragmentPrecision:Hc()},setProgramName:{name:e.name},addProgramDefines:t,insertVersion:t};let i=e.fragment,n=e.vertex;Object.keys(es).forEach(o=>{const a=r[o];i=es[o](i,a,!0),n=es[o](n,a,!1)}),this.fragment=i,this.vertex=n,this.transformFeedbackVaryings=e.transformFeedbackVaryings,this._key=Lt(`${this.vertex}:${this.fragment}`,"gl-program")}destroy(){this.fragment=null,this.vertex=null,this._attributeData=null,this._uniformData=null,this._uniformBlockData=null,this.transformFeedbackVaryings=null}static from(e){const t=`${e.vertex}:${e.fragment}`;return ts[t]||(ts[t]=new ks(e)),ts[t]}};ha.defaultOptions={preferredVertexPrecision:"highp",preferredFragmentPrecision:"mediump"};let pt=ha;const Tn={uint8x2:{size:2,stride:2,normalised:!1},uint8x4:{size:4,stride:4,normalised:!1},sint8x2:{size:2,stride:2,normalised:!1},sint8x4:{size:4,stride:4,normalised:!1},unorm8x2:{size:2,stride:2,normalised:!0},unorm8x4:{size:4,stride:4,normalised:!0},snorm8x2:{size:2,stride:2,normalised:!0},snorm8x4:{size:4,stride:4,normalised:!0},uint16x2:{size:2,stride:4,normalised:!1},uint16x4:{size:4,stride:8,normalised:!1},sint16x2:{size:2,stride:4,normalised:!1},sint16x4:{size:4,stride:8,normalised:!1},unorm16x2:{size:2,stride:4,normalised:!0},unorm16x4:{size:4,stride:8,normalised:!0},snorm16x2:{size:2,stride:4,normalised:!0},snorm16x4:{size:4,stride:8,normalised:!0},float16x2:{size:2,stride:4,normalised:!1},float16x4:{size:4,stride:8,normalised:!1},float32:{size:1,stride:4,normalised:!1},float32x2:{size:2,stride:8,normalised:!1},float32x3:{size:3,stride:12,normalised:!1},float32x4:{size:4,stride:16,normalised:!1},uint32:{size:1,stride:4,normalised:!1},uint32x2:{size:2,stride:8,normalised:!1},uint32x3:{size:3,stride:12,normalised:!1},uint32x4:{size:4,stride:16,normalised:!1},sint32:{size:1,stride:4,normalised:!1},sint32x2:{size:2,stride:8,normalised:!1},sint32x3:{size:3,stride:12,normalised:!1},sint32x4:{size:4,stride:16,normalised:!1}};function De(s){return Tn[s]??Tn.float32}const qc={f32:"float32","vec2<f32>":"float32x2","vec3<f32>":"float32x3","vec4<f32>":"float32x4",vec2f:"float32x2",vec3f:"float32x3",vec4f:"float32x4",i32:"sint32","vec2<i32>":"sint32x2","vec3<i32>":"sint32x3","vec4<i32>":"sint32x4",u32:"uint32","vec2<u32>":"uint32x2","vec3<u32>":"uint32x3","vec4<u32>":"uint32x4",bool:"uint32","vec2<bool>":"uint32x2","vec3<bool>":"uint32x3","vec4<bool>":"uint32x4"};function Kc({source:s,entryPoint:e}){const t={},r=s.indexOf(`fn ${e}`);if(r!==-1){const i=s.indexOf("->",r);if(i!==-1){const n=s.substring(r,i),o=/@location\((\d+)\)\s+([a-zA-Z0-9_]+)\s*:\s*([a-zA-Z0-9_<>]+)(?:,|\s|$)/g;let a;for(;(a=o.exec(n))!==null;){const h=qc[a[3]]??"float32";t[a[2]]={location:parseInt(a[1],10),format:h,stride:De(h).stride,offset:0,instance:!1,start:0}}}}return t}function rs(s){const e=/(^|[^/])@(group|binding)\(\d+\)[^;]+;/g,t=/@group\((\d+)\)/,r=/@binding\((\d+)\)/,i=/var(<[^>]+>)? (\w+)/,n=/:\s*(\w+)/,o=/struct\s+(\w+)\s*{([^}]+)}/g,a=/(\w+)\s*:\s*([\w\<\>]+)/g,h=/struct\s+(\w+)/,l=s.match(e)?.map(u=>({group:parseInt(u.match(t)[1],10),binding:parseInt(u.match(r)[1],10),name:u.match(i)[2],isUniform:u.match(i)[1]==="<uniform>",type:u.match(n)[1]}));if(!l)return{groups:[],structs:[]};const c=s.match(o)?.map(u=>{const f=u.match(h)[1],d=u.match(a).reduce((p,g)=>{const[m,_]=g.split(":");return p[m.trim()]=_.trim(),p},{});return d?{name:f,members:d}:null}).filter(({name:u})=>l.some(f=>f.type===u))??[];return{groups:l,structs:c}}var Mt=(s=>(s[s.VERTEX=1]="VERTEX",s[s.FRAGMENT=2]="FRAGMENT",s[s.COMPUTE=4]="COMPUTE",s))(Mt||{});function Zc({groups:s}){const e=[];for(let t=0;t<s.length;t++){const r=s[t];e[r.group]||(e[r.group]=[]),r.isUniform?e[r.group].push({binding:r.binding,visibility:Mt.VERTEX|Mt.FRAGMENT,buffer:{type:"uniform"}}):r.type==="sampler"?e[r.group].push({binding:r.binding,visibility:Mt.FRAGMENT,sampler:{type:"filtering"}}):r.type==="texture_2d"&&e[r.group].push({binding:r.binding,visibility:Mt.FRAGMENT,texture:{sampleType:"float",viewDimension:"2d",multisampled:!1}})}return e}function Qc({groups:s}){const e=[];for(let t=0;t<s.length;t++){const r=s[t];e[r.group]||(e[r.group]={}),e[r.group][r.name]=r.binding}return e}function Jc(s,e){const t=new Set,r=new Set,i=[...s.structs,...e.structs].filter(o=>t.has(o.name)?!1:(t.add(o.name),!0)),n=[...s.groups,...e.groups].filter(o=>{const a=`${o.name}-${o.binding}`;return r.has(a)?!1:(r.add(a),!0)});return{structs:i,groups:n}}const ss=Object.create(null);class tt{constructor(e){this._layoutKey=0,this._attributeLocationsKey=0;const{fragment:t,vertex:r,layout:i,gpuLayout:n,name:o}=e;if(this.name=o,this.fragment=t,this.vertex=r,t.source===r.source){const a=rs(t.source);this.structsAndGroups=a}else{const a=rs(r.source),h=rs(t.source);this.structsAndGroups=Jc(a,h)}this.layout=i??Qc(this.structsAndGroups),this.gpuLayout=n??Zc(this.structsAndGroups),this.autoAssignGlobalUniforms=this.layout[0]?.globalUniforms!==void 0,this.autoAssignLocalUniforms=this.layout[1]?.localUniforms!==void 0,this._generateProgramKey()}_generateProgramKey(){const{vertex:e,fragment:t}=this,r=e.source+t.source+e.entryPoint+t.entryPoint;this._layoutKey=Lt(r,"program")}get attributeData(){return this._attributeData??(this._attributeData=Kc(this.vertex)),this._attributeData}destroy(){this.gpuLayout=null,this.layout=null,this.structsAndGroups=null,this.fragment=null,this.vertex=null}static from(e){const t=`${e.vertex.source}:${e.fragment.source}:${e.fragment.entryPoint}:${e.vertex.entryPoint}`;return ss[t]||(ss[t]=new tt(e)),ss[t]}}const la=["f32","i32","vec2<f32>","vec3<f32>","vec4<f32>","mat2x2<f32>","mat3x3<f32>","mat4x4<f32>","mat3x2<f32>","mat4x2<f32>","mat2x3<f32>","mat4x3<f32>","mat2x4<f32>","mat3x4<f32>","vec2<i32>","vec3<i32>","vec4<i32>"],eu=la.reduce((s,e)=>(s[e]=!0,s),{});function tu(s,e){switch(s){case"f32":return 0;case"vec2<f32>":return new Float32Array(2*e);case"vec3<f32>":return new Float32Array(3*e);case"vec4<f32>":return new Float32Array(4*e);case"mat2x2<f32>":return new Float32Array([1,0,0,1]);case"mat3x3<f32>":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4x4<f32>":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const ca=class ua{constructor(e,t){this._touched=0,this.uid=V("uniform"),this._resourceType="uniformGroup",this._resourceId=V("resource"),this.isUniformGroup=!0,this._dirtyId=0,this.destroyed=!1,t={...ua.defaultOptions,...t},this.uniformStructures=e;const r={};for(const i in e){const n=e[i];if(n.name=i,n.size=n.size??1,!eu[n.type])throw new Error(`Uniform type ${n.type} is not supported. Supported uniform types are: ${la.join(", ")}`);n.value??(n.value=tu(n.type,n.size)),r[i]=n.value}this.uniforms=r,this._dirtyId=1,this.ubo=t.ubo,this.isStatic=t.isStatic,this._signature=Lt(Object.keys(r).map(i=>`${i}-${e[i].type}`).join("-"),"uniform-group")}update(){this._dirtyId++}};ca.defaultOptions={ubo:!1,isStatic:!1};let ne=ca;class Re{constructor(e){this.resources=Object.create(null),this._dirty=!0;let t=0;for(const r in e){const i=e[r];this.setResource(i,t++)}this._updateKey()}_updateKey(){if(!this._dirty)return;this._dirty=!1;const e=[];let t=0;for(const r in this.resources)e[t++]=this.resources[r]._resourceId;this._key=e.join("|")}setResource(e,t){const r=this.resources[t];e!==r&&(r&&e.off?.("change",this.onResourceChange,this),e.on?.("change",this.onResourceChange,this),this.resources[t]=e,this._dirty=!0)}getResource(e){return this.resources[e]}_touch(e){const t=this.resources;for(const r in t)t[r]._touched=e}destroy(){const e=this.resources;for(const t in e)e[t].off?.("change",this.onResourceChange,this);this.resources=null}onResourceChange(e){if(this._dirty=!0,e.destroyed){const t=this.resources;for(const r in t)t[r]===e&&(t[r]=null)}else this._updateKey()}}var ye=(s=>(s[s.WEBGL=1]="WEBGL",s[s.WEBGPU=2]="WEBGPU",s[s.BOTH=3]="BOTH",s))(ye||{});class pe extends de{constructor(e){super(),this.uid=V("shader"),this._uniformBindMap=Object.create(null),this._ownedBindGroups=[];let{gpuProgram:t,glProgram:r,groups:i,resources:n,compatibleRenderers:o,groupMap:a}=e;this.gpuProgram=t,this.glProgram=r,o===void 0&&(o=0,t&&(o|=ye.WEBGPU),r&&(o|=ye.WEBGL)),this.compatibleRenderers=o;const h={};if(!n&&!i&&(n={}),n&&i)throw new Error("[Shader] Cannot have both resources and groups");if(!t&&i&&!a)throw new Error("[Shader] No group map or WebGPU shader provided - consider using resources instead.");if(!t&&i&&a)for(const l in a)for(const c in a[l]){const u=a[l][c];h[u]={group:l,binding:c,name:u}}else if(t&&i&&!a){const l=t.structsAndGroups.groups;a={},l.forEach(c=>{a[c.group]=a[c.group]||{},a[c.group][c.binding]=c.name,h[c.name]=c})}else if(n){i={},a={},t&&t.structsAndGroups.groups.forEach(u=>{a[u.group]=a[u.group]||{},a[u.group][u.binding]=u.name,h[u.name]=u});let l=0;for(const c in n)h[c]||(i[99]||(i[99]=new Re,this._ownedBindGroups.push(i[99])),h[c]={group:99,binding:l,name:c},a[99]=a[99]||{},a[99][l]=c,l++);for(const c in n){const u=c;let f=n[c];!f.source&&!f._resourceType&&(f=new ne(f));const d=h[u];d&&(i[d.group]||(i[d.group]=new Re,this._ownedBindGroups.push(i[d.group])),i[d.group].setResource(f,d.binding))}}this.groups=i,this._uniformBindMap=a,this.resources=this._buildResourceAccessor(i,h)}addResource(e,t,r){var i,n;(i=this._uniformBindMap)[t]||(i[t]={}),(n=this._uniformBindMap[t])[r]||(n[r]=e),this.groups[t]||(this.groups[t]=new Re,this._ownedBindGroups.push(this.groups[t]))}_buildResourceAccessor(e,t){const r={};for(const i in t){const n=t[i];Object.defineProperty(r,n.name,{get(){return e[n.group].getResource(n.binding)},set(o){e[n.group].setResource(o,n.binding)}})}return r}destroy(e=!1){this.emit("destroy",this),e&&(this.gpuProgram?.destroy(),this.glProgram?.destroy()),this.gpuProgram=null,this.glProgram=null,this.removeAllListeners(),this._uniformBindMap=null,this._ownedBindGroups.forEach(t=>{t.destroy()}),this._ownedBindGroups=null,this.resources=null,this.groups=null}static from(e){const{gpu:t,gl:r,...i}=e;let n,o;return t&&(n=tt.from(t)),r&&(o=pt.from(r)),new pe({gpuProgram:n,glProgram:o,...i})}}const ru={normal:0,add:1,multiply:2,screen:3,overlay:4,erase:5,"normal-npm":6,"add-npm":7,"screen-npm":8,min:9,max:10},is=0,ns=1,os=2,as=3,hs=4,ls=5,Gs=class da{constructor(){this.data=0,this.blendMode="normal",this.polygonOffset=0,this.blend=!0,this.depthMask=!0}get blend(){return!!(this.data&1<<is)}set blend(e){!!(this.data&1<<is)!==e&&(this.data^=1<<is)}get offsets(){return!!(this.data&1<<ns)}set offsets(e){!!(this.data&1<<ns)!==e&&(this.data^=1<<ns)}set cullMode(e){if(e==="none"){this.culling=!1;return}this.culling=!0,this.clockwiseFrontFace=e==="front"}get cullMode(){return this.culling?this.clockwiseFrontFace?"front":"back":"none"}get culling(){return!!(this.data&1<<os)}set culling(e){!!(this.data&1<<os)!==e&&(this.data^=1<<os)}get depthTest(){return!!(this.data&1<<as)}set depthTest(e){!!(this.data&1<<as)!==e&&(this.data^=1<<as)}get depthMask(){return!!(this.data&1<<ls)}set depthMask(e){!!(this.data&1<<ls)!==e&&(this.data^=1<<ls)}get clockwiseFrontFace(){return!!(this.data&1<<hs)}set clockwiseFrontFace(e){!!(this.data&1<<hs)!==e&&(this.data^=1<<hs)}get blendMode(){return this._blendMode}set blendMode(e){this.blend=e!=="none",this._blendMode=e,this._blendModeId=ru[e]||0}get polygonOffset(){return this._polygonOffset}set polygonOffset(e){this.offsets=!!e,this._polygonOffset=e}toString(){return`[pixi.js/core:State blendMode=${this.blendMode} clockwiseFrontFace=${this.clockwiseFrontFace} culling=${this.culling} depthMask=${this.depthMask} polygonOffset=${this.polygonOffset}]`}static for2d(){const e=new da;return e.depthTest=!1,e.blend=!0,e}};Gs.default2d=Gs.for2d();let be=Gs;const fa=class Is extends pe{constructor(e){e={...Is.defaultOptions,...e},super(e),this.enabled=!0,this._state=be.for2d(),this.blendMode=e.blendMode,this.padding=e.padding,typeof e.antialias=="boolean"?this.antialias=e.antialias?"on":"off":this.antialias=e.antialias,this.resolution=e.resolution,this.blendRequired=e.blendRequired,this.clipToViewport=e.clipToViewport,this.addResource("uTexture",0,1)}apply(e,t,r,i){e.applyFilter(this,t,r,i)}get blendMode(){return this._state.blendMode}set blendMode(e){this._state.blendMode=e}static from(e){const{gpu:t,gl:r,...i}=e;let n,o;return t&&(n=tt.from(t)),r&&(o=pt.from(r)),new Is({gpuProgram:n,glProgram:o,...i})}};fa.defaultOptions={blendMode:"normal",resolution:1,padding:0,antialias:"off",blendRequired:!1,clipToViewport:!0};let su=fa;const Fs=[];q.handleByNamedList(x.Environment,Fs);async function iu(s){if(!s)for(let e=0;e<Fs.length;e++){const t=Fs[e];if(t.value.test()){await t.value.load();return}}}let vt;function pa(){if(typeof vt=="boolean")return vt;try{vt=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{vt=!1}return vt}function Sn(s,e,t=2){const r=e&&e.length,i=r?e[0]*t:s.length;let n=ma(s,0,i,t,!0);const o=[];if(!n||n.next===n.prev)return o;let a,h,l;if(r&&(n=lu(s,e,n,t)),s.length>80*t){a=1/0,h=1/0;let c=-1/0,u=-1/0;for(let f=t;f<i;f+=t){const d=s[f],p=s[f+1];d<a&&(a=d),p<h&&(h=p),d>c&&(c=d),p>u&&(u=p)}l=Math.max(c-a,u-h),l=l!==0?32767/l:0}return Nt(n,o,t,a,h,l,0),o}function ma(s,e,t,r,i){let n;if(i===bu(s,e,t,r)>0)for(let o=e;o<t;o+=r)n=wn(o/r|0,s[o],s[o+1],n);else for(let o=t-r;o>=e;o-=r)n=wn(o/r|0,s[o],s[o+1],n);return n&&ut(n,n.next)&&(zt(n),n=n.next),n}function et(s,e){if(!s)return s;e||(e=s);let t=s,r;do if(r=!1,!t.steiner&&(ut(t,t.next)||j(t.prev,t,t.next)===0)){if(zt(t),t=e=t.prev,t===t.next)break;r=!0}else t=t.next;while(r||t!==e);return e}function Nt(s,e,t,r,i,n,o){if(!s)return;!o&&n&&pu(s,r,i,n);let a=s;for(;s.prev!==s.next;){const h=s.prev,l=s.next;if(n?ou(s,r,i,n):nu(s)){e.push(h.i,s.i,l.i),zt(s),s=l.next,a=l.next;continue}if(s=l,s===a){o?o===1?(s=au(et(s),e),Nt(s,e,t,r,i,n,2)):o===2&&hu(s,e,t,r,i,n):Nt(et(s),e,t,r,i,n,1);break}}}function nu(s){const e=s.prev,t=s,r=s.next;if(j(e,t,r)>=0)return!1;const i=e.x,n=t.x,o=r.x,a=e.y,h=t.y,l=r.y,c=Math.min(i,n,o),u=Math.min(a,h,l),f=Math.max(i,n,o),d=Math.max(a,h,l);let p=r.next;for(;p!==e;){if(p.x>=c&&p.x<=f&&p.y>=u&&p.y<=d&&At(i,a,n,h,o,l,p.x,p.y)&&j(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function ou(s,e,t,r){const i=s.prev,n=s,o=s.next;if(j(i,n,o)>=0)return!1;const a=i.x,h=n.x,l=o.x,c=i.y,u=n.y,f=o.y,d=Math.min(a,h,l),p=Math.min(c,u,f),g=Math.max(a,h,l),m=Math.max(c,u,f),_=Us(d,p,e,t,r),y=Us(g,m,e,t,r);let b=s.prevZ,v=s.nextZ;for(;b&&b.z>=_&&v&&v.z<=y;){if(b.x>=d&&b.x<=g&&b.y>=p&&b.y<=m&&b!==i&&b!==o&&At(a,c,h,u,l,f,b.x,b.y)&&j(b.prev,b,b.next)>=0||(b=b.prevZ,v.x>=d&&v.x<=g&&v.y>=p&&v.y<=m&&v!==i&&v!==o&&At(a,c,h,u,l,f,v.x,v.y)&&j(v.prev,v,v.next)>=0))return!1;v=v.nextZ}for(;b&&b.z>=_;){if(b.x>=d&&b.x<=g&&b.y>=p&&b.y<=m&&b!==i&&b!==o&&At(a,c,h,u,l,f,b.x,b.y)&&j(b.prev,b,b.next)>=0)return!1;b=b.prevZ}for(;v&&v.z<=y;){if(v.x>=d&&v.x<=g&&v.y>=p&&v.y<=m&&v!==i&&v!==o&&At(a,c,h,u,l,f,v.x,v.y)&&j(v.prev,v,v.next)>=0)return!1;v=v.nextZ}return!0}function au(s,e){let t=s;do{const r=t.prev,i=t.next.next;!ut(r,i)&&_a(r,t,t.next,i)&&Ht(r,i)&&Ht(i,r)&&(e.push(r.i,t.i,i.i),zt(t),zt(t.next),t=s=i),t=t.next}while(t!==s);return et(t)}function hu(s,e,t,r,i,n){let o=s;do{let a=o.next.next;for(;a!==o.prev;){if(o.i!==a.i&&_u(o,a)){let h=xa(o,a);o=et(o,o.next),h=et(h,h.next),Nt(o,e,t,r,i,n,0),Nt(h,e,t,r,i,n,0);return}a=a.next}o=o.next}while(o!==s)}function lu(s,e,t,r){const i=[];for(let n=0,o=e.length;n<o;n++){const a=e[n]*r,h=n<o-1?e[n+1]*r:s.length,l=ma(s,a,h,r,!1);l===l.next&&(l.steiner=!0),i.push(gu(l))}i.sort(cu);for(let n=0;n<i.length;n++)t=uu(i[n],t);return t}function cu(s,e){let t=s.x-e.x;if(t===0&&(t=s.y-e.y,t===0)){const r=(s.next.y-s.y)/(s.next.x-s.x),i=(e.next.y-e.y)/(e.next.x-e.x);t=r-i}return t}function uu(s,e){const t=du(s,e);if(!t)return e;const r=xa(t,s);return et(r,r.next),et(t,t.next)}function du(s,e){let t=e;const r=s.x,i=s.y;let n=-1/0,o;if(ut(s,t))return t;do{if(ut(s,t.next))return t.next;if(i<=t.y&&i>=t.next.y&&t.next.y!==t.y){const u=t.x+(i-t.y)*(t.next.x-t.x)/(t.next.y-t.y);if(u<=r&&u>n&&(n=u,o=t.x<t.next.x?t:t.next,u===r))return o}t=t.next}while(t!==e);if(!o)return null;const a=o,h=o.x,l=o.y;let c=1/0;t=o;do{if(r>=t.x&&t.x>=h&&r!==t.x&&ga(i<l?r:n,i,h,l,i<l?n:r,i,t.x,t.y)){const u=Math.abs(i-t.y)/(r-t.x);Ht(t,s)&&(u<c||u===c&&(t.x>o.x||t.x===o.x&&fu(o,t)))&&(o=t,c=u)}t=t.next}while(t!==a);return o}function fu(s,e){return j(s.prev,s,e.prev)<0&&j(e.next,s,s.next)<0}function pu(s,e,t,r){let i=s;do i.z===0&&(i.z=Us(i.x,i.y,e,t,r)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next;while(i!==s);i.prevZ.nextZ=null,i.prevZ=null,mu(i)}function mu(s){let e,t=1;do{let r=s,i;s=null;let n=null;for(e=0;r;){e++;let o=r,a=0;for(let l=0;l<t&&(a++,o=o.nextZ,!!o);l++);let h=t;for(;a>0||h>0&&o;)a!==0&&(h===0||!o||r.z<=o.z)?(i=r,r=r.nextZ,a--):(i=o,o=o.nextZ,h--),n?n.nextZ=i:s=i,i.prevZ=n,n=i;r=o}n.nextZ=null,t*=2}while(e>1);return s}function Us(s,e,t,r,i){return s=(s-t)*i|0,e=(e-r)*i|0,s=(s|s<<8)&16711935,s=(s|s<<4)&252645135,s=(s|s<<2)&858993459,s=(s|s<<1)&1431655765,e=(e|e<<8)&16711935,e=(e|e<<4)&252645135,e=(e|e<<2)&858993459,e=(e|e<<1)&1431655765,s|e<<1}function gu(s){let e=s,t=s;do(e.x<t.x||e.x===t.x&&e.y<t.y)&&(t=e),e=e.next;while(e!==s);return t}function ga(s,e,t,r,i,n,o,a){return(i-o)*(e-a)>=(s-o)*(n-a)&&(s-o)*(r-a)>=(t-o)*(e-a)&&(t-o)*(n-a)>=(i-o)*(r-a)}function At(s,e,t,r,i,n,o,a){return!(s===o&&e===a)&&ga(s,e,t,r,i,n,o,a)}function _u(s,e){return s.next.i!==e.i&&s.prev.i!==e.i&&!xu(s,e)&&(Ht(s,e)&&Ht(e,s)&&yu(s,e)&&(j(s.prev,s,e.prev)||j(s,e.prev,e))||ut(s,e)&&j(s.prev,s,s.next)>0&&j(e.prev,e,e.next)>0)}function j(s,e,t){return(e.y-s.y)*(t.x-e.x)-(e.x-s.x)*(t.y-e.y)}function ut(s,e){return s.x===e.x&&s.y===e.y}function _a(s,e,t,r){const i=cr(j(s,e,t)),n=cr(j(s,e,r)),o=cr(j(t,r,s)),a=cr(j(t,r,e));return!!(i!==n&&o!==a||i===0&&lr(s,t,e)||n===0&&lr(s,r,e)||o===0&&lr(t,s,r)||a===0&&lr(t,e,r))}function lr(s,e,t){return e.x<=Math.max(s.x,t.x)&&e.x>=Math.min(s.x,t.x)&&e.y<=Math.max(s.y,t.y)&&e.y>=Math.min(s.y,t.y)}function cr(s){return s>0?1:s<0?-1:0}function xu(s,e){let t=s;do{if(t.i!==s.i&&t.next.i!==s.i&&t.i!==e.i&&t.next.i!==e.i&&_a(t,t.next,s,e))return!0;t=t.next}while(t!==s);return!1}function Ht(s,e){return j(s.prev,s,s.next)<0?j(s,e,s.next)>=0&&j(s,s.prev,e)>=0:j(s,e,s.prev)<0||j(s,s.next,e)<0}function yu(s,e){let t=s,r=!1;const i=(s.x+e.x)/2,n=(s.y+e.y)/2;do t.y>n!=t.next.y>n&&t.next.y!==t.y&&i<(t.next.x-t.x)*(n-t.y)/(t.next.y-t.y)+t.x&&(r=!r),t=t.next;while(t!==s);return r}function xa(s,e){const t=Ds(s.i,s.x,s.y),r=Ds(e.i,e.x,e.y),i=s.next,n=e.prev;return s.next=e,e.prev=s,t.next=i,i.prev=t,r.next=t,t.prev=r,n.next=r,r.prev=n,r}function wn(s,e,t,r){const i=Ds(s,e,t);return r?(i.next=r.next,i.prev=r,r.next.prev=i,r.next=i):(i.prev=i,i.next=i),i}function zt(s){s.next.prev=s.prev,s.prev.next=s.next,s.prevZ&&(s.prevZ.nextZ=s.nextZ),s.nextZ&&(s.nextZ.prevZ=s.prevZ)}function Ds(s,e,t){return{i:s,x:e,y:t,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function bu(s,e,t,r){let i=0;for(let n=e,o=t-r;n<t;n+=r)i+=(s[o]-s[n])*(s[n+1]+s[o+1]),o=n;return i}const vu=Sn.default||Sn;var ce=(s=>(s[s.NONE=0]="NONE",s[s.COLOR=16384]="COLOR",s[s.STENCIL=1024]="STENCIL",s[s.DEPTH=256]="DEPTH",s[s.COLOR_DEPTH=16640]="COLOR_DEPTH",s[s.COLOR_STENCIL=17408]="COLOR_STENCIL",s[s.DEPTH_STENCIL=1280]="DEPTH_STENCIL",s[s.ALL=17664]="ALL",s))(ce||{});class ya{constructor(e){this.items=[],this._name=e}emit(e,t,r,i,n,o,a,h){const{name:l,items:c}=this;for(let u=0,f=c.length;u<f;u++)c[u][l](e,t,r,i,n,o,a,h);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const Tu=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],ba=class va extends de{constructor(e){super(),this.uid=V("renderer"),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...Tu,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await iu(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const r in this._systemsHash)e={...this._systemsHash[r].constructor.defaultOptions,...e};e={...va.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let r=0;r<this.runners.init.items.length;r++)await this.runners.init.items[r].init(e);this._initOptions=e}render(e,t){let r=e;if(r instanceof xe&&(r={container:r},t&&(I(O,"passing a second argument is deprecated, please use render options instead"),r.target=t.renderTexture)),r.target||(r.target=this.view.renderTarget),r.target===this.view.renderTarget&&(this._lastObjectRendered=r.container,r.clearColor??(r.clearColor=this.background.colorRgba),r.clear??(r.clear=this.background.clearBeforeRender)),r.clearColor){const i=Array.isArray(r.clearColor)&&r.clearColor.length===4;r.clearColor=i?r.clearColor:L.shared.setValue(r.clearColor).toArray()}r.transform||(r.container.updateLocalTransform(),r.transform=r.container.localTransform),r.container.enableRenderGroup(),this.runners.prerender.emit(r),this.runners.renderStart.emit(r),this.runners.render.emit(r),this.runners.renderEnd.emit(r),this.runners.postrender.emit(r)}resize(e,t,r){const i=this.view.resolution;this.view.resize(e,t,r),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),r!==void 0&&r!==i&&this.runners.resolutionChange.emit(r)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=ce.ALL);const{clear:r,clearColor:i,target:n}=e;L.shared.setValue(i??this.background.colorRgba),t.renderTarget.clear(n,r,L.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new ya(t)})}_addSystems(e){let t;for(t in e){const r=e[t];this._addSystem(r.value,r.name)}}_addSystem(e,t){const r=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=r,this._systemsHash[t]=r;for(const i in this.runners)this.runners[i].add(r);return this}_addPipes(e,t){const r=t.reduce((i,n)=>(i[n.name]=n.value,i),{});e.forEach(i=>{const n=i.value,o=i.name,a=r[o];this.renderPipes[o]=new n(this,a?new a:null)})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!pa())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};ba.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let Fr=ba,ur;function Su(s){return ur!==void 0||(ur=(()=>{const e={stencil:!0,failIfMajorPerformanceCaveat:s??Fr.defaultOptions.failIfMajorPerformanceCaveat};try{if(!$.get().getWebGLRenderingContext())return!1;let r=$.get().createCanvas().getContext("webgl",e);const i=!!r?.getContextAttributes()?.stencil;if(r){const n=r.getExtension("WEBGL_lose_context");n&&n.loseContext()}return r=null,i}catch{return!1}})()),ur}let dr;async function wu(s={}){return dr!==void 0||(dr=await(async()=>{const e=$.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(s)).requestDevice(),!0}catch{return!1}})()),dr}const Pn=["webgl","webgpu","canvas"];async function Pu(s){let e=[];s.preference?(e.push(s.preference),Pn.forEach(n=>{n!==s.preference&&e.push(n)})):e=Pn.slice();let t,r={};for(let n=0;n<e.length;n++){const o=e[n];if(o==="webgpu"&&await wu()){const{WebGPURenderer:a}=await wr(async()=>{const{WebGPURenderer:h}=await Promise.resolve().then(()=>km);return{WebGPURenderer:h}},void 0,import.meta.url);t=a,r={...s,...s.webgpu};break}else if(o==="webgl"&&Su(s.failIfMajorPerformanceCaveat??Fr.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:a}=await wr(async()=>{const{WebGLRenderer:h}=await Promise.resolve().then(()=>fm);return{WebGLRenderer:h}},void 0,import.meta.url);t=a,r={...s,...s.webgl};break}else if(o==="canvas")throw r={...s},new Error("CanvasRenderer is not yet implemented")}if(delete r.webgpu,delete r.webgl,!t)throw new Error("No available renderer for the current environment");const i=new t;return await i.init(r),i}const Mr="8.10.1";class Ta{static init(){globalThis.__PIXI_APP_INIT__?.(this,Mr)}static destroy(){}}Ta.extension=x.Application;class Sa{constructor(e){this._renderer=e}init(){globalThis.__PIXI_RENDERER_INIT__?.(this._renderer,Mr)}destroy(){this._renderer=null}}Sa.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"initHook",priority:-10};const wa=class Os{constructor(...e){this.stage=new xe,e[0]!==void 0&&I(O,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await Pu(e),Os._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return I(O,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const r=Os._plugins.slice(0);r.reverse(),r.forEach(i=>{i.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};wa._plugins=[];let Cu=wa;q.handleByList(x.Application,Cu._plugins);q.add(Ta);class Eu{static init(e){Object.defineProperty(this,"resizeTo",{set(t){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=t,t&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get(){return this._resizeTo}}),this.queueResize=()=>{this._resizeTo&&(this._cancelResize(),this._resizeId=requestAnimationFrame(()=>this.resize()))},this._cancelResize=()=>{this._resizeId&&(cancelAnimationFrame(this._resizeId),this._resizeId=null)},this.resize=()=>{if(!this._resizeTo)return;this._cancelResize();let t,r;if(this._resizeTo===globalThis.window)t=globalThis.innerWidth,r=globalThis.innerHeight;else{const{clientWidth:i,clientHeight:n}=this._resizeTo;t=i,r=n}this.renderer.resize(t,r),this.render()},this._resizeId=null,this._resizeTo=null,this.resizeTo=e.resizeTo||null}static destroy(){globalThis.removeEventListener("resize",this.queueResize),this._cancelResize(),this._cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null}}Eu.extension=x.Application;class Mu{static init(e){e=Object.assign({autoStart:!0,sharedTicker:!1},e),Object.defineProperty(this,"ticker",{set(t){this._ticker&&this._ticker.remove(this.render,this),this._ticker=t,t&&t.add(this.render,this,Dt.LOW)},get(){return this._ticker}}),this.stop=()=>{this._ticker.stop()},this.start=()=>{this._ticker.start()},this._ticker=null,this.ticker=e.sharedTicker?we.shared:new we,e.autoStart&&this.start()}static destroy(){if(this._ticker){const e=this._ticker;this.ticker=null,e.destroy()}}}Mu.extension=x.Application;class Au extends de{constructor(){super(...arguments),this.chars=Object.create(null),this.lineHeight=0,this.fontFamily="",this.fontMetrics={fontSize:0,ascent:0,descent:0},this.baseLineOffset=0,this.distanceField={type:"none",range:0},this.pages=[],this.applyFillAsTint=!0,this.baseMeasurementFontSize=100,this.baseRenderedFontSize=100}get font(){return I(O,"BitmapFont.font is deprecated, please use BitmapFont.fontFamily instead."),this.fontFamily}get pageTextures(){return I(O,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}get size(){return I(O,"BitmapFont.size is deprecated, please use BitmapFont.fontMetrics.fontSize instead."),this.fontMetrics.fontSize}get distanceFieldRange(){return I(O,"BitmapFont.distanceFieldRange is deprecated, please use BitmapFont.distanceField.range instead."),this.distanceField.range}get distanceFieldType(){return I(O,"BitmapFont.distanceFieldType is deprecated, please use BitmapFont.distanceField.type instead."),this.distanceField.type}destroy(e=!1){this.emit("destroy",this),this.removeAllListeners();for(const t in this.chars)this.chars[t].texture?.destroy();this.chars=null,e&&(this.pages.forEach(t=>t.texture.destroy(!0)),this.pages=null)}}const Bu=["serif","sans-serif","monospace","cursive","fantasy","system-ui"];function Ar(s){const e=typeof s.fontSize=="number"?`${s.fontSize}px`:s.fontSize;let t=s.fontFamily;Array.isArray(s.fontFamily)||(t=s.fontFamily.split(","));for(let r=t.length-1;r>=0;r--){let i=t[r].trim();!/([\"\'])[^\'\"]+\1/.test(i)&&!Bu.includes(i)&&(i=`"${i}"`),t[r]=i}return`${s.fontStyle} ${s.fontVariant} ${s.fontWeight} ${e} ${t.join(",")}`}const cs={willReadFrequently:!0},Pe=class M{static get experimentalLetterSpacingSupported(){let e=M._experimentalLetterSpacingSupported;if(e===void 0){const t=$.get().getCanvasRenderingContext2D().prototype;e=M._experimentalLetterSpacingSupported="letterSpacing"in t||"textLetterSpacing"in t}return e}constructor(e,t,r,i,n,o,a,h,l){this.text=e,this.style=t,this.width=r,this.height=i,this.lines=n,this.lineWidths=o,this.lineHeight=a,this.maxLineWidth=h,this.fontProperties=l}static measureText(e=" ",t,r=M._canvas,i=t.wordWrap){const n=Ar(t),o=M.measureFont(n);o.fontSize===0&&(o.fontSize=t.fontSize,o.ascent=t.fontSize);const a=M.__context;a.font=n;const l=(i?M._wordWrap(e,t,r):e).split(/(?:\r\n|\r|\n)/),c=new Array(l.length);let u=0;for(let _=0;_<l.length;_++){const y=M._measureText(l[_],t.letterSpacing,a);c[_]=y,u=Math.max(u,y)}const f=t._stroke?.width||0;let d=u+f;t.dropShadow&&(d+=t.dropShadow.distance);const p=t.lineHeight||o.fontSize;let g=Math.max(p,o.fontSize+f)+(l.length-1)*(p+t.leading);return t.dropShadow&&(g+=t.dropShadow.distance),new M(e,t,d,g,l,c,p+t.leading,u,o)}static _measureText(e,t,r){let i=!1;M.experimentalLetterSpacingSupported&&(M.experimentalLetterSpacing?(r.letterSpacing=`${t}px`,r.textLetterSpacing=`${t}px`,i=!0):(r.letterSpacing="0px",r.textLetterSpacing="0px"));const n=r.measureText(e);let o=n.width;const a=-n.actualBoundingBoxLeft;let l=n.actualBoundingBoxRight-a;if(o>0)if(i)o-=t,l-=t;else{const c=(M.graphemeSegmenter(e).length-1)*t;o+=c,l+=c}return Math.max(o,l)}static _wordWrap(e,t,r=M._canvas){const i=r.getContext("2d",cs);let n=0,o="",a="";const h=Object.create(null),{letterSpacing:l,whiteSpace:c}=t,u=M._collapseSpaces(c),f=M._collapseNewlines(c);let d=!u;const p=t.wordWrapWidth+l,g=M._tokenize(e);for(let m=0;m<g.length;m++){let _=g[m];if(M._isNewline(_)){if(!f){a+=M._addLine(o),d=!u,o="",n=0;continue}_=" "}if(u){const b=M.isBreakingSpace(_),v=M.isBreakingSpace(o[o.length-1]);if(b&&v)continue}const y=M._getFromCache(_,l,h,i);if(y>p)if(o!==""&&(a+=M._addLine(o),o="",n=0),M.canBreakWords(_,t.breakWords)){const b=M.wordWrapSplit(_);for(let v=0;v<b.length;v++){let w=b[v],S=w,T=1;for(;b[v+T];){const C=b[v+T];if(!M.canBreakChars(S,C,_,v,t.breakWords))w+=C;else break;S=C,T++}v+=T-1;const k=M._getFromCache(w,l,h,i);k+n>p&&(a+=M._addLine(o),d=!1,o="",n=0),o+=w,n+=k}}else{o.length>0&&(a+=M._addLine(o),o="",n=0);const b=m===g.length-1;a+=M._addLine(_,!b),d=!1,o="",n=0}else y+n>p&&(d=!1,a+=M._addLine(o),o="",n=0),(o.length>0||!M.isBreakingSpace(_)||d)&&(o+=_,n+=y)}return a+=M._addLine(o,!1),a}static _addLine(e,t=!0){return e=M._trimRight(e),e=t?`${e}
`:e,e}static _getFromCache(e,t,r,i){let n=r[e];return typeof n!="number"&&(n=M._measureText(e,t,i)+t,r[e]=n),n}static _collapseSpaces(e){return e==="normal"||e==="pre-line"}static _collapseNewlines(e){return e==="normal"}static _trimRight(e){if(typeof e!="string")return"";for(let t=e.length-1;t>=0;t--){const r=e[t];if(!M.isBreakingSpace(r))break;e=e.slice(0,-1)}return e}static _isNewline(e){return typeof e!="string"?!1:M._newlines.includes(e.charCodeAt(0))}static isBreakingSpace(e,t){return typeof e!="string"?!1:M._breakingSpaces.includes(e.charCodeAt(0))}static _tokenize(e){const t=[];let r="";if(typeof e!="string")return t;for(let i=0;i<e.length;i++){const n=e[i],o=e[i+1];if(M.isBreakingSpace(n,o)||M._isNewline(n)){r!==""&&(t.push(r),r=""),t.push(n);continue}r+=n}return r!==""&&t.push(r),t}static canBreakWords(e,t){return t}static canBreakChars(e,t,r,i,n){return!0}static wordWrapSplit(e){return M.graphemeSegmenter(e)}static measureFont(e){if(M._fonts[e])return M._fonts[e];const t=M._context;t.font=e;const r=t.measureText(M.METRICS_STRING+M.BASELINE_SYMBOL),i={ascent:r.actualBoundingBoxAscent,descent:r.actualBoundingBoxDescent,fontSize:r.actualBoundingBoxAscent+r.actualBoundingBoxDescent};return M._fonts[e]=i,i}static clearMetrics(e=""){e?delete M._fonts[e]:M._fonts={}}static get _canvas(){if(!M.__canvas){let e;try{const t=new OffscreenCanvas(0,0);if(t.getContext("2d",cs)?.measureText)return M.__canvas=t,t;e=$.get().createCanvas()}catch{e=$.get().createCanvas()}e.width=e.height=10,M.__canvas=e}return M.__canvas}static get _context(){return M.__context||(M.__context=M._canvas.getContext("2d",cs)),M.__context}};Pe.METRICS_STRING="|ÉqÅ";Pe.BASELINE_SYMBOL="M";Pe.BASELINE_MULTIPLIER=1.4;Pe.HEIGHT_MULTIPLIER=2;Pe.graphemeSegmenter=(()=>{if(typeof Intl?.Segmenter=="function"){const s=new Intl.Segmenter;return e=>{const t=s.segment(e),r=[];let i=0;for(const n of t)r[i++]=n.segment;return r}}return s=>[...s]})();Pe.experimentalLetterSpacing=!1;Pe._fonts={};Pe._newlines=[10,13];Pe._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];let Se=Pe;const Cn=[{offset:0,color:"white"},{offset:1,color:"black"}],ni=class Ls{constructor(...e){this.uid=V("fillGradient"),this.type="linear",this.colorStops=[];let t=Ru(e);t={...t.type==="radial"?Ls.defaultRadialOptions:Ls.defaultLinearOptions,...Ao(t)},this._textureSize=t.textureSize,this._wrapMode=t.wrapMode,t.type==="radial"?(this.center=t.center,this.outerCenter=t.outerCenter??this.center,this.innerRadius=t.innerRadius,this.outerRadius=t.outerRadius,this.scale=t.scale,this.rotation=t.rotation):(this.start=t.start,this.end=t.end),this.textureSpace=t.textureSpace,this.type=t.type,t.colorStops.forEach(i=>{this.addColorStop(i.offset,i.color)})}addColorStop(e,t){return this.colorStops.push({offset:e,color:L.shared.setValue(t).toHexa()}),this}buildLinearGradient(){if(this.texture)return;let{x:e,y:t}=this.start,{x:r,y:i}=this.end,n=r-e,o=i-t;const a=n<0||o<0;if(this._wrapMode==="clamp-to-edge"){if(n<0){const m=e;e=r,r=m,n*=-1}if(o<0){const m=t;t=i,i=m,o*=-1}}const h=this.colorStops.length?this.colorStops:Cn,l=this._textureSize,{canvas:c,context:u}=Mn(l,1),f=a?u.createLinearGradient(this._textureSize,0,0,0):u.createLinearGradient(0,0,this._textureSize,0);En(f,h),u.fillStyle=f,u.fillRect(0,0,l,1),this.texture=new A({source:new Ut({resource:c,addressMode:this._wrapMode})});const d=Math.sqrt(n*n+o*o),p=Math.atan2(o,n),g=new B;g.scale(d/l,1),g.rotate(p),g.translate(e,t),this.textureSpace==="local"&&g.scale(l,l),this.transform=g}buildGradient(){this.type==="linear"?this.buildLinearGradient():this.buildRadialGradient()}buildRadialGradient(){if(this.texture)return;const e=this.colorStops.length?this.colorStops:Cn,t=this._textureSize,{canvas:r,context:i}=Mn(t,t),{x:n,y:o}=this.center,{x:a,y:h}=this.outerCenter,l=this.innerRadius,c=this.outerRadius,u=a-c,f=h-c,d=t/(c*2),p=(n-u)*d,g=(o-f)*d,m=i.createRadialGradient(p,g,l*d,(a-u)*d,(h-f)*d,c*d);En(m,e),i.fillStyle=e[e.length-1].color,i.fillRect(0,0,t,t),i.fillStyle=m,i.translate(p,g),i.rotate(this.rotation),i.scale(1,this.scale),i.translate(-p,-g),i.fillRect(0,0,t,t),this.texture=new A({source:new Ut({resource:r,addressMode:this._wrapMode})});const _=new B;_.scale(1/d,1/d),_.translate(u,f),this.textureSpace==="local"&&_.scale(t,t),this.transform=_}get styleKey(){return this.uid}destroy(){this.texture?.destroy(!0),this.texture=null}};ni.defaultLinearOptions={start:{x:0,y:0},end:{x:0,y:1},colorStops:[],textureSpace:"local",type:"linear",textureSize:256,wrapMode:"clamp-to-edge"};ni.defaultRadialOptions={center:{x:.5,y:.5},innerRadius:0,outerRadius:.5,colorStops:[],scale:1,textureSpace:"local",type:"radial",textureSize:256,wrapMode:"clamp-to-edge"};let ke=ni;function En(s,e){for(let t=0;t<e.length;t++){const r=e[t];s.addColorStop(r.offset,r.color)}}function Mn(s,e){const t=$.get().createCanvas(s,e),r=t.getContext("2d");return{canvas:t,context:r}}function Ru(s){let e=s[0]??{};return(typeof e=="number"||s[1])&&(I("8.5.2","use options object instead"),e={type:"linear",start:{x:s[0],y:s[1]},end:{x:s[2],y:s[3]},textureSpace:s[4],textureSize:s[5]??ke.defaultLinearOptions.textureSize}),e}const An={repeat:{addressModeU:"repeat",addressModeV:"repeat"},"repeat-x":{addressModeU:"repeat",addressModeV:"clamp-to-edge"},"repeat-y":{addressModeU:"clamp-to-edge",addressModeV:"repeat"},"no-repeat":{addressModeU:"clamp-to-edge",addressModeV:"clamp-to-edge"}};class Ur{constructor(e,t){this.uid=V("fillPattern"),this.transform=new B,this._styleKey=null,this.texture=e,this.transform.scale(1/e.frame.width,1/e.frame.height),t&&(e.source.style.addressModeU=An[t].addressModeU,e.source.style.addressModeV=An[t].addressModeV)}setTransform(e){const t=this.texture;this.transform.copyFrom(e),this.transform.invert(),this.transform.scale(1/t.frame.width,1/t.frame.height),this._styleKey=null}get styleKey(){return this._styleKey?this._styleKey:(this._styleKey=`fill-pattern-${this.uid}-${this.texture.uid}-${this.transform.toArray().join("-")}`,this._styleKey)}}var ku=Iu,us={a:7,c:6,h:1,l:2,m:2,q:4,s:4,t:2,v:1,z:0},Gu=/([astvzqmhlc])([^astvzqmhlc]*)/ig;function Iu(s){var e=[];return s.replace(Gu,function(t,r,i){var n=r.toLowerCase();for(i=Uu(i),n=="m"&&i.length>2&&(e.push([r].concat(i.splice(0,2))),n="l",r=r=="m"?"l":"L");;){if(i.length==us[n])return i.unshift(r),e.push(i);if(i.length<us[n])throw new Error("malformed path data");e.push([r].concat(i.splice(0,us[n])))}}),e}var Fu=/-?[0-9]*\.?[0-9]+(?:e[-+]?\d+)?/ig;function Uu(s){var e=s.match(Fu);return e?e.map(Number):[]}const Du=Po(ku);function Ou(s,e){const t=Du(s),r=[];let i=null,n=0,o=0;for(let a=0;a<t.length;a++){const h=t[a],l=h[0],c=h;switch(l){case"M":n=c[1],o=c[2],e.moveTo(n,o);break;case"m":n+=c[1],o+=c[2],e.moveTo(n,o);break;case"H":n=c[1],e.lineTo(n,o);break;case"h":n+=c[1],e.lineTo(n,o);break;case"V":o=c[1],e.lineTo(n,o);break;case"v":o+=c[1],e.lineTo(n,o);break;case"L":n=c[1],o=c[2],e.lineTo(n,o);break;case"l":n+=c[1],o+=c[2],e.lineTo(n,o);break;case"C":n=c[5],o=c[6],e.bezierCurveTo(c[1],c[2],c[3],c[4],n,o);break;case"c":e.bezierCurveTo(n+c[1],o+c[2],n+c[3],o+c[4],n+c[5],o+c[6]),n+=c[5],o+=c[6];break;case"S":n=c[3],o=c[4],e.bezierCurveToShort(c[1],c[2],n,o);break;case"s":e.bezierCurveToShort(n+c[1],o+c[2],n+c[3],o+c[4]),n+=c[3],o+=c[4];break;case"Q":n=c[3],o=c[4],e.quadraticCurveTo(c[1],c[2],n,o);break;case"q":e.quadraticCurveTo(n+c[1],o+c[2],n+c[3],o+c[4]),n+=c[3],o+=c[4];break;case"T":n=c[1],o=c[2],e.quadraticCurveToShort(n,o);break;case"t":n+=c[1],o+=c[2],e.quadraticCurveToShort(n,o);break;case"A":n=c[6],o=c[7],e.arcToSvg(c[1],c[2],c[3],c[4],c[5],n,o);break;case"a":n+=c[6],o+=c[7],e.arcToSvg(c[1],c[2],c[3],c[4],c[5],n,o);break;case"Z":case"z":e.closePath(),r.length>0&&(i=r.pop(),i?(n=i.startX,o=i.startY):(n=0,o=0)),i=null;break;default:F(`Unknown SVG path command: ${l}`)}l!=="Z"&&l!=="z"&&i===null&&(i={startX:n,startY:o},r.push(i))}return e}class oi{constructor(e=0,t=0,r=0){this.type="circle",this.x=e,this.y=t,this.radius=r}clone(){return new oi(this.x,this.y,this.radius)}contains(e,t){if(this.radius<=0)return!1;const r=this.radius*this.radius;let i=this.x-e,n=this.y-t;return i*=i,n*=n,i+n<=r}strokeContains(e,t,r,i=.5){if(this.radius===0)return!1;const n=this.x-e,o=this.y-t,a=this.radius,h=(1-i)*r,l=Math.sqrt(n*n+o*o);return l<=a+h&&l>a-(r-h)}getBounds(e){return e||(e=new N),e.x=this.x-this.radius,e.y=this.y-this.radius,e.width=this.radius*2,e.height=this.radius*2,e}copyFrom(e){return this.x=e.x,this.y=e.y,this.radius=e.radius,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:Circle x=${this.x} y=${this.y} radius=${this.radius}]`}}class ai{constructor(e=0,t=0,r=0,i=0){this.type="ellipse",this.x=e,this.y=t,this.halfWidth=r,this.halfHeight=i}clone(){return new ai(this.x,this.y,this.halfWidth,this.halfHeight)}contains(e,t){if(this.halfWidth<=0||this.halfHeight<=0)return!1;let r=(e-this.x)/this.halfWidth,i=(t-this.y)/this.halfHeight;return r*=r,i*=i,r+i<=1}strokeContains(e,t,r,i=.5){const{halfWidth:n,halfHeight:o}=this;if(n<=0||o<=0)return!1;const a=r*(1-i),h=r-a,l=n-h,c=o-h,u=n+a,f=o+a,d=e-this.x,p=t-this.y,g=d*d/(l*l)+p*p/(c*c),m=d*d/(u*u)+p*p/(f*f);return g>1&&m<=1}getBounds(e){return e||(e=new N),e.x=this.x-this.halfWidth,e.y=this.y-this.halfHeight,e.width=this.halfWidth*2,e.height=this.halfHeight*2,e}copyFrom(e){return this.x=e.x,this.y=e.y,this.halfWidth=e.halfWidth,this.halfHeight=e.halfHeight,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:Ellipse x=${this.x} y=${this.y} halfWidth=${this.halfWidth} halfHeight=${this.halfHeight}]`}}function Lu(s,e,t,r,i,n){const o=s-t,a=e-r,h=i-t,l=n-r,c=o*h+a*l,u=h*h+l*l;let f=-1;u!==0&&(f=c/u);let d,p;f<0?(d=t,p=r):f>1?(d=i,p=n):(d=t+f*h,p=r+f*l);const g=s-d,m=e-p;return g*g+m*m}let Nu,Hu;class kt{constructor(...e){this.type="polygon";let t=Array.isArray(e[0])?e[0]:e;if(typeof t[0]!="number"){const r=[];for(let i=0,n=t.length;i<n;i++)r.push(t[i].x,t[i].y);t=r}this.points=t,this.closePath=!0}isClockwise(){let e=0;const t=this.points,r=t.length;for(let i=0;i<r;i+=2){const n=t[i],o=t[i+1],a=t[(i+2)%r],h=t[(i+3)%r];e+=(a-n)*(h+o)}return e<0}containsPolygon(e){const t=this.getBounds(Nu),r=e.getBounds(Hu);if(!t.containsRect(r))return!1;const i=e.points;for(let n=0;n<i.length;n+=2){const o=i[n],a=i[n+1];if(!this.contains(o,a))return!1}return!0}clone(){const e=this.points.slice(),t=new kt(e);return t.closePath=this.closePath,t}contains(e,t){let r=!1;const i=this.points.length/2;for(let n=0,o=i-1;n<i;o=n++){const a=this.points[n*2],h=this.points[n*2+1],l=this.points[o*2],c=this.points[o*2+1];h>t!=c>t&&e<(l-a)*((t-h)/(c-h))+a&&(r=!r)}return r}strokeContains(e,t,r,i=.5){const n=r*r,o=n*(1-i),a=n-o,{points:h}=this,l=h.length-(this.closePath?0:2);for(let c=0;c<l;c+=2){const u=h[c],f=h[c+1],d=h[(c+2)%h.length],p=h[(c+3)%h.length],g=Lu(e,t,u,f,d,p),m=Math.sign((d-u)*(t-f)-(p-f)*(e-u));if(g<=(m<0?a:o))return!0}return!1}getBounds(e){e||(e=new N);const t=this.points;let r=1/0,i=-1/0,n=1/0,o=-1/0;for(let a=0,h=t.length;a<h;a+=2){const l=t[a],c=t[a+1];r=l<r?l:r,i=l>i?l:i,n=c<n?c:n,o=c>o?c:o}return e.x=r,e.width=i-r,e.y=n,e.height=o-n,e}copyFrom(e){return this.points=e.points.slice(),this.closePath=e.closePath,this}copyTo(e){return e.copyFrom(this),e}toString(){return`[pixi.js/math:PolygoncloseStroke=${this.closePath}points=${this.points.reduce((e,t)=>`${e}, ${t}`,"")}]`}get lastX(){return this.points[this.points.length-2]}get lastY(){return this.points[this.points.length-1]}get x(){return this.points[this.points.length-2]}get y(){return this.points[this.points.length-1]}}const fr=(s,e,t,r,i,n,o)=>{const a=s-t,h=e-r,l=Math.sqrt(a*a+h*h);return l>=i-n&&l<=i+o};class hi{constructor(e=0,t=0,r=0,i=0,n=20){this.type="roundedRectangle",this.x=e,this.y=t,this.width=r,this.height=i,this.radius=n}getBounds(e){return e||(e=new N),e.x=this.x,e.y=this.y,e.width=this.width,e.height=this.height,e}clone(){return new hi(this.x,this.y,this.width,this.height,this.radius)}copyFrom(e){return this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height,this}copyTo(e){return e.copyFrom(this),e}contains(e,t){if(this.width<=0||this.height<=0)return!1;if(e>=this.x&&e<=this.x+this.width&&t>=this.y&&t<=this.y+this.height){const r=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(t>=this.y+r&&t<=this.y+this.height-r||e>=this.x+r&&e<=this.x+this.width-r)return!0;let i=e-(this.x+r),n=t-(this.y+r);const o=r*r;if(i*i+n*n<=o||(i=e-(this.x+this.width-r),i*i+n*n<=o)||(n=t-(this.y+this.height-r),i*i+n*n<=o)||(i=e-(this.x+r),i*i+n*n<=o))return!0}return!1}strokeContains(e,t,r,i=.5){const{x:n,y:o,width:a,height:h,radius:l}=this,c=r*(1-i),u=r-c,f=n+l,d=o+l,p=a-l*2,g=h-l*2,m=n+a,_=o+h;return(e>=n-c&&e<=n+u||e>=m-u&&e<=m+c)&&t>=d&&t<=d+g||(t>=o-c&&t<=o+u||t>=_-u&&t<=_+c)&&e>=f&&e<=f+p?!0:e<f&&t<d&&fr(e,t,f,d,l,u,c)||e>m-l&&t<d&&fr(e,t,m-l,d,l,u,c)||e>m-l&&t>_-l&&fr(e,t,m-l,_-l,l,u,c)||e<f&&t>_-l&&fr(e,t,f,_-l,l,u,c)}toString(){return`[pixi.js/math:RoundedRectangle x=${this.x} y=${this.y}width=${this.width} height=${this.height} radius=${this.radius}]`}}const Pa={};function li(s,e,t){let r=2166136261;for(let i=0;i<e;i++)r^=s[i].uid,r=Math.imul(r,16777619),r>>>=0;return Pa[r]||zu(s,e,r,t)}function zu(s,e,t,r){const i={};let n=0;for(let a=0;a<r;a++){const h=a<e?s[a]:A.EMPTY.source;i[n++]=h.source,i[n++]=h.style}const o=new Re(i);return Pa[t]=o,o}class at{constructor(e){typeof e=="number"?this.rawBinaryData=new ArrayBuffer(e):e instanceof Uint8Array?this.rawBinaryData=e.buffer:this.rawBinaryData=e,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData),this.size=this.rawBinaryData.byteLength}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}get float64View(){return this._float64Array||(this._float64Array=new Float64Array(this.rawBinaryData)),this._float64Array}get bigUint64View(){return this._bigUint64Array||(this._bigUint64Array=new BigUint64Array(this.rawBinaryData)),this._bigUint64Array}view(e){return this[`${e}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this.uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(e){switch(e){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${e} isn't a valid view type`)}}}function Ns(s,e){const t=s.byteLength/8|0,r=new Float64Array(s,0,t);new Float64Array(e,0,t).set(r);const n=s.byteLength-t*8;if(n>0){const o=new Uint8Array(s,t*8,n);new Uint8Array(e,t*8,n).set(o)}}const Wu={normal:"normal-npm",add:"add-npm",screen:"screen-npm"};var Z=(s=>(s[s.DISABLED=0]="DISABLED",s[s.RENDERING_MASK_ADD=1]="RENDERING_MASK_ADD",s[s.MASK_ACTIVE=2]="MASK_ACTIVE",s[s.INVERSE_MASK_ACTIVE=3]="INVERSE_MASK_ACTIVE",s[s.RENDERING_MASK_REMOVE=4]="RENDERING_MASK_REMOVE",s[s.NONE=5]="NONE",s))(Z||{});function Wt(s,e){return e.alphaMode==="no-premultiply-alpha"&&Wu[s]||s}const Vu=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function $u(s){let e="";for(let t=0;t<s;++t)t>0&&(e+=`
else `),t<s-1&&(e+=`if(test == ${t}.0){}`);return e}function Ca(s,e){if(s===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const t=e.createShader(e.FRAGMENT_SHADER);try{for(;;){const r=Vu.replace(/%forloop%/gi,$u(s));if(e.shaderSource(t,r),e.compileShader(t),!e.getShaderParameter(t,e.COMPILE_STATUS))s=s/2|0;else break}}finally{e.deleteShader(t)}return s}let st=null;function Xu(){if(st)return st;const s=aa();return st=s.getParameter(s.MAX_TEXTURE_IMAGE_UNITS),st=Ca(st,s),s.getExtension("WEBGL_lose_context")?.loseContext(),st}class Yu{constructor(){this.ids=Object.create(null),this.textures=[],this.count=0}clear(){for(let e=0;e<this.count;e++){const t=this.textures[e];this.textures[e]=null,this.ids[t.uid]=null}this.count=0}}class ju{constructor(){this.renderPipeId="batch",this.action="startBatch",this.start=0,this.size=0,this.textures=new Yu,this.blendMode="normal",this.topology="triangle-strip",this.canBundle=!0}destroy(){this.textures=null,this.gpuBindGroup=null,this.bindGroup=null,this.batcher=null}}const Ea=[];let Hs=0;function Bn(){return Hs>0?Ea[--Hs]:new ju}function Rn(s){Ea[Hs++]=s}let Tt=0;const Ma=class Aa{constructor(e){this.uid=V("batcher"),this.dirty=!0,this.batchIndex=0,this.batches=[],this._elements=[],e={...Aa.defaultOptions,...e},e.maxTextures||(I("v8.8.0","maxTextures is a required option for Batcher now, please pass it in the options"),e.maxTextures=Xu());const{maxTextures:t,attributesInitialSize:r,indicesInitialSize:i}=e;this.attributeBuffer=new at(r*4),this.indexBuffer=new Uint16Array(i),this.maxTextures=t}begin(){this.elementSize=0,this.elementStart=0,this.indexSize=0,this.attributeSize=0;for(let e=0;e<this.batchIndex;e++)Rn(this.batches[e]);this.batchIndex=0,this._batchIndexStart=0,this._batchIndexSize=0,this.dirty=!0}add(e){this._elements[this.elementSize++]=e,e._indexStart=this.indexSize,e._attributeStart=this.attributeSize,e._batcher=this,this.indexSize+=e.indexSize,this.attributeSize+=e.attributeSize*this.vertexSize}checkAndUpdateTexture(e,t){const r=e._batch.textures.ids[t._source.uid];return!r&&r!==0?!1:(e._textureId=r,e.texture=t,!0)}updateElement(e){this.dirty=!0;const t=this.attributeBuffer;e.packAsQuad?this.packQuadAttributes(e,t.float32View,t.uint32View,e._attributeStart,e._textureId):this.packAttributes(e,t.float32View,t.uint32View,e._attributeStart,e._textureId)}break(e){const t=this._elements;if(!t[this.elementStart])return;let r=Bn(),i=r.textures;i.clear();const n=t[this.elementStart];let o=Wt(n.blendMode,n.texture._source),a=n.topology;this.attributeSize*4>this.attributeBuffer.size&&this._resizeAttributeBuffer(this.attributeSize*4),this.indexSize>this.indexBuffer.length&&this._resizeIndexBuffer(this.indexSize);const h=this.attributeBuffer.float32View,l=this.attributeBuffer.uint32View,c=this.indexBuffer;let u=this._batchIndexSize,f=this._batchIndexStart,d="startBatch";const p=this.maxTextures;for(let g=this.elementStart;g<this.elementSize;++g){const m=t[g];t[g]=null;const y=m.texture._source,b=Wt(m.blendMode,y),v=o!==b||a!==m.topology;if(y._batchTick===Tt&&!v){m._textureId=y._textureBindLocation,u+=m.indexSize,m.packAsQuad?(this.packQuadAttributes(m,h,l,m._attributeStart,m._textureId),this.packQuadIndex(c,m._indexStart,m._attributeStart/this.vertexSize)):(this.packAttributes(m,h,l,m._attributeStart,m._textureId),this.packIndex(m,c,m._indexStart,m._attributeStart/this.vertexSize)),m._batch=r;continue}y._batchTick=Tt,(i.count>=p||v)&&(this._finishBatch(r,f,u-f,i,o,a,e,d),d="renderBatch",f=u,o=b,a=m.topology,r=Bn(),i=r.textures,i.clear(),++Tt),m._textureId=y._textureBindLocation=i.count,i.ids[y.uid]=i.count,i.textures[i.count++]=y,m._batch=r,u+=m.indexSize,m.packAsQuad?(this.packQuadAttributes(m,h,l,m._attributeStart,m._textureId),this.packQuadIndex(c,m._indexStart,m._attributeStart/this.vertexSize)):(this.packAttributes(m,h,l,m._attributeStart,m._textureId),this.packIndex(m,c,m._indexStart,m._attributeStart/this.vertexSize))}i.count>0&&(this._finishBatch(r,f,u-f,i,o,a,e,d),f=u,++Tt),this.elementStart=this.elementSize,this._batchIndexStart=f,this._batchIndexSize=u}_finishBatch(e,t,r,i,n,o,a,h){e.gpuBindGroup=null,e.bindGroup=null,e.action=h,e.batcher=this,e.textures=i,e.blendMode=n,e.topology=o,e.start=t,e.size=r,++Tt,this.batches[this.batchIndex++]=e,a.add(e)}finish(e){this.break(e)}ensureAttributeBuffer(e){e*4<=this.attributeBuffer.size||this._resizeAttributeBuffer(e*4)}ensureIndexBuffer(e){e<=this.indexBuffer.length||this._resizeIndexBuffer(e)}_resizeAttributeBuffer(e){const t=Math.max(e,this.attributeBuffer.size*2),r=new at(t);Ns(this.attributeBuffer.rawBinaryData,r.rawBinaryData),this.attributeBuffer=r}_resizeIndexBuffer(e){const t=this.indexBuffer;let r=Math.max(e,t.length*1.5);r+=r%2;const i=r>65535?new Uint32Array(r):new Uint16Array(r);if(i.BYTES_PER_ELEMENT!==t.BYTES_PER_ELEMENT)for(let n=0;n<t.length;n++)i[n]=t[n];else Ns(t.buffer,i.buffer);this.indexBuffer=i}packQuadIndex(e,t,r){e[t]=r+0,e[t+1]=r+1,e[t+2]=r+2,e[t+3]=r+0,e[t+4]=r+2,e[t+5]=r+3}packIndex(e,t,r,i){const n=e.indices,o=e.indexSize,a=e.indexOffset,h=e.attributeOffset;for(let l=0;l<o;l++)t[r++]=i+n[l+a]-h}destroy(){for(let e=0;e<this.batches.length;e++)Rn(this.batches[e]);this.batches=null;for(let e=0;e<this._elements.length;e++)this._elements[e]._batch=null;this._elements=null,this.indexBuffer=null,this.attributeBuffer.destroy(),this.attributeBuffer=null}};Ma.defaultOptions={maxTextures:null,attributesInitialSize:4,indicesInitialSize:6};let qu=Ma;var U=(s=>(s[s.MAP_READ=1]="MAP_READ",s[s.MAP_WRITE=2]="MAP_WRITE",s[s.COPY_SRC=4]="COPY_SRC",s[s.COPY_DST=8]="COPY_DST",s[s.INDEX=16]="INDEX",s[s.VERTEX=32]="VERTEX",s[s.UNIFORM=64]="UNIFORM",s[s.STORAGE=128]="STORAGE",s[s.INDIRECT=256]="INDIRECT",s[s.QUERY_RESOLVE=512]="QUERY_RESOLVE",s[s.STATIC=1024]="STATIC",s))(U||{});class ue extends de{constructor(e){let{data:t,size:r}=e;const{usage:i,label:n,shrinkToFit:o}=e;super(),this.uid=V("buffer"),this._resourceType="buffer",this._resourceId=V("resource"),this._touched=0,this._updateID=1,this._dataInt32=null,this.shrinkToFit=!0,this.destroyed=!1,t instanceof Array&&(t=new Float32Array(t)),this._data=t,r??(r=t?.byteLength);const a=!!t;this.descriptor={size:r,usage:i,mappedAtCreation:a,label:n},this.shrinkToFit=o??!0}get data(){return this._data}set data(e){this.setDataWithSize(e,e.length,!0)}get dataInt32(){return this._dataInt32||(this._dataInt32=new Int32Array(this.data.buffer)),this._dataInt32}get static(){return!!(this.descriptor.usage&U.STATIC)}set static(e){e?this.descriptor.usage|=U.STATIC:this.descriptor.usage&=~U.STATIC}setDataWithSize(e,t,r){if(this._updateID++,this._updateSize=t*e.BYTES_PER_ELEMENT,this._data===e){r&&this.emit("update",this);return}const i=this._data;if(this._data=e,this._dataInt32=null,!i||i.length!==e.length){!this.shrinkToFit&&i&&e.byteLength<i.byteLength?r&&this.emit("update",this):(this.descriptor.size=e.byteLength,this._resourceId=V("resource"),this.emit("change",this));return}r&&this.emit("update",this)}update(e){this._updateSize=e??this._updateSize,this._updateID++,this.emit("update",this)}destroy(){this.destroyed=!0,this.emit("destroy",this),this.emit("change",this),this._data=null,this.descriptor=null,this.removeAllListeners()}}function Ba(s,e){if(!(s instanceof ue)){let t=e?U.INDEX:U.VERTEX;s instanceof Array&&(e?(s=new Uint32Array(s),t=U.INDEX|U.COPY_DST):(s=new Float32Array(s),t=U.VERTEX|U.COPY_DST)),s=new ue({data:s,label:e?"index-mesh-buffer":"vertex-mesh-buffer",usage:t})}return s}function Ku(s,e,t){const r=s.getAttribute(e);if(!r)return t.minX=0,t.minY=0,t.maxX=0,t.maxY=0,t;const i=r.buffer.data;let n=1/0,o=1/0,a=-1/0,h=-1/0;const l=i.BYTES_PER_ELEMENT,c=(r.offset||0)/l,u=(r.stride||2*4)/l;for(let f=c;f<i.length;f+=u){const d=i[f],p=i[f+1];d>a&&(a=d),p>h&&(h=p),d<n&&(n=d),p<o&&(o=p)}return t.minX=n,t.minY=o,t.maxX=a,t.maxY=h,t}function Zu(s){return(s instanceof ue||Array.isArray(s)||s.BYTES_PER_ELEMENT)&&(s={buffer:s}),s.buffer=Ba(s.buffer,!1),s}class Xt extends de{constructor(e={}){super(),this.uid=V("geometry"),this._layoutKey=0,this.instanceCount=1,this._bounds=new se,this._boundsDirty=!0;const{attributes:t,indexBuffer:r,topology:i}=e;if(this.buffers=[],this.attributes={},t)for(const n in t)this.addAttribute(n,t[n]);this.instanceCount=e.instanceCount??1,r&&this.addIndex(r),this.topology=i||"triangle-list"}onBufferUpdate(){this._boundsDirty=!0,this.emit("update",this)}getAttribute(e){return this.attributes[e]}getIndex(){return this.indexBuffer}getBuffer(e){return this.getAttribute(e).buffer}getSize(){for(const e in this.attributes){const t=this.attributes[e];return t.buffer.data.length/(t.stride/4||t.size)}return 0}addAttribute(e,t){const r=Zu(t);this.buffers.indexOf(r.buffer)===-1&&(this.buffers.push(r.buffer),r.buffer.on("update",this.onBufferUpdate,this),r.buffer.on("change",this.onBufferUpdate,this)),this.attributes[e]=r}addIndex(e){this.indexBuffer=Ba(e,!0),this.buffers.push(this.indexBuffer)}get bounds(){return this._boundsDirty?(this._boundsDirty=!1,Ku(this,"aPosition",this._bounds)):this._bounds}destroy(e=!1){this.emit("destroy",this),this.removeAllListeners(),e&&this.buffers.forEach(t=>t.destroy()),this.attributes=null,this.buffers=null,this.indexBuffer=null,this._bounds=null}}const Qu=new Float32Array(1),Ju=new Uint32Array(1);class ed extends Xt{constructor(){const t=new ue({data:Qu,label:"attribute-batch-buffer",usage:U.VERTEX|U.COPY_DST,shrinkToFit:!1}),r=new ue({data:Ju,label:"index-batch-buffer",usage:U.INDEX|U.COPY_DST,shrinkToFit:!1}),i=6*4;super({attributes:{aPosition:{buffer:t,format:"float32x2",stride:i,offset:0},aUV:{buffer:t,format:"float32x2",stride:i,offset:2*4},aColor:{buffer:t,format:"unorm8x4",stride:i,offset:4*4},aTextureIdAndRound:{buffer:t,format:"uint16x2",stride:i,offset:5*4}},indexBuffer:r})}}function kn(s,e,t){if(s)for(const r in s){const i=r.toLocaleLowerCase(),n=e[i];if(n){let o=s[r];r==="header"&&(o=o.replace(/@in\s+[^;]+;\s*/g,"").replace(/@out\s+[^;]+;\s*/g,"")),t&&n.push(`//----${t}----//`),n.push(o)}else F(`${r} placement hook does not exist in shader`)}}const td=/\{\{(.*?)\}\}/g;function Gn(s){const e={};return(s.match(td)?.map(r=>r.replace(/[{()}]/g,""))??[]).forEach(r=>{e[r]=[]}),e}function In(s,e){let t;const r=/@in\s+([^;]+);/g;for(;(t=r.exec(s))!==null;)e.push(t[1])}function Fn(s,e,t=!1){const r=[];In(e,r),s.forEach(a=>{a.header&&In(a.header,r)});const i=r;t&&i.sort();const n=i.map((a,h)=>`       @location(${h}) ${a},`).join(`
`);let o=e.replace(/@in\s+[^;]+;\s*/g,"");return o=o.replace("{{in}}",`
${n}
`),o}function Un(s,e){let t;const r=/@out\s+([^;]+);/g;for(;(t=r.exec(s))!==null;)e.push(t[1])}function rd(s){const t=/\b(\w+)\s*:/g.exec(s);return t?t[1]:""}function sd(s){const e=/@.*?\s+/g;return s.replace(e,"")}function id(s,e){const t=[];Un(e,t),s.forEach(h=>{h.header&&Un(h.header,t)});let r=0;const i=t.sort().map(h=>h.indexOf("builtin")>-1?h:`@location(${r++}) ${h}`).join(`,
`),n=t.sort().map(h=>`       var ${sd(h)};`).join(`
`),o=`return VSOutput(
            ${t.sort().map(h=>` ${rd(h)}`).join(`,
`)});`;let a=e.replace(/@out\s+[^;]+;\s*/g,"");return a=a.replace("{{struct}}",`
${i}
`),a=a.replace("{{start}}",`
${n}
`),a=a.replace("{{return}}",`
${o}
`),a}function Dn(s,e){let t=s;for(const r in e){const i=e[r];i.join(`
`).length?t=t.replace(`{{${r}}}`,`//-----${r} START-----//
${i.join(`
`)}
//----${r} FINISH----//`):t=t.replace(`{{${r}}}`,"")}return t}const Ue=Object.create(null),ds=new Map;let nd=0;function od({template:s,bits:e}){const t=Ra(s,e);if(Ue[t])return Ue[t];const{vertex:r,fragment:i}=hd(s,e);return Ue[t]=ka(r,i,e),Ue[t]}function ad({template:s,bits:e}){const t=Ra(s,e);return Ue[t]||(Ue[t]=ka(s.vertex,s.fragment,e)),Ue[t]}function hd(s,e){const t=e.map(o=>o.vertex).filter(o=>!!o),r=e.map(o=>o.fragment).filter(o=>!!o);let i=Fn(t,s.vertex,!0);i=id(t,i);const n=Fn(r,s.fragment,!0);return{vertex:i,fragment:n}}function Ra(s,e){return e.map(t=>(ds.has(t)||ds.set(t,nd++),ds.get(t))).sort((t,r)=>t-r).join("-")+s.vertex+s.fragment}function ka(s,e,t){const r=Gn(s),i=Gn(e);return t.forEach(n=>{kn(n.vertex,r,n.name),kn(n.fragment,i,n.name)}),{vertex:Dn(s,r),fragment:Dn(e,i)}}const ld=`
    @in aPosition: vec2<f32>;
    @in aUV: vec2<f32>;

    @out @builtin(position) vPosition: vec4<f32>;
    @out vUV : vec2<f32>;
    @out vColor : vec4<f32>;

    {{header}}

    struct VSOutput {
        {{struct}}
    };

    @vertex
    fn main( {{in}} ) -> VSOutput {

        var worldTransformMatrix = globalUniforms.uWorldTransformMatrix;
        var modelMatrix = mat3x3<f32>(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        var position = aPosition;
        var uv = aUV;

        {{start}}

        vColor = vec4<f32>(1., 1., 1., 1.);

        {{main}}

        vUV = uv;

        var modelViewProjectionMatrix = globalUniforms.uProjectionMatrix * worldTransformMatrix * modelMatrix;

        vPosition =  vec4<f32>((modelViewProjectionMatrix *  vec3<f32>(position, 1.0)).xy, 0.0, 1.0);

        vColor *= globalUniforms.uWorldColorAlpha;

        {{end}}

        {{return}}
    };
`,cd=`
    @in vUV : vec2<f32>;
    @in vColor : vec4<f32>;

    {{header}}

    @fragment
    fn main(
        {{in}}
      ) -> @location(0) vec4<f32> {

        {{start}}

        var outColor:vec4<f32>;

        {{main}}

        var finalColor:vec4<f32> = outColor * vColor;

        {{end}}

        return finalColor;
      };
`,ud=`
    in vec2 aPosition;
    in vec2 aUV;

    out vec4 vColor;
    out vec2 vUV;

    {{header}}

    void main(void){

        mat3 worldTransformMatrix = uWorldTransformMatrix;
        mat3 modelMatrix = mat3(
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0
          );
        vec2 position = aPosition;
        vec2 uv = aUV;

        {{start}}

        vColor = vec4(1.);

        {{main}}

        vUV = uv;

        mat3 modelViewProjectionMatrix = uProjectionMatrix * worldTransformMatrix * modelMatrix;

        gl_Position = vec4((modelViewProjectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);

        vColor *= uWorldColorAlpha;

        {{end}}
    }
`,dd=`

    in vec4 vColor;
    in vec2 vUV;

    out vec4 finalColor;

    {{header}}

    void main(void) {

        {{start}}

        vec4 outColor;

        {{main}}

        finalColor = outColor * vColor;

        {{end}}
    }
`,fd={name:"global-uniforms-bit",vertex:{header:`
        struct GlobalUniforms {
            uProjectionMatrix:mat3x3<f32>,
            uWorldTransformMatrix:mat3x3<f32>,
            uWorldColorAlpha: vec4<f32>,
            uResolution: vec2<f32>,
        }

        @group(0) @binding(0) var<uniform> globalUniforms : GlobalUniforms;
        `}},pd={name:"global-uniforms-bit",vertex:{header:`
          uniform mat3 uProjectionMatrix;
          uniform mat3 uWorldTransformMatrix;
          uniform vec4 uWorldColorAlpha;
          uniform vec2 uResolution;
        `}};function Yt({bits:s,name:e}){const t=od({template:{fragment:cd,vertex:ld},bits:[fd,...s]});return tt.from({name:e,vertex:{source:t.vertex,entryPoint:"main"},fragment:{source:t.fragment,entryPoint:"main"}})}function jt({bits:s,name:e}){return new pt({name:e,...ad({template:{vertex:ud,fragment:dd},bits:[pd,...s]})})}const ci={name:"color-bit",vertex:{header:`
            @in aColor: vec4<f32>;
        `,main:`
            vColor *= vec4<f32>(aColor.rgb * aColor.a, aColor.a);
        `}},ui={name:"color-bit",vertex:{header:`
            in vec4 aColor;
        `,main:`
            vColor *= vec4(aColor.rgb * aColor.a, aColor.a);
        `}},fs={};function md(s){const e=[];if(s===1)e.push("@group(1) @binding(0) var textureSource1: texture_2d<f32>;"),e.push("@group(1) @binding(1) var textureSampler1: sampler;");else{let t=0;for(let r=0;r<s;r++)e.push(`@group(1) @binding(${t++}) var textureSource${r+1}: texture_2d<f32>;`),e.push(`@group(1) @binding(${t++}) var textureSampler${r+1}: sampler;`)}return e.join(`
`)}function gd(s){const e=[];if(s===1)e.push("outColor = textureSampleGrad(textureSource1, textureSampler1, vUV, uvDx, uvDy);");else{e.push("switch vTextureId {");for(let t=0;t<s;t++)t===s-1?e.push("  default:{"):e.push(`  case ${t}:{`),e.push(`      outColor = textureSampleGrad(textureSource${t+1}, textureSampler${t+1}, vUV, uvDx, uvDy);`),e.push("      break;}");e.push("}")}return e.join(`
`)}function di(s){return fs[s]||(fs[s]={name:"texture-batch-bit",vertex:{header:`
                @in aTextureIdAndRound: vec2<u32>;
                @out @interpolate(flat) vTextureId : u32;
            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1)
                {
                    vPosition = vec4<f32>(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
                }
            `},fragment:{header:`
                @in @interpolate(flat) vTextureId: u32;

                ${md(s)}
            `,main:`
                var uvDx = dpdx(vUV);
                var uvDy = dpdy(vUV);

                ${gd(s)}
            `}}),fs[s]}const ps={};function _d(s){const e=[];for(let t=0;t<s;t++)t>0&&e.push("else"),t<s-1&&e.push(`if(vTextureId < ${t}.5)`),e.push("{"),e.push(`	outColor = texture(uTextures[${t}], vUV);`),e.push("}");return e.join(`
`)}function fi(s){return ps[s]||(ps[s]={name:"texture-batch-bit",vertex:{header:`
                in vec2 aTextureIdAndRound;
                out float vTextureId;

            `,main:`
                vTextureId = aTextureIdAndRound.y;
            `,end:`
                if(aTextureIdAndRound.x == 1.)
                {
                    gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
                }
            `},fragment:{header:`
                in float vTextureId;

                uniform sampler2D uTextures[${s}];

            `,main:`

                ${_d(s)}
            `}}),ps[s]}const qt={name:"round-pixels-bit",vertex:{header:`
            fn roundPixels(position: vec2<f32>, targetSize: vec2<f32>) -> vec2<f32>
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},Kt={name:"round-pixels-bit",vertex:{header:`
            vec2 roundPixels(vec2 position, vec2 targetSize)
            {
                return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
            }
        `}},On={};function pi(s){let e=On[s];if(e)return e;const t=new Int32Array(s);for(let r=0;r<s;r++)t[r]=r;return e=On[s]=new ne({uTextures:{value:t,type:"i32",size:s}},{isStatic:!0}),e}class xd extends pe{constructor(e){const t=jt({name:"batch",bits:[ui,fi(e),Kt]}),r=Yt({name:"batch",bits:[ci,di(e),qt]});super({glProgram:t,gpuProgram:r,resources:{batchSamplers:pi(e)}})}}let ms=null;const Ga=class Ia extends qu{constructor(e){super(e),this.geometry=new ed,this.name=Ia.extension.name,this.vertexSize=6,ms??(ms=new xd(e.maxTextures)),this.shader=ms}packAttributes(e,t,r,i,n){const o=n<<16|e.roundPixels&65535,a=e.transform,h=a.a,l=a.b,c=a.c,u=a.d,f=a.tx,d=a.ty,{positions:p,uvs:g}=e,m=e.color,_=e.attributeOffset,y=_+e.attributeSize;for(let b=_;b<y;b++){const v=b*2,w=p[v],S=p[v+1];t[i++]=h*w+c*S+f,t[i++]=u*S+l*w+d,t[i++]=g[v],t[i++]=g[v+1],r[i++]=m,r[i++]=o}}packQuadAttributes(e,t,r,i,n){const o=e.texture,a=e.transform,h=a.a,l=a.b,c=a.c,u=a.d,f=a.tx,d=a.ty,p=e.bounds,g=p.maxX,m=p.minX,_=p.maxY,y=p.minY,b=o.uvs,v=e.color,w=n<<16|e.roundPixels&65535;t[i+0]=h*m+c*y+f,t[i+1]=u*y+l*m+d,t[i+2]=b.x0,t[i+3]=b.y0,r[i+4]=v,r[i+5]=w,t[i+6]=h*g+c*y+f,t[i+7]=u*y+l*g+d,t[i+8]=b.x1,t[i+9]=b.y1,r[i+10]=v,r[i+11]=w,t[i+12]=h*g+c*_+f,t[i+13]=u*_+l*g+d,t[i+14]=b.x2,t[i+15]=b.y2,r[i+16]=v,r[i+17]=w,t[i+18]=h*m+c*_+f,t[i+19]=u*_+l*m+d,t[i+20]=b.x3,t[i+21]=b.y3,r[i+22]=v,r[i+23]=w}};Ga.extension={type:[x.Batcher],name:"default"};let mi=Ga;function yd(s,e,t,r,i,n,o,a=null){let h=0;t*=e,i*=n;const l=a.a,c=a.b,u=a.c,f=a.d,d=a.tx,p=a.ty;for(;h<o;){const g=s[t],m=s[t+1];r[i]=l*g+u*m+d,r[i+1]=c*g+f*m+p,i+=n,t+=e,h++}}function bd(s,e,t,r){let i=0;for(e*=t;i<r;)s[e]=0,s[e+1]=0,e+=t,i++}function Fa(s,e,t,r,i){const n=e.a,o=e.b,a=e.c,h=e.d,l=e.tx,c=e.ty;t||(t=0),r||(r=2),i||(i=s.length/r-t);let u=t*r;for(let f=0;f<i;f++){const d=s[u],p=s[u+1];s[u]=n*d+a*p+l,s[u+1]=o*d+h*p+c,u+=r}}const vd=new B;class gi{constructor(){this.packAsQuad=!1,this.batcherName="default",this.topology="triangle-list",this.applyTransform=!0,this.roundPixels=0,this._batcher=null,this._batch=null}get uvs(){return this.geometryData.uvs}get positions(){return this.geometryData.vertices}get indices(){return this.geometryData.indices}get blendMode(){return this.renderable&&this.applyTransform?this.renderable.groupBlendMode:"normal"}get color(){const e=this.baseColor,t=e>>16|e&65280|(e&255)<<16,r=this.renderable;return r?Ho(t,r.groupColor)+(this.alpha*r.groupAlpha*255<<24):t+(this.alpha*255<<24)}get transform(){return this.renderable?.groupTransform||vd}copyTo(e){e.indexOffset=this.indexOffset,e.indexSize=this.indexSize,e.attributeOffset=this.attributeOffset,e.attributeSize=this.attributeSize,e.baseColor=this.baseColor,e.alpha=this.alpha,e.texture=this.texture,e.geometryData=this.geometryData,e.topology=this.topology}reset(){this.applyTransform=!0,this.renderable=null,this.topology="triangle-list"}}const Vt={extension:{type:x.ShapeBuilder,name:"circle"},build(s,e){let t,r,i,n,o,a;if(s.type==="circle"){const v=s;if(o=a=v.radius,o<=0)return!1;t=v.x,r=v.y,i=n=0}else if(s.type==="ellipse"){const v=s;if(o=v.halfWidth,a=v.halfHeight,o<=0||a<=0)return!1;t=v.x,r=v.y,i=n=0}else{const v=s,w=v.width/2,S=v.height/2;t=v.x+w,r=v.y+S,o=a=Math.max(0,Math.min(v.radius,Math.min(w,S))),i=w-o,n=S-a}if(i<0||n<0)return!1;const h=Math.ceil(2.3*Math.sqrt(o+a)),l=h*8+(i?4:0)+(n?4:0);if(l===0)return!1;if(h===0)return e[0]=e[6]=t+i,e[1]=e[3]=r+n,e[2]=e[4]=t-i,e[5]=e[7]=r-n,!0;let c=0,u=h*4+(i?2:0)+2,f=u,d=l,p=i+o,g=n,m=t+p,_=t-p,y=r+g;if(e[c++]=m,e[c++]=y,e[--u]=y,e[--u]=_,n){const v=r-g;e[f++]=_,e[f++]=v,e[--d]=v,e[--d]=m}for(let v=1;v<h;v++){const w=Math.PI/2*(v/h),S=i+Math.cos(w)*o,T=n+Math.sin(w)*a,k=t+S,C=t-S,P=r+T,E=r-T;e[c++]=k,e[c++]=P,e[--u]=P,e[--u]=C,e[f++]=C,e[f++]=E,e[--d]=E,e[--d]=k}p=i,g=n+a,m=t+p,_=t-p,y=r+g;const b=r-g;return e[c++]=m,e[c++]=y,e[--d]=b,e[--d]=m,i&&(e[c++]=_,e[c++]=y,e[--d]=b,e[--d]=_),!0},triangulate(s,e,t,r,i,n){if(s.length===0)return;let o=0,a=0;for(let c=0;c<s.length;c+=2)o+=s[c],a+=s[c+1];o/=s.length/2,a/=s.length/2;let h=r;e[h*t]=o,e[h*t+1]=a;const l=h++;for(let c=0;c<s.length;c+=2)e[h*t]=s[c],e[h*t+1]=s[c+1],c>0&&(i[n++]=h,i[n++]=l,i[n++]=h-1),h++;i[n++]=l+1,i[n++]=l,i[n++]=h-1}},Td={...Vt,extension:{...Vt.extension,name:"ellipse"}},Sd={...Vt,extension:{...Vt.extension,name:"roundedRectangle"}},Ua=1e-4,Ln=1e-4;function wd(s){const e=s.length;if(e<6)return 1;let t=0;for(let r=0,i=s[e-2],n=s[e-1];r<e;r+=2){const o=s[r],a=s[r+1];t+=(o-i)*(a+n),i=o,n=a}return t<0?-1:1}function Nn(s,e,t,r,i,n,o,a){const h=s-t*i,l=e-r*i,c=s+t*n,u=e+r*n;let f,d;o?(f=r,d=-t):(f=-r,d=t);const p=h+f,g=l+d,m=c+f,_=u+d;return a.push(p,g),a.push(m,_),2}function $e(s,e,t,r,i,n,o,a){const h=t-s,l=r-e;let c=Math.atan2(h,l),u=Math.atan2(i-s,n-e);a&&c<u?c+=Math.PI*2:!a&&c>u&&(u+=Math.PI*2);let f=c;const d=u-c,p=Math.abs(d),g=Math.sqrt(h*h+l*l),m=(15*p*Math.sqrt(g)/Math.PI>>0)+1,_=d/m;if(f+=_,a){o.push(s,e),o.push(t,r);for(let y=1,b=f;y<m;y++,b+=_)o.push(s,e),o.push(s+Math.sin(b)*g,e+Math.cos(b)*g);o.push(s,e),o.push(i,n)}else{o.push(t,r),o.push(s,e);for(let y=1,b=f;y<m;y++,b+=_)o.push(s+Math.sin(b)*g,e+Math.cos(b)*g),o.push(s,e);o.push(i,n),o.push(s,e)}return m*2}function Pd(s,e,t,r,i,n){const o=Ua;if(s.length===0)return;const a=e;let h=a.alignment;if(e.alignment!==.5){let W=wd(s);h=(h-.5)*W+.5}const l=new H(s[0],s[1]),c=new H(s[s.length-2],s[s.length-1]),u=r,f=Math.abs(l.x-c.x)<o&&Math.abs(l.y-c.y)<o;if(u){s=s.slice(),f&&(s.pop(),s.pop(),c.set(s[s.length-2],s[s.length-1]));const W=(l.x+c.x)*.5,Ge=(c.y+l.y)*.5;s.unshift(W,Ge),s.push(W,Ge)}const d=i,p=s.length/2;let g=s.length;const m=d.length/2,_=a.width/2,y=_*_,b=a.miterLimit*a.miterLimit;let v=s[0],w=s[1],S=s[2],T=s[3],k=0,C=0,P=-(w-T),E=v-S,X=0,z=0,oe=Math.sqrt(P*P+E*E);P/=oe,E/=oe,P*=_,E*=_;const mt=h,R=(1-mt)*2,G=mt*2;u||(a.cap==="round"?g+=$e(v-P*(R-G)*.5,w-E*(R-G)*.5,v-P*R,w-E*R,v+P*G,w+E*G,d,!0)+2:a.cap==="square"&&(g+=Nn(v,w,P,E,R,G,!0,d))),d.push(v-P*R,w-E*R),d.push(v+P*G,w+E*G);for(let W=1;W<p-1;++W){v=s[(W-1)*2],w=s[(W-1)*2+1],S=s[W*2],T=s[W*2+1],k=s[(W+1)*2],C=s[(W+1)*2+1],P=-(w-T),E=v-S,oe=Math.sqrt(P*P+E*E),P/=oe,E/=oe,P*=_,E*=_,X=-(T-C),z=S-k,oe=Math.sqrt(X*X+z*z),X/=oe,z/=oe,X*=_,z*=_;const Ge=S-v,gt=w-T,_t=S-k,xt=C-T,Di=Ge*_t+gt*xt,Qt=gt*_t-xt*Ge,yt=Qt<0;if(Math.abs(Qt)<.001*Math.abs(Di)){d.push(S-P*R,T-E*R),d.push(S+P*G,T+E*G),Di>=0&&(a.join==="round"?g+=$e(S,T,S-P*R,T-E*R,S-X*R,T-z*R,d,!1)+4:g+=2,d.push(S-X*G,T-z*G),d.push(S+X*R,T+z*R));continue}const Oi=(-P+v)*(-E+T)-(-P+S)*(-E+w),Li=(-X+k)*(-z+T)-(-X+S)*(-z+C),Jt=(Ge*Li-_t*Oi)/Qt,er=(xt*Oi-gt*Li)/Qt,Lr=(Jt-S)*(Jt-S)+(er-T)*(er-T),He=S+(Jt-S)*R,ze=T+(er-T)*R,We=S-(Jt-S)*G,Ve=T-(er-T)*G,El=Math.min(Ge*Ge+gt*gt,_t*_t+xt*xt),Ni=yt?R:G,Ml=El+Ni*Ni*y;Lr<=Ml?a.join==="bevel"||Lr/y>b?(yt?(d.push(He,ze),d.push(S+P*G,T+E*G),d.push(He,ze),d.push(S+X*G,T+z*G)):(d.push(S-P*R,T-E*R),d.push(We,Ve),d.push(S-X*R,T-z*R),d.push(We,Ve)),g+=2):a.join==="round"?yt?(d.push(He,ze),d.push(S+P*G,T+E*G),g+=$e(S,T,S+P*G,T+E*G,S+X*G,T+z*G,d,!0)+4,d.push(He,ze),d.push(S+X*G,T+z*G)):(d.push(S-P*R,T-E*R),d.push(We,Ve),g+=$e(S,T,S-P*R,T-E*R,S-X*R,T-z*R,d,!1)+4,d.push(S-X*R,T-z*R),d.push(We,Ve)):(d.push(He,ze),d.push(We,Ve)):(d.push(S-P*R,T-E*R),d.push(S+P*G,T+E*G),a.join==="round"?yt?g+=$e(S,T,S+P*G,T+E*G,S+X*G,T+z*G,d,!0)+2:g+=$e(S,T,S-P*R,T-E*R,S-X*R,T-z*R,d,!1)+2:a.join==="miter"&&Lr/y<=b&&(yt?(d.push(We,Ve),d.push(We,Ve)):(d.push(He,ze),d.push(He,ze)),g+=2),d.push(S-X*R,T-z*R),d.push(S+X*G,T+z*G),g+=2)}v=s[(p-2)*2],w=s[(p-2)*2+1],S=s[(p-1)*2],T=s[(p-1)*2+1],P=-(w-T),E=v-S,oe=Math.sqrt(P*P+E*E),P/=oe,E/=oe,P*=_,E*=_,d.push(S-P*R,T-E*R),d.push(S+P*G,T+E*G),u||(a.cap==="round"?g+=$e(S-P*(R-G)*.5,T-E*(R-G)*.5,S-P*R,T-E*R,S+P*G,T+E*G,d,!1)+2:a.cap==="square"&&(g+=Nn(S,T,P,E,R,G,!1,d)));const Cl=Ln*Ln;for(let W=m;W<g+m-2;++W)v=d[W*2],w=d[W*2+1],S=d[(W+1)*2],T=d[(W+1)*2+1],k=d[(W+2)*2],C=d[(W+2)*2+1],!(Math.abs(v*(T-C)+S*(C-w)+k*(w-T))<Cl)&&n.push(W,W+1,W+2)}function Cd(s,e,t,r){const i=Ua;if(s.length===0)return;const n=s[0],o=s[1],a=s[s.length-2],h=s[s.length-1],l=e||Math.abs(n-a)<i&&Math.abs(o-h)<i,c=t,u=s.length/2,f=c.length/2;for(let d=0;d<u;d++)c.push(s[d*2]),c.push(s[d*2+1]);for(let d=0;d<u-1;d++)r.push(f+d,f+d+1);l&&r.push(f+u-1,f)}function Da(s,e,t,r,i,n,o){const a=vu(s,e,2);if(!a)return;for(let l=0;l<a.length;l+=3)n[o++]=a[l]+i,n[o++]=a[l+1]+i,n[o++]=a[l+2]+i;let h=i*r;for(let l=0;l<s.length;l+=2)t[h]=s[l],t[h+1]=s[l+1],h+=r}const Ed=[],Md={extension:{type:x.ShapeBuilder,name:"polygon"},build(s,e){for(let t=0;t<s.points.length;t++)e[t]=s.points[t];return!0},triangulate(s,e,t,r,i,n){Da(s,Ed,e,t,r,i,n)}},Ad={extension:{type:x.ShapeBuilder,name:"rectangle"},build(s,e){const t=s,r=t.x,i=t.y,n=t.width,o=t.height;return n>0&&o>0?(e[0]=r,e[1]=i,e[2]=r+n,e[3]=i,e[4]=r+n,e[5]=i+o,e[6]=r,e[7]=i+o,!0):!1},triangulate(s,e,t,r,i,n){let o=0;r*=t,e[r+o]=s[0],e[r+o+1]=s[1],o+=t,e[r+o]=s[2],e[r+o+1]=s[3],o+=t,e[r+o]=s[6],e[r+o+1]=s[7],o+=t,e[r+o]=s[4],e[r+o+1]=s[5],o+=t;const a=r/t;i[n++]=a,i[n++]=a+1,i[n++]=a+2,i[n++]=a+1,i[n++]=a+3,i[n++]=a+2}},Bd={extension:{type:x.ShapeBuilder,name:"triangle"},build(s,e){return e[0]=s.x,e[1]=s.y,e[2]=s.x2,e[3]=s.y2,e[4]=s.x3,e[5]=s.y3,!0},triangulate(s,e,t,r,i,n){let o=0;r*=t,e[r+o]=s[0],e[r+o+1]=s[1],o+=t,e[r+o]=s[2],e[r+o+1]=s[3],o+=t,e[r+o]=s[4],e[r+o+1]=s[5];const a=r/t;i[n++]=a,i[n++]=a+1,i[n++]=a+2}},Rd=new B,kd=new N;function Gd(s,e,t,r){const i=e.matrix?s.copyFrom(e.matrix).invert():s.identity();if(e.textureSpace==="local"){const o=t.getBounds(kd);e.width&&o.pad(e.width);const{x:a,y:h}=o,l=1/o.width,c=1/o.height,u=-a*l,f=-h*c,d=i.a,p=i.b,g=i.c,m=i.d;i.a*=l,i.b*=l,i.c*=c,i.d*=c,i.tx=u*d+f*g+i.tx,i.ty=u*p+f*m+i.ty}else i.translate(e.texture.frame.x,e.texture.frame.y),i.scale(1/e.texture.source.width,1/e.texture.source.height);const n=e.texture.source.style;return!(e.fill instanceof ke)&&n.addressMode==="clamp-to-edge"&&(n.addressMode="repeat",n.update()),r&&i.append(Rd.copyFrom(r).invert()),i}const Dr={};q.handleByMap(x.ShapeBuilder,Dr);q.add(Ad,Md,Bd,Vt,Td,Sd);const Id=new N,Fd=new B;function Ud(s,e){const{geometryData:t,batches:r}=e;r.length=0,t.indices.length=0,t.vertices.length=0,t.uvs.length=0;for(let i=0;i<s.instructions.length;i++){const n=s.instructions[i];if(n.action==="texture")Dd(n.data,r,t);else if(n.action==="fill"||n.action==="stroke"){const o=n.action==="stroke",a=n.data.path.shapePath,h=n.data.style,l=n.data.hole;o&&l&&Hn(l.shapePath,h,!0,r,t),l&&(a.shapePrimitives[a.shapePrimitives.length-1].holes=l.shapePath.shapePrimitives),Hn(a,h,o,r,t)}}}function Dd(s,e,t){const r=[],i=Dr.rectangle,n=Id;n.x=s.dx,n.y=s.dy,n.width=s.dw,n.height=s.dh;const o=s.transform;if(!i.build(n,r))return;const{vertices:a,uvs:h,indices:l}=t,c=l.length,u=a.length/2;o&&Fa(r,o),i.triangulate(r,a,2,u,l,c);const f=s.image,d=f.uvs;h.push(d.x0,d.y0,d.x1,d.y1,d.x3,d.y3,d.x2,d.y2);const p=ee.get(gi);p.indexOffset=c,p.indexSize=l.length-c,p.attributeOffset=u,p.attributeSize=a.length/2-u,p.baseColor=s.style,p.alpha=s.alpha,p.texture=f,p.geometryData=t,e.push(p)}function Hn(s,e,t,r,i){const{vertices:n,uvs:o,indices:a}=i;s.shapePrimitives.forEach(({shape:h,transform:l,holes:c})=>{const u=[],f=Dr[h.type];if(!f.build(h,u))return;const d=a.length,p=n.length/2;let g="triangle-list";if(l&&Fa(u,l),t){const b=h.closePath??!0,v=e;v.pixelLine?(Cd(u,b,n,a),g="line-list"):Pd(u,v,!1,b,n,a)}else if(c){const b=[],v=u.slice();Od(c).forEach(S=>{b.push(v.length/2),v.push(...S)}),Da(v,b,n,2,p,a,d)}else f.triangulate(u,n,2,p,a,d);const m=o.length/2,_=e.texture;if(_!==A.WHITE){const b=Gd(Fd,e,h,l);yd(n,2,p,o,m,2,n.length/2-p,b)}else bd(o,m,2,n.length/2-p);const y=ee.get(gi);y.indexOffset=d,y.indexSize=a.length-d,y.attributeOffset=p,y.attributeSize=n.length/2-p,y.baseColor=e.color,y.alpha=e.alpha,y.texture=_,y.geometryData=i,y.topology=g,r.push(y)})}function Od(s){const e=[];for(let t=0;t<s.length;t++){const r=s[t].shape,i=[];Dr[r.type].build(r,i)&&e.push(i)}return e}class Ld{constructor(){this.batches=[],this.geometryData={vertices:[],uvs:[],indices:[]}}}class Nd{constructor(){this.instructions=new Vo}init(e){this.batcher=new mi({maxTextures:e}),this.instructions.reset()}get geometry(){return I(Nl,"GraphicsContextRenderData#geometry is deprecated, please use batcher.geometry instead."),this.batcher.geometry}}const _i=class zs{constructor(e){this._gpuContextHash={},this._graphicsDataContextHash=Object.create(null),this._renderer=e,e.renderableGC.addManagedHash(this,"_gpuContextHash"),e.renderableGC.addManagedHash(this,"_graphicsDataContextHash")}init(e){zs.defaultOptions.bezierSmoothness=e?.bezierSmoothness??zs.defaultOptions.bezierSmoothness}getContextRenderData(e){return this._graphicsDataContextHash[e.uid]||this._initContextRenderData(e)}updateGpuContext(e){let t=this._gpuContextHash[e.uid]||this._initContext(e);if(e.dirty){t?this._cleanGraphicsContextData(e):t=this._initContext(e),Ud(e,t);const r=e.batchMode;e.customShader||r==="no-batch"?t.isBatchable=!1:r==="auto"?t.isBatchable=t.geometryData.vertices.length<400:t.isBatchable=!0,e.dirty=!1}return t}getGpuContext(e){return this._gpuContextHash[e.uid]||this._initContext(e)}_initContextRenderData(e){const t=ee.get(Nd,{maxTextures:this._renderer.limits.maxBatchableTextures}),{batches:r,geometryData:i}=this._gpuContextHash[e.uid],n=i.vertices.length,o=i.indices.length;for(let c=0;c<r.length;c++)r[c].applyTransform=!1;const a=t.batcher;a.ensureAttributeBuffer(n),a.ensureIndexBuffer(o),a.begin();for(let c=0;c<r.length;c++){const u=r[c];a.add(u)}a.finish(t.instructions);const h=a.geometry;h.indexBuffer.setDataWithSize(a.indexBuffer,a.indexSize,!0),h.buffers[0].setDataWithSize(a.attributeBuffer.float32View,a.attributeSize,!0);const l=a.batches;for(let c=0;c<l.length;c++){const u=l[c];u.bindGroup=li(u.textures.textures,u.textures.count,this._renderer.limits.maxBatchableTextures)}return this._graphicsDataContextHash[e.uid]=t,t}_initContext(e){const t=new Ld;return t.context=e,this._gpuContextHash[e.uid]=t,e.on("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[e.uid]}onGraphicsContextDestroy(e){this._cleanGraphicsContextData(e),e.off("destroy",this.onGraphicsContextDestroy,this),this._gpuContextHash[e.uid]=null}_cleanGraphicsContextData(e){const t=this._gpuContextHash[e.uid];t.isBatchable||this._graphicsDataContextHash[e.uid]&&(ee.return(this.getContextRenderData(e)),this._graphicsDataContextHash[e.uid]=null),t.batches&&t.batches.forEach(r=>{ee.return(r)})}destroy(){for(const e in this._gpuContextHash)this._gpuContextHash[e]&&this.onGraphicsContextDestroy(this._gpuContextHash[e].context)}};_i.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"graphicsContext"};_i.defaultOptions={bezierSmoothness:.5};let Oa=_i;const Hd=8,pr=11920929e-14,zd=1;function La(s,e,t,r,i,n,o,a,h,l){const u=Math.min(.99,Math.max(0,l??Oa.defaultOptions.bezierSmoothness));let f=(zd-u)/1;return f*=f,Wd(e,t,r,i,n,o,a,h,s,f),s}function Wd(s,e,t,r,i,n,o,a,h,l){Ws(s,e,t,r,i,n,o,a,h,l,0),h.push(o,a)}function Ws(s,e,t,r,i,n,o,a,h,l,c){if(c>Hd)return;const u=(s+t)/2,f=(e+r)/2,d=(t+i)/2,p=(r+n)/2,g=(i+o)/2,m=(n+a)/2,_=(u+d)/2,y=(f+p)/2,b=(d+g)/2,v=(p+m)/2,w=(_+b)/2,S=(y+v)/2;if(c>0){let T=o-s,k=a-e;const C=Math.abs((t-o)*k-(r-a)*T),P=Math.abs((i-o)*k-(n-a)*T);if(C>pr&&P>pr){if((C+P)*(C+P)<=l*(T*T+k*k)){h.push(w,S);return}}else if(C>pr){if(C*C<=l*(T*T+k*k)){h.push(w,S);return}}else if(P>pr){if(P*P<=l*(T*T+k*k)){h.push(w,S);return}}else if(T=w-(s+o)/2,k=S-(e+a)/2,T*T+k*k<=l){h.push(w,S);return}}Ws(s,e,u,f,_,y,w,S,h,l,c+1),Ws(w,S,b,v,g,m,o,a,h,l,c+1)}const Vd=8,$d=11920929e-14,Xd=1;function Yd(s,e,t,r,i,n,o,a){const l=Math.min(.99,Math.max(0,a??Oa.defaultOptions.bezierSmoothness));let c=(Xd-l)/1;return c*=c,jd(e,t,r,i,n,o,s,c),s}function jd(s,e,t,r,i,n,o,a){Vs(o,s,e,t,r,i,n,a,0),o.push(i,n)}function Vs(s,e,t,r,i,n,o,a,h){if(h>Vd)return;const l=(e+r)/2,c=(t+i)/2,u=(r+n)/2,f=(i+o)/2,d=(l+u)/2,p=(c+f)/2;let g=n-e,m=o-t;const _=Math.abs((r-n)*m-(i-o)*g);if(_>$d){if(_*_<=a*(g*g+m*m)){s.push(d,p);return}}else if(g=d-(e+n)/2,m=p-(t+o)/2,g*g+m*m<=a){s.push(d,p);return}Vs(s,e,t,l,c,d,p,a,h+1),Vs(s,d,p,u,f,n,o,a,h+1)}function Na(s,e,t,r,i,n,o,a){let h=Math.abs(i-n);(!o&&i>n||o&&n>i)&&(h=2*Math.PI-h),a||(a=Math.max(6,Math.floor(6*Math.pow(r,1/3)*(h/Math.PI)))),a=Math.max(a,3);let l=h/a,c=i;l*=o?-1:1;for(let u=0;u<a+1;u++){const f=Math.cos(c),d=Math.sin(c),p=e+f*r,g=t+d*r;s.push(p,g),c+=l}}function qd(s,e,t,r,i,n){const o=s[s.length-2],h=s[s.length-1]-t,l=o-e,c=i-t,u=r-e,f=Math.abs(h*u-l*c);if(f<1e-8||n===0){(s[s.length-2]!==e||s[s.length-1]!==t)&&s.push(e,t);return}const d=h*h+l*l,p=c*c+u*u,g=h*c+l*u,m=n*Math.sqrt(d)/f,_=n*Math.sqrt(p)/f,y=m*g/d,b=_*g/p,v=m*u+_*l,w=m*c+_*h,S=l*(_+y),T=h*(_+y),k=u*(m+b),C=c*(m+b),P=Math.atan2(T-w,S-v),E=Math.atan2(C-w,k-v);Na(s,v+e,w+t,n,P,E,l*c>u*h)}const Gt=Math.PI*2,gs={centerX:0,centerY:0,ang1:0,ang2:0},_s=({x:s,y:e},t,r,i,n,o,a,h)=>{s*=t,e*=r;const l=i*s-n*e,c=n*s+i*e;return h.x=l+o,h.y=c+a,h};function Kd(s,e){const t=e===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(e/4),r=e===1.5707963267948966?.551915024494:t,i=Math.cos(s),n=Math.sin(s),o=Math.cos(s+e),a=Math.sin(s+e);return[{x:i-n*r,y:n+i*r},{x:o+a*r,y:a-o*r},{x:o,y:a}]}const zn=(s,e,t,r)=>{const i=s*r-e*t<0?-1:1;let n=s*t+e*r;return n>1&&(n=1),n<-1&&(n=-1),i*Math.acos(n)},Zd=(s,e,t,r,i,n,o,a,h,l,c,u,f)=>{const d=Math.pow(i,2),p=Math.pow(n,2),g=Math.pow(c,2),m=Math.pow(u,2);let _=d*p-d*m-p*g;_<0&&(_=0),_/=d*m+p*g,_=Math.sqrt(_)*(o===a?-1:1);const y=_*i/n*u,b=_*-n/i*c,v=l*y-h*b+(s+t)/2,w=h*y+l*b+(e+r)/2,S=(c-y)/i,T=(u-b)/n,k=(-c-y)/i,C=(-u-b)/n,P=zn(1,0,S,T);let E=zn(S,T,k,C);a===0&&E>0&&(E-=Gt),a===1&&E<0&&(E+=Gt),f.centerX=v,f.centerY=w,f.ang1=P,f.ang2=E};function Qd(s,e,t,r,i,n,o,a=0,h=0,l=0){if(n===0||o===0)return;const c=Math.sin(a*Gt/360),u=Math.cos(a*Gt/360),f=u*(e-r)/2+c*(t-i)/2,d=-c*(e-r)/2+u*(t-i)/2;if(f===0&&d===0)return;n=Math.abs(n),o=Math.abs(o);const p=Math.pow(f,2)/Math.pow(n,2)+Math.pow(d,2)/Math.pow(o,2);p>1&&(n*=Math.sqrt(p),o*=Math.sqrt(p)),Zd(e,t,r,i,n,o,h,l,c,u,f,d,gs);let{ang1:g,ang2:m}=gs;const{centerX:_,centerY:y}=gs;let b=Math.abs(m)/(Gt/4);Math.abs(1-b)<1e-7&&(b=1);const v=Math.max(Math.ceil(b),1);m/=v;let w=s[s.length-2],S=s[s.length-1];const T={x:0,y:0};for(let k=0;k<v;k++){const C=Kd(g,m),{x:P,y:E}=_s(C[0],n,o,u,c,_,y,T),{x:X,y:z}=_s(C[1],n,o,u,c,_,y,T),{x:oe,y:mt}=_s(C[2],n,o,u,c,_,y,T);La(s,w,S,P,E,X,z,oe,mt),w=oe,S=mt,g+=m}}function Jd(s,e,t){const r=(o,a)=>{const h=a.x-o.x,l=a.y-o.y,c=Math.sqrt(h*h+l*l),u=h/c,f=l/c;return{len:c,nx:u,ny:f}},i=(o,a)=>{o===0?s.moveTo(a.x,a.y):s.lineTo(a.x,a.y)};let n=e[e.length-1];for(let o=0;o<e.length;o++){const a=e[o%e.length],h=a.radius??t;if(h<=0){i(o,a),n=a;continue}const l=e[(o+1)%e.length],c=r(a,n),u=r(a,l);if(c.len<1e-4||u.len<1e-4){i(o,a),n=a;continue}let f=Math.asin(c.nx*u.ny-c.ny*u.nx),d=1,p=!1;c.nx*u.nx-c.ny*-u.ny<0?f<0?f=Math.PI+f:(f=Math.PI-f,d=-1,p=!0):f>0&&(d=-1,p=!0);const g=f/2;let m,_=Math.abs(Math.cos(g)*h/Math.sin(g));_>Math.min(c.len/2,u.len/2)?(_=Math.min(c.len/2,u.len/2),m=Math.abs(_*Math.sin(g)/Math.cos(g))):m=h;const y=a.x+u.nx*_+-u.ny*m*d,b=a.y+u.ny*_+u.nx*m*d,v=Math.atan2(c.ny,c.nx)+Math.PI/2*d,w=Math.atan2(u.ny,u.nx)-Math.PI/2*d;o===0&&s.moveTo(y+Math.cos(v)*m,b+Math.sin(v)*m),s.arc(y,b,m,v,w,p),n=a}}function ef(s,e,t,r){const i=(a,h)=>Math.sqrt((a.x-h.x)**2+(a.y-h.y)**2),n=(a,h,l)=>({x:a.x+(h.x-a.x)*l,y:a.y+(h.y-a.y)*l}),o=e.length;for(let a=0;a<o;a++){const h=e[(a+1)%o],l=h.radius??t;if(l<=0){a===0?s.moveTo(h.x,h.y):s.lineTo(h.x,h.y);continue}const c=e[a],u=e[(a+2)%o],f=i(c,h);let d;if(f<1e-4)d=h;else{const m=Math.min(f/2,l);d=n(h,c,m/f)}const p=i(u,h);let g;if(p<1e-4)g=h;else{const m=Math.min(p/2,l);g=n(h,u,m/p)}a===0?s.moveTo(d.x,d.y):s.lineTo(d.x,d.y),s.quadraticCurveTo(h.x,h.y,g.x,g.y,r)}}const tf=new N;class rf{constructor(e){this.shapePrimitives=[],this._currentPoly=null,this._bounds=new se,this._graphicsPath2D=e,this.signed=e.checkForHoles}moveTo(e,t){return this.startPoly(e,t),this}lineTo(e,t){this._ensurePoly();const r=this._currentPoly.points,i=r[r.length-2],n=r[r.length-1];return(i!==e||n!==t)&&r.push(e,t),this}arc(e,t,r,i,n,o){this._ensurePoly(!1);const a=this._currentPoly.points;return Na(a,e,t,r,i,n,o),this}arcTo(e,t,r,i,n){this._ensurePoly();const o=this._currentPoly.points;return qd(o,e,t,r,i,n),this}arcToSvg(e,t,r,i,n,o,a){const h=this._currentPoly.points;return Qd(h,this._currentPoly.lastX,this._currentPoly.lastY,o,a,e,t,r,i,n),this}bezierCurveTo(e,t,r,i,n,o,a){this._ensurePoly();const h=this._currentPoly;return La(this._currentPoly.points,h.lastX,h.lastY,e,t,r,i,n,o,a),this}quadraticCurveTo(e,t,r,i,n){this._ensurePoly();const o=this._currentPoly;return Yd(this._currentPoly.points,o.lastX,o.lastY,e,t,r,i,n),this}closePath(){return this.endPoly(!0),this}addPath(e,t){this.endPoly(),t&&!t.isIdentity()&&(e=e.clone(!0),e.transform(t));const r=this.shapePrimitives,i=r.length;for(let n=0;n<e.instructions.length;n++){const o=e.instructions[n];this[o.action](...o.data)}if(e.checkForHoles&&r.length-i>1){let n=null;for(let o=i;o<r.length;o++){const a=r[o];if(a.shape.type==="polygon"){const h=a.shape,l=n?.shape;l&&l.containsPolygon(h)?(n.holes||(n.holes=[]),n.holes.push(a),r.copyWithin(o,o+1),r.length--,o--):n=a}}}return this}finish(e=!1){this.endPoly(e)}rect(e,t,r,i,n){return this.drawShape(new N(e,t,r,i),n),this}circle(e,t,r,i){return this.drawShape(new oi(e,t,r),i),this}poly(e,t,r){const i=new kt(e);return i.closePath=t,this.drawShape(i,r),this}regularPoly(e,t,r,i,n=0,o){i=Math.max(i|0,3);const a=-1*Math.PI/2+n,h=Math.PI*2/i,l=[];for(let c=0;c<i;c++){const u=a-c*h;l.push(e+r*Math.cos(u),t+r*Math.sin(u))}return this.poly(l,!0,o),this}roundPoly(e,t,r,i,n,o=0,a){if(i=Math.max(i|0,3),n<=0)return this.regularPoly(e,t,r,i,o);const h=r*Math.sin(Math.PI/i)-.001;n=Math.min(n,h);const l=-1*Math.PI/2+o,c=Math.PI*2/i,u=(i-2)*Math.PI/i/2;for(let f=0;f<i;f++){const d=f*c+l,p=e+r*Math.cos(d),g=t+r*Math.sin(d),m=d+Math.PI+u,_=d-Math.PI-u,y=p+n*Math.cos(m),b=g+n*Math.sin(m),v=p+n*Math.cos(_),w=g+n*Math.sin(_);f===0?this.moveTo(y,b):this.lineTo(y,b),this.quadraticCurveTo(p,g,v,w,a)}return this.closePath()}roundShape(e,t,r=!1,i){return e.length<3?this:(r?ef(this,e,t,i):Jd(this,e,t),this.closePath())}filletRect(e,t,r,i,n){if(n===0)return this.rect(e,t,r,i);const o=Math.min(r,i)/2,a=Math.min(o,Math.max(-o,n)),h=e+r,l=t+i,c=a<0?-a:0,u=Math.abs(a);return this.moveTo(e,t+u).arcTo(e+c,t+c,e+u,t,u).lineTo(h-u,t).arcTo(h-c,t+c,h,t+u,u).lineTo(h,l-u).arcTo(h-c,l-c,e+r-u,l,u).lineTo(e+u,l).arcTo(e+c,l-c,e,l-u,u).closePath()}chamferRect(e,t,r,i,n,o){if(n<=0)return this.rect(e,t,r,i);const a=Math.min(n,Math.min(r,i)/2),h=e+r,l=t+i,c=[e+a,t,h-a,t,h,t+a,h,l-a,h-a,l,e+a,l,e,l-a,e,t+a];for(let u=c.length-1;u>=2;u-=2)c[u]===c[u-2]&&c[u-1]===c[u-3]&&c.splice(u-1,2);return this.poly(c,!0,o)}ellipse(e,t,r,i,n){return this.drawShape(new ai(e,t,r,i),n),this}roundRect(e,t,r,i,n,o){return this.drawShape(new hi(e,t,r,i,n),o),this}drawShape(e,t){return this.endPoly(),this.shapePrimitives.push({shape:e,transform:t}),this}startPoly(e,t){let r=this._currentPoly;return r&&this.endPoly(),r=new kt,r.points.push(e,t),this._currentPoly=r,this}endPoly(e=!1){const t=this._currentPoly;return t&&t.points.length>2&&(t.closePath=e,this.shapePrimitives.push({shape:t})),this._currentPoly=null,this}_ensurePoly(e=!0){if(!this._currentPoly&&(this._currentPoly=new kt,e)){const t=this.shapePrimitives[this.shapePrimitives.length-1];if(t){let r=t.shape.x,i=t.shape.y;if(t.transform&&!t.transform.isIdentity()){const n=t.transform,o=r;r=n.a*r+n.c*i+n.tx,i=n.b*o+n.d*i+n.ty}this._currentPoly.points.push(r,i)}else this._currentPoly.points.push(0,0)}}buildPath(){const e=this._graphicsPath2D;this.shapePrimitives.length=0,this._currentPoly=null;for(let t=0;t<e.instructions.length;t++){const r=e.instructions[t];this[r.action](...r.data)}this.finish()}get bounds(){const e=this._bounds;e.clear();const t=this.shapePrimitives;for(let r=0;r<t.length;r++){const i=t[r],n=i.shape.getBounds(tf);i.transform?e.addRect(n,i.transform):e.addRect(n)}return e}}class dt{constructor(e,t=!1){this.instructions=[],this.uid=V("graphicsPath"),this._dirty=!0,this.checkForHoles=t,typeof e=="string"?Ou(e,this):this.instructions=e?.slice()??[]}get shapePath(){return this._shapePath||(this._shapePath=new rf(this)),this._dirty&&(this._dirty=!1,this._shapePath.buildPath()),this._shapePath}addPath(e,t){return e=e.clone(),this.instructions.push({action:"addPath",data:[e,t]}),this._dirty=!0,this}arc(...e){return this.instructions.push({action:"arc",data:e}),this._dirty=!0,this}arcTo(...e){return this.instructions.push({action:"arcTo",data:e}),this._dirty=!0,this}arcToSvg(...e){return this.instructions.push({action:"arcToSvg",data:e}),this._dirty=!0,this}bezierCurveTo(...e){return this.instructions.push({action:"bezierCurveTo",data:e}),this._dirty=!0,this}bezierCurveToShort(e,t,r,i,n){const o=this.instructions[this.instructions.length-1],a=this.getLastPoint(H.shared);let h=0,l=0;if(!o||o.action!=="bezierCurveTo")h=a.x,l=a.y;else{h=o.data[2],l=o.data[3];const c=a.x,u=a.y;h=c+(c-h),l=u+(u-l)}return this.instructions.push({action:"bezierCurveTo",data:[h,l,e,t,r,i,n]}),this._dirty=!0,this}closePath(){return this.instructions.push({action:"closePath",data:[]}),this._dirty=!0,this}ellipse(...e){return this.instructions.push({action:"ellipse",data:e}),this._dirty=!0,this}lineTo(...e){return this.instructions.push({action:"lineTo",data:e}),this._dirty=!0,this}moveTo(...e){return this.instructions.push({action:"moveTo",data:e}),this}quadraticCurveTo(...e){return this.instructions.push({action:"quadraticCurveTo",data:e}),this._dirty=!0,this}quadraticCurveToShort(e,t,r){const i=this.instructions[this.instructions.length-1],n=this.getLastPoint(H.shared);let o=0,a=0;if(!i||i.action!=="quadraticCurveTo")o=n.x,a=n.y;else{o=i.data[0],a=i.data[1];const h=n.x,l=n.y;o=h+(h-o),a=l+(l-a)}return this.instructions.push({action:"quadraticCurveTo",data:[o,a,e,t,r]}),this._dirty=!0,this}rect(e,t,r,i,n){return this.instructions.push({action:"rect",data:[e,t,r,i,n]}),this._dirty=!0,this}circle(e,t,r,i){return this.instructions.push({action:"circle",data:[e,t,r,i]}),this._dirty=!0,this}roundRect(...e){return this.instructions.push({action:"roundRect",data:e}),this._dirty=!0,this}poly(...e){return this.instructions.push({action:"poly",data:e}),this._dirty=!0,this}regularPoly(...e){return this.instructions.push({action:"regularPoly",data:e}),this._dirty=!0,this}roundPoly(...e){return this.instructions.push({action:"roundPoly",data:e}),this._dirty=!0,this}roundShape(...e){return this.instructions.push({action:"roundShape",data:e}),this._dirty=!0,this}filletRect(...e){return this.instructions.push({action:"filletRect",data:e}),this._dirty=!0,this}chamferRect(...e){return this.instructions.push({action:"chamferRect",data:e}),this._dirty=!0,this}star(e,t,r,i,n,o,a){n||(n=i/2);const h=-1*Math.PI/2+o,l=r*2,c=Math.PI*2/l,u=[];for(let f=0;f<l;f++){const d=f%2?n:i,p=f*c+h;u.push(e+d*Math.cos(p),t+d*Math.sin(p))}return this.poly(u,!0,a),this}clone(e=!1){const t=new dt;if(t.checkForHoles=this.checkForHoles,!e)t.instructions=this.instructions.slice();else for(let r=0;r<this.instructions.length;r++){const i=this.instructions[r];t.instructions.push({action:i.action,data:i.data.slice()})}return t}clear(){return this.instructions.length=0,this._dirty=!0,this}transform(e){if(e.isIdentity())return this;const t=e.a,r=e.b,i=e.c,n=e.d,o=e.tx,a=e.ty;let h=0,l=0,c=0,u=0,f=0,d=0,p=0,g=0;for(let m=0;m<this.instructions.length;m++){const _=this.instructions[m],y=_.data;switch(_.action){case"moveTo":case"lineTo":h=y[0],l=y[1],y[0]=t*h+i*l+o,y[1]=r*h+n*l+a;break;case"bezierCurveTo":c=y[0],u=y[1],f=y[2],d=y[3],h=y[4],l=y[5],y[0]=t*c+i*u+o,y[1]=r*c+n*u+a,y[2]=t*f+i*d+o,y[3]=r*f+n*d+a,y[4]=t*h+i*l+o,y[5]=r*h+n*l+a;break;case"quadraticCurveTo":c=y[0],u=y[1],h=y[2],l=y[3],y[0]=t*c+i*u+o,y[1]=r*c+n*u+a,y[2]=t*h+i*l+o,y[3]=r*h+n*l+a;break;case"arcToSvg":h=y[5],l=y[6],p=y[0],g=y[1],y[0]=t*p+i*g,y[1]=r*p+n*g,y[5]=t*h+i*l+o,y[6]=r*h+n*l+a;break;case"circle":y[4]=St(y[3],e);break;case"rect":y[4]=St(y[4],e);break;case"ellipse":y[8]=St(y[8],e);break;case"roundRect":y[5]=St(y[5],e);break;case"addPath":y[0].transform(e);break;case"poly":y[2]=St(y[2],e);break;default:F("unknown transform action",_.action);break}}return this._dirty=!0,this}get bounds(){return this.shapePath.bounds}getLastPoint(e){let t=this.instructions.length-1,r=this.instructions[t];if(!r)return e.x=0,e.y=0,e;for(;r.action==="closePath";){if(t--,t<0)return e.x=0,e.y=0,e;r=this.instructions[t]}switch(r.action){case"moveTo":case"lineTo":e.x=r.data[0],e.y=r.data[1];break;case"quadraticCurveTo":e.x=r.data[2],e.y=r.data[3];break;case"bezierCurveTo":e.x=r.data[4],e.y=r.data[5];break;case"arc":case"arcToSvg":e.x=r.data[5],e.y=r.data[6];break;case"addPath":r.data[0].getLastPoint(e);break}return e}}function St(s,e){return s?s.prepend(e):e.clone()}function K(s,e,t){const r=s.getAttribute(e);return r?Number(r):t}function sf(s,e){const t=s.querySelectorAll("defs");for(let r=0;r<t.length;r++){const i=t[r];for(let n=0;n<i.children.length;n++){const o=i.children[n];switch(o.nodeName.toLowerCase()){case"lineargradient":e.defs[o.id]=nf(o);break;case"radialgradient":e.defs[o.id]=of();break}}}}function nf(s){const e=K(s,"x1",0),t=K(s,"y1",0),r=K(s,"x2",1),i=K(s,"y2",0),n=s.getAttribute("gradientUnits")||"objectBoundingBox",o=new ke(e,t,r,i,n==="objectBoundingBox"?"local":"global");for(let a=0;a<s.children.length;a++){const h=s.children[a],l=K(h,"offset",0),c=L.shared.setValue(h.getAttribute("stop-color")).toNumber();o.addColorStop(l,c)}return o}function of(s){return F("[SVG Parser] Radial gradients are not yet supported"),new ke(0,0,1,0)}function Wn(s){const e=s.match(/url\s*\(\s*['"]?\s*#([^'"\s)]+)\s*['"]?\s*\)/i);return e?e[1]:""}const Vn={fill:{type:"paint",default:0},"fill-opacity":{type:"number",default:1},stroke:{type:"paint",default:0},"stroke-width":{type:"number",default:1},"stroke-opacity":{type:"number",default:1},"stroke-linecap":{type:"string",default:"butt"},"stroke-linejoin":{type:"string",default:"miter"},"stroke-miterlimit":{type:"number",default:10},"stroke-dasharray":{type:"string",default:"none"},"stroke-dashoffset":{type:"number",default:0},opacity:{type:"number",default:1}};function Ha(s,e){const t=s.getAttribute("style"),r={},i={},n={strokeStyle:r,fillStyle:i,useFill:!1,useStroke:!1};for(const o in Vn){const a=s.getAttribute(o);a&&$n(e,n,o,a.trim())}if(t){const o=t.split(";");for(let a=0;a<o.length;a++){const h=o[a].trim(),[l,c]=h.split(":");Vn[l]&&$n(e,n,l,c.trim())}}return{strokeStyle:n.useStroke?r:null,fillStyle:n.useFill?i:null,useFill:n.useFill,useStroke:n.useStroke}}function $n(s,e,t,r){switch(t){case"stroke":if(r!=="none"){if(r.startsWith("url(")){const i=Wn(r);e.strokeStyle.fill=s.defs[i]}else e.strokeStyle.color=L.shared.setValue(r).toNumber();e.useStroke=!0}break;case"stroke-width":e.strokeStyle.width=Number(r);break;case"fill":if(r!=="none"){if(r.startsWith("url(")){const i=Wn(r);e.fillStyle.fill=s.defs[i]}else e.fillStyle.color=L.shared.setValue(r).toNumber();e.useFill=!0}break;case"fill-opacity":e.fillStyle.alpha=Number(r);break;case"stroke-opacity":e.strokeStyle.alpha=Number(r);break;case"opacity":e.fillStyle.alpha=Number(r),e.strokeStyle.alpha=Number(r);break}}function af(s,e){if(typeof s=="string"){const o=document.createElement("div");o.innerHTML=s.trim(),s=o.querySelector("svg")}const t={context:e,defs:{},path:new dt};sf(s,t);const r=s.children,{fillStyle:i,strokeStyle:n}=Ha(s,t);for(let o=0;o<r.length;o++){const a=r[o];a.nodeName.toLowerCase()!=="defs"&&za(a,t,i,n)}return e}function za(s,e,t,r){const i=s.children,{fillStyle:n,strokeStyle:o}=Ha(s,e);n&&t?t={...t,...n}:n&&(t=n),o&&r?r={...r,...o}:o&&(r=o);const a=!t&&!r;a&&(t={color:0});let h,l,c,u,f,d,p,g,m,_,y,b,v,w,S,T,k;switch(s.nodeName.toLowerCase()){case"path":w=s.getAttribute("d"),s.getAttribute("fill-rule")==="evenodd"&&F("SVG Evenodd fill rule not supported, your svg may render incorrectly"),S=new dt(w,!0),e.context.path(S),t&&e.context.fill(t),r&&e.context.stroke(r);break;case"circle":p=K(s,"cx",0),g=K(s,"cy",0),m=K(s,"r",0),e.context.ellipse(p,g,m,m),t&&e.context.fill(t),r&&e.context.stroke(r);break;case"rect":h=K(s,"x",0),l=K(s,"y",0),T=K(s,"width",0),k=K(s,"height",0),_=K(s,"rx",0),y=K(s,"ry",0),_||y?e.context.roundRect(h,l,T,k,_||y):e.context.rect(h,l,T,k),t&&e.context.fill(t),r&&e.context.stroke(r);break;case"ellipse":p=K(s,"cx",0),g=K(s,"cy",0),_=K(s,"rx",0),y=K(s,"ry",0),e.context.beginPath(),e.context.ellipse(p,g,_,y),t&&e.context.fill(t),r&&e.context.stroke(r);break;case"line":c=K(s,"x1",0),u=K(s,"y1",0),f=K(s,"x2",0),d=K(s,"y2",0),e.context.beginPath(),e.context.moveTo(c,u),e.context.lineTo(f,d),r&&e.context.stroke(r);break;case"polygon":v=s.getAttribute("points"),b=v.match(/\d+/g).map(C=>parseInt(C,10)),e.context.poly(b,!0),t&&e.context.fill(t),r&&e.context.stroke(r);break;case"polyline":v=s.getAttribute("points"),b=v.match(/\d+/g).map(C=>parseInt(C,10)),e.context.poly(b,!1),r&&e.context.stroke(r);break;case"g":case"svg":break;default:{F(`[SVG parser] <${s.nodeName}> elements unsupported`);break}}a&&(t=null);for(let C=0;C<i.length;C++)za(i[C],e,t,r)}function hf(s){return L.isColorLike(s)}function Xn(s){return s instanceof Ur}function Yn(s){return s instanceof ke}function lf(s){return s instanceof A}function cf(s,e,t){const r=L.shared.setValue(e??0);return s.color=r.toNumber(),s.alpha=r.alpha===1?t.alpha:r.alpha,s.texture=A.WHITE,{...t,...s}}function uf(s,e,t){return s.texture=e,{...t,...s}}function jn(s,e,t){return s.fill=e,s.color=16777215,s.texture=e.texture,s.matrix=e.transform,{...t,...s}}function qn(s,e,t){return e.buildGradient(),s.fill=e,s.color=16777215,s.texture=e.texture,s.matrix=e.transform,s.textureSpace=e.textureSpace,{...t,...s}}function df(s,e){const t={...e,...s},r=L.shared.setValue(t.color);return t.alpha*=r.alpha,t.color=r.toNumber(),t}function Ze(s,e){if(s==null)return null;const t={},r=s;return hf(s)?cf(t,s,e):lf(s)?uf(t,s,e):Xn(s)?jn(t,s,e):Yn(s)?qn(t,s,e):r.fill&&Xn(r.fill)?jn(r,r.fill,e):r.fill&&Yn(r.fill)?qn(r,r.fill,e):df(r,e)}function Br(s,e){const{width:t,alignment:r,miterLimit:i,cap:n,join:o,pixelLine:a,...h}=e,l=Ze(s,h);return l?{width:t,alignment:r,miterLimit:i,cap:n,join:o,pixelLine:a,...l}:null}const ff=new H,Kn=new B,xi=class Te extends de{constructor(){super(...arguments),this.uid=V("graphicsContext"),this.dirty=!0,this.batchMode="auto",this.instructions=[],this._activePath=new dt,this._transform=new B,this._fillStyle={...Te.defaultFillStyle},this._strokeStyle={...Te.defaultStrokeStyle},this._stateStack=[],this._tick=0,this._bounds=new se,this._boundsDirty=!0}clone(){const e=new Te;return e.batchMode=this.batchMode,e.instructions=this.instructions.slice(),e._activePath=this._activePath.clone(),e._transform=this._transform.clone(),e._fillStyle={...this._fillStyle},e._strokeStyle={...this._strokeStyle},e._stateStack=this._stateStack.slice(),e._bounds=this._bounds.clone(),e._boundsDirty=!0,e}get fillStyle(){return this._fillStyle}set fillStyle(e){this._fillStyle=Ze(e,Te.defaultFillStyle)}get strokeStyle(){return this._strokeStyle}set strokeStyle(e){this._strokeStyle=Br(e,Te.defaultStrokeStyle)}setFillStyle(e){return this._fillStyle=Ze(e,Te.defaultFillStyle),this}setStrokeStyle(e){return this._strokeStyle=Ze(e,Te.defaultStrokeStyle),this}texture(e,t,r,i,n,o){return this.instructions.push({action:"texture",data:{image:e,dx:r||0,dy:i||0,dw:n||e.frame.width,dh:o||e.frame.height,transform:this._transform.clone(),alpha:this._fillStyle.alpha,style:t?L.shared.setValue(t).toNumber():16777215}}),this.onUpdate(),this}beginPath(){return this._activePath=new dt,this}fill(e,t){let r;const i=this.instructions[this.instructions.length-1];return this._tick===0&&i&&i.action==="stroke"?r=i.data.path:r=this._activePath.clone(),r?(e!=null&&(t!==void 0&&typeof e=="number"&&(I(O,"GraphicsContext.fill(color, alpha) is deprecated, use GraphicsContext.fill({ color, alpha }) instead"),e={color:e,alpha:t}),this._fillStyle=Ze(e,Te.defaultFillStyle)),this.instructions.push({action:"fill",data:{style:this.fillStyle,path:r}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}_initNextPathLocation(){const{x:e,y:t}=this._activePath.getLastPoint(H.shared);this._activePath.clear(),this._activePath.moveTo(e,t)}stroke(e){let t;const r=this.instructions[this.instructions.length-1];return this._tick===0&&r&&r.action==="fill"?t=r.data.path:t=this._activePath.clone(),t?(e!=null&&(this._strokeStyle=Br(e,Te.defaultStrokeStyle)),this.instructions.push({action:"stroke",data:{style:this.strokeStyle,path:t}}),this.onUpdate(),this._initNextPathLocation(),this._tick=0,this):this}cut(){for(let e=0;e<2;e++){const t=this.instructions[this.instructions.length-1-e],r=this._activePath.clone();if(t&&(t.action==="stroke"||t.action==="fill"))if(t.data.hole)t.data.hole.addPath(r);else{t.data.hole=r;break}}return this._initNextPathLocation(),this}arc(e,t,r,i,n,o){this._tick++;const a=this._transform;return this._activePath.arc(a.a*e+a.c*t+a.tx,a.b*e+a.d*t+a.ty,r,i,n,o),this}arcTo(e,t,r,i,n){this._tick++;const o=this._transform;return this._activePath.arcTo(o.a*e+o.c*t+o.tx,o.b*e+o.d*t+o.ty,o.a*r+o.c*i+o.tx,o.b*r+o.d*i+o.ty,n),this}arcToSvg(e,t,r,i,n,o,a){this._tick++;const h=this._transform;return this._activePath.arcToSvg(e,t,r,i,n,h.a*o+h.c*a+h.tx,h.b*o+h.d*a+h.ty),this}bezierCurveTo(e,t,r,i,n,o,a){this._tick++;const h=this._transform;return this._activePath.bezierCurveTo(h.a*e+h.c*t+h.tx,h.b*e+h.d*t+h.ty,h.a*r+h.c*i+h.tx,h.b*r+h.d*i+h.ty,h.a*n+h.c*o+h.tx,h.b*n+h.d*o+h.ty,a),this}closePath(){return this._tick++,this._activePath?.closePath(),this}ellipse(e,t,r,i){return this._tick++,this._activePath.ellipse(e,t,r,i,this._transform.clone()),this}circle(e,t,r){return this._tick++,this._activePath.circle(e,t,r,this._transform.clone()),this}path(e){return this._tick++,this._activePath.addPath(e,this._transform.clone()),this}lineTo(e,t){this._tick++;const r=this._transform;return this._activePath.lineTo(r.a*e+r.c*t+r.tx,r.b*e+r.d*t+r.ty),this}moveTo(e,t){this._tick++;const r=this._transform,i=this._activePath.instructions,n=r.a*e+r.c*t+r.tx,o=r.b*e+r.d*t+r.ty;return i.length===1&&i[0].action==="moveTo"?(i[0].data[0]=n,i[0].data[1]=o,this):(this._activePath.moveTo(n,o),this)}quadraticCurveTo(e,t,r,i,n){this._tick++;const o=this._transform;return this._activePath.quadraticCurveTo(o.a*e+o.c*t+o.tx,o.b*e+o.d*t+o.ty,o.a*r+o.c*i+o.tx,o.b*r+o.d*i+o.ty,n),this}rect(e,t,r,i){return this._tick++,this._activePath.rect(e,t,r,i,this._transform.clone()),this}roundRect(e,t,r,i,n){return this._tick++,this._activePath.roundRect(e,t,r,i,n,this._transform.clone()),this}poly(e,t){return this._tick++,this._activePath.poly(e,t,this._transform.clone()),this}regularPoly(e,t,r,i,n=0,o){return this._tick++,this._activePath.regularPoly(e,t,r,i,n,o),this}roundPoly(e,t,r,i,n,o){return this._tick++,this._activePath.roundPoly(e,t,r,i,n,o),this}roundShape(e,t,r,i){return this._tick++,this._activePath.roundShape(e,t,r,i),this}filletRect(e,t,r,i,n){return this._tick++,this._activePath.filletRect(e,t,r,i,n),this}chamferRect(e,t,r,i,n,o){return this._tick++,this._activePath.chamferRect(e,t,r,i,n,o),this}star(e,t,r,i,n=0,o=0){return this._tick++,this._activePath.star(e,t,r,i,n,o,this._transform.clone()),this}svg(e){return this._tick++,af(e,this),this}restore(){const e=this._stateStack.pop();return e&&(this._transform=e.transform,this._fillStyle=e.fillStyle,this._strokeStyle=e.strokeStyle),this}save(){return this._stateStack.push({transform:this._transform.clone(),fillStyle:{...this._fillStyle},strokeStyle:{...this._strokeStyle}}),this}getTransform(){return this._transform}resetTransform(){return this._transform.identity(),this}rotate(e){return this._transform.rotate(e),this}scale(e,t=e){return this._transform.scale(e,t),this}setTransform(e,t,r,i,n,o){return e instanceof B?(this._transform.set(e.a,e.b,e.c,e.d,e.tx,e.ty),this):(this._transform.set(e,t,r,i,n,o),this)}transform(e,t,r,i,n,o){return e instanceof B?(this._transform.append(e),this):(Kn.set(e,t,r,i,n,o),this._transform.append(Kn),this)}translate(e,t=e){return this._transform.translate(e,t),this}clear(){return this._activePath.clear(),this.instructions.length=0,this.resetTransform(),this.onUpdate(),this}onUpdate(){this.dirty||(this.emit("update",this,16),this.dirty=!0,this._boundsDirty=!0)}get bounds(){if(!this._boundsDirty)return this._bounds;const e=this._bounds;e.clear();for(let t=0;t<this.instructions.length;t++){const r=this.instructions[t],i=r.action;if(i==="fill"){const n=r.data;e.addBounds(n.path.bounds)}else if(i==="texture"){const n=r.data;e.addFrame(n.dx,n.dy,n.dx+n.dw,n.dy+n.dh,n.transform)}if(i==="stroke"){const n=r.data,o=n.style.alignment,a=n.style.width*(1-o),h=n.path.bounds;e.addFrame(h.minX-a,h.minY-a,h.maxX+a,h.maxY+a)}}return e}containsPoint(e){if(!this.bounds.containsPoint(e.x,e.y))return!1;const t=this.instructions;let r=!1;for(let i=0;i<t.length;i++){const n=t[i],o=n.data,a=o.path;if(!n.action||!a)continue;const h=o.style,l=a.shapePath.shapePrimitives;for(let c=0;c<l.length;c++){const u=l[c].shape;if(!h||!u)continue;const f=l[c].transform,d=f?f.applyInverse(e,ff):e;if(n.action==="fill")r=u.contains(d.x,d.y);else{const g=h;r=u.strokeContains(d.x,d.y,g.width,g.alignment)}const p=o.hole;if(p){const g=p.shapePath?.shapePrimitives;if(g)for(let m=0;m<g.length;m++)g[m].shape.contains(d.x,d.y)&&(r=!1)}if(r)return!0}}return r}destroy(e=!1){if(this._stateStack.length=0,this._transform=null,this.emit("destroy",this),this.removeAllListeners(),typeof e=="boolean"?e:e?.texture){const r=typeof e=="boolean"?e:e?.textureSource;this._fillStyle.texture&&this._fillStyle.texture.destroy(r),this._strokeStyle.texture&&this._strokeStyle.texture.destroy(r)}this._fillStyle=null,this._strokeStyle=null,this.instructions=null,this._activePath=null,this._bounds=null,this._stateStack=null,this.customShader=null,this._transform=null}};xi.defaultFillStyle={color:16777215,alpha:1,texture:A.WHITE,matrix:null,fill:null,textureSpace:"local"};xi.defaultStrokeStyle={width:1,color:16777215,alpha:1,alignment:.5,miterLimit:10,cap:"butt",join:"miter",texture:A.WHITE,matrix:null,fill:null,textureSpace:"local",pixelLine:!1};let _e=xi;const Zn=["align","breakWords","cssOverrides","fontVariant","fontWeight","leading","letterSpacing","lineHeight","padding","textBaseline","trim","whiteSpace","wordWrap","wordWrapWidth","fontFamily","fontStyle","fontSize"];function pf(s){const e=[];let t=0;for(let r=0;r<Zn.length;r++){const i=`_${Zn[r]}`;e[t++]=s[i]}return t=Wa(s._fill,e,t),t=gf(s._stroke,e,t),t=_f(s.dropShadow,e,t),t=mf(s.filters,e,t),e.join("-")}function mf(s,e,t){if(!s)return t;for(const r of s)e[t++]=r.uid;return t}function Wa(s,e,t){return s&&(e[t++]=s.color,e[t++]=s.alpha,e[t++]=s.fill?.styleKey),t}function gf(s,e,t){return s&&(t=Wa(s,e,t),e[t++]=s.width,e[t++]=s.alignment,e[t++]=s.cap,e[t++]=s.join,e[t++]=s.miterLimit),t}function _f(s,e,t){return s&&(e[t++]=s.alpha,e[t++]=s.angle,e[t++]=s.blur,e[t++]=s.distance,e[t++]=L.shared.setValue(s.color).toNumber()),t}const yi=class ot extends de{constructor(e={}){super(),xf(e);const t={...ot.defaultTextStyle,...e};for(const r in t){const i=r;this[i]=t[r]}this.update()}get align(){return this._align}set align(e){this._align=e,this.update()}get breakWords(){return this._breakWords}set breakWords(e){this._breakWords=e,this.update()}get dropShadow(){return this._dropShadow}set dropShadow(e){e!==null&&typeof e=="object"?this._dropShadow=this._createProxy({...ot.defaultDropShadow,...e}):this._dropShadow=e?this._createProxy({...ot.defaultDropShadow}):null,this.update()}get fontFamily(){return this._fontFamily}set fontFamily(e){this._fontFamily=e,this.update()}get fontSize(){return this._fontSize}set fontSize(e){typeof e=="string"?this._fontSize=parseInt(e,10):this._fontSize=e,this.update()}get fontStyle(){return this._fontStyle}set fontStyle(e){this._fontStyle=e.toLowerCase(),this.update()}get fontVariant(){return this._fontVariant}set fontVariant(e){this._fontVariant=e,this.update()}get fontWeight(){return this._fontWeight}set fontWeight(e){this._fontWeight=e,this.update()}get leading(){return this._leading}set leading(e){this._leading=e,this.update()}get letterSpacing(){return this._letterSpacing}set letterSpacing(e){this._letterSpacing=e,this.update()}get lineHeight(){return this._lineHeight}set lineHeight(e){this._lineHeight=e,this.update()}get padding(){return this._padding}set padding(e){this._padding=e,this.update()}get filters(){return this._filters}set filters(e){this._filters=e,this.update()}get trim(){return this._trim}set trim(e){this._trim=e,this.update()}get textBaseline(){return this._textBaseline}set textBaseline(e){this._textBaseline=e,this.update()}get whiteSpace(){return this._whiteSpace}set whiteSpace(e){this._whiteSpace=e,this.update()}get wordWrap(){return this._wordWrap}set wordWrap(e){this._wordWrap=e,this.update()}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(e){this._wordWrapWidth=e,this.update()}get fill(){return this._originalFill}set fill(e){e!==this._originalFill&&(this._originalFill=e,this._isFillStyle(e)&&(this._originalFill=this._createProxy({..._e.defaultFillStyle,...e},()=>{this._fill=Ze({...this._originalFill},_e.defaultFillStyle)})),this._fill=Ze(e===0?"black":e,_e.defaultFillStyle),this.update())}get stroke(){return this._originalStroke}set stroke(e){e!==this._originalStroke&&(this._originalStroke=e,this._isFillStyle(e)&&(this._originalStroke=this._createProxy({..._e.defaultStrokeStyle,...e},()=>{this._stroke=Br({...this._originalStroke},_e.defaultStrokeStyle)})),this._stroke=Br(e,_e.defaultStrokeStyle),this.update())}_generateKey(){return this._styleKey=pf(this),this._styleKey}update(){this._styleKey=null,this.emit("update",this)}reset(){const e=ot.defaultTextStyle;for(const t in e)this[t]=e[t]}get styleKey(){return this._styleKey||this._generateKey()}clone(){return new ot({align:this.align,breakWords:this.breakWords,dropShadow:this._dropShadow?{...this._dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,leading:this.leading,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,textBaseline:this.textBaseline,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,filters:this._filters?[...this._filters]:void 0})}_getFinalPadding(){let e=0;if(this._filters)for(let t=0;t<this._filters.length;t++)e+=this._filters[t].padding;return Math.max(this._padding,e)}destroy(e=!1){if(this.removeAllListeners(),typeof e=="boolean"?e:e?.texture){const r=typeof e=="boolean"?e:e?.textureSource;this._fill?.texture&&this._fill.texture.destroy(r),this._originalFill?.texture&&this._originalFill.texture.destroy(r),this._stroke?.texture&&this._stroke.texture.destroy(r),this._originalStroke?.texture&&this._originalStroke.texture.destroy(r)}this._fill=null,this._stroke=null,this.dropShadow=null,this._originalStroke=null,this._originalFill=null}_createProxy(e,t){return new Proxy(e,{set:(r,i,n)=>(r[i]=n,t?.(i,n),this.update(),!0)})}_isFillStyle(e){return(e??null)!==null&&!(L.isColorLike(e)||e instanceof ke||e instanceof Ur)}};yi.defaultDropShadow={alpha:1,angle:Math.PI/6,blur:0,color:"black",distance:5};yi.defaultTextStyle={align:"left",breakWords:!1,dropShadow:null,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,padding:0,stroke:null,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let Oe=yi;function xf(s){const e=s;if(typeof e.dropShadow=="boolean"&&e.dropShadow){const t=Oe.defaultDropShadow;s.dropShadow={alpha:e.dropShadowAlpha??t.alpha,angle:e.dropShadowAngle??t.angle,blur:e.dropShadowBlur??t.blur,color:e.dropShadowColor??t.color,distance:e.dropShadowDistance??t.distance}}if(e.strokeThickness!==void 0){I(O,"strokeThickness is now a part of stroke");const t=e.stroke;let r={};if(L.isColorLike(t))r.color=t;else if(t instanceof ke||t instanceof Ur)r.fill=t;else if(Object.hasOwnProperty.call(t,"color")||Object.hasOwnProperty.call(t,"fill"))r=t;else throw new Error("Invalid stroke value.");s.stroke={...r,width:e.strokeThickness}}if(Array.isArray(e.fillGradientStops)){I(O,"gradient fill is now a fill pattern: `new FillGradient(...)`");let t;s.fontSize==null?s.fontSize=Oe.defaultTextStyle.fontSize:typeof s.fontSize=="string"?t=parseInt(s.fontSize,10):t=s.fontSize;const r=new ke({start:{x:0,y:0},end:{x:0,y:(t||0)*1.7}}),i=e.fillGradientStops.map(n=>L.shared.setValue(n).toNumber());i.forEach((n,o)=>{const a=o/(i.length-1);r.addColorStop(a,n)}),s.fill={fill:r}}}class yf{constructor(e){this._canvasPool=Object.create(null),this.canvasOptions=e||{},this.enableFullScreen=!1}_createCanvasAndContext(e,t){const r=$.get().createCanvas();r.width=e,r.height=t;const i=r.getContext("2d");return{canvas:r,context:i}}getOptimalCanvasAndContext(e,t,r=1){e=Math.ceil(e*r-1e-6),t=Math.ceil(t*r-1e-6),e=ht(e),t=ht(t);const i=(e<<17)+(t<<1);this._canvasPool[i]||(this._canvasPool[i]=[]);let n=this._canvasPool[i].pop();return n||(n=this._createCanvasAndContext(e,t)),n}returnCanvasAndContext(e){const t=e.canvas,{width:r,height:i}=t,n=(r<<17)+(i<<1);e.context.clearRect(0,0,r,i),this._canvasPool[n].push(e)}clear(){this._canvasPool={}}}const Le=new yf,Qn=1e5;function Rr(s,e,t,r=0){if(s.texture===A.WHITE&&!s.fill)return L.shared.setValue(s.color).setAlpha(s.alpha??1).toHexa();if(s.fill){if(s.fill instanceof Ur){const i=s.fill,n=e.createPattern(i.texture.source.resource,"repeat"),o=i.transform.copyTo(B.shared);return o.scale(i.texture.frame.width,i.texture.frame.height),n.setTransform(o),n}else if(s.fill instanceof ke){const i=s.fill,n=i.type==="linear",o=i.textureSpace==="local";let a=1,h=1;o&&t&&(a=t.width+r,h=t.height+r);let l,c=!1;if(n){const{start:u,end:f}=i;l=e.createLinearGradient(u.x*a,u.y*h,f.x*a,f.y*h),c=Math.abs(f.x-u.x)<Math.abs((f.y-u.y)*.1)}else{const{center:u,innerRadius:f,outerCenter:d,outerRadius:p}=i;l=e.createRadialGradient(u.x*a,u.y*h,f*a,d.x*a,d.y*h,p*a)}if(c&&o&&t){const u=t.lineHeight/h;for(let f=0;f<t.lines.length;f++){const d=(f*t.lineHeight+r/2)/h;i.colorStops.forEach(p=>{const g=d+p.offset*u;l.addColorStop(Math.floor(g*Qn)/Qn,L.shared.setValue(p.color).toHex())})}}else i.colorStops.forEach(u=>{l.addColorStop(u.offset,L.shared.setValue(u.color).toHex())});return l}}else{const i=e.createPattern(s.texture.source.resource,"repeat"),n=s.matrix.copyTo(B.shared);return n.scale(s.texture.frame.width,s.texture.frame.height),i.setTransform(n),i}return F("FillStyle not recognised",s),"red"}const Va=class $a extends Au{constructor(e){super(),this.resolution=1,this.pages=[],this._padding=0,this._measureCache=Object.create(null),this._currentChars=[],this._currentX=0,this._currentY=0,this._currentMaxCharHeight=0,this._currentPageIndex=-1,this._skipKerning=!1;const t={...$a.defaultOptions,...e};this._textureSize=t.textureSize,this._mipmap=t.mipmap;const r=t.style.clone();t.overrideFill&&(r._fill.color=16777215,r._fill.alpha=1,r._fill.texture=A.WHITE,r._fill.fill=null),this.applyFillAsTint=t.overrideFill;const i=r.fontSize;r.fontSize=this.baseMeasurementFontSize;const n=Ar(r);t.overrideSize?r._stroke&&(r._stroke.width*=this.baseRenderedFontSize/i):r.fontSize=this.baseRenderedFontSize=i,this._style=r,this._skipKerning=t.skipKerning??!1,this.resolution=t.resolution??1,this._padding=t.padding??4,t.textureStyle&&(this._textureStyle=t.textureStyle instanceof Je?t.textureStyle:new Je(t.textureStyle)),this.fontMetrics=Se.measureFont(n),this.lineHeight=r.lineHeight||this.fontMetrics.fontSize||r.fontSize}ensureCharacters(e){const t=Se.graphemeSegmenter(e).filter(m=>!this._currentChars.includes(m)).filter((m,_,y)=>y.indexOf(m)===_);if(!t.length)return;this._currentChars=[...this._currentChars,...t];let r;this._currentPageIndex===-1?r=this._nextPage():r=this.pages[this._currentPageIndex];let{canvas:i,context:n}=r.canvasAndContext,o=r.texture.source;const a=this._style;let h=this._currentX,l=this._currentY,c=this._currentMaxCharHeight;const u=this.baseRenderedFontSize/this.baseMeasurementFontSize,f=this._padding*u;let d=!1;const p=i.width/this.resolution,g=i.height/this.resolution;for(let m=0;m<t.length;m++){const _=t[m],y=Se.measureText(_,a,i,!1);y.lineHeight=y.height;const b=y.width*u,v=Math.ceil((a.fontStyle==="italic"?2:1)*b),w=y.height*u,S=v+f*2,T=w+f*2;if(d=!1,_!==`
`&&_!=="\r"&&_!=="	"&&_!==" "&&(d=!0,c=Math.ceil(Math.max(T,c))),h+S>p&&(l+=c,c=T,h=0,l+c>g)){o.update();const C=this._nextPage();i=C.canvasAndContext.canvas,n=C.canvasAndContext.context,o=C.texture.source,h=0,l=0,c=0}const k=b/u-(a.dropShadow?.distance??0)-(a._stroke?.width??0);if(this.chars[_]={id:_.codePointAt(0),xOffset:-this._padding,yOffset:-this._padding,xAdvance:k,kerning:{}},d){this._drawGlyph(n,y,h+f,l+f,u,a);const C=o.width*u,P=o.height*u,E=new N(h/C*o.width,l/P*o.height,S/C*o.width,T/P*o.height);this.chars[_].texture=new A({source:o,frame:E}),h+=Math.ceil(S)}}o.update(),this._currentX=h,this._currentY=l,this._currentMaxCharHeight=c,this._skipKerning&&this._applyKerning(t,n)}get pageTextures(){return I(O,"BitmapFont.pageTextures is deprecated, please use BitmapFont.pages instead."),this.pages}_applyKerning(e,t){const r=this._measureCache;for(let i=0;i<e.length;i++){const n=e[i];for(let o=0;o<this._currentChars.length;o++){const a=this._currentChars[o];let h=r[n];h||(h=r[n]=t.measureText(n).width);let l=r[a];l||(l=r[a]=t.measureText(a).width);let c=t.measureText(n+a).width,u=c-(h+l);u&&(this.chars[n].kerning[a]=u),c=t.measureText(n+a).width,u=c-(h+l),u&&(this.chars[a].kerning[n]=u)}}}_nextPage(){this._currentPageIndex++;const e=this.resolution,t=Le.getOptimalCanvasAndContext(this._textureSize,this._textureSize,e);this._setupContext(t.context,this._style,e);const r=e*(this.baseRenderedFontSize/this.baseMeasurementFontSize),i=new A({source:new Ut({resource:t.canvas,resolution:r,alphaMode:"premultiply-alpha-on-upload",autoGenerateMipmaps:this._mipmap})});this._textureStyle&&(i.source.style=this._textureStyle);const n={canvasAndContext:t,texture:i};return this.pages[this._currentPageIndex]=n,n}_setupContext(e,t,r){t.fontSize=this.baseRenderedFontSize,e.scale(r,r),e.font=Ar(t),t.fontSize=this.baseMeasurementFontSize,e.textBaseline=t.textBaseline;const i=t._stroke,n=i?.width??0;if(i&&(e.lineWidth=n,e.lineJoin=i.join,e.miterLimit=i.miterLimit,e.strokeStyle=Rr(i,e)),t._fill&&(e.fillStyle=Rr(t._fill,e)),t.dropShadow){const o=t.dropShadow,a=L.shared.setValue(o.color).toArray(),h=o.blur*r,l=o.distance*r;e.shadowColor=`rgba(${a[0]*255},${a[1]*255},${a[2]*255},${o.alpha})`,e.shadowBlur=h,e.shadowOffsetX=Math.cos(o.angle)*l,e.shadowOffsetY=Math.sin(o.angle)*l}else e.shadowColor="black",e.shadowBlur=0,e.shadowOffsetX=0,e.shadowOffsetY=0}_drawGlyph(e,t,r,i,n,o){const a=t.text,h=t.fontProperties,c=(o._stroke?.width??0)*n,u=r+c/2,f=i-c/2,d=h.descent*n,p=t.lineHeight*n;o.stroke&&c&&e.strokeText(a,u,f+p-d),o._fill&&e.fillText(a,u,f+p-d)}destroy(){super.destroy();for(let e=0;e<this.pages.length;e++){const{canvasAndContext:t,texture:r}=this.pages[e];Le.returnCanvasAndContext(t),r.destroy(!0)}this.pages=null}};Va.defaultOptions={textureSize:512,style:new Oe,mipmap:!0};let Jn=Va;function Xa(s,e,t,r){const i={width:0,height:0,offsetY:0,scale:e.fontSize/t.baseMeasurementFontSize,lines:[{width:0,charPositions:[],spaceWidth:0,spacesIndex:[],chars:[]}]};i.offsetY=t.baseLineOffset;let n=i.lines[0],o=null,a=!0;const h={width:0,start:0,index:0,positions:[],chars:[]},l=p=>{const g=n.width;for(let m=0;m<h.index;m++){const _=p.positions[m];n.chars.push(p.chars[m]),n.charPositions.push(_+g)}n.width+=p.width,a=!1,h.width=0,h.index=0,h.chars.length=0},c=()=>{let p=n.chars.length-1;if(r){let g=n.chars[p];for(;g===" ";)n.width-=t.chars[g].xAdvance,g=n.chars[--p]}i.width=Math.max(i.width,n.width),n={width:0,charPositions:[],chars:[],spaceWidth:0,spacesIndex:[]},a=!0,i.lines.push(n),i.height+=t.lineHeight},u=t.baseMeasurementFontSize/e.fontSize,f=e.letterSpacing*u,d=e.wordWrapWidth*u;for(let p=0;p<s.length+1;p++){let g;const m=p===s.length;m||(g=s[p]);const _=t.chars[g]||t.chars[" "];if(/(?:\s)/.test(g)||g==="\r"||g===`
`||m){if(!a&&e.wordWrap&&n.width+h.width-f>d?(c(),l(h),m||n.charPositions.push(0)):(h.start=n.width,l(h),m||n.charPositions.push(0)),g==="\r"||g===`
`)n.width!==0&&c();else if(!m){const w=_.xAdvance+(_.kerning[o]||0)+f;n.width+=w,n.spaceWidth=w,n.spacesIndex.push(n.charPositions.length),n.chars.push(g)}}else{const v=_.kerning[o]||0,w=_.xAdvance+v+f;h.positions[h.index++]=h.width+v,h.chars.push(g),h.width+=w}o=g}return c(),e.align==="center"?bf(i):e.align==="right"?vf(i):e.align==="justify"&&Tf(i),i}function bf(s){for(let e=0;e<s.lines.length;e++){const t=s.lines[e],r=s.width/2-t.width/2;for(let i=0;i<t.charPositions.length;i++)t.charPositions[i]+=r}}function vf(s){for(let e=0;e<s.lines.length;e++){const t=s.lines[e],r=s.width-t.width;for(let i=0;i<t.charPositions.length;i++)t.charPositions[i]+=r}}function Tf(s){const e=s.width;for(let t=0;t<s.lines.length;t++){const r=s.lines[t];let i=0,n=r.spacesIndex[i++],o=0;const a=r.spacesIndex.length,l=(e-r.width)/a;for(let c=0;c<r.charPositions.length;c++)c===n&&(n=r.spacesIndex[i++],o+=l),r.charPositions[c]+=o}}function Sf(s){if(s==="")return[];typeof s=="string"&&(s=[s]);const e=[];for(let t=0,r=s.length;t<r;t++){const i=s[t];if(Array.isArray(i)){if(i.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${i.length}.`);if(i[0].length===0||i[1].length===0)throw new Error("[BitmapFont]: Invalid character delimiter.");const n=i[0].charCodeAt(0),o=i[1].charCodeAt(0);if(o<n)throw new Error("[BitmapFont]: Invalid character range.");for(let a=n,h=o;a<=h;a++)e.push(String.fromCharCode(a))}else e.push(...Array.from(i))}if(e.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return e}let mr=0;class wf{constructor(){this.ALPHA=[["a","z"],["A","Z"]," "],this.NUMERIC=[["0","9"]],this.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],this.ASCII=[[" ","~"]],this.defaultOptions={chars:this.ALPHANUMERIC,resolution:1,padding:4,skipKerning:!1,textureStyle:null}}getFont(e,t){let r=`${t.fontFamily}-bitmap`,i=!0;if(t._fill.fill&&!t._stroke)r+=t._fill.fill.styleKey,i=!1;else if(t._stroke||t.dropShadow){let o=t.styleKey;o=o.substring(0,o.lastIndexOf("-")),r=`${o}-bitmap`,i=!1}if(!re.has(r)){const o=new Jn({style:t,overrideFill:i,overrideSize:!0,...this.defaultOptions});mr++,mr>50&&F("BitmapText",`You have dynamically created ${mr} bitmap fonts, this can be inefficient. Try pre installing your font styles using \`BitmapFont.install({name:"style1", style})\``),o.once("destroy",()=>{mr--,re.remove(r)}),re.set(r,o)}const n=re.get(r);return n.ensureCharacters?.(e),n}getLayout(e,t,r=!0){const i=this.getFont(e,t),n=Se.graphemeSegmenter(e);return Xa(n,t,i,r)}measureText(e,t,r=!0){return this.getLayout(e,t,r)}install(...e){let t=e[0];typeof t=="string"&&(t={name:t,style:e[1],chars:e[2]?.chars,resolution:e[2]?.resolution,padding:e[2]?.padding,skipKerning:e[2]?.skipKerning},I(O,"BitmapFontManager.install(name, style, options) is deprecated, use BitmapFontManager.install({name, style, ...options})"));const r=t?.name;if(!r)throw new Error("[BitmapFontManager] Property `name` is required.");t={...this.defaultOptions,...t};const i=t.style,n=i instanceof Oe?i:new Oe(i),o=n._fill.fill!==null&&n._fill.fill!==void 0,a=new Jn({style:n,overrideFill:o,skipKerning:t.skipKerning,padding:t.padding,resolution:t.resolution,overrideSize:!1,textureStyle:t.textureStyle}),h=Sf(t.chars);return a.ensureCharacters(h.join("")),re.set(`${r}-bitmap`,a),a.once("destroy",()=>re.remove(`${r}-bitmap`)),a}uninstall(e){const t=`${e}-bitmap`,r=re.get(t);r&&r.destroy()}}const Pf=new wf;class Cf{constructor(e){this._attachedDomElements=[],this._renderer=e,this._renderer.runners.postrender.add(this),this._domElement=document.createElement("div"),this._domElement.style.position="absolute",this._domElement.style.top="0",this._domElement.style.left="0",this._domElement.style.pointerEvents="none",this._domElement.style.zIndex="1000"}addRenderable(e,t){this._attachedDomElements.includes(e)||this._attachedDomElements.push(e)}updateRenderable(e){}validateRenderable(e){return!0}postrender(){const e=this._attachedDomElements;if(e.length===0){this._domElement.remove();return}const t=this._renderer.view.canvas;this._domElement.parentNode!==t.parentNode&&t.parentNode?.appendChild(this._domElement);const r=parseFloat(t.style.width)/t.width*this._renderer.resolution,i=parseFloat(t.style.height)/t.height*this._renderer.resolution;this._domElement.style.transform=`translate(${t.offsetLeft}px, ${t.offsetTop}px) scale(${r}, ${i})`;for(let n=0;n<e.length;n++){const o=e[n],a=o.element;if(!o.parent||o.globalDisplayStatus<7)a?.remove(),e.splice(n,1),n--;else{this._domElement.contains(a)||(a.style.position="absolute",a.style.pointerEvents="auto",this._domElement.appendChild(a));const h=o.worldTransform,l=o._anchor,c=o.width*l.x,u=o.height*l.y;a.style.transformOrigin=`${c}px ${u}px`,a.style.transform=`matrix(${h.a}, ${h.b}, ${h.c}, ${h.d}, ${h.tx-c}, ${h.ty-u})`,a.style.opacity=o.groupAlpha.toString()}}}destroy(){this._renderer.runners.postrender.remove(this);for(let e=0;e<this._attachedDomElements.length;e++)this._attachedDomElements[e].element?.remove();this._attachedDomElements.length=0,this._domElement.remove(),this._renderer=null}}Cf.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"dom"};class Ef{constructor(){this.interactionFrequency=10,this._deltaTime=0,this._didMove=!1,this._tickerAdded=!1,this._pauseUpdate=!0}init(e){this.removeTickerListener(),this.events=e,this.interactionFrequency=10,this._deltaTime=0,this._didMove=!1,this._tickerAdded=!1,this._pauseUpdate=!0}get pauseUpdate(){return this._pauseUpdate}set pauseUpdate(e){this._pauseUpdate=e}addTickerListener(){this._tickerAdded||!this.domElement||(we.system.add(this._tickerUpdate,this,Dt.INTERACTION),this._tickerAdded=!0)}removeTickerListener(){this._tickerAdded&&(we.system.remove(this._tickerUpdate,this),this._tickerAdded=!1)}pointerMoved(){this._didMove=!0}_update(){if(!this.domElement||this._pauseUpdate)return;if(this._didMove){this._didMove=!1;return}const e=this.events._rootPointerEvent;this.events.supportsTouchEvents&&e.pointerType==="touch"||globalThis.document.dispatchEvent(this.events.supportsPointerEvents?new PointerEvent("pointermove",{clientX:e.clientX,clientY:e.clientY,pointerType:e.pointerType,pointerId:e.pointerId}):new MouseEvent("mousemove",{clientX:e.clientX,clientY:e.clientY}))}_tickerUpdate(e){this._deltaTime+=e.deltaTime,!(this._deltaTime<this.interactionFrequency)&&(this._deltaTime=0,this._update())}}const Fe=new Ef;class kr extends $t{constructor(){super(...arguments),this.client=new H,this.movement=new H,this.offset=new H,this.global=new H,this.screen=new H}get clientX(){return this.client.x}get clientY(){return this.client.y}get x(){return this.clientX}get y(){return this.clientY}get movementX(){return this.movement.x}get movementY(){return this.movement.y}get offsetX(){return this.offset.x}get offsetY(){return this.offset.y}get globalX(){return this.global.x}get globalY(){return this.global.y}get screenX(){return this.screen.x}get screenY(){return this.screen.y}getLocalPosition(e,t,r){return e.worldTransform.applyInverse(r||this.global,t)}getModifierState(e){return"getModifierState"in this.nativeEvent&&this.nativeEvent.getModifierState(e)}initMouseEvent(e,t,r,i,n,o,a,h,l,c,u,f,d,p,g){throw new Error("Method not implemented.")}}class ge extends kr{constructor(){super(...arguments),this.width=0,this.height=0,this.isPrimary=!1}getCoalescedEvents(){return this.type==="pointermove"||this.type==="mousemove"||this.type==="touchmove"?[this]:[]}getPredictedEvents(){throw new Error("getPredictedEvents is not supported!")}}class ft extends kr{constructor(){super(...arguments),this.DOM_DELTA_PIXEL=0,this.DOM_DELTA_LINE=1,this.DOM_DELTA_PAGE=2}}ft.DOM_DELTA_PIXEL=0;ft.DOM_DELTA_LINE=1;ft.DOM_DELTA_PAGE=2;const Mf=2048,Af=new H,wt=new H;class Bf{constructor(e){this.dispatch=new de,this.moveOnAll=!1,this.enableGlobalMoveEvents=!0,this.mappingState={trackingData:{}},this.eventPool=new Map,this._allInteractiveElements=[],this._hitElements=[],this._isPointerMoveEvent=!1,this.rootTarget=e,this.hitPruneFn=this.hitPruneFn.bind(this),this.hitTestFn=this.hitTestFn.bind(this),this.mapPointerDown=this.mapPointerDown.bind(this),this.mapPointerMove=this.mapPointerMove.bind(this),this.mapPointerOut=this.mapPointerOut.bind(this),this.mapPointerOver=this.mapPointerOver.bind(this),this.mapPointerUp=this.mapPointerUp.bind(this),this.mapPointerUpOutside=this.mapPointerUpOutside.bind(this),this.mapWheel=this.mapWheel.bind(this),this.mappingTable={},this.addEventMapping("pointerdown",this.mapPointerDown),this.addEventMapping("pointermove",this.mapPointerMove),this.addEventMapping("pointerout",this.mapPointerOut),this.addEventMapping("pointerleave",this.mapPointerOut),this.addEventMapping("pointerover",this.mapPointerOver),this.addEventMapping("pointerup",this.mapPointerUp),this.addEventMapping("pointerupoutside",this.mapPointerUpOutside),this.addEventMapping("wheel",this.mapWheel)}addEventMapping(e,t){this.mappingTable[e]||(this.mappingTable[e]=[]),this.mappingTable[e].push({fn:t,priority:0}),this.mappingTable[e].sort((r,i)=>r.priority-i.priority)}dispatchEvent(e,t){e.propagationStopped=!1,e.propagationImmediatelyStopped=!1,this.propagate(e,t),this.dispatch.emit(t||e.type,e)}mapEvent(e){if(!this.rootTarget)return;const t=this.mappingTable[e.type];if(t)for(let r=0,i=t.length;r<i;r++)t[r].fn(e);else F(`[EventBoundary]: Event mapping not defined for ${e.type}`)}hitTest(e,t){Fe.pauseUpdate=!0;const i=this._isPointerMoveEvent&&this.enableGlobalMoveEvents?"hitTestMoveRecursive":"hitTestRecursive",n=this[i](this.rootTarget,this.rootTarget.eventMode,Af.set(e,t),this.hitTestFn,this.hitPruneFn);return n&&n[0]}propagate(e,t){if(!e.target)return;const r=e.composedPath();e.eventPhase=e.CAPTURING_PHASE;for(let i=0,n=r.length-1;i<n;i++)if(e.currentTarget=r[i],this.notifyTarget(e,t),e.propagationStopped||e.propagationImmediatelyStopped)return;if(e.eventPhase=e.AT_TARGET,e.currentTarget=e.target,this.notifyTarget(e,t),!(e.propagationStopped||e.propagationImmediatelyStopped)){e.eventPhase=e.BUBBLING_PHASE;for(let i=r.length-2;i>=0;i--)if(e.currentTarget=r[i],this.notifyTarget(e,t),e.propagationStopped||e.propagationImmediatelyStopped)return}}all(e,t,r=this._allInteractiveElements){if(r.length===0)return;e.eventPhase=e.BUBBLING_PHASE;const i=Array.isArray(t)?t:[t];for(let n=r.length-1;n>=0;n--)i.forEach(o=>{e.currentTarget=r[n],this.notifyTarget(e,o)})}propagationPath(e){const t=[e];for(let r=0;r<Mf&&e!==this.rootTarget&&e.parent;r++){if(!e.parent)throw new Error("Cannot find propagation path to disconnected target");t.push(e.parent),e=e.parent}return t.reverse(),t}hitTestMoveRecursive(e,t,r,i,n,o=!1){let a=!1;if(this._interactivePrune(e))return null;if((e.eventMode==="dynamic"||t==="dynamic")&&(Fe.pauseUpdate=!1),e.interactiveChildren&&e.children){const c=e.children;for(let u=c.length-1;u>=0;u--){const f=c[u],d=this.hitTestMoveRecursive(f,this._isInteractive(t)?t:f.eventMode,r,i,n,o||n(e,r));if(d){if(d.length>0&&!d[d.length-1].parent)continue;const p=e.isInteractive();(d.length>0||p)&&(p&&this._allInteractiveElements.push(e),d.push(e)),this._hitElements.length===0&&(this._hitElements=d),a=!0}}}const h=this._isInteractive(t),l=e.isInteractive();return l&&l&&this._allInteractiveElements.push(e),o||this._hitElements.length>0?null:a?this._hitElements:h&&!n(e,r)&&i(e,r)?l?[e]:[]:null}hitTestRecursive(e,t,r,i,n){if(this._interactivePrune(e)||n(e,r))return null;if((e.eventMode==="dynamic"||t==="dynamic")&&(Fe.pauseUpdate=!1),e.interactiveChildren&&e.children){const h=e.children,l=r;for(let c=h.length-1;c>=0;c--){const u=h[c],f=this.hitTestRecursive(u,this._isInteractive(t)?t:u.eventMode,l,i,n);if(f){if(f.length>0&&!f[f.length-1].parent)continue;const d=e.isInteractive();return(f.length>0||d)&&f.push(e),f}}}const o=this._isInteractive(t),a=e.isInteractive();return o&&i(e,r)?a?[e]:[]:null}_isInteractive(e){return e==="static"||e==="dynamic"}_interactivePrune(e){return!e||!e.visible||!e.renderable||!e.measurable||e.eventMode==="none"||e.eventMode==="passive"&&!e.interactiveChildren}hitPruneFn(e,t){if(e.hitArea&&(e.worldTransform.applyInverse(t,wt),!e.hitArea.contains(wt.x,wt.y)))return!0;if(e.effects&&e.effects.length)for(let r=0;r<e.effects.length;r++){const i=e.effects[r];if(i.containsPoint&&!i.containsPoint(t,this.hitTestFn))return!0}return!1}hitTestFn(e,t){return e.hitArea?!0:e?.containsPoint?(e.worldTransform.applyInverse(t,wt),e.containsPoint(wt)):!1}notifyTarget(e,t){if(!e.currentTarget.isInteractive())return;t??(t=e.type);const r=`on${t}`;e.currentTarget[r]?.(e);const i=e.eventPhase===e.CAPTURING_PHASE||e.eventPhase===e.AT_TARGET?`${t}capture`:t;this._notifyListeners(e,i),e.eventPhase===e.AT_TARGET&&this._notifyListeners(e,t)}mapPointerDown(e){if(!(e instanceof ge)){F("EventBoundary cannot map a non-pointer event as a pointer event");return}const t=this.createPointerEvent(e);if(this.dispatchEvent(t,"pointerdown"),t.pointerType==="touch")this.dispatchEvent(t,"touchstart");else if(t.pointerType==="mouse"||t.pointerType==="pen"){const i=t.button===2;this.dispatchEvent(t,i?"rightdown":"mousedown")}const r=this.trackingData(e.pointerId);r.pressTargetsByButton[e.button]=t.composedPath(),this.freeEvent(t)}mapPointerMove(e){if(!(e instanceof ge)){F("EventBoundary cannot map a non-pointer event as a pointer event");return}this._allInteractiveElements.length=0,this._hitElements.length=0,this._isPointerMoveEvent=!0;const t=this.createPointerEvent(e);this._isPointerMoveEvent=!1;const r=t.pointerType==="mouse"||t.pointerType==="pen",i=this.trackingData(e.pointerId),n=this.findMountedTarget(i.overTargets);if(i.overTargets?.length>0&&n!==t.target){const h=e.type==="mousemove"?"mouseout":"pointerout",l=this.createPointerEvent(e,h,n);if(this.dispatchEvent(l,"pointerout"),r&&this.dispatchEvent(l,"mouseout"),!t.composedPath().includes(n)){const c=this.createPointerEvent(e,"pointerleave",n);for(c.eventPhase=c.AT_TARGET;c.target&&!t.composedPath().includes(c.target);)c.currentTarget=c.target,this.notifyTarget(c),r&&this.notifyTarget(c,"mouseleave"),c.target=c.target.parent;this.freeEvent(c)}this.freeEvent(l)}if(n!==t.target){const h=e.type==="mousemove"?"mouseover":"pointerover",l=this.clonePointerEvent(t,h);this.dispatchEvent(l,"pointerover"),r&&this.dispatchEvent(l,"mouseover");let c=n?.parent;for(;c&&c!==this.rootTarget.parent&&c!==t.target;)c=c.parent;if(!c||c===this.rootTarget.parent){const f=this.clonePointerEvent(t,"pointerenter");for(f.eventPhase=f.AT_TARGET;f.target&&f.target!==n&&f.target!==this.rootTarget.parent;)f.currentTarget=f.target,this.notifyTarget(f),r&&this.notifyTarget(f,"mouseenter"),f.target=f.target.parent;this.freeEvent(f)}this.freeEvent(l)}const o=[],a=this.enableGlobalMoveEvents??!0;this.moveOnAll?o.push("pointermove"):this.dispatchEvent(t,"pointermove"),a&&o.push("globalpointermove"),t.pointerType==="touch"&&(this.moveOnAll?o.splice(1,0,"touchmove"):this.dispatchEvent(t,"touchmove"),a&&o.push("globaltouchmove")),r&&(this.moveOnAll?o.splice(1,0,"mousemove"):this.dispatchEvent(t,"mousemove"),a&&o.push("globalmousemove"),this.cursor=t.target?.cursor),o.length>0&&this.all(t,o),this._allInteractiveElements.length=0,this._hitElements.length=0,i.overTargets=t.composedPath(),this.freeEvent(t)}mapPointerOver(e){if(!(e instanceof ge)){F("EventBoundary cannot map a non-pointer event as a pointer event");return}const t=this.trackingData(e.pointerId),r=this.createPointerEvent(e),i=r.pointerType==="mouse"||r.pointerType==="pen";this.dispatchEvent(r,"pointerover"),i&&this.dispatchEvent(r,"mouseover"),r.pointerType==="mouse"&&(this.cursor=r.target?.cursor);const n=this.clonePointerEvent(r,"pointerenter");for(n.eventPhase=n.AT_TARGET;n.target&&n.target!==this.rootTarget.parent;)n.currentTarget=n.target,this.notifyTarget(n),i&&this.notifyTarget(n,"mouseenter"),n.target=n.target.parent;t.overTargets=r.composedPath(),this.freeEvent(r),this.freeEvent(n)}mapPointerOut(e){if(!(e instanceof ge)){F("EventBoundary cannot map a non-pointer event as a pointer event");return}const t=this.trackingData(e.pointerId);if(t.overTargets){const r=e.pointerType==="mouse"||e.pointerType==="pen",i=this.findMountedTarget(t.overTargets),n=this.createPointerEvent(e,"pointerout",i);this.dispatchEvent(n),r&&this.dispatchEvent(n,"mouseout");const o=this.createPointerEvent(e,"pointerleave",i);for(o.eventPhase=o.AT_TARGET;o.target&&o.target!==this.rootTarget.parent;)o.currentTarget=o.target,this.notifyTarget(o),r&&this.notifyTarget(o,"mouseleave"),o.target=o.target.parent;t.overTargets=null,this.freeEvent(n),this.freeEvent(o)}this.cursor=null}mapPointerUp(e){if(!(e instanceof ge)){F("EventBoundary cannot map a non-pointer event as a pointer event");return}const t=performance.now(),r=this.createPointerEvent(e);if(this.dispatchEvent(r,"pointerup"),r.pointerType==="touch")this.dispatchEvent(r,"touchend");else if(r.pointerType==="mouse"||r.pointerType==="pen"){const a=r.button===2;this.dispatchEvent(r,a?"rightup":"mouseup")}const i=this.trackingData(e.pointerId),n=this.findMountedTarget(i.pressTargetsByButton[e.button]);let o=n;if(n&&!r.composedPath().includes(n)){let a=n;for(;a&&!r.composedPath().includes(a);){if(r.currentTarget=a,this.notifyTarget(r,"pointerupoutside"),r.pointerType==="touch")this.notifyTarget(r,"touchendoutside");else if(r.pointerType==="mouse"||r.pointerType==="pen"){const h=r.button===2;this.notifyTarget(r,h?"rightupoutside":"mouseupoutside")}a=a.parent}delete i.pressTargetsByButton[e.button],o=a}if(o){const a=this.clonePointerEvent(r,"click");a.target=o,a.path=null,i.clicksByButton[e.button]||(i.clicksByButton[e.button]={clickCount:0,target:a.target,timeStamp:t});const h=i.clicksByButton[e.button];if(h.target===a.target&&t-h.timeStamp<200?++h.clickCount:h.clickCount=1,h.target=a.target,h.timeStamp=t,a.detail=h.clickCount,a.pointerType==="mouse"){const l=a.button===2;this.dispatchEvent(a,l?"rightclick":"click")}else a.pointerType==="touch"&&this.dispatchEvent(a,"tap");this.dispatchEvent(a,"pointertap"),this.freeEvent(a)}this.freeEvent(r)}mapPointerUpOutside(e){if(!(e instanceof ge)){F("EventBoundary cannot map a non-pointer event as a pointer event");return}const t=this.trackingData(e.pointerId),r=this.findMountedTarget(t.pressTargetsByButton[e.button]),i=this.createPointerEvent(e);if(r){let n=r;for(;n;)i.currentTarget=n,this.notifyTarget(i,"pointerupoutside"),i.pointerType==="touch"?this.notifyTarget(i,"touchendoutside"):(i.pointerType==="mouse"||i.pointerType==="pen")&&this.notifyTarget(i,i.button===2?"rightupoutside":"mouseupoutside"),n=n.parent;delete t.pressTargetsByButton[e.button]}this.freeEvent(i)}mapWheel(e){if(!(e instanceof ft)){F("EventBoundary cannot map a non-wheel event as a wheel event");return}const t=this.createWheelEvent(e);this.dispatchEvent(t),this.freeEvent(t)}findMountedTarget(e){if(!e)return null;let t=e[0];for(let r=1;r<e.length&&e[r].parent===t;r++)t=e[r];return t}createPointerEvent(e,t,r){const i=this.allocateEvent(ge);return this.copyPointerData(e,i),this.copyMouseData(e,i),this.copyData(e,i),i.nativeEvent=e.nativeEvent,i.originalEvent=e,i.target=r??this.hitTest(i.global.x,i.global.y)??this._hitElements[0],typeof t=="string"&&(i.type=t),i}createWheelEvent(e){const t=this.allocateEvent(ft);return this.copyWheelData(e,t),this.copyMouseData(e,t),this.copyData(e,t),t.nativeEvent=e.nativeEvent,t.originalEvent=e,t.target=this.hitTest(t.global.x,t.global.y),t}clonePointerEvent(e,t){const r=this.allocateEvent(ge);return r.nativeEvent=e.nativeEvent,r.originalEvent=e.originalEvent,this.copyPointerData(e,r),this.copyMouseData(e,r),this.copyData(e,r),r.target=e.target,r.path=e.composedPath().slice(),r.type=t??r.type,r}copyWheelData(e,t){t.deltaMode=e.deltaMode,t.deltaX=e.deltaX,t.deltaY=e.deltaY,t.deltaZ=e.deltaZ}copyPointerData(e,t){e instanceof ge&&t instanceof ge&&(t.pointerId=e.pointerId,t.width=e.width,t.height=e.height,t.isPrimary=e.isPrimary,t.pointerType=e.pointerType,t.pressure=e.pressure,t.tangentialPressure=e.tangentialPressure,t.tiltX=e.tiltX,t.tiltY=e.tiltY,t.twist=e.twist)}copyMouseData(e,t){e instanceof kr&&t instanceof kr&&(t.altKey=e.altKey,t.button=e.button,t.buttons=e.buttons,t.client.copyFrom(e.client),t.ctrlKey=e.ctrlKey,t.metaKey=e.metaKey,t.movement.copyFrom(e.movement),t.screen.copyFrom(e.screen),t.shiftKey=e.shiftKey,t.global.copyFrom(e.global))}copyData(e,t){t.isTrusted=e.isTrusted,t.srcElement=e.srcElement,t.timeStamp=performance.now(),t.type=e.type,t.detail=e.detail,t.view=e.view,t.which=e.which,t.layer.copyFrom(e.layer),t.page.copyFrom(e.page)}trackingData(e){return this.mappingState.trackingData[e]||(this.mappingState.trackingData[e]={pressTargetsByButton:{},clicksByButton:{},overTarget:null}),this.mappingState.trackingData[e]}allocateEvent(e){this.eventPool.has(e)||this.eventPool.set(e,[]);const t=this.eventPool.get(e).pop()||new e(this);return t.eventPhase=t.NONE,t.currentTarget=null,t.defaultPrevented=!1,t.path=null,t.target=null,t}freeEvent(e){if(e.manager!==this)throw new Error("It is illegal to free an event not managed by this EventBoundary!");const t=e.constructor;this.eventPool.has(t)||this.eventPool.set(t,[]),this.eventPool.get(t).push(e)}_notifyListeners(e,t){const r=e.currentTarget._events[t];if(r)if("fn"in r)r.once&&e.currentTarget.removeListener(t,r.fn,void 0,!0),r.fn.call(r.context,e);else for(let i=0,n=r.length;i<n&&!e.propagationImmediatelyStopped;i++)r[i].once&&e.currentTarget.removeListener(t,r[i].fn,void 0,!0),r[i].fn.call(r[i].context,e)}}const Rf=1,kf={touchstart:"pointerdown",touchend:"pointerup",touchendoutside:"pointerupoutside",touchmove:"pointermove",touchcancel:"pointercancel"},bi=class $s{constructor(e){this.supportsTouchEvents="ontouchstart"in globalThis,this.supportsPointerEvents=!!globalThis.PointerEvent,this.domElement=null,this.resolution=1,this.renderer=e,this.rootBoundary=new Bf(null),Fe.init(this),this.autoPreventDefault=!0,this._eventsAdded=!1,this._rootPointerEvent=new ge(null),this._rootWheelEvent=new ft(null),this.cursorStyles={default:"inherit",pointer:"pointer"},this.features=new Proxy({...$s.defaultEventFeatures},{set:(t,r,i)=>(r==="globalMove"&&(this.rootBoundary.enableGlobalMoveEvents=i),t[r]=i,!0)}),this._onPointerDown=this._onPointerDown.bind(this),this._onPointerMove=this._onPointerMove.bind(this),this._onPointerUp=this._onPointerUp.bind(this),this._onPointerOverOut=this._onPointerOverOut.bind(this),this.onWheel=this.onWheel.bind(this)}static get defaultEventMode(){return this._defaultEventMode}init(e){const{canvas:t,resolution:r}=this.renderer;this.setTargetElement(t),this.resolution=r,$s._defaultEventMode=e.eventMode??"passive",Object.assign(this.features,e.eventFeatures??{}),this.rootBoundary.enableGlobalMoveEvents=this.features.globalMove}resolutionChange(e){this.resolution=e}destroy(){this.setTargetElement(null),this.renderer=null,this._currentCursor=null}setCursor(e){e||(e="default");let t=!0;if(globalThis.OffscreenCanvas&&this.domElement instanceof OffscreenCanvas&&(t=!1),this._currentCursor===e)return;this._currentCursor=e;const r=this.cursorStyles[e];if(r)switch(typeof r){case"string":t&&(this.domElement.style.cursor=r);break;case"function":r(e);break;case"object":t&&Object.assign(this.domElement.style,r);break}else t&&typeof e=="string"&&!Object.prototype.hasOwnProperty.call(this.cursorStyles,e)&&(this.domElement.style.cursor=e)}get pointer(){return this._rootPointerEvent}_onPointerDown(e){if(!this.features.click)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered;const t=this._normalizeToPointerData(e);this.autoPreventDefault&&t[0].isNormalized&&(e.cancelable||!("cancelable"in e))&&e.preventDefault();for(let r=0,i=t.length;r<i;r++){const n=t[r],o=this._bootstrapEvent(this._rootPointerEvent,n);this.rootBoundary.mapEvent(o)}this.setCursor(this.rootBoundary.cursor)}_onPointerMove(e){if(!this.features.move)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,Fe.pointerMoved();const t=this._normalizeToPointerData(e);for(let r=0,i=t.length;r<i;r++){const n=this._bootstrapEvent(this._rootPointerEvent,t[r]);this.rootBoundary.mapEvent(n)}this.setCursor(this.rootBoundary.cursor)}_onPointerUp(e){if(!this.features.click)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered;let t=e.target;e.composedPath&&e.composedPath().length>0&&(t=e.composedPath()[0]);const r=t!==this.domElement?"outside":"",i=this._normalizeToPointerData(e);for(let n=0,o=i.length;n<o;n++){const a=this._bootstrapEvent(this._rootPointerEvent,i[n]);a.type+=r,this.rootBoundary.mapEvent(a)}this.setCursor(this.rootBoundary.cursor)}_onPointerOverOut(e){if(!this.features.click)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered;const t=this._normalizeToPointerData(e);for(let r=0,i=t.length;r<i;r++){const n=this._bootstrapEvent(this._rootPointerEvent,t[r]);this.rootBoundary.mapEvent(n)}this.setCursor(this.rootBoundary.cursor)}onWheel(e){if(!this.features.wheel)return;const t=this.normalizeWheelEvent(e);this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.rootBoundary.mapEvent(t)}setTargetElement(e){this._removeEvents(),this.domElement=e,Fe.domElement=e,this._addEvents()}_addEvents(){if(this._eventsAdded||!this.domElement)return;Fe.addTickerListener();const e=this.domElement.style;e&&(globalThis.navigator.msPointerEnabled?(e.msContentZooming="none",e.msTouchAction="none"):this.supportsPointerEvents&&(e.touchAction="none")),this.supportsPointerEvents?(globalThis.document.addEventListener("pointermove",this._onPointerMove,!0),this.domElement.addEventListener("pointerdown",this._onPointerDown,!0),this.domElement.addEventListener("pointerleave",this._onPointerOverOut,!0),this.domElement.addEventListener("pointerover",this._onPointerOverOut,!0),globalThis.addEventListener("pointerup",this._onPointerUp,!0)):(globalThis.document.addEventListener("mousemove",this._onPointerMove,!0),this.domElement.addEventListener("mousedown",this._onPointerDown,!0),this.domElement.addEventListener("mouseout",this._onPointerOverOut,!0),this.domElement.addEventListener("mouseover",this._onPointerOverOut,!0),globalThis.addEventListener("mouseup",this._onPointerUp,!0),this.supportsTouchEvents&&(this.domElement.addEventListener("touchstart",this._onPointerDown,!0),this.domElement.addEventListener("touchend",this._onPointerUp,!0),this.domElement.addEventListener("touchmove",this._onPointerMove,!0))),this.domElement.addEventListener("wheel",this.onWheel,{passive:!0,capture:!0}),this._eventsAdded=!0}_removeEvents(){if(!this._eventsAdded||!this.domElement)return;Fe.removeTickerListener();const e=this.domElement.style;e&&(globalThis.navigator.msPointerEnabled?(e.msContentZooming="",e.msTouchAction=""):this.supportsPointerEvents&&(e.touchAction="")),this.supportsPointerEvents?(globalThis.document.removeEventListener("pointermove",this._onPointerMove,!0),this.domElement.removeEventListener("pointerdown",this._onPointerDown,!0),this.domElement.removeEventListener("pointerleave",this._onPointerOverOut,!0),this.domElement.removeEventListener("pointerover",this._onPointerOverOut,!0),globalThis.removeEventListener("pointerup",this._onPointerUp,!0)):(globalThis.document.removeEventListener("mousemove",this._onPointerMove,!0),this.domElement.removeEventListener("mousedown",this._onPointerDown,!0),this.domElement.removeEventListener("mouseout",this._onPointerOverOut,!0),this.domElement.removeEventListener("mouseover",this._onPointerOverOut,!0),globalThis.removeEventListener("mouseup",this._onPointerUp,!0),this.supportsTouchEvents&&(this.domElement.removeEventListener("touchstart",this._onPointerDown,!0),this.domElement.removeEventListener("touchend",this._onPointerUp,!0),this.domElement.removeEventListener("touchmove",this._onPointerMove,!0))),this.domElement.removeEventListener("wheel",this.onWheel,!0),this.domElement=null,this._eventsAdded=!1}mapPositionToPoint(e,t,r){const i=this.domElement.isConnected?this.domElement.getBoundingClientRect():{width:this.domElement.width,height:this.domElement.height,left:0,top:0},n=1/this.resolution;e.x=(t-i.left)*(this.domElement.width/i.width)*n,e.y=(r-i.top)*(this.domElement.height/i.height)*n}_normalizeToPointerData(e){const t=[];if(this.supportsTouchEvents&&e instanceof TouchEvent)for(let r=0,i=e.changedTouches.length;r<i;r++){const n=e.changedTouches[r];typeof n.button>"u"&&(n.button=0),typeof n.buttons>"u"&&(n.buttons=1),typeof n.isPrimary>"u"&&(n.isPrimary=e.touches.length===1&&e.type==="touchstart"),typeof n.width>"u"&&(n.width=n.radiusX||1),typeof n.height>"u"&&(n.height=n.radiusY||1),typeof n.tiltX>"u"&&(n.tiltX=0),typeof n.tiltY>"u"&&(n.tiltY=0),typeof n.pointerType>"u"&&(n.pointerType="touch"),typeof n.pointerId>"u"&&(n.pointerId=n.identifier||0),typeof n.pressure>"u"&&(n.pressure=n.force||.5),typeof n.twist>"u"&&(n.twist=0),typeof n.tangentialPressure>"u"&&(n.tangentialPressure=0),typeof n.layerX>"u"&&(n.layerX=n.offsetX=n.clientX),typeof n.layerY>"u"&&(n.layerY=n.offsetY=n.clientY),n.isNormalized=!0,n.type=e.type,t.push(n)}else if(!globalThis.MouseEvent||e instanceof MouseEvent&&(!this.supportsPointerEvents||!(e instanceof globalThis.PointerEvent))){const r=e;typeof r.isPrimary>"u"&&(r.isPrimary=!0),typeof r.width>"u"&&(r.width=1),typeof r.height>"u"&&(r.height=1),typeof r.tiltX>"u"&&(r.tiltX=0),typeof r.tiltY>"u"&&(r.tiltY=0),typeof r.pointerType>"u"&&(r.pointerType="mouse"),typeof r.pointerId>"u"&&(r.pointerId=Rf),typeof r.pressure>"u"&&(r.pressure=.5),typeof r.twist>"u"&&(r.twist=0),typeof r.tangentialPressure>"u"&&(r.tangentialPressure=0),r.isNormalized=!0,t.push(r)}else t.push(e);return t}normalizeWheelEvent(e){const t=this._rootWheelEvent;return this._transferMouseData(t,e),t.deltaX=e.deltaX,t.deltaY=e.deltaY,t.deltaZ=e.deltaZ,t.deltaMode=e.deltaMode,this.mapPositionToPoint(t.screen,e.clientX,e.clientY),t.global.copyFrom(t.screen),t.offset.copyFrom(t.screen),t.nativeEvent=e,t.type=e.type,t}_bootstrapEvent(e,t){return e.originalEvent=null,e.nativeEvent=t,e.pointerId=t.pointerId,e.width=t.width,e.height=t.height,e.isPrimary=t.isPrimary,e.pointerType=t.pointerType,e.pressure=t.pressure,e.tangentialPressure=t.tangentialPressure,e.tiltX=t.tiltX,e.tiltY=t.tiltY,e.twist=t.twist,this._transferMouseData(e,t),this.mapPositionToPoint(e.screen,t.clientX,t.clientY),e.global.copyFrom(e.screen),e.offset.copyFrom(e.screen),e.isTrusted=t.isTrusted,e.type==="pointerleave"&&(e.type="pointerout"),e.type.startsWith("mouse")&&(e.type=e.type.replace("mouse","pointer")),e.type.startsWith("touch")&&(e.type=kf[e.type]||e.type),e}_transferMouseData(e,t){e.isTrusted=t.isTrusted,e.srcElement=t.srcElement,e.timeStamp=performance.now(),e.type=t.type,e.altKey=t.altKey,e.button=t.button,e.buttons=t.buttons,e.client.x=t.clientX,e.client.y=t.clientY,e.ctrlKey=t.ctrlKey,e.metaKey=t.metaKey,e.movement.x=t.movementX,e.movement.y=t.movementY,e.page.x=t.pageX,e.page.y=t.pageY,e.relatedTarget=null,e.shiftKey=t.shiftKey}};bi.extension={name:"events",type:[x.WebGLSystem,x.CanvasSystem,x.WebGPUSystem],priority:-1};bi.defaultEventFeatures={move:!0,globalMove:!0,click:!0,wheel:!0};let Gf=bi;const Bg={onclick:null,onmousedown:null,onmouseenter:null,onmouseleave:null,onmousemove:null,onglobalmousemove:null,onmouseout:null,onmouseover:null,onmouseup:null,onmouseupoutside:null,onpointercancel:null,onpointerdown:null,onpointerenter:null,onpointerleave:null,onpointermove:null,onglobalpointermove:null,onpointerout:null,onpointerover:null,onpointertap:null,onpointerup:null,onpointerupoutside:null,onrightclick:null,onrightdown:null,onrightup:null,onrightupoutside:null,ontap:null,ontouchcancel:null,ontouchend:null,ontouchendoutside:null,ontouchmove:null,onglobaltouchmove:null,ontouchstart:null,onwheel:null,get interactive(){return this.eventMode==="dynamic"||this.eventMode==="static"},set interactive(s){this.eventMode=s?"static":"passive"},_internalEventMode:void 0,get eventMode(){return this._internalEventMode??Gf.defaultEventMode},set eventMode(s){this._internalEventMode=s},isInteractive(){return this.eventMode==="static"||this.eventMode==="dynamic"},interactiveChildren:!0,hitArea:null,addEventListener(s,e,t){const r=typeof t=="boolean"&&t||typeof t=="object"&&t.capture,i=typeof t=="object"?t.signal:void 0,n=typeof t=="object"?t.once===!0:!1,o=typeof e=="function"?void 0:e;s=r?`${s}capture`:s;const a=typeof e=="function"?e:e.handleEvent,h=this;i&&i.addEventListener("abort",()=>{h.off(s,a,o)}),n?h.once(s,a,o):h.on(s,a,o)},removeEventListener(s,e,t){const r=typeof t=="boolean"&&t||typeof t=="object"&&t.capture,i=typeof e=="function"?void 0:e;s=r?`${s}capture`:s,e=typeof e=="function"?e:e.handleEvent,this.off(s,e,i)},dispatchEvent(s){if(!(s instanceof $t))throw new Error("Container cannot propagate events outside of the Federated Events API");return s.defaultPrevented=!1,s.path=null,s.target=this,s.manager.dispatchEvent(s),!s.defaultPrevented}};class If{constructor(e){this._renderer=e}push(e,t,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",canBundle:!1,action:"pushFilter",container:t,filterEffect:e})}pop(e,t,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"filter",action:"popFilter",canBundle:!1})}execute(e){e.action==="pushFilter"?this._renderer.filter.push(e):e.action==="popFilter"&&this._renderer.filter.pop()}destroy(){this._renderer=null}}If.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"filter"};function Ff(s,e){e.clear();const t=e.matrix;for(let r=0;r<s.length;r++){const i=s[r];i.globalDisplayStatus<7||(e.matrix=i.worldTransform,e.addBounds(i.bounds))}return e.matrix=t,e}const Uf=new Xt({attributes:{aPosition:{buffer:new Float32Array([0,0,1,0,1,1,0,1]),format:"float32x2",stride:2*4,offset:0}},indexBuffer:new Uint32Array([0,1,2,0,2,3])});class Df{constructor(){this.skip=!1,this.inputTexture=null,this.backTexture=null,this.filters=null,this.bounds=new se,this.container=null,this.blendRequired=!1,this.outputRenderSurface=null,this.outputOffset={x:0,y:0},this.globalFrame={x:0,y:0,width:0,height:0}}}class Of{constructor(e){this._filterStackIndex=0,this._filterStack=[],this._filterGlobalUniforms=new ne({uInputSize:{value:new Float32Array(4),type:"vec4<f32>"},uInputPixel:{value:new Float32Array(4),type:"vec4<f32>"},uInputClamp:{value:new Float32Array(4),type:"vec4<f32>"},uOutputFrame:{value:new Float32Array(4),type:"vec4<f32>"},uGlobalFrame:{value:new Float32Array(4),type:"vec4<f32>"},uOutputTexture:{value:new Float32Array(4),type:"vec4<f32>"}}),this._globalFilterBindGroup=new Re({}),this.renderer=e}get activeBackTexture(){return this._activeFilterData?.backTexture}push(e){const t=this.renderer,r=e.filterEffect.filters,i=this._pushFilterData();i.skip=!1,i.filters=r,i.container=e.container,i.outputRenderSurface=t.renderTarget.renderSurface;const n=t.renderTarget.renderTarget.colorTexture.source,o=n.resolution,a=n.antialias;if(r.length===0){i.skip=!0;return}const h=i.bounds;if(e.renderables?Ff(e.renderables,h):e.filterEffect.filterArea?(h.clear(),h.addRect(e.filterEffect.filterArea),h.applyMatrix(e.container.worldTransform)):e.container.getFastGlobalBounds(!0,h),e.container){const g=(e.container.renderGroup||e.container.parentRenderGroup).cacheToLocalTransform;g&&h.applyMatrix(g)}if(this._calculateFilterBounds(i,t.renderTarget.rootViewPort,a,o,1),i.skip)return;const l=this._getPreviousFilterData();let c=o,u=0,f=0;l&&(u=l.bounds.minX,f=l.bounds.minY,c=l.inputTexture.source._resolution),i.outputOffset.x=h.minX-u,i.outputOffset.y=h.minY-f;const d=i.globalFrame;if(d.x=u*c,d.y=f*c,d.width=n.width*c,d.height=n.height*c,i.backTexture=A.EMPTY,i.blendRequired){t.renderTarget.finishRenderPass();const p=t.renderTarget.getRenderTarget(i.outputRenderSurface);i.backTexture=this.getBackTexture(p,h,l?.bounds)}i.inputTexture=te.getOptimalTexture(h.width,h.height,i.resolution,i.antialias),t.renderTarget.bind(i.inputTexture,!0),t.globalUniforms.push({offset:h})}generateFilteredTexture({texture:e,filters:t}){const r=this._pushFilterData();this._activeFilterData=r,r.skip=!1,r.filters=t;const i=e.source,n=i.resolution,o=i.antialias;if(t.length===0)return r.skip=!0,e;const a=r.bounds;if(a.addRect(e.frame),this._calculateFilterBounds(r,a.rectangle,o,n,0),r.skip)return e;const h=n,l=0,c=0;r.outputOffset.x=-a.minX,r.outputOffset.y=-a.minY;const u=r.globalFrame;u.x=l*h,u.y=c*h,u.width=i.width*h,u.height=i.height*h,r.outputRenderSurface=te.getOptimalTexture(a.width,a.height,r.resolution,r.antialias),r.backTexture=A.EMPTY,r.inputTexture=e,this.renderer.renderTarget.finishRenderPass(),this._applyFiltersToTexture(r,!0);const d=r.outputRenderSurface;return d.source.alphaMode="premultiplied-alpha",d}pop(){const e=this.renderer,t=this._popFilterData();t.skip||(e.globalUniforms.pop(),e.renderTarget.finishRenderPass(),this._activeFilterData=t,this._applyFiltersToTexture(t,!1),t.blendRequired&&te.returnTexture(t.backTexture),te.returnTexture(t.inputTexture))}getBackTexture(e,t,r){const i=e.colorTexture.source._resolution,n=te.getOptimalTexture(t.width,t.height,i,!1);let o=t.minX,a=t.minY;r&&(o-=r.minX,a-=r.minY),o=Math.floor(o*i),a=Math.floor(a*i);const h=Math.ceil(t.width*i),l=Math.ceil(t.height*i);return this.renderer.renderTarget.copyToTexture(e,n,{x:o,y:a},{width:h,height:l},{x:0,y:0}),n}applyFilter(e,t,r,i){const n=this.renderer,o=this._activeFilterData,a=o.outputRenderSurface,h=this._filterGlobalUniforms,l=h.uniforms,c=l.uOutputFrame,u=l.uInputSize,f=l.uInputPixel,d=l.uInputClamp,p=l.uGlobalFrame,g=l.uOutputTexture;a===r?(c[0]=o.outputOffset.x,c[1]=o.outputOffset.y):(c[0]=0,c[1]=0),c[2]=t.frame.width,c[3]=t.frame.height,u[0]=t.source.width,u[1]=t.source.height,u[2]=1/u[0],u[3]=1/u[1],f[0]=t.source.pixelWidth,f[1]=t.source.pixelHeight,f[2]=1/f[0],f[3]=1/f[1],d[0]=.5*f[2],d[1]=.5*f[3],d[2]=t.frame.width*u[2]-.5*f[2],d[3]=t.frame.height*u[3]-.5*f[3],p[0]=o.globalFrame.x,p[1]=o.globalFrame.y,p[2]=o.globalFrame.width,p[3]=o.globalFrame.height,r instanceof A&&(r.source.resource=null);const m=this.renderer.renderTarget.getRenderTarget(r);if(n.renderTarget.bind(r,!!i),r instanceof A?(g[0]=r.frame.width,g[1]=r.frame.height):(g[0]=m.width,g[1]=m.height),g[2]=m.isRoot?-1:1,h.update(),n.renderPipes.uniformBatch){const _=n.renderPipes.uniformBatch.getUboResource(h);this._globalFilterBindGroup.setResource(_,0)}else this._globalFilterBindGroup.setResource(h,0);this._globalFilterBindGroup.setResource(t.source,1),this._globalFilterBindGroup.setResource(t.source.style,2),e.groups[0]=this._globalFilterBindGroup,n.encoder.draw({geometry:Uf,shader:e,state:e._state,topology:"triangle-list"}),n.type===ye.WEBGL&&n.renderTarget.finishRenderPass()}calculateSpriteMatrix(e,t){const r=this._activeFilterData,i=e.set(r.inputTexture._source.width,0,0,r.inputTexture._source.height,r.bounds.minX,r.bounds.minY),n=t.worldTransform.copyTo(B.shared),o=t.renderGroup||t.parentRenderGroup;return o&&o.cacheToLocalTransform&&n.prepend(o.cacheToLocalTransform),n.invert(),i.prepend(n),i.scale(1/t.texture.frame.width,1/t.texture.frame.height),i.translate(t.anchor.x,t.anchor.y),i}destroy(){}_applyFiltersToTexture(e,t){const r=e.inputTexture,i=e.bounds,n=e.filters;if(this._globalFilterBindGroup.setResource(r.source.style,2),this._globalFilterBindGroup.setResource(e.backTexture.source,3),n.length===1)n[0].apply(this,r,e.outputRenderSurface,t);else{let o=e.inputTexture;const a=te.getOptimalTexture(i.width,i.height,o.source._resolution,!1);let h=a,l=0;for(l=0;l<n.length-1;++l){n[l].apply(this,o,h,!0);const u=o;o=h,h=u}n[l].apply(this,o,e.outputRenderSurface,t),te.returnTexture(a)}}_calculateFilterBounds(e,t,r,i,n){const o=this.renderer,a=e.bounds,h=e.filters;let l=1/0,c=0,u=!0,f=!1,d=!1,p=!0;for(let g=0;g<h.length;g++){const m=h[g];if(l=Math.min(l,m.resolution==="inherit"?i:m.resolution),c+=m.padding,m.antialias==="off"?u=!1:m.antialias==="inherit"&&u&&(u=r),m.clipToViewport||(p=!1),!!!(m.compatibleRenderers&o.type)){d=!1;break}if(m.blendRequired&&!(o.backBuffer?.useBackBuffer??!0)){F("Blend filter requires backBuffer on WebGL renderer to be enabled. Set `useBackBuffer: true` in the renderer options."),d=!1;break}d=m.enabled||d,f||(f=m.blendRequired)}if(!d){e.skip=!0;return}if(p&&a.fitBounds(0,t.width/i,0,t.height/i),a.scale(l).ceil().scale(1/l).pad((c|0)*n),!a.isPositive){e.skip=!0;return}e.antialias=u,e.resolution=l,e.blendRequired=f}_popFilterData(){return this._filterStackIndex--,this._filterStack[this._filterStackIndex]}_getPreviousFilterData(){let e,t=this._filterStackIndex-1;for(;t>1&&(t--,e=this._filterStack[t],!!e.skip););return e}_pushFilterData(){let e=this._filterStack[this._filterStackIndex];return e||(e=this._filterStack[this._filterStackIndex]=new Df),this._filterStackIndex++,e}}Of.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"filter"};var Lf=`in vec2 vMaskCoord;
in vec2 vTextureCoord;

uniform sampler2D uTexture;
uniform sampler2D uMaskTexture;

uniform float uAlpha;
uniform vec4 uMaskClamp;
uniform float uInverse;

out vec4 finalColor;

void main(void)
{
    float clip = step(3.5,
        step(uMaskClamp.x, vMaskCoord.x) +
        step(uMaskClamp.y, vMaskCoord.y) +
        step(vMaskCoord.x, uMaskClamp.z) +
        step(vMaskCoord.y, uMaskClamp.w));

    // TODO look into why this is needed
    float npmAlpha = uAlpha;
    vec4 original = texture(uTexture, vTextureCoord);
    vec4 masky = texture(uMaskTexture, vMaskCoord);
    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);

    float a = alphaMul * masky.r * npmAlpha * clip;

    if (uInverse == 1.0) {
        a = 1.0 - a;
    }

    finalColor = original * a;
}
`,Nf=`in vec2 aPosition;

out vec2 vTextureCoord;
out vec2 vMaskCoord;


uniform vec4 uInputSize;
uniform vec4 uOutputFrame;
uniform vec4 uOutputTexture;
uniform mat3 uFilterMatrix;

vec4 filterVertexPosition(  vec2 aPosition )
{
    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;
       
    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

vec2 filterTextureCoord(  vec2 aPosition )
{
    return aPosition * (uOutputFrame.zw * uInputSize.zw);
}

vec2 getFilterCoord( vec2 aPosition )
{
    return  ( uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;
}   

void main(void)
{
    gl_Position = filterVertexPosition(aPosition);
    vTextureCoord = filterTextureCoord(aPosition);
    vMaskCoord = getFilterCoord(aPosition);
}
`,eo=`struct GlobalFilterUniforms {
  uInputSize:vec4<f32>,
  uInputPixel:vec4<f32>,
  uInputClamp:vec4<f32>,
  uOutputFrame:vec4<f32>,
  uGlobalFrame:vec4<f32>,
  uOutputTexture:vec4<f32>,
};

struct MaskUniforms {
  uFilterMatrix:mat3x3<f32>,
  uMaskClamp:vec4<f32>,
  uAlpha:f32,
  uInverse:f32,
};

@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;
@group(0) @binding(1) var uTexture: texture_2d<f32>;
@group(0) @binding(2) var uSampler : sampler;

@group(1) @binding(0) var<uniform> filterUniforms : MaskUniforms;
@group(1) @binding(1) var uMaskTexture: texture_2d<f32>;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>,
    @location(1) filterUv : vec2<f32>,
};

fn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>
{
    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;

    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;
    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;

    return vec4(position, 0.0, 1.0);
}

fn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);
}

fn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>
{
  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);
}

fn getFilterCoord(aPosition:vec2<f32> ) -> vec2<f32>
{
  return ( filterUniforms.uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;
}

fn getSize() -> vec2<f32>
{
  return gfu.uGlobalFrame.zw;
}

@vertex
fn mainVertex(
  @location(0) aPosition : vec2<f32>,
) -> VSOutput {
  return VSOutput(
   filterVertexPosition(aPosition),
   filterTextureCoord(aPosition),
   getFilterCoord(aPosition)
  );
}

@fragment
fn mainFragment(
  @location(0) uv: vec2<f32>,
  @location(1) filterUv: vec2<f32>,
  @builtin(position) position: vec4<f32>
) -> @location(0) vec4<f32> {

    var maskClamp = filterUniforms.uMaskClamp;
    var uAlpha = filterUniforms.uAlpha;

    var clip = step(3.5,
      step(maskClamp.x, filterUv.x) +
      step(maskClamp.y, filterUv.y) +
      step(filterUv.x, maskClamp.z) +
      step(filterUv.y, maskClamp.w));

    var mask = textureSample(uMaskTexture, uSampler, filterUv);
    var source = textureSample(uTexture, uSampler, uv);
    var alphaMul = 1.0 - uAlpha * (1.0 - mask.a);

    var a: f32 = alphaMul * mask.r * uAlpha * clip;

    if (filterUniforms.uInverse == 1.0) {
        a = 1.0 - a;
    }

    return source * a;
}
`;class Hf extends su{constructor(e){const{sprite:t,...r}=e,i=new Io(t.texture),n=new ne({uFilterMatrix:{value:new B,type:"mat3x3<f32>"},uMaskClamp:{value:i.uClampFrame,type:"vec4<f32>"},uAlpha:{value:1,type:"f32"},uInverse:{value:e.inverse?1:0,type:"f32"}}),o=tt.from({vertex:{source:eo,entryPoint:"mainVertex"},fragment:{source:eo,entryPoint:"mainFragment"}}),a=pt.from({vertex:Nf,fragment:Lf,name:"mask-filter"});super({...r,gpuProgram:o,glProgram:a,resources:{filterUniforms:n,uMaskTexture:t.texture.source}}),this.sprite=t,this._textureMatrix=i}set inverse(e){this.resources.filterUniforms.uniforms.uInverse=e?1:0}get inverse(){return this.resources.filterUniforms.uniforms.uInverse===1}apply(e,t,r,i){this._textureMatrix.texture=this.sprite.texture,e.calculateSpriteMatrix(this.resources.filterUniforms.uniforms.uFilterMatrix,this.sprite).prepend(this._textureMatrix.mapCoord),this.resources.uMaskTexture=this.sprite.texture.source,e.applyFilter(this,t,r,i)}}class Gr extends $o{constructor(e){e instanceof _e&&(e={context:e});const{context:t,roundPixels:r,...i}=e||{};super({label:"Graphics",...i}),this.renderPipeId="graphics",t?this._context=t:this._context=this._ownedContext=new _e,this._context.on("update",this.onViewUpdate,this),this.didViewUpdate=!0,this.allowChildren=!1,this.roundPixels=r??!1}set context(e){e!==this._context&&(this._context.off("update",this.onViewUpdate,this),this._context=e,this._context.on("update",this.onViewUpdate,this),this.onViewUpdate())}get context(){return this._context}get bounds(){return this._context.bounds}updateBounds(){}containsPoint(e){return this._context.containsPoint(e)}destroy(e){this._ownedContext&&!e?this._ownedContext.destroy(e):(e===!0||e?.context===!0)&&this._context.destroy(e),this._ownedContext=null,this._context=null,super.destroy(e)}_callContextMethod(e,t){return this.context[e](...t),this}setFillStyle(...e){return this._callContextMethod("setFillStyle",e)}setStrokeStyle(...e){return this._callContextMethod("setStrokeStyle",e)}fill(...e){return this._callContextMethod("fill",e)}stroke(...e){return this._callContextMethod("stroke",e)}texture(...e){return this._callContextMethod("texture",e)}beginPath(){return this._callContextMethod("beginPath",[])}cut(){return this._callContextMethod("cut",[])}arc(...e){return this._callContextMethod("arc",e)}arcTo(...e){return this._callContextMethod("arcTo",e)}arcToSvg(...e){return this._callContextMethod("arcToSvg",e)}bezierCurveTo(...e){return this._callContextMethod("bezierCurveTo",e)}closePath(){return this._callContextMethod("closePath",[])}ellipse(...e){return this._callContextMethod("ellipse",e)}circle(...e){return this._callContextMethod("circle",e)}path(...e){return this._callContextMethod("path",e)}lineTo(...e){return this._callContextMethod("lineTo",e)}moveTo(...e){return this._callContextMethod("moveTo",e)}quadraticCurveTo(...e){return this._callContextMethod("quadraticCurveTo",e)}rect(...e){return this._callContextMethod("rect",e)}roundRect(...e){return this._callContextMethod("roundRect",e)}poly(...e){return this._callContextMethod("poly",e)}regularPoly(...e){return this._callContextMethod("regularPoly",e)}roundPoly(...e){return this._callContextMethod("roundPoly",e)}roundShape(...e){return this._callContextMethod("roundShape",e)}filletRect(...e){return this._callContextMethod("filletRect",e)}chamferRect(...e){return this._callContextMethod("chamferRect",e)}star(...e){return this._callContextMethod("star",e)}svg(...e){return this._callContextMethod("svg",e)}restore(...e){return this._callContextMethod("restore",e)}save(){return this._callContextMethod("save",[])}getTransform(){return this.context.getTransform()}resetTransform(){return this._callContextMethod("resetTransform",[])}rotateTransform(...e){return this._callContextMethod("rotate",e)}scaleTransform(...e){return this._callContextMethod("scale",e)}setTransform(...e){return this._callContextMethod("setTransform",e)}transform(...e){return this._callContextMethod("transform",e)}translateTransform(...e){return this._callContextMethod("translate",e)}clear(){return this._callContextMethod("clear",[])}get fillStyle(){return this._context.fillStyle}set fillStyle(e){this._context.fillStyle=e}get strokeStyle(){return this._context.strokeStyle}set strokeStyle(e){this._context.strokeStyle=e}clone(e=!1){return e?new Gr(this._context.clone()):(this._ownedContext=null,new Gr(this._context))}lineStyle(e,t,r){I(O,"Graphics#lineStyle is no longer needed. Use Graphics#setStrokeStyle to set the stroke style.");const i={};return e&&(i.width=e),t&&(i.color=t),r&&(i.alpha=r),this.context.strokeStyle=i,this}beginFill(e,t){I(O,"Graphics#beginFill is no longer needed. Use Graphics#fill to fill the shape with the desired style.");const r={};return e!==void 0&&(r.color=e),t!==void 0&&(r.alpha=t),this.context.fillStyle=r,this}endFill(){I(O,"Graphics#endFill is no longer needed. Use Graphics#fill to fill the shape with the desired style."),this.context.fill();const e=this.context.strokeStyle;return(e.width!==_e.defaultStrokeStyle.width||e.color!==_e.defaultStrokeStyle.color||e.alpha!==_e.defaultStrokeStyle.alpha)&&this.context.stroke(),this}drawCircle(...e){return I(O,"Graphics#drawCircle has been renamed to Graphics#circle"),this._callContextMethod("circle",e)}drawEllipse(...e){return I(O,"Graphics#drawEllipse has been renamed to Graphics#ellipse"),this._callContextMethod("ellipse",e)}drawPolygon(...e){return I(O,"Graphics#drawPolygon has been renamed to Graphics#poly"),this._callContextMethod("poly",e)}drawRect(...e){return I(O,"Graphics#drawRect has been renamed to Graphics#rect"),this._callContextMethod("rect",e)}drawRoundedRect(...e){return I(O,"Graphics#drawRoundedRect has been renamed to Graphics#roundRect"),this._callContextMethod("roundRect",e)}drawStar(...e){return I(O,"Graphics#drawStar has been renamed to Graphics#star"),this._callContextMethod("star",e)}}const Ya=class ja extends Xt{constructor(...e){let t=e[0]??{};t instanceof Float32Array&&(I(O,"use new MeshGeometry({ positions, uvs, indices }) instead"),t={positions:t,uvs:e[1],indices:e[2]}),t={...ja.defaultOptions,...t};const r=t.positions||new Float32Array([0,0,1,0,1,1,0,1]);let i=t.uvs;i||(t.positions?i=new Float32Array(r.length):i=new Float32Array([0,0,1,0,1,1,0,1]));const n=t.indices||new Uint32Array([0,1,2,0,2,3]),o=t.shrinkBuffersToFit,a=new ue({data:r,label:"attribute-mesh-positions",shrinkToFit:o,usage:U.VERTEX|U.COPY_DST}),h=new ue({data:i,label:"attribute-mesh-uvs",shrinkToFit:o,usage:U.VERTEX|U.COPY_DST}),l=new ue({data:n,label:"index-mesh-buffer",shrinkToFit:o,usage:U.INDEX|U.COPY_DST});super({attributes:{aPosition:{buffer:a,format:"float32x2",stride:2*4,offset:0},aUV:{buffer:h,format:"float32x2",stride:2*4,offset:0}},indexBuffer:l,topology:t.topology}),this.batchMode="auto"}get positions(){return this.attributes.aPosition.buffer.data}set positions(e){this.attributes.aPosition.buffer.data=e}get uvs(){return this.attributes.aUV.buffer.data}set uvs(e){this.attributes.aUV.buffer.data=e}get indices(){return this.indexBuffer.data}set indices(e){this.indexBuffer.data=e}};Ya.defaultOptions={topology:"triangle-list",shrinkBuffersToFit:!1};let vi=Ya,Xe=null,Me=null;function zf(s,e){Xe||(Xe=$.get().createCanvas(256,128),Me=Xe.getContext("2d",{willReadFrequently:!0}),Me.globalCompositeOperation="copy",Me.globalAlpha=1),(Xe.width<s||Xe.height<e)&&(Xe.width=ht(s),Xe.height=ht(e))}function to(s,e,t){for(let r=0,i=4*t*e;r<e;++r,i+=4)if(s[i+3]!==0)return!1;return!0}function ro(s,e,t,r,i){const n=4*e;for(let o=r,a=r*n+4*t;o<=i;++o,a+=n)if(s[a+3]!==0)return!1;return!0}function Wf(...s){let e=s[0];e.canvas||(e={canvas:s[0],resolution:s[1]});const{canvas:t}=e,r=Math.min(e.resolution??1,1),i=e.width??t.width,n=e.height??t.height;let o=e.output;if(zf(i,n),!Me)throw new TypeError("Failed to get canvas 2D context");Me.drawImage(t,0,0,i,n,0,0,i*r,n*r);const h=Me.getImageData(0,0,i,n).data;let l=0,c=0,u=i-1,f=n-1;for(;c<n&&to(h,i,c);)++c;if(c===n)return N.EMPTY;for(;to(h,i,f);)--f;for(;ro(h,i,l,c,f);)++l;for(;ro(h,i,u,c,f);)--u;return++u,++f,Me.globalCompositeOperation="source-over",Me.strokeRect(l,c,u-l,f-c),Me.globalCompositeOperation="copy",o??(o=new N),o.set(l/r,c/r,(u-l)/r,(f-c)/r),o}const so=new N;class Vf{getCanvasAndContext(e){const{text:t,style:r,resolution:i=1}=e,n=r._getFinalPadding(),o=Se.measureText(t||" ",r),a=Math.ceil(Math.ceil(Math.max(1,o.width)+n*2)*i),h=Math.ceil(Math.ceil(Math.max(1,o.height)+n*2)*i),l=Le.getOptimalCanvasAndContext(a,h);this._renderTextToCanvas(t,r,n,i,l);const c=r.trim?Wf({canvas:l.canvas,width:a,height:h,resolution:1,output:so}):so.set(0,0,a,h);return{canvasAndContext:l,frame:c}}returnCanvasAndContext(e){Le.returnCanvasAndContext(e)}_renderTextToCanvas(e,t,r,i,n){const{canvas:o,context:a}=n,h=Ar(t),l=Se.measureText(e||" ",t),c=l.lines,u=l.lineHeight,f=l.lineWidths,d=l.maxLineWidth,p=l.fontProperties,g=o.height;if(a.resetTransform(),a.scale(i,i),a.textBaseline=t.textBaseline,t._stroke?.width){const b=t._stroke;a.lineWidth=b.width,a.miterLimit=b.miterLimit,a.lineJoin=b.join,a.lineCap=b.cap}a.font=h;let m,_;const y=t.dropShadow?2:1;for(let b=0;b<y;++b){const v=t.dropShadow&&b===0,w=v?Math.ceil(Math.max(1,g)+r*2):0,S=w*i;if(v){a.fillStyle="black",a.strokeStyle="black";const C=t.dropShadow,P=C.color,E=C.alpha;a.shadowColor=L.shared.setValue(P).setAlpha(E).toRgbaString();const X=C.blur*i,z=C.distance*i;a.shadowBlur=X,a.shadowOffsetX=Math.cos(C.angle)*z,a.shadowOffsetY=Math.sin(C.angle)*z+S}else{if(a.fillStyle=t._fill?Rr(t._fill,a,l):null,t._stroke?.width){const C=t._stroke.width*t._stroke.alignment;a.strokeStyle=Rr(t._stroke,a,l,C)}a.shadowColor="black"}let T=(u-p.fontSize)/2;u-p.fontSize<0&&(T=0);const k=t._stroke?.width??0;for(let C=0;C<c.length;C++)m=k/2,_=k/2+C*u+p.ascent+T,t.align==="right"?m+=d-f[C]:t.align==="center"&&(m+=(d-f[C])/2),t._stroke?.width&&this._drawLetterSpacing(c[C],t,n,m+r,_+r-w,!0),t._fill!==void 0&&this._drawLetterSpacing(c[C],t,n,m+r,_+r-w)}}_drawLetterSpacing(e,t,r,i,n,o=!1){const{context:a}=r,h=t.letterSpacing;let l=!1;if(Se.experimentalLetterSpacingSupported&&(Se.experimentalLetterSpacing?(a.letterSpacing=`${h}px`,a.textLetterSpacing=`${h}px`,l=!0):(a.letterSpacing="0px",a.textLetterSpacing="0px")),h===0||l){o?a.strokeText(e,i,n):a.fillText(e,i,n);return}let c=i;const u=Se.graphemeSegmenter(e);let f=a.measureText(e).width,d=0;for(let p=0;p<u.length;++p){const g=u[p];o?a.strokeText(g,c,n):a.fillText(g,c,n);let m="";for(let _=p+1;_<u.length;++_)m+=u[_];d=a.measureText(m).width,c+=f-d+h,f=d}}}const xs=new Vf;function $f(s){const e=s._stroke,t=s._fill,i=[`div { ${[`color: ${L.shared.setValue(t.color).toHex()}`,`font-size: ${s.fontSize}px`,`font-family: ${s.fontFamily}`,`font-weight: ${s.fontWeight}`,`font-style: ${s.fontStyle}`,`font-variant: ${s.fontVariant}`,`letter-spacing: ${s.letterSpacing}px`,`text-align: ${s.align}`,`padding: ${s.padding}px`,`white-space: ${s.whiteSpace==="pre"&&s.wordWrap?"pre-wrap":s.whiteSpace}`,...s.lineHeight?[`line-height: ${s.lineHeight}px`]:[],...s.wordWrap?[`word-wrap: ${s.breakWords?"break-all":"break-word"}`,`max-width: ${s.wordWrapWidth}px`]:[],...e?[Ka(e)]:[],...s.dropShadow?[qa(s.dropShadow)]:[],...s.cssOverrides].join(";")} }`];return Xf(s.tagStyles,i),i.join(" ")}function qa(s){const e=L.shared.setValue(s.color).setAlpha(s.alpha).toHexa(),t=Math.round(Math.cos(s.angle)*s.distance),r=Math.round(Math.sin(s.angle)*s.distance),i=`${t}px ${r}px`;return s.blur>0?`text-shadow: ${i} ${s.blur}px ${e}`:`text-shadow: ${i} ${e}`}function Ka(s){return[`-webkit-text-stroke-width: ${s.width}px`,`-webkit-text-stroke-color: ${L.shared.setValue(s.color).toHex()}`,`text-stroke-width: ${s.width}px`,`text-stroke-color: ${L.shared.setValue(s.color).toHex()}`,"paint-order: stroke"].join(";")}const io={fontSize:"font-size: {{VALUE}}px",fontFamily:"font-family: {{VALUE}}",fontWeight:"font-weight: {{VALUE}}",fontStyle:"font-style: {{VALUE}}",fontVariant:"font-variant: {{VALUE}}",letterSpacing:"letter-spacing: {{VALUE}}px",align:"text-align: {{VALUE}}",padding:"padding: {{VALUE}}px",whiteSpace:"white-space: {{VALUE}}",lineHeight:"line-height: {{VALUE}}px",wordWrapWidth:"max-width: {{VALUE}}px"},no={fill:s=>`color: ${L.shared.setValue(s).toHex()}`,breakWords:s=>`word-wrap: ${s?"break-all":"break-word"}`,stroke:Ka,dropShadow:qa};function Xf(s,e){for(const t in s){const r=s[t],i=[];for(const n in r)no[n]?i.push(no[n](r[n])):io[n]&&i.push(io[n].replace("{{VALUE}}",r[n]));e.push(`${t} { ${i.join(";")} }`)}}class Ti extends Oe{constructor(e={}){super(e),this._cssOverrides=[],this.cssOverrides=e.cssOverrides??[],this.tagStyles=e.tagStyles??{}}set cssOverrides(e){this._cssOverrides=e instanceof Array?e:[e],this.update()}get cssOverrides(){return this._cssOverrides}update(){this._cssStyle=null,super.update()}clone(){return new Ti({align:this.align,breakWords:this.breakWords,dropShadow:this.dropShadow?{...this.dropShadow}:null,fill:this._fill,fontFamily:this.fontFamily,fontSize:this.fontSize,fontStyle:this.fontStyle,fontVariant:this.fontVariant,fontWeight:this.fontWeight,letterSpacing:this.letterSpacing,lineHeight:this.lineHeight,padding:this.padding,stroke:this._stroke,whiteSpace:this.whiteSpace,wordWrap:this.wordWrap,wordWrapWidth:this.wordWrapWidth,cssOverrides:this.cssOverrides,tagStyles:{...this.tagStyles}})}get cssStyle(){return this._cssStyle||(this._cssStyle=$f(this)),this._cssStyle}addOverride(...e){const t=e.filter(r=>!this.cssOverrides.includes(r));t.length>0&&(this.cssOverrides.push(...t),this.update())}removeOverride(...e){const t=e.filter(r=>this.cssOverrides.includes(r));t.length>0&&(this.cssOverrides=this.cssOverrides.filter(r=>!t.includes(r)),this.update())}set fill(e){typeof e!="string"&&typeof e!="number"&&F("[HTMLTextStyle] only color fill is not supported by HTMLText"),super.fill=e}set stroke(e){e&&typeof e!="string"&&typeof e!="number"&&F("[HTMLTextStyle] only color stroke is not supported by HTMLText"),super.stroke=e}}const oo="http://www.w3.org/2000/svg",ao="http://www.w3.org/1999/xhtml";class Za{constructor(){this.svgRoot=document.createElementNS(oo,"svg"),this.foreignObject=document.createElementNS(oo,"foreignObject"),this.domElement=document.createElementNS(ao,"div"),this.styleElement=document.createElementNS(ao,"style"),this.image=new Image;const{foreignObject:e,svgRoot:t,styleElement:r,domElement:i}=this;e.setAttribute("width","10000"),e.setAttribute("height","10000"),e.style.overflow="hidden",t.appendChild(e),e.appendChild(r),e.appendChild(i)}}let ho;function Yf(s,e,t,r){r||(r=ho||(ho=new Za));const{domElement:i,styleElement:n,svgRoot:o}=r;i.innerHTML=`<style>${e.cssStyle};</style><div style='padding:0'>${s}</div>`,i.setAttribute("style","transform-origin: top left; display: inline-block"),t&&(n.textContent=t),document.body.appendChild(o);const a=i.getBoundingClientRect();o.remove();const h=e.padding*2;return{width:a.width-h,height:a.height-h}}class Qa{constructor(){this._tempState=be.for2d(),this._didUploadHash={}}init(e){e.renderer.runners.contextChange.add(this)}contextChange(){this._didUploadHash={}}start(e,t,r){const i=e.renderer,n=this._didUploadHash[r.uid];i.shader.bind(r,n),n||(this._didUploadHash[r.uid]=!0),i.shader.updateUniformGroup(i.globalUniforms.uniformGroup),i.geometry.bind(t,r.glProgram)}execute(e,t){const r=e.renderer;this._tempState.blendMode=t.blendMode,r.state.set(this._tempState);const i=t.textures.textures;for(let n=0;n<t.textures.count;n++)r.texture.bind(i[n],n);r.geometry.draw(t.topology,t.size,t.start)}}Qa.extension={type:[x.WebGLPipesAdaptor],name:"batch"};const gr=be.for2d();class Ja{start(e,t,r){const i=e.renderer,n=i.encoder,o=r.gpuProgram;this._shader=r,this._geometry=t,n.setGeometry(t,o),gr.blendMode="normal",i.pipeline.getPipeline(t,o,gr);const a=i.globalUniforms.bindGroup;n.resetBindGroup(1),n.setBindGroup(0,a,o)}execute(e,t){const r=this._shader.gpuProgram,i=e.renderer,n=i.encoder;if(!t.bindGroup){const h=t.textures;t.bindGroup=li(h.textures,h.count,i.limits.maxBatchableTextures)}gr.blendMode=t.blendMode;const o=i.bindGroup.getBindGroup(t.bindGroup,r,1),a=i.pipeline.getPipeline(this._geometry,r,gr,t.topology);t.bindGroup._touch(i.textureGC.count),n.setPipeline(a),n.renderPassEncoder.setBindGroup(1,o),n.renderPassEncoder.drawIndexed(t.size,1,t.start)}}Ja.extension={type:[x.WebGPUPipesAdaptor],name:"batch"};const Si=class eh{constructor(e,t){this.state=be.for2d(),this._batchersByInstructionSet=Object.create(null),this._activeBatches=Object.create(null),this.renderer=e,this._adaptor=t,this._adaptor.init?.(this)}static getBatcher(e){return new this._availableBatchers[e]}buildStart(e){let t=this._batchersByInstructionSet[e.uid];t||(t=this._batchersByInstructionSet[e.uid]=Object.create(null),t.default||(t.default=new mi({maxTextures:this.renderer.limits.maxBatchableTextures}))),this._activeBatches=t,this._activeBatch=this._activeBatches.default;for(const r in this._activeBatches)this._activeBatches[r].begin()}addToBatch(e,t){if(this._activeBatch.name!==e.batcherName){this._activeBatch.break(t);let r=this._activeBatches[e.batcherName];r||(r=this._activeBatches[e.batcherName]=eh.getBatcher(e.batcherName),r.begin()),this._activeBatch=r}this._activeBatch.add(e)}break(e){this._activeBatch.break(e)}buildEnd(e){this._activeBatch.break(e);const t=this._activeBatches;for(const r in t){const i=t[r],n=i.geometry;n.indexBuffer.setDataWithSize(i.indexBuffer,i.indexSize,!0),n.buffers[0].setDataWithSize(i.attributeBuffer.float32View,i.attributeSize,!1)}}upload(e){const t=this._batchersByInstructionSet[e.uid];for(const r in t){const i=t[r],n=i.geometry;i.dirty&&(i.dirty=!1,n.buffers[0].update(i.attributeSize*4))}}execute(e){if(e.action==="startBatch"){const t=e.batcher,r=t.geometry,i=t.shader;this._adaptor.start(this,r,i)}this._adaptor.execute(this,e)}destroy(){this.state=null,this.renderer=null,this._adaptor=null;for(const e in this._activeBatches)this._activeBatches[e].destroy();this._activeBatches=null}};Si.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"batch"};Si._availableBatchers=Object.create(null);let th=Si;q.handleByMap(x.Batcher,th._availableBatchers);q.add(mi);const It={name:"local-uniform-bit",vertex:{header:`

            struct LocalUniforms {
                uTransformMatrix:mat3x3<f32>,
                uColor:vec4<f32>,
                uRound:f32,
            }

            @group(1) @binding(0) var<uniform> localUniforms : LocalUniforms;
        `,main:`
            vColor *= localUniforms.uColor;
            modelMatrix *= localUniforms.uTransformMatrix;
        `,end:`
            if(localUniforms.uRound == 1)
            {
                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
            }
        `}},jf={...It,vertex:{...It.vertex,header:It.vertex.header.replace("group(1)","group(2)")}},wi={name:"local-uniform-bit",vertex:{header:`

            uniform mat3 uTransformMatrix;
            uniform vec4 uColor;
            uniform float uRound;
        `,main:`
            vColor *= uColor;
            modelMatrix = uTransformMatrix;
        `,end:`
            if(uRound == 1.)
            {
                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
            }
        `}},qf={name:"texture-bit",vertex:{header:`

        struct TextureUniforms {
            uTextureMatrix:mat3x3<f32>,
        }

        @group(2) @binding(2) var<uniform> textureUniforms : TextureUniforms;
        `,main:`
            uv = (textureUniforms.uTextureMatrix * vec3(uv, 1.0)).xy;
        `},fragment:{header:`
            @group(2) @binding(0) var uTexture: texture_2d<f32>;
            @group(2) @binding(1) var uSampler: sampler;


        `,main:`
            outColor = textureSample(uTexture, uSampler, vUV);
        `}},Kf={name:"texture-bit",vertex:{header:`
            uniform mat3 uTextureMatrix;
        `,main:`
            uv = (uTextureMatrix * vec3(uv, 1.0)).xy;
        `},fragment:{header:`
        uniform sampler2D uTexture;


        `,main:`
            outColor = texture(uTexture, vUV);
        `}},Zf=new se;class Qf extends Pr{constructor(){super(),this.filters=[new Hf({sprite:new ct(A.EMPTY),inverse:!1,resolution:"inherit",antialias:"inherit"})]}get sprite(){return this.filters[0].sprite}set sprite(e){this.filters[0].sprite=e}get inverse(){return this.filters[0].inverse}set inverse(e){this.filters[0].inverse=e}}class rh{constructor(e){this._activeMaskStage=[],this._renderer=e}push(e,t,r){const i=this._renderer;if(i.renderPipes.batch.break(r),r.add({renderPipeId:"alphaMask",action:"pushMaskBegin",mask:e,inverse:t._maskOptions.inverse,canBundle:!1,maskedContainer:t}),e.inverse=t._maskOptions.inverse,e.renderMaskToTexture){const n=e.mask;n.includeInBuild=!0,n.collectRenderables(r,i,null),n.includeInBuild=!1}i.renderPipes.batch.break(r),r.add({renderPipeId:"alphaMask",action:"pushMaskEnd",mask:e,maskedContainer:t,inverse:t._maskOptions.inverse,canBundle:!1})}pop(e,t,r){this._renderer.renderPipes.batch.break(r),r.add({renderPipeId:"alphaMask",action:"popMaskEnd",mask:e,inverse:t._maskOptions.inverse,canBundle:!1})}execute(e){const t=this._renderer,r=e.mask.renderMaskToTexture;if(e.action==="pushMaskBegin"){const i=ee.get(Qf);if(i.inverse=e.inverse,r){e.mask.mask.measurable=!0;const n=ei(e.mask.mask,!0,Zf);e.mask.mask.measurable=!1,n.ceil();const o=t.renderTarget.renderTarget.colorTexture.source,a=te.getOptimalTexture(n.width,n.height,o._resolution,o.antialias);t.renderTarget.push(a,!0),t.globalUniforms.push({offset:n,worldColor:4294967295});const h=i.sprite;h.texture=a,h.worldTransform.tx=n.minX,h.worldTransform.ty=n.minY,this._activeMaskStage.push({filterEffect:i,maskedContainer:e.maskedContainer,filterTexture:a})}else i.sprite=e.mask.mask,this._activeMaskStage.push({filterEffect:i,maskedContainer:e.maskedContainer})}else if(e.action==="pushMaskEnd"){const i=this._activeMaskStage[this._activeMaskStage.length-1];r&&(t.type===ye.WEBGL&&t.renderTarget.finishRenderPass(),t.renderTarget.pop(),t.globalUniforms.pop()),t.filter.push({renderPipeId:"filter",action:"pushFilter",container:i.maskedContainer,filterEffect:i.filterEffect,canBundle:!1})}else if(e.action==="popMaskEnd"){t.filter.pop();const i=this._activeMaskStage.pop();r&&te.returnTexture(i.filterTexture),ee.return(i.filterEffect)}}destroy(){this._renderer=null,this._activeMaskStage=null}}rh.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"alphaMask"};class sh{constructor(e){this._colorStack=[],this._colorStackIndex=0,this._currentColor=0,this._renderer=e}buildStart(){this._colorStack[0]=15,this._colorStackIndex=1,this._currentColor=15}push(e,t,r){this._renderer.renderPipes.batch.break(r);const n=this._colorStack;n[this._colorStackIndex]=n[this._colorStackIndex-1]&e.mask;const o=this._colorStack[this._colorStackIndex];o!==this._currentColor&&(this._currentColor=o,r.add({renderPipeId:"colorMask",colorMask:o,canBundle:!1})),this._colorStackIndex++}pop(e,t,r){this._renderer.renderPipes.batch.break(r);const n=this._colorStack;this._colorStackIndex--;const o=n[this._colorStackIndex-1];o!==this._currentColor&&(this._currentColor=o,r.add({renderPipeId:"colorMask",colorMask:o,canBundle:!1}))}execute(e){this._renderer.colorMask.setMask(e.colorMask)}destroy(){this._colorStack=null}}sh.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"colorMask"};class ih{constructor(e){this._maskStackHash={},this._maskHash=new WeakMap,this._renderer=e}push(e,t,r){var i;const n=e,o=this._renderer;o.renderPipes.batch.break(r),o.renderPipes.blendMode.setBlendMode(n.mask,"none",r),r.add({renderPipeId:"stencilMask",action:"pushMaskBegin",mask:e,inverse:t._maskOptions.inverse,canBundle:!1});const a=n.mask;a.includeInBuild=!0,this._maskHash.has(n)||this._maskHash.set(n,{instructionsStart:0,instructionsLength:0});const h=this._maskHash.get(n);h.instructionsStart=r.instructionSize,a.collectRenderables(r,o,null),a.includeInBuild=!1,o.renderPipes.batch.break(r),r.add({renderPipeId:"stencilMask",action:"pushMaskEnd",mask:e,inverse:t._maskOptions.inverse,canBundle:!1});const l=r.instructionSize-h.instructionsStart-1;h.instructionsLength=l;const c=o.renderTarget.renderTarget.uid;(i=this._maskStackHash)[c]??(i[c]=0)}pop(e,t,r){const i=e,n=this._renderer;n.renderPipes.batch.break(r),n.renderPipes.blendMode.setBlendMode(i.mask,"none",r),r.add({renderPipeId:"stencilMask",action:"popMaskBegin",inverse:t._maskOptions.inverse,canBundle:!1});const o=this._maskHash.get(e);for(let a=0;a<o.instructionsLength;a++)r.instructions[r.instructionSize++]=r.instructions[o.instructionsStart++];r.add({renderPipeId:"stencilMask",action:"popMaskEnd",canBundle:!1})}execute(e){var t;const r=this._renderer,i=r.renderTarget.renderTarget.uid;let n=(t=this._maskStackHash)[i]??(t[i]=0);e.action==="pushMaskBegin"?(r.renderTarget.ensureDepthStencil(),r.stencil.setStencilMode(Z.RENDERING_MASK_ADD,n),n++,r.colorMask.setMask(0)):e.action==="pushMaskEnd"?(e.inverse?r.stencil.setStencilMode(Z.INVERSE_MASK_ACTIVE,n):r.stencil.setStencilMode(Z.MASK_ACTIVE,n),r.colorMask.setMask(15)):e.action==="popMaskBegin"?(r.colorMask.setMask(0),n!==0?r.stencil.setStencilMode(Z.RENDERING_MASK_REMOVE,n):(r.renderTarget.clear(null,ce.STENCIL),r.stencil.setStencilMode(Z.DISABLED,n)),n--):e.action==="popMaskEnd"&&(e.inverse?r.stencil.setStencilMode(Z.INVERSE_MASK_ACTIVE,n):r.stencil.setStencilMode(Z.MASK_ACTIVE,n),r.colorMask.setMask(15)),this._maskStackHash[i]=n}destroy(){this._renderer=null,this._maskStackHash=null,this._maskHash=null}}ih.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"stencilMask"};var vr=(s=>(s[s.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",s[s.ARRAY_BUFFER=34962]="ARRAY_BUFFER",s[s.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",s))(vr||{});class Jf{constructor(e,t){this._lastBindBaseLocation=-1,this._lastBindCallId=-1,this.buffer=e||null,this.updateID=-1,this.byteLength=-1,this.type=t}}class nh{constructor(e){this._gpuBuffers=Object.create(null),this._boundBufferBases=Object.create(null),this._minBaseLocation=0,this._nextBindBaseIndex=this._minBaseLocation,this._bindCallId=0,this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_gpuBuffers")}destroy(){this._renderer=null,this._gl=null,this._gpuBuffers=null,this._boundBufferBases=null}contextChange(){this._gl=this._renderer.gl,this._gpuBuffers=Object.create(null),this._maxBindings=this._renderer.limits.maxUniformBindings}getGlBuffer(e){return this._gpuBuffers[e.uid]||this.createGLBuffer(e)}bind(e){const{_gl:t}=this,r=this.getGlBuffer(e);t.bindBuffer(r.type,r.buffer)}bindBufferBase(e,t){const{_gl:r}=this;this._boundBufferBases[t]!==e&&(this._boundBufferBases[t]=e,e._lastBindBaseLocation=t,r.bindBufferBase(r.UNIFORM_BUFFER,t,e.buffer))}nextBindBase(e){this._bindCallId++,this._minBaseLocation=0,e&&(this._boundBufferBases[0]=null,this._minBaseLocation=1,this._nextBindBaseIndex<1&&(this._nextBindBaseIndex=1))}freeLocationForBufferBase(e){let t=this.getLastBindBaseLocation(e);if(t>=this._minBaseLocation)return e._lastBindCallId=this._bindCallId,t;let r=0,i=this._nextBindBaseIndex;for(;r<2;){i>=this._maxBindings&&(i=this._minBaseLocation,r++);const n=this._boundBufferBases[i];if(n&&n._lastBindCallId===this._bindCallId){i++;continue}break}return t=i,this._nextBindBaseIndex=i+1,r>=2?-1:(e._lastBindCallId=this._bindCallId,this._boundBufferBases[t]=null,t)}getLastBindBaseLocation(e){const t=e._lastBindBaseLocation;return this._boundBufferBases[t]===e?t:-1}bindBufferRange(e,t,r,i){const{_gl:n}=this;r||(r=0),t||(t=0),this._boundBufferBases[t]=null,n.bindBufferRange(n.UNIFORM_BUFFER,t||0,e.buffer,r*256,i||256)}updateBuffer(e){const{_gl:t}=this,r=this.getGlBuffer(e);if(e._updateID===r.updateID)return r;r.updateID=e._updateID,t.bindBuffer(r.type,r.buffer);const i=e.data,n=e.descriptor.usage&U.STATIC?t.STATIC_DRAW:t.DYNAMIC_DRAW;return i?r.byteLength>=i.byteLength?t.bufferSubData(r.type,0,i,0,e._updateSize/i.BYTES_PER_ELEMENT):(r.byteLength=i.byteLength,t.bufferData(r.type,i,n)):(r.byteLength=e.descriptor.size,t.bufferData(r.type,r.byteLength,n)),r}destroyAll(){const e=this._gl;for(const t in this._gpuBuffers)e.deleteBuffer(this._gpuBuffers[t].buffer);this._gpuBuffers=Object.create(null)}onBufferDestroy(e,t){const r=this._gpuBuffers[e.uid],i=this._gl;t||i.deleteBuffer(r.buffer),this._gpuBuffers[e.uid]=null}createGLBuffer(e){const{_gl:t}=this;let r=vr.ARRAY_BUFFER;e.descriptor.usage&U.INDEX?r=vr.ELEMENT_ARRAY_BUFFER:e.descriptor.usage&U.UNIFORM&&(r=vr.UNIFORM_BUFFER);const i=new Jf(t.createBuffer(),r);return this._gpuBuffers[e.uid]=i,e.on("destroy",this.onBufferDestroy,this),i}resetState(){this._boundBufferBases=Object.create(null)}}nh.extension={type:[x.WebGLSystem],name:"buffer"};const Pi=class oh{constructor(e){this.supports={uint32Indices:!0,uniformBufferObject:!0,vertexArrayObject:!0,srgbTextures:!0,nonPowOf2wrapping:!0,msaa:!0,nonPowOf2mipmaps:!0},this._renderer=e,this.extensions=Object.create(null),this.handleContextLost=this.handleContextLost.bind(this),this.handleContextRestored=this.handleContextRestored.bind(this)}get isLost(){return!this.gl||this.gl.isContextLost()}contextChange(e){this.gl=e,this._renderer.gl=e}init(e){e={...oh.defaultOptions,...e};let t=this.multiView=e.multiView;if(e.context&&t&&(F("Renderer created with both a context and multiview enabled. Disabling multiView as both cannot work together."),t=!1),t?this.canvas=$.get().createCanvas(this._renderer.canvas.width,this._renderer.canvas.height):this.canvas=this._renderer.view.canvas,e.context)this.initFromContext(e.context);else{const r=this._renderer.background.alpha<1,i=e.premultipliedAlpha??!0,n=e.antialias&&!this._renderer.backBuffer.useBackBuffer;this.createContext(e.preferWebGLVersion,{alpha:r,premultipliedAlpha:i,antialias:n,stencil:!0,preserveDrawingBuffer:e.preserveDrawingBuffer,powerPreference:e.powerPreference??"default"})}}ensureCanvasSize(e){if(!this.multiView){e!==this.canvas&&F("multiView is disabled, but targetCanvas is not the main canvas");return}const{canvas:t}=this;(t.width<e.width||t.height<e.height)&&(t.width=Math.max(e.width,e.width),t.height=Math.max(e.height,e.height))}initFromContext(e){this.gl=e,this.webGLVersion=e instanceof $.get().getWebGLRenderingContext()?1:2,this.getExtensions(),this.validateContext(e),this._renderer.runners.contextChange.emit(e);const t=this._renderer.view.canvas;t.addEventListener("webglcontextlost",this.handleContextLost,!1),t.addEventListener("webglcontextrestored",this.handleContextRestored,!1)}createContext(e,t){let r;const i=this.canvas;if(e===2&&(r=i.getContext("webgl2",t)),!r&&(r=i.getContext("webgl",t),!r))throw new Error("This browser does not support WebGL. Try using the canvas renderer");this.gl=r,this.initFromContext(this.gl)}getExtensions(){const{gl:e}=this,t={anisotropicFiltering:e.getExtension("EXT_texture_filter_anisotropic"),floatTextureLinear:e.getExtension("OES_texture_float_linear"),s3tc:e.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:e.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:e.getExtension("WEBGL_compressed_texture_etc"),etc1:e.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:e.getExtension("WEBGL_compressed_texture_pvrtc")||e.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:e.getExtension("WEBGL_compressed_texture_atc"),astc:e.getExtension("WEBGL_compressed_texture_astc"),bptc:e.getExtension("EXT_texture_compression_bptc"),rgtc:e.getExtension("EXT_texture_compression_rgtc"),loseContext:e.getExtension("WEBGL_lose_context")};if(this.webGLVersion===1)this.extensions={...t,drawBuffers:e.getExtension("WEBGL_draw_buffers"),depthTexture:e.getExtension("WEBGL_depth_texture"),vertexArrayObject:e.getExtension("OES_vertex_array_object")||e.getExtension("MOZ_OES_vertex_array_object")||e.getExtension("WEBKIT_OES_vertex_array_object"),uint32ElementIndex:e.getExtension("OES_element_index_uint"),floatTexture:e.getExtension("OES_texture_float"),floatTextureLinear:e.getExtension("OES_texture_float_linear"),textureHalfFloat:e.getExtension("OES_texture_half_float"),textureHalfFloatLinear:e.getExtension("OES_texture_half_float_linear"),vertexAttribDivisorANGLE:e.getExtension("ANGLE_instanced_arrays"),srgb:e.getExtension("EXT_sRGB")};else{this.extensions={...t,colorBufferFloat:e.getExtension("EXT_color_buffer_float")};const r=e.getExtension("WEBGL_provoking_vertex");r&&r.provokingVertexWEBGL(r.FIRST_VERTEX_CONVENTION_WEBGL)}}handleContextLost(e){e.preventDefault(),this._contextLossForced&&(this._contextLossForced=!1,setTimeout(()=>{this.gl.isContextLost()&&this.extensions.loseContext?.restoreContext()},0))}handleContextRestored(){this.getExtensions(),this._renderer.runners.contextChange.emit(this.gl)}destroy(){const e=this._renderer.view.canvas;this._renderer=null,e.removeEventListener("webglcontextlost",this.handleContextLost),e.removeEventListener("webglcontextrestored",this.handleContextRestored),this.gl.useProgram(null),this.extensions.loseContext?.loseContext()}forceContextLoss(){this.extensions.loseContext?.loseContext(),this._contextLossForced=!0}validateContext(e){const t=e.getContextAttributes();t&&!t.stencil&&F("Provided WebGL context does not have a stencil buffer, masks may not render correctly");const r=this.supports,i=this.webGLVersion===2,n=this.extensions;r.uint32Indices=i||!!n.uint32ElementIndex,r.uniformBufferObject=i,r.vertexArrayObject=i||!!n.vertexArrayObject,r.srgbTextures=i||!!n.srgb,r.nonPowOf2wrapping=i,r.nonPowOf2mipmaps=i,r.msaa=i,r.uint32Indices||F("Provided WebGL context does not support 32 index buffer, large scenes may not render correctly")}};Pi.extension={type:[x.WebGLSystem],name:"context"};Pi.defaultOptions={context:null,premultipliedAlpha:!0,preserveDrawingBuffer:!1,powerPreference:void 0,preferWebGLVersion:2,multiView:!1};let ep=Pi;function ah(s,e){for(const t in s.attributes){const r=s.attributes[t],i=e[t];i?(r.format??(r.format=i.format),r.offset??(r.offset=i.offset),r.instance??(r.instance=i.instance)):F(`Attribute ${t} is not present in the shader, but is present in the geometry. Unable to infer attribute details.`)}tp(s)}function tp(s){const{buffers:e,attributes:t}=s,r={},i={};for(const n in e){const o=e[n];r[o.uid]=0,i[o.uid]=0}for(const n in t){const o=t[n];r[o.buffer.uid]+=De(o.format).stride}for(const n in t){const o=t[n];o.stride??(o.stride=r[o.buffer.uid]),o.start??(o.start=i[o.buffer.uid]),i[o.buffer.uid]+=De(o.format).stride}}var Xs=(s=>(s[s.RGBA=6408]="RGBA",s[s.RGB=6407]="RGB",s[s.RG=33319]="RG",s[s.RED=6403]="RED",s[s.RGBA_INTEGER=36249]="RGBA_INTEGER",s[s.RGB_INTEGER=36248]="RGB_INTEGER",s[s.RG_INTEGER=33320]="RG_INTEGER",s[s.RED_INTEGER=36244]="RED_INTEGER",s[s.ALPHA=6406]="ALPHA",s[s.LUMINANCE=6409]="LUMINANCE",s[s.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",s[s.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",s[s.DEPTH_STENCIL=34041]="DEPTH_STENCIL",s))(Xs||{}),hh=(s=>(s[s.TEXTURE_2D=3553]="TEXTURE_2D",s[s.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",s[s.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",s[s.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",s[s.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",s[s.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",s[s.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",s[s.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",s[s.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",s))(hh||{}),D=(s=>(s[s.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",s[s.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",s[s.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",s[s.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",s[s.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",s[s.UNSIGNED_INT=5125]="UNSIGNED_INT",s[s.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",s[s.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",s[s.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",s[s.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",s[s.BYTE=5120]="BYTE",s[s.SHORT=5122]="SHORT",s[s.INT=5124]="INT",s[s.FLOAT=5126]="FLOAT",s[s.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",s[s.HALF_FLOAT=36193]="HALF_FLOAT",s))(D||{});const lo={uint8x2:D.UNSIGNED_BYTE,uint8x4:D.UNSIGNED_BYTE,sint8x2:D.BYTE,sint8x4:D.BYTE,unorm8x2:D.UNSIGNED_BYTE,unorm8x4:D.UNSIGNED_BYTE,snorm8x2:D.BYTE,snorm8x4:D.BYTE,uint16x2:D.UNSIGNED_SHORT,uint16x4:D.UNSIGNED_SHORT,sint16x2:D.SHORT,sint16x4:D.SHORT,unorm16x2:D.UNSIGNED_SHORT,unorm16x4:D.UNSIGNED_SHORT,snorm16x2:D.SHORT,snorm16x4:D.SHORT,float16x2:D.HALF_FLOAT,float16x4:D.HALF_FLOAT,float32:D.FLOAT,float32x2:D.FLOAT,float32x3:D.FLOAT,float32x4:D.FLOAT,uint32:D.UNSIGNED_INT,uint32x2:D.UNSIGNED_INT,uint32x3:D.UNSIGNED_INT,uint32x4:D.UNSIGNED_INT,sint32:D.INT,sint32x2:D.INT,sint32x3:D.INT,sint32x4:D.INT};function rp(s){return lo[s]??lo.float32}const sp={"point-list":0,"line-list":1,"line-strip":3,"triangle-list":4,"triangle-strip":5};class lh{constructor(e){this._geometryVaoHash=Object.create(null),this._renderer=e,this._activeGeometry=null,this._activeVao=null,this.hasVao=!0,this.hasInstance=!0,this._renderer.renderableGC.addManagedHash(this,"_geometryVaoHash")}contextChange(){const e=this.gl=this._renderer.gl;if(!this._renderer.context.supports.vertexArrayObject)throw new Error("[PixiJS] Vertex Array Objects are not supported on this device");const t=this._renderer.context.extensions.vertexArrayObject;t&&(e.createVertexArray=()=>t.createVertexArrayOES(),e.bindVertexArray=i=>t.bindVertexArrayOES(i),e.deleteVertexArray=i=>t.deleteVertexArrayOES(i));const r=this._renderer.context.extensions.vertexAttribDivisorANGLE;r&&(e.drawArraysInstanced=(i,n,o,a)=>{r.drawArraysInstancedANGLE(i,n,o,a)},e.drawElementsInstanced=(i,n,o,a,h)=>{r.drawElementsInstancedANGLE(i,n,o,a,h)},e.vertexAttribDivisor=(i,n)=>r.vertexAttribDivisorANGLE(i,n)),this._activeGeometry=null,this._activeVao=null,this._geometryVaoHash=Object.create(null)}bind(e,t){const r=this.gl;this._activeGeometry=e;const i=this.getVao(e,t);this._activeVao!==i&&(this._activeVao=i,r.bindVertexArray(i)),this.updateBuffers()}resetState(){this.unbind()}updateBuffers(){const e=this._activeGeometry,t=this._renderer.buffer;for(let r=0;r<e.buffers.length;r++){const i=e.buffers[r];t.updateBuffer(i)}}checkCompatibility(e,t){const r=e.attributes,i=t._attributeData;for(const n in i)if(!r[n])throw new Error(`shader and geometry incompatible, geometry missing the "${n}" attribute`)}getSignature(e,t){const r=e.attributes,i=t._attributeData,n=["g",e.uid];for(const o in r)i[o]&&n.push(o,i[o].location);return n.join("-")}getVao(e,t){return this._geometryVaoHash[e.uid]?.[t._key]||this.initGeometryVao(e,t)}initGeometryVao(e,t,r=!0){const i=this._renderer.gl,n=this._renderer.buffer;this._renderer.shader._getProgramData(t),this.checkCompatibility(e,t);const o=this.getSignature(e,t);this._geometryVaoHash[e.uid]||(this._geometryVaoHash[e.uid]=Object.create(null),e.on("destroy",this.onGeometryDestroy,this));const a=this._geometryVaoHash[e.uid];let h=a[o];if(h)return a[t._key]=h,h;ah(e,t._attributeData);const l=e.buffers;h=i.createVertexArray(),i.bindVertexArray(h);for(let c=0;c<l.length;c++){const u=l[c];n.bind(u)}return this.activateVao(e,t),a[t._key]=h,a[o]=h,i.bindVertexArray(null),h}onGeometryDestroy(e,t){const r=this._geometryVaoHash[e.uid],i=this.gl;if(r){if(t)for(const n in r)this._activeVao!==r[n]&&this.unbind(),i.deleteVertexArray(r[n]);this._geometryVaoHash[e.uid]=null}}destroyAll(e=!1){const t=this.gl;for(const r in this._geometryVaoHash){if(e)for(const i in this._geometryVaoHash[r]){const n=this._geometryVaoHash[r];this._activeVao!==n&&this.unbind(),t.deleteVertexArray(n[i])}this._geometryVaoHash[r]=null}}activateVao(e,t){const r=this._renderer.gl,i=this._renderer.buffer,n=e.attributes;e.indexBuffer&&i.bind(e.indexBuffer);let o=null;for(const a in n){const h=n[a],l=h.buffer,c=i.getGlBuffer(l),u=t._attributeData[a];if(u){o!==c&&(i.bind(l),o=c);const f=u.location;r.enableVertexAttribArray(f);const d=De(h.format),p=rp(h.format);if(u.format?.substring(1,4)==="int"?r.vertexAttribIPointer(f,d.size,p,h.stride,h.offset):r.vertexAttribPointer(f,d.size,p,d.normalised,h.stride,h.offset),h.instance)if(this.hasInstance){const g=h.divisor??1;r.vertexAttribDivisor(f,g)}else throw new Error("geometry error, GPU Instancing is not supported on this device")}}}draw(e,t,r,i){const{gl:n}=this._renderer,o=this._activeGeometry,a=sp[e||o.topology];if(i??(i=o.instanceCount),o.indexBuffer){const h=o.indexBuffer.data.BYTES_PER_ELEMENT,l=h===2?n.UNSIGNED_SHORT:n.UNSIGNED_INT;i>1?n.drawElementsInstanced(a,t||o.indexBuffer.data.length,l,(r||0)*h,i):n.drawElements(a,t||o.indexBuffer.data.length,l,(r||0)*h)}else i>1?n.drawArraysInstanced(a,r||0,t||o.getSize(),i):n.drawArrays(a,r||0,t||o.getSize());return this}unbind(){this.gl.bindVertexArray(null),this._activeVao=null,this._activeGeometry=null}destroy(){this._renderer=null,this.gl=null,this._activeVao=null,this._activeGeometry=null}}lh.extension={type:[x.WebGLSystem],name:"geometry"};const ip=new Xt({attributes:{aPosition:[-1,-1,3,-1,-1,3]}}),Ci=class ch{constructor(e){this.useBackBuffer=!1,this._useBackBufferThisRender=!1,this._renderer=e}init(e={}){const{useBackBuffer:t,antialias:r}={...ch.defaultOptions,...e};this.useBackBuffer=t,this._antialias=r,this._renderer.context.supports.msaa||(F("antialiasing, is not supported on when using the back buffer"),this._antialias=!1),this._state=be.for2d();const i=new pt({vertex:`
                attribute vec2 aPosition;
                out vec2 vUv;

                void main() {
                    gl_Position = vec4(aPosition, 0.0, 1.0);

                    vUv = (aPosition + 1.0) / 2.0;

                    // flip dem UVs
                    vUv.y = 1.0 - vUv.y;
                }`,fragment:`
                in vec2 vUv;
                out vec4 finalColor;

                uniform sampler2D uTexture;

                void main() {
                    finalColor = texture(uTexture, vUv);
                }`,name:"big-triangle"});this._bigTriangleShader=new pe({glProgram:i,resources:{uTexture:A.WHITE.source}})}renderStart(e){const t=this._renderer.renderTarget.getRenderTarget(e.target);if(this._useBackBufferThisRender=this.useBackBuffer&&!!t.isRoot,this._useBackBufferThisRender){const r=this._renderer.renderTarget.getRenderTarget(e.target);this._targetTexture=r.colorTexture,e.target=this._getBackBufferTexture(r.colorTexture)}}renderEnd(){this._presentBackBuffer()}_presentBackBuffer(){const e=this._renderer;e.renderTarget.finishRenderPass(),this._useBackBufferThisRender&&(e.renderTarget.bind(this._targetTexture,!1),this._bigTriangleShader.resources.uTexture=this._backBufferTexture.source,e.encoder.draw({geometry:ip,shader:this._bigTriangleShader,state:this._state}))}_getBackBufferTexture(e){return this._backBufferTexture=this._backBufferTexture||new A({source:new Q({width:e.width,height:e.height,resolution:e._resolution,antialias:this._antialias})}),this._backBufferTexture.source.resize(e.width,e.height,e._resolution),this._backBufferTexture}destroy(){this._backBufferTexture&&(this._backBufferTexture.destroy(),this._backBufferTexture=null)}};Ci.extension={type:[x.WebGLSystem],name:"backBuffer",priority:1};Ci.defaultOptions={useBackBuffer:!1};let np=Ci;class uh{constructor(e){this._colorMaskCache=15,this._renderer=e}setMask(e){this._colorMaskCache!==e&&(this._colorMaskCache=e,this._renderer.gl.colorMask(!!(e&8),!!(e&4),!!(e&2),!!(e&1)))}}uh.extension={type:[x.WebGLSystem],name:"colorMask"};class dh{constructor(e){this.commandFinished=Promise.resolve(),this._renderer=e}setGeometry(e,t){this._renderer.geometry.bind(e,t.glProgram)}finishRenderPass(){}draw(e){const t=this._renderer,{geometry:r,shader:i,state:n,skipSync:o,topology:a,size:h,start:l,instanceCount:c}=e;t.shader.bind(i,o),t.geometry.bind(r,t.shader._activeProgram),n&&t.state.set(n),t.geometry.draw(a,h,l,c??r.instanceCount)}destroy(){this._renderer=null}}dh.extension={type:[x.WebGLSystem],name:"encoder"};class fh{constructor(e){this._renderer=e}contextChange(){const e=this._renderer.gl;this.maxTextures=e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS),this.maxBatchableTextures=Ca(this.maxTextures,e),this.maxUniformBindings=e.getParameter(e.MAX_UNIFORM_BUFFER_BINDINGS)}destroy(){}}fh.extension={type:[x.WebGLSystem],name:"limits"};class op{constructor(){this.width=-1,this.height=-1,this.msaa=!1,this.msaaRenderBuffer=[]}}const Ne=[];Ne[Z.NONE]=void 0;Ne[Z.DISABLED]={stencilWriteMask:0,stencilReadMask:0};Ne[Z.RENDERING_MASK_ADD]={stencilFront:{compare:"equal",passOp:"increment-clamp"},stencilBack:{compare:"equal",passOp:"increment-clamp"}};Ne[Z.RENDERING_MASK_REMOVE]={stencilFront:{compare:"equal",passOp:"decrement-clamp"},stencilBack:{compare:"equal",passOp:"decrement-clamp"}};Ne[Z.MASK_ACTIVE]={stencilWriteMask:0,stencilFront:{compare:"equal",passOp:"keep"},stencilBack:{compare:"equal",passOp:"keep"}};Ne[Z.INVERSE_MASK_ACTIVE]={stencilWriteMask:0,stencilFront:{compare:"not-equal",passOp:"keep"},stencilBack:{compare:"not-equal",passOp:"keep"}};class ph{constructor(e){this._stencilCache={enabled:!1,stencilReference:0,stencilMode:Z.NONE},this._renderTargetStencilState=Object.create(null),e.renderTarget.onRenderTargetChange.add(this)}contextChange(e){this._gl=e,this._comparisonFuncMapping={always:e.ALWAYS,never:e.NEVER,equal:e.EQUAL,"not-equal":e.NOTEQUAL,less:e.LESS,"less-equal":e.LEQUAL,greater:e.GREATER,"greater-equal":e.GEQUAL},this._stencilOpsMapping={keep:e.KEEP,zero:e.ZERO,replace:e.REPLACE,invert:e.INVERT,"increment-clamp":e.INCR,"decrement-clamp":e.DECR,"increment-wrap":e.INCR_WRAP,"decrement-wrap":e.DECR_WRAP},this.resetState()}onRenderTargetChange(e){if(this._activeRenderTarget===e)return;this._activeRenderTarget=e;let t=this._renderTargetStencilState[e.uid];t||(t=this._renderTargetStencilState[e.uid]={stencilMode:Z.DISABLED,stencilReference:0}),this.setStencilMode(t.stencilMode,t.stencilReference)}resetState(){this._stencilCache.enabled=!1,this._stencilCache.stencilMode=Z.NONE,this._stencilCache.stencilReference=0}setStencilMode(e,t){const r=this._renderTargetStencilState[this._activeRenderTarget.uid],i=this._gl,n=Ne[e],o=this._stencilCache;if(r.stencilMode=e,r.stencilReference=t,e===Z.DISABLED){this._stencilCache.enabled&&(this._stencilCache.enabled=!1,i.disable(i.STENCIL_TEST));return}this._stencilCache.enabled||(this._stencilCache.enabled=!0,i.enable(i.STENCIL_TEST)),(e!==o.stencilMode||o.stencilReference!==t)&&(o.stencilMode=e,o.stencilReference=t,i.stencilFunc(this._comparisonFuncMapping[n.stencilBack.compare],t,255),i.stencilOp(i.KEEP,i.KEEP,this._stencilOpsMapping[n.stencilBack.passOp]))}}ph.extension={type:[x.WebGLSystem],name:"stencil"};class mh{constructor(e){this._syncFunctionHash=Object.create(null),this._adaptor=e,this._systemCheck()}_systemCheck(){if(!pa())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}ensureUniformGroup(e){const t=this.getUniformGroupData(e);e.buffer||(e.buffer=new ue({data:new Float32Array(t.layout.size/4),usage:U.UNIFORM|U.COPY_DST}))}getUniformGroupData(e){return this._syncFunctionHash[e._signature]||this._initUniformGroup(e)}_initUniformGroup(e){const t=e._signature;let r=this._syncFunctionHash[t];if(!r){const i=Object.keys(e.uniformStructures).map(a=>e.uniformStructures[a]),n=this._adaptor.createUboElements(i),o=this._generateUboSync(n.uboElements);r=this._syncFunctionHash[t]={layout:n,syncFunction:o}}return this._syncFunctionHash[t]}_generateUboSync(e){return this._adaptor.generateUboSync(e)}syncUniformGroup(e,t,r){const i=this.getUniformGroupData(e);e.buffer||(e.buffer=new ue({data:new Float32Array(i.layout.size/4),usage:U.UNIFORM|U.COPY_DST}));let n=null;return t||(t=e.buffer.data,n=e.buffer.dataInt32),r||(r=0),i.syncFunction(e.uniforms,t,n,r),!0}updateUniformGroup(e){if(e.isStatic&&!e._dirtyId)return!1;e._dirtyId=0;const t=this.syncUniformGroup(e);return e.buffer.update(),t}destroy(){this._syncFunctionHash=null}}const gh={f32:4,i32:4,"vec2<f32>":8,"vec3<f32>":12,"vec4<f32>":16,"vec2<i32>":8,"vec3<i32>":12,"vec4<i32>":16,"mat2x2<f32>":16*2,"mat3x3<f32>":16*3,"mat4x4<f32>":16*4};function ap(s){const e=s.map(n=>({data:n,offset:0,size:0})),t=16;let r=0,i=0;for(let n=0;n<e.length;n++){const o=e[n];if(r=gh[o.data.type],!r)throw new Error(`Unknown type ${o.data.type}`);o.data.size>1&&(r=Math.max(r,t)*o.data.size);const a=r===12?16:r;o.size=r;const h=i%t;h>0&&t-h<a?i+=(t-h)%16:i+=(r-h%r)%r,o.offset=i,i+=r}return i=Math.ceil(i/16)*16,{uboElements:e,size:i}}const Qe=[{type:"mat3x3<f32>",test:s=>s.value.a!==void 0,ubo:`
            var matrix = uv[name].toArray(true);
            data[offset] = matrix[0];
            data[offset + 1] = matrix[1];
            data[offset + 2] = matrix[2];
            data[offset + 4] = matrix[3];
            data[offset + 5] = matrix[4];
            data[offset + 6] = matrix[5];
            data[offset + 8] = matrix[6];
            data[offset + 9] = matrix[7];
            data[offset + 10] = matrix[8];
        `,uniform:`
            gl.uniformMatrix3fv(ud[name].location, false, uv[name].toArray(true));
        `},{type:"vec4<f32>",test:s=>s.type==="vec4<f32>"&&s.size===1&&s.value.width!==void 0,ubo:`
            v = uv[name];
            data[offset] = v.x;
            data[offset + 1] = v.y;
            data[offset + 2] = v.width;
            data[offset + 3] = v.height;
        `,uniform:`
            cv = ud[name].value;
            v = uv[name];
            if (cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height) {
                cv[0] = v.x;
                cv[1] = v.y;
                cv[2] = v.width;
                cv[3] = v.height;
                gl.uniform4f(ud[name].location, v.x, v.y, v.width, v.height);
            }
        `},{type:"vec2<f32>",test:s=>s.type==="vec2<f32>"&&s.size===1&&s.value.x!==void 0,ubo:`
            v = uv[name];
            data[offset] = v.x;
            data[offset + 1] = v.y;
        `,uniform:`
            cv = ud[name].value;
            v = uv[name];
            if (cv[0] !== v.x || cv[1] !== v.y) {
                cv[0] = v.x;
                cv[1] = v.y;
                gl.uniform2f(ud[name].location, v.x, v.y);
            }
        `},{type:"vec4<f32>",test:s=>s.type==="vec4<f32>"&&s.size===1&&s.value.red!==void 0,ubo:`
            v = uv[name];
            data[offset] = v.red;
            data[offset + 1] = v.green;
            data[offset + 2] = v.blue;
            data[offset + 3] = v.alpha;
        `,uniform:`
            cv = ud[name].value;
            v = uv[name];
            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha) {
                cv[0] = v.red;
                cv[1] = v.green;
                cv[2] = v.blue;
                cv[3] = v.alpha;
                gl.uniform4f(ud[name].location, v.red, v.green, v.blue, v.alpha);
            }
        `},{type:"vec3<f32>",test:s=>s.type==="vec3<f32>"&&s.size===1&&s.value.red!==void 0,ubo:`
            v = uv[name];
            data[offset] = v.red;
            data[offset + 1] = v.green;
            data[offset + 2] = v.blue;
        `,uniform:`
            cv = ud[name].value;
            v = uv[name];
            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue) {
                cv[0] = v.red;
                cv[1] = v.green;
                cv[2] = v.blue;
                gl.uniform3f(ud[name].location, v.red, v.green, v.blue);
            }
        `}];function _h(s,e,t,r){const i=[`
        var v = null;
        var v2 = null;
        var t = 0;
        var index = 0;
        var name = null;
        var arrayOffset = null;
    `];let n=0;for(let a=0;a<s.length;a++){const h=s[a],l=h.data.name;let c=!1,u=0;for(let f=0;f<Qe.length;f++)if(Qe[f].test(h.data)){u=h.offset/4,i.push(`name = "${l}";`,`offset += ${u-n};`,Qe[f][e]||Qe[f].ubo),c=!0;break}if(!c)if(h.data.size>1)u=h.offset/4,i.push(t(h,u-n));else{const f=r[h.data.type];u=h.offset/4,i.push(`
                    v = uv.${l};
                    offset += ${u-n};
                    ${f};
                `)}n=u}const o=i.join(`
`);return new Function("uv","data","dataInt32","offset",o)}function it(s,e){return`
        for (let i = 0; i < ${s*e}; i++) {
            data[offset + (((i / ${s})|0) * 4) + (i % ${s})] = v[i];
        }
    `}const xh={f32:`
        data[offset] = v;`,i32:`
        dataInt32[offset] = v;`,"vec2<f32>":`
        data[offset] = v[0];
        data[offset + 1] = v[1];`,"vec3<f32>":`
        data[offset] = v[0];
        data[offset + 1] = v[1];
        data[offset + 2] = v[2];`,"vec4<f32>":`
        data[offset] = v[0];
        data[offset + 1] = v[1];
        data[offset + 2] = v[2];
        data[offset + 3] = v[3];`,"vec2<i32>":`
        dataInt32[offset] = v[0];
        dataInt32[offset + 1] = v[1];`,"vec3<i32>":`
        dataInt32[offset] = v[0];
        dataInt32[offset + 1] = v[1];
        dataInt32[offset + 2] = v[2];`,"vec4<i32>":`
        dataInt32[offset] = v[0];
        dataInt32[offset + 1] = v[1];
        dataInt32[offset + 2] = v[2];
        dataInt32[offset + 3] = v[3];`,"mat2x2<f32>":`
        data[offset] = v[0];
        data[offset + 1] = v[1];
        data[offset + 4] = v[2];
        data[offset + 5] = v[3];`,"mat3x3<f32>":`
        data[offset] = v[0];
        data[offset + 1] = v[1];
        data[offset + 2] = v[2];
        data[offset + 4] = v[3];
        data[offset + 5] = v[4];
        data[offset + 6] = v[5];
        data[offset + 8] = v[6];
        data[offset + 9] = v[7];
        data[offset + 10] = v[8];`,"mat4x4<f32>":`
        for (let i = 0; i < 16; i++) {
            data[offset + i] = v[i];
        }`,"mat3x2<f32>":it(3,2),"mat4x2<f32>":it(4,2),"mat2x3<f32>":it(2,3),"mat4x3<f32>":it(4,3),"mat2x4<f32>":it(2,4),"mat3x4<f32>":it(3,4)},hp={...xh,"mat2x2<f32>":`
        data[offset] = v[0];
        data[offset + 1] = v[1];
        data[offset + 2] = v[2];
        data[offset + 3] = v[3];
    `};function lp(s,e){const t=Math.max(gh[s.data.type]/16,1),r=s.data.value.length/s.data.size,i=(4-r%4)%4,n=s.data.type.indexOf("i32")>=0?"dataInt32":"data";return`
        v = uv.${s.data.name};
        offset += ${e};

        arrayOffset = offset;

        t = 0;

        for(var i=0; i < ${s.data.size*t}; i++)
        {
            for(var j = 0; j < ${r}; j++)
            {
                ${n}[arrayOffset++] = v[t++];
            }
            ${i!==0?`arrayOffset += ${i};`:""}
        }
    `}function cp(s){return _h(s,"uboStd40",lp,xh)}class yh extends mh{constructor(){super({createUboElements:ap,generateUboSync:cp})}}yh.extension={type:[x.WebGLSystem],name:"ubo"};class up{constructor(){this._clearColorCache=[0,0,0,0],this._viewPortCache=new N}init(e,t){this._renderer=e,this._renderTargetSystem=t,e.runners.contextChange.add(this)}contextChange(){this._clearColorCache=[0,0,0,0],this._viewPortCache=new N}copyToTexture(e,t,r,i,n){const o=this._renderTargetSystem,a=this._renderer,h=o.getGpuRenderTarget(e),l=a.gl;return this.finishRenderPass(e),l.bindFramebuffer(l.FRAMEBUFFER,h.resolveTargetFramebuffer),a.texture.bind(t,0),l.copyTexSubImage2D(l.TEXTURE_2D,0,n.x,n.y,r.x,r.y,i.width,i.height),t}startRenderPass(e,t=!0,r,i){const n=this._renderTargetSystem,o=e.colorTexture,a=n.getGpuRenderTarget(e);let h=i.y;e.isRoot&&(h=o.pixelHeight-i.height),e.colorTextures.forEach(u=>{this._renderer.texture.unbind(u)});const l=this._renderer.gl;l.bindFramebuffer(l.FRAMEBUFFER,a.framebuffer);const c=this._viewPortCache;(c.x!==i.x||c.y!==h||c.width!==i.width||c.height!==i.height)&&(c.x=i.x,c.y=h,c.width=i.width,c.height=i.height,l.viewport(i.x,h,i.width,i.height)),!a.depthStencilRenderBuffer&&(e.stencil||e.depth)&&this._initStencil(a),this.clear(e,t,r)}finishRenderPass(e){const r=this._renderTargetSystem.getGpuRenderTarget(e);if(!r.msaa)return;const i=this._renderer.gl;i.bindFramebuffer(i.FRAMEBUFFER,r.resolveTargetFramebuffer),i.bindFramebuffer(i.READ_FRAMEBUFFER,r.framebuffer),i.blitFramebuffer(0,0,r.width,r.height,0,0,r.width,r.height,i.COLOR_BUFFER_BIT,i.NEAREST),i.bindFramebuffer(i.FRAMEBUFFER,r.framebuffer)}initGpuRenderTarget(e){const r=this._renderer.gl,i=new op;return e.colorTexture instanceof Be?(this._renderer.context.ensureCanvasSize(e.colorTexture.resource),i.framebuffer=null,i):(this._initColor(e,i),r.bindFramebuffer(r.FRAMEBUFFER,null),i)}destroyGpuRenderTarget(e){const t=this._renderer.gl;e.framebuffer&&(t.deleteFramebuffer(e.framebuffer),e.framebuffer=null),e.resolveTargetFramebuffer&&(t.deleteFramebuffer(e.resolveTargetFramebuffer),e.resolveTargetFramebuffer=null),e.depthStencilRenderBuffer&&(t.deleteRenderbuffer(e.depthStencilRenderBuffer),e.depthStencilRenderBuffer=null),e.msaaRenderBuffer.forEach(r=>{t.deleteRenderbuffer(r)}),e.msaaRenderBuffer=null}clear(e,t,r){if(!t)return;const i=this._renderTargetSystem;typeof t=="boolean"&&(t=t?ce.ALL:ce.NONE);const n=this._renderer.gl;if(t&ce.COLOR){r??(r=i.defaultClearColor);const o=this._clearColorCache,a=r;(o[0]!==a[0]||o[1]!==a[1]||o[2]!==a[2]||o[3]!==a[3])&&(o[0]=a[0],o[1]=a[1],o[2]=a[2],o[3]=a[3],n.clearColor(a[0],a[1],a[2],a[3]))}n.clear(t)}resizeGpuRenderTarget(e){if(e.isRoot)return;const r=this._renderTargetSystem.getGpuRenderTarget(e);this._resizeColor(e,r),(e.stencil||e.depth)&&this._resizeStencil(r)}_initColor(e,t){const r=this._renderer,i=r.gl,n=i.createFramebuffer();if(t.resolveTargetFramebuffer=n,i.bindFramebuffer(i.FRAMEBUFFER,n),t.width=e.colorTexture.source.pixelWidth,t.height=e.colorTexture.source.pixelHeight,e.colorTextures.forEach((o,a)=>{const h=o.source;h.antialias&&(r.context.supports.msaa?t.msaa=!0:F("[RenderTexture] Antialiasing on textures is not supported in WebGL1")),r.texture.bindSource(h,0);const c=r.texture.getGlSource(h).texture;i.framebufferTexture2D(i.FRAMEBUFFER,i.COLOR_ATTACHMENT0+a,3553,c,0)}),t.msaa){const o=i.createFramebuffer();t.framebuffer=o,i.bindFramebuffer(i.FRAMEBUFFER,o),e.colorTextures.forEach((a,h)=>{const l=i.createRenderbuffer();t.msaaRenderBuffer[h]=l})}else t.framebuffer=n;this._resizeColor(e,t)}_resizeColor(e,t){const r=e.colorTexture.source;if(t.width=r.pixelWidth,t.height=r.pixelHeight,e.colorTextures.forEach((i,n)=>{n!==0&&i.source.resize(r.width,r.height,r._resolution)}),t.msaa){const i=this._renderer,n=i.gl,o=t.framebuffer;n.bindFramebuffer(n.FRAMEBUFFER,o),e.colorTextures.forEach((a,h)=>{const l=a.source;i.texture.bindSource(l,0);const u=i.texture.getGlSource(l).internalFormat,f=t.msaaRenderBuffer[h];n.bindRenderbuffer(n.RENDERBUFFER,f),n.renderbufferStorageMultisample(n.RENDERBUFFER,4,u,l.pixelWidth,l.pixelHeight),n.framebufferRenderbuffer(n.FRAMEBUFFER,n.COLOR_ATTACHMENT0+h,n.RENDERBUFFER,f)})}}_initStencil(e){if(e.framebuffer===null)return;const t=this._renderer.gl,r=t.createRenderbuffer();e.depthStencilRenderBuffer=r,t.bindRenderbuffer(t.RENDERBUFFER,r),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_STENCIL_ATTACHMENT,t.RENDERBUFFER,r),this._resizeStencil(e)}_resizeStencil(e){const t=this._renderer.gl;t.bindRenderbuffer(t.RENDERBUFFER,e.depthStencilRenderBuffer),e.msaa?t.renderbufferStorageMultisample(t.RENDERBUFFER,4,t.DEPTH24_STENCIL8,e.width,e.height):t.renderbufferStorage(t.RENDERBUFFER,this._renderer.context.webGLVersion===2?t.DEPTH24_STENCIL8:t.DEPTH_STENCIL,e.width,e.height)}prerender(e){const t=e.colorTexture.resource;this._renderer.context.multiView&&Be.test(t)&&this._renderer.context.ensureCanvasSize(t)}postrender(e){if(this._renderer.context.multiView&&Be.test(e.colorTexture.resource)){const t=this._renderer.context.canvas,r=e.colorTexture;r.context2D.drawImage(t,0,r.pixelHeight-t.height)}}}function dp(s,e,t,r,i,n){const o=n?1:-1;return s.identity(),s.a=1/r*2,s.d=o*(1/i*2),s.tx=-1-e*s.a,s.ty=-o-t*s.d,s}const Pt=new Map;function bh(s,e){if(!Pt.has(s)){const t=new A({source:new Be({resource:s,...e})}),r=()=>{Pt.get(s)===t&&Pt.delete(s)};t.once("destroy",r),t.source.once("destroy",r),Pt.set(s,t)}return Pt.get(s)}function fp(s){const e=s.colorTexture.source.resource;return globalThis.HTMLCanvasElement&&e instanceof HTMLCanvasElement&&document.body.contains(e)}const vh=class Th{constructor(e={}){if(this.uid=V("renderTarget"),this.colorTextures=[],this.dirtyId=0,this.isRoot=!1,this._size=new Float32Array(2),this._managedColorTextures=!1,e={...Th.defaultOptions,...e},this.stencil=e.stencil,this.depth=e.depth,this.isRoot=e.isRoot,typeof e.colorTextures=="number"){this._managedColorTextures=!0;for(let t=0;t<e.colorTextures;t++)this.colorTextures.push(new Q({width:e.width,height:e.height,resolution:e.resolution,antialias:e.antialias}))}else{this.colorTextures=[...e.colorTextures.map(r=>r.source)];const t=this.colorTexture.source;this.resize(t.width,t.height,t._resolution)}this.colorTexture.source.on("resize",this.onSourceResize,this),(e.depthStencilTexture||this.stencil)&&(e.depthStencilTexture instanceof A||e.depthStencilTexture instanceof Q?this.depthStencilTexture=e.depthStencilTexture.source:this.ensureDepthStencilTexture())}get size(){const e=this._size;return e[0]=this.pixelWidth,e[1]=this.pixelHeight,e}get width(){return this.colorTexture.source.width}get height(){return this.colorTexture.source.height}get pixelWidth(){return this.colorTexture.source.pixelWidth}get pixelHeight(){return this.colorTexture.source.pixelHeight}get resolution(){return this.colorTexture.source._resolution}get colorTexture(){return this.colorTextures[0]}onSourceResize(e){this.resize(e.width,e.height,e._resolution,!0)}ensureDepthStencilTexture(){this.depthStencilTexture||(this.depthStencilTexture=new Q({width:this.width,height:this.height,resolution:this.resolution,format:"depth24plus-stencil8",autoGenerateMipmaps:!1,antialias:!1,mipLevelCount:1}))}resize(e,t,r=this.resolution,i=!1){this.dirtyId++,this.colorTextures.forEach((n,o)=>{i&&o===0||n.source.resize(e,t,r)}),this.depthStencilTexture&&this.depthStencilTexture.source.resize(e,t,r)}destroy(){this.colorTexture.source.off("resize",this.onSourceResize,this),this._managedColorTextures&&this.colorTextures.forEach(e=>{e.destroy()}),this.depthStencilTexture&&(this.depthStencilTexture.destroy(),delete this.depthStencilTexture)}};vh.defaultOptions={width:0,height:0,resolution:1,colorTextures:1,stencil:!1,depth:!1,antialias:!1,isRoot:!1};let Ys=vh;class Sh{constructor(e){this.rootViewPort=new N,this.viewport=new N,this.onRenderTargetChange=new ya("onRenderTargetChange"),this.projectionMatrix=new B,this.defaultClearColor=[0,0,0,0],this._renderSurfaceToRenderTargetHash=new Map,this._gpuRenderTargetHash=Object.create(null),this._renderTargetStack=[],this._renderer=e,e.renderableGC.addManagedHash(this,"_gpuRenderTargetHash")}finishRenderPass(){this.adaptor.finishRenderPass(this.renderTarget)}renderStart({target:e,clear:t,clearColor:r,frame:i}){this._renderTargetStack.length=0,this.push(e,t,r,i),this.rootViewPort.copyFrom(this.viewport),this.rootRenderTarget=this.renderTarget,this.renderingToScreen=fp(this.rootRenderTarget),this.adaptor.prerender?.(this.rootRenderTarget)}postrender(){this.adaptor.postrender?.(this.rootRenderTarget)}bind(e,t=!0,r,i){const n=this.getRenderTarget(e),o=this.renderTarget!==n;this.renderTarget=n,this.renderSurface=e;const a=this.getGpuRenderTarget(n);(n.pixelWidth!==a.width||n.pixelHeight!==a.height)&&(this.adaptor.resizeGpuRenderTarget(n),a.width=n.pixelWidth,a.height=n.pixelHeight);const h=n.colorTexture,l=this.viewport,c=h.pixelWidth,u=h.pixelHeight;if(!i&&e instanceof A&&(i=e.frame),i){const f=h._resolution;l.x=i.x*f+.5|0,l.y=i.y*f+.5|0,l.width=i.width*f+.5|0,l.height=i.height*f+.5|0}else l.x=0,l.y=0,l.width=c,l.height=u;return dp(this.projectionMatrix,0,0,l.width/h.resolution,l.height/h.resolution,!n.isRoot),this.adaptor.startRenderPass(n,t,r,l),o&&this.onRenderTargetChange.emit(n),n}clear(e,t=ce.ALL,r){t&&(e&&(e=this.getRenderTarget(e)),this.adaptor.clear(e||this.renderTarget,t,r,this.viewport))}contextChange(){this._gpuRenderTargetHash=Object.create(null)}push(e,t=ce.ALL,r,i){const n=this.bind(e,t,r,i);return this._renderTargetStack.push({renderTarget:n,frame:i}),n}pop(){this._renderTargetStack.pop();const e=this._renderTargetStack[this._renderTargetStack.length-1];this.bind(e.renderTarget,!1,null,e.frame)}getRenderTarget(e){return e.isTexture&&(e=e.source),this._renderSurfaceToRenderTargetHash.get(e)??this._initRenderTarget(e)}copyToTexture(e,t,r,i,n){r.x<0&&(i.width+=r.x,n.x-=r.x,r.x=0),r.y<0&&(i.height+=r.y,n.y-=r.y,r.y=0);const{pixelWidth:o,pixelHeight:a}=e;return i.width=Math.min(i.width,o-r.x),i.height=Math.min(i.height,a-r.y),this.adaptor.copyToTexture(e,t,r,i,n)}ensureDepthStencil(){this.renderTarget.stencil||(this.renderTarget.stencil=!0,this.adaptor.startRenderPass(this.renderTarget,!1,null,this.viewport))}destroy(){this._renderer=null,this._renderSurfaceToRenderTargetHash.forEach((e,t)=>{e!==t&&e.destroy()}),this._renderSurfaceToRenderTargetHash.clear(),this._gpuRenderTargetHash=Object.create(null)}_initRenderTarget(e){let t=null;return Be.test(e)&&(e=bh(e).source),e instanceof Ys?t=e:e instanceof Q&&(t=new Ys({colorTextures:[e]}),e.source instanceof Be&&(t.isRoot=!0),e.once("destroy",()=>{t.destroy(),this._renderSurfaceToRenderTargetHash.delete(e);const r=this._gpuRenderTargetHash[t.uid];r&&(this._gpuRenderTargetHash[t.uid]=null,this.adaptor.destroyGpuRenderTarget(r))})),this._renderSurfaceToRenderTargetHash.set(e,t),t}getGpuRenderTarget(e){return this._gpuRenderTargetHash[e.uid]||(this._gpuRenderTargetHash[e.uid]=this.adaptor.initGpuRenderTarget(e))}resetState(){this.renderTarget=null,this.renderSurface=null}}class wh extends Sh{constructor(e){super(e),this.adaptor=new up,this.adaptor.init(e,this)}}wh.extension={type:[x.WebGLSystem],name:"renderTarget"};class Ei extends de{constructor({buffer:e,offset:t,size:r}){super(),this.uid=V("buffer"),this._resourceType="bufferResource",this._touched=0,this._resourceId=V("resource"),this._bufferResource=!0,this.destroyed=!1,this.buffer=e,this.offset=t|0,this.size=r,this.buffer.on("change",this.onBufferChange,this)}onBufferChange(){this._resourceId=V("resource"),this.emit("change",this)}destroy(e=!1){this.destroyed=!0,e&&this.buffer.destroy(),this.emit("change",this),this.buffer=null}}function pp(s,e){const t=[],r=[`
        var g = s.groups;
        var sS = r.shader;
        var p = s.glProgram;
        var ugS = r.uniformGroup;
        var resources;
    `];let i=!1,n=0;const o=e._getProgramData(s.glProgram);for(const h in s.groups){const l=s.groups[h];t.push(`
            resources = g[${h}].resources;
        `);for(const c in l.resources){const u=l.resources[c];if(u instanceof ne)if(u.ubo){const f=s._uniformBindMap[h][Number(c)];t.push(`
                        sS.bindUniformBlock(
                            resources[${c}],
                            '${f}',
                            ${s.glProgram._uniformBlockData[f].index}
                        );
                    `)}else t.push(`
                        ugS.updateUniformGroup(resources[${c}], p, sD);
                    `);else if(u instanceof Ei){const f=s._uniformBindMap[h][Number(c)];t.push(`
                    sS.bindUniformBlock(
                        resources[${c}],
                        '${f}',
                        ${s.glProgram._uniformBlockData[f].index}
                    );
                `)}else if(u instanceof Q){const f=s._uniformBindMap[h][c],d=o.uniformData[f];d&&(i||(i=!0,r.push(`
                        var tS = r.texture;
                        `)),e._gl.uniform1i(d.location,n),t.push(`
                        tS.bind(resources[${c}], ${n});
                    `),n++)}}}const a=[...r,...t].join(`
`);return new Function("r","s","sD",a)}class mp{constructor(e,t){this.program=e,this.uniformData=t,this.uniformGroups={},this.uniformDirtyGroups={},this.uniformBlockBindings={}}destroy(){this.uniformData=null,this.uniformGroups=null,this.uniformDirtyGroups=null,this.uniformBlockBindings=null,this.program=null}}function co(s,e,t){const r=s.createShader(e);return s.shaderSource(r,t),s.compileShader(r),r}function ys(s){const e=new Array(s);for(let t=0;t<e.length;t++)e[t]=!1;return e}function Ph(s,e){switch(s){case"float":return 0;case"vec2":return new Float32Array(2*e);case"vec3":return new Float32Array(3*e);case"vec4":return new Float32Array(4*e);case"int":case"uint":case"sampler2D":case"sampler2DArray":return 0;case"ivec2":return new Int32Array(2*e);case"ivec3":return new Int32Array(3*e);case"ivec4":return new Int32Array(4*e);case"uvec2":return new Uint32Array(2*e);case"uvec3":return new Uint32Array(3*e);case"uvec4":return new Uint32Array(4*e);case"bool":return!1;case"bvec2":return ys(2*e);case"bvec3":return ys(3*e);case"bvec4":return ys(4*e);case"mat2":return new Float32Array([1,0,0,1]);case"mat3":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}let _r=null;const uo={FLOAT:"float",FLOAT_VEC2:"vec2",FLOAT_VEC3:"vec3",FLOAT_VEC4:"vec4",INT:"int",INT_VEC2:"ivec2",INT_VEC3:"ivec3",INT_VEC4:"ivec4",UNSIGNED_INT:"uint",UNSIGNED_INT_VEC2:"uvec2",UNSIGNED_INT_VEC3:"uvec3",UNSIGNED_INT_VEC4:"uvec4",BOOL:"bool",BOOL_VEC2:"bvec2",BOOL_VEC3:"bvec3",BOOL_VEC4:"bvec4",FLOAT_MAT2:"mat2",FLOAT_MAT3:"mat3",FLOAT_MAT4:"mat4",SAMPLER_2D:"sampler2D",INT_SAMPLER_2D:"sampler2D",UNSIGNED_INT_SAMPLER_2D:"sampler2D",SAMPLER_CUBE:"samplerCube",INT_SAMPLER_CUBE:"samplerCube",UNSIGNED_INT_SAMPLER_CUBE:"samplerCube",SAMPLER_2D_ARRAY:"sampler2DArray",INT_SAMPLER_2D_ARRAY:"sampler2DArray",UNSIGNED_INT_SAMPLER_2D_ARRAY:"sampler2DArray"},gp={float:"float32",vec2:"float32x2",vec3:"float32x3",vec4:"float32x4",int:"sint32",ivec2:"sint32x2",ivec3:"sint32x3",ivec4:"sint32x4",uint:"uint32",uvec2:"uint32x2",uvec3:"uint32x3",uvec4:"uint32x4",bool:"uint32",bvec2:"uint32x2",bvec3:"uint32x3",bvec4:"uint32x4"};function Ch(s,e){if(!_r){const t=Object.keys(uo);_r={};for(let r=0;r<t.length;++r){const i=t[r];_r[s[i]]=uo[i]}}return _r[e]}function _p(s,e){const t=Ch(s,e);return gp[t]||"float32"}function xp(s,e,t=!1){const r={},i=e.getProgramParameter(s,e.ACTIVE_ATTRIBUTES);for(let o=0;o<i;o++){const a=e.getActiveAttrib(s,o);if(a.name.startsWith("gl_"))continue;const h=_p(e,a.type);r[a.name]={location:0,format:h,stride:De(h).stride,offset:0,instance:!1,start:0}}const n=Object.keys(r);if(t){n.sort((o,a)=>o>a?1:-1);for(let o=0;o<n.length;o++)r[n[o]].location=o,e.bindAttribLocation(s,o,n[o]);e.linkProgram(s)}else for(let o=0;o<n.length;o++)r[n[o]].location=e.getAttribLocation(s,n[o]);return r}function yp(s,e){if(!e.ACTIVE_UNIFORM_BLOCKS)return{};const t={},r=e.getProgramParameter(s,e.ACTIVE_UNIFORM_BLOCKS);for(let i=0;i<r;i++){const n=e.getActiveUniformBlockName(s,i),o=e.getUniformBlockIndex(s,n),a=e.getActiveUniformBlockParameter(s,i,e.UNIFORM_BLOCK_DATA_SIZE);t[n]={name:n,index:o,size:a}}return t}function bp(s,e){const t={},r=e.getProgramParameter(s,e.ACTIVE_UNIFORMS);for(let i=0;i<r;i++){const n=e.getActiveUniform(s,i),o=n.name.replace(/\[.*?\]$/,""),a=!!n.name.match(/\[.*?\]$/),h=Ch(e,n.type);t[o]={name:o,index:i,type:h,size:n.size,isArray:a,value:Ph(h,n.size)}}return t}function fo(s,e){const t=s.getShaderSource(e).split(`
`).map((l,c)=>`${c}: ${l}`),r=s.getShaderInfoLog(e),i=r.split(`
`),n={},o=i.map(l=>parseFloat(l.replace(/^ERROR\: 0\:([\d]+)\:.*$/,"$1"))).filter(l=>l&&!n[l]?(n[l]=!0,!0):!1),a=[""];o.forEach(l=>{t[l-1]=`%c${t[l-1]}%c`,a.push("background: #FF0000; color:#FFFFFF; font-size: 10px","font-size: 10px")});const h=t.join(`
`);a[0]=h,console.error(r),console.groupCollapsed("click to view full shader code"),console.warn(...a),console.groupEnd()}function vp(s,e,t,r){s.getProgramParameter(e,s.LINK_STATUS)||(s.getShaderParameter(t,s.COMPILE_STATUS)||fo(s,t),s.getShaderParameter(r,s.COMPILE_STATUS)||fo(s,r),console.error("PixiJS Error: Could not initialize shader."),s.getProgramInfoLog(e)!==""&&console.warn("PixiJS Warning: gl.getProgramInfoLog()",s.getProgramInfoLog(e)))}function Tp(s,e){const t=co(s,s.VERTEX_SHADER,e.vertex),r=co(s,s.FRAGMENT_SHADER,e.fragment),i=s.createProgram();s.attachShader(i,t),s.attachShader(i,r);const n=e.transformFeedbackVaryings;n&&(typeof s.transformFeedbackVaryings!="function"?F("TransformFeedback is not supported but TransformFeedbackVaryings are given."):s.transformFeedbackVaryings(i,n.names,n.bufferMode==="separate"?s.SEPARATE_ATTRIBS:s.INTERLEAVED_ATTRIBS)),s.linkProgram(i),s.getProgramParameter(i,s.LINK_STATUS)||vp(s,i,t,r),e._attributeData=xp(i,s,!/^[ \t]*#[ \t]*version[ \t]+300[ \t]+es[ \t]*$/m.test(e.vertex)),e._uniformData=bp(i,s),e._uniformBlockData=yp(i,s),s.deleteShader(t),s.deleteShader(r);const o={};for(const h in e._uniformData){const l=e._uniformData[h];o[h]={location:s.getUniformLocation(i,h),value:Ph(l.type,l.size)}}return new mp(i,o)}const xr={textureCount:0,blockIndex:0};class Eh{constructor(e){this._activeProgram=null,this._programDataHash=Object.create(null),this._shaderSyncFunctions=Object.create(null),this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_programDataHash")}contextChange(e){this._gl=e,this._programDataHash=Object.create(null),this._shaderSyncFunctions=Object.create(null),this._activeProgram=null}bind(e,t){if(this._setProgram(e.glProgram),t)return;xr.textureCount=0,xr.blockIndex=0;let r=this._shaderSyncFunctions[e.glProgram._key];r||(r=this._shaderSyncFunctions[e.glProgram._key]=this._generateShaderSync(e,this)),this._renderer.buffer.nextBindBase(!!e.glProgram.transformFeedbackVaryings),r(this._renderer,e,xr)}updateUniformGroup(e){this._renderer.uniformGroup.updateUniformGroup(e,this._activeProgram,xr)}bindUniformBlock(e,t,r=0){const i=this._renderer.buffer,n=this._getProgramData(this._activeProgram),o=e._bufferResource;o||this._renderer.ubo.updateUniformGroup(e);const a=e.buffer,h=i.updateBuffer(a),l=i.freeLocationForBufferBase(h);if(o){const{offset:u,size:f}=e;u===0&&f===a.data.byteLength?i.bindBufferBase(h,l):i.bindBufferRange(h,l,u)}else i.getLastBindBaseLocation(h)!==l&&i.bindBufferBase(h,l);const c=this._activeProgram._uniformBlockData[t].index;n.uniformBlockBindings[r]!==l&&(n.uniformBlockBindings[r]=l,this._renderer.gl.uniformBlockBinding(n.program,c,l))}_setProgram(e){if(this._activeProgram===e)return;this._activeProgram=e;const t=this._getProgramData(e);this._gl.useProgram(t.program)}_getProgramData(e){return this._programDataHash[e._key]||this._createProgramData(e)}_createProgramData(e){const t=e._key;return this._programDataHash[t]=Tp(this._gl,e),this._programDataHash[t]}destroy(){for(const e of Object.keys(this._programDataHash))this._programDataHash[e].destroy(),this._programDataHash[e]=null;this._programDataHash=null}_generateShaderSync(e,t){return pp(e,t)}resetState(){this._activeProgram=null}}Eh.extension={type:[x.WebGLSystem],name:"shader"};const Sp={f32:`if (cv !== v) {
            cu.value = v;
            gl.uniform1f(location, v);
        }`,"vec2<f32>":`if (cv[0] !== v[0] || cv[1] !== v[1]) {
            cv[0] = v[0];
            cv[1] = v[1];
            gl.uniform2f(location, v[0], v[1]);
        }`,"vec3<f32>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            gl.uniform3f(location, v[0], v[1], v[2]);
        }`,"vec4<f32>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            cv[3] = v[3];
            gl.uniform4f(location, v[0], v[1], v[2], v[3]);
        }`,i32:`if (cv !== v) {
            cu.value = v;
            gl.uniform1i(location, v);
        }`,"vec2<i32>":`if (cv[0] !== v[0] || cv[1] !== v[1]) {
            cv[0] = v[0];
            cv[1] = v[1];
            gl.uniform2i(location, v[0], v[1]);
        }`,"vec3<i32>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            gl.uniform3i(location, v[0], v[1], v[2]);
        }`,"vec4<i32>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            cv[3] = v[3];
            gl.uniform4i(location, v[0], v[1], v[2], v[3]);
        }`,u32:`if (cv !== v) {
            cu.value = v;
            gl.uniform1ui(location, v);
        }`,"vec2<u32>":`if (cv[0] !== v[0] || cv[1] !== v[1]) {
            cv[0] = v[0];
            cv[1] = v[1];
            gl.uniform2ui(location, v[0], v[1]);
        }`,"vec3<u32>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            gl.uniform3ui(location, v[0], v[1], v[2]);
        }`,"vec4<u32>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            cv[3] = v[3];
            gl.uniform4ui(location, v[0], v[1], v[2], v[3]);
        }`,bool:`if (cv !== v) {
            cu.value = v;
            gl.uniform1i(location, v);
        }`,"vec2<bool>":`if (cv[0] !== v[0] || cv[1] !== v[1]) {
            cv[0] = v[0];
            cv[1] = v[1];
            gl.uniform2i(location, v[0], v[1]);
        }`,"vec3<bool>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            gl.uniform3i(location, v[0], v[1], v[2]);
        }`,"vec4<bool>":`if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3]) {
            cv[0] = v[0];
            cv[1] = v[1];
            cv[2] = v[2];
            cv[3] = v[3];
            gl.uniform4i(location, v[0], v[1], v[2], v[3]);
        }`,"mat2x2<f32>":"gl.uniformMatrix2fv(location, false, v);","mat3x3<f32>":"gl.uniformMatrix3fv(location, false, v);","mat4x4<f32>":"gl.uniformMatrix4fv(location, false, v);"},wp={f32:"gl.uniform1fv(location, v);","vec2<f32>":"gl.uniform2fv(location, v);","vec3<f32>":"gl.uniform3fv(location, v);","vec4<f32>":"gl.uniform4fv(location, v);","mat2x2<f32>":"gl.uniformMatrix2fv(location, false, v);","mat3x3<f32>":"gl.uniformMatrix3fv(location, false, v);","mat4x4<f32>":"gl.uniformMatrix4fv(location, false, v);",i32:"gl.uniform1iv(location, v);","vec2<i32>":"gl.uniform2iv(location, v);","vec3<i32>":"gl.uniform3iv(location, v);","vec4<i32>":"gl.uniform4iv(location, v);",u32:"gl.uniform1iv(location, v);","vec2<u32>":"gl.uniform2iv(location, v);","vec3<u32>":"gl.uniform3iv(location, v);","vec4<u32>":"gl.uniform4iv(location, v);",bool:"gl.uniform1iv(location, v);","vec2<bool>":"gl.uniform2iv(location, v);","vec3<bool>":"gl.uniform3iv(location, v);","vec4<bool>":"gl.uniform4iv(location, v);"};function Pp(s,e){const t=[`
        var v = null;
        var cv = null;
        var cu = null;
        var t = 0;
        var gl = renderer.gl;
        var name = null;
    `];for(const r in s.uniforms){if(!e[r]){s.uniforms[r]instanceof ne?s.uniforms[r].ubo?t.push(`
                        renderer.shader.bindUniformBlock(uv.${r}, "${r}");
                    `):t.push(`
                        renderer.shader.updateUniformGroup(uv.${r});
                    `):s.uniforms[r]instanceof Ei&&t.push(`
                        renderer.shader.bindBufferResource(uv.${r}, "${r}");
                    `);continue}const i=s.uniformStructures[r];let n=!1;for(let o=0;o<Qe.length;o++){const a=Qe[o];if(i.type===a.type&&a.test(i)){t.push(`name = "${r}";`,Qe[o].uniform),n=!0;break}}if(!n){const a=(i.size===1?Sp:wp)[i.type].replace("location",`ud["${r}"].location`);t.push(`
            cu = ud["${r}"];
            cv = cu.value;
            v = uv["${r}"];
            ${a};`)}}return new Function("ud","uv","renderer","syncData",t.join(`
`))}class Mh{constructor(e){this._cache={},this._uniformGroupSyncHash={},this._renderer=e,this.gl=null,this._cache={}}contextChange(e){this.gl=e}updateUniformGroup(e,t,r){const i=this._renderer.shader._getProgramData(t);(!e.isStatic||e._dirtyId!==i.uniformDirtyGroups[e.uid])&&(i.uniformDirtyGroups[e.uid]=e._dirtyId,this._getUniformSyncFunction(e,t)(i.uniformData,e.uniforms,this._renderer,r))}_getUniformSyncFunction(e,t){return this._uniformGroupSyncHash[e._signature]?.[t._key]||this._createUniformSyncFunction(e,t)}_createUniformSyncFunction(e,t){const r=this._uniformGroupSyncHash[e._signature]||(this._uniformGroupSyncHash[e._signature]={}),i=this._getSignature(e,t._uniformData,"u");return this._cache[i]||(this._cache[i]=this._generateUniformsSync(e,t._uniformData)),r[t._key]=this._cache[i],r[t._key]}_generateUniformsSync(e,t){return Pp(e,t)}_getSignature(e,t,r){const i=e.uniforms,n=[`${r}-`];for(const o in i)n.push(o),t[o]&&n.push(t[o].type);return n.join("-")}destroy(){this._renderer=null,this._cache=null}}Mh.extension={type:[x.WebGLSystem],name:"uniformGroup"};function Cp(s){const e={};if(e.normal=[s.ONE,s.ONE_MINUS_SRC_ALPHA],e.add=[s.ONE,s.ONE],e.multiply=[s.DST_COLOR,s.ONE_MINUS_SRC_ALPHA,s.ONE,s.ONE_MINUS_SRC_ALPHA],e.screen=[s.ONE,s.ONE_MINUS_SRC_COLOR,s.ONE,s.ONE_MINUS_SRC_ALPHA],e.none=[0,0],e["normal-npm"]=[s.SRC_ALPHA,s.ONE_MINUS_SRC_ALPHA,s.ONE,s.ONE_MINUS_SRC_ALPHA],e["add-npm"]=[s.SRC_ALPHA,s.ONE,s.ONE,s.ONE],e["screen-npm"]=[s.SRC_ALPHA,s.ONE_MINUS_SRC_COLOR,s.ONE,s.ONE_MINUS_SRC_ALPHA],e.erase=[s.ZERO,s.ONE_MINUS_SRC_ALPHA],!(s instanceof $.get().getWebGLRenderingContext()))e.min=[s.ONE,s.ONE,s.ONE,s.ONE,s.MIN,s.MIN],e.max=[s.ONE,s.ONE,s.ONE,s.ONE,s.MAX,s.MAX];else{const r=s.getExtension("EXT_blend_minmax");r&&(e.min=[s.ONE,s.ONE,s.ONE,s.ONE,r.MIN_EXT,r.MIN_EXT],e.max=[s.ONE,s.ONE,s.ONE,s.ONE,r.MAX_EXT,r.MAX_EXT])}return e}const Ep=0,Mp=1,Ap=2,Bp=3,Rp=4,kp=5,Ah=class js{constructor(e){this._invertFrontFace=!1,this.gl=null,this.stateId=0,this.polygonOffset=0,this.blendMode="none",this._blendEq=!1,this.map=[],this.map[Ep]=this.setBlend,this.map[Mp]=this.setOffset,this.map[Ap]=this.setCullFace,this.map[Bp]=this.setDepthTest,this.map[Rp]=this.setFrontFace,this.map[kp]=this.setDepthMask,this.checks=[],this.defaultState=be.for2d(),e.renderTarget.onRenderTargetChange.add(this)}onRenderTargetChange(e){this._invertFrontFace=!e.isRoot,this._cullFace?this.setFrontFace(this._frontFace):this._frontFaceDirty=!0}contextChange(e){this.gl=e,this.blendModesMap=Cp(e),this.resetState()}set(e){if(e||(e=this.defaultState),this.stateId!==e.data){let t=this.stateId^e.data,r=0;for(;t;)t&1&&this.map[r].call(this,!!(e.data&1<<r)),t>>=1,r++;this.stateId=e.data}for(let t=0;t<this.checks.length;t++)this.checks[t](this,e)}forceState(e){e||(e=this.defaultState);for(let t=0;t<this.map.length;t++)this.map[t].call(this,!!(e.data&1<<t));for(let t=0;t<this.checks.length;t++)this.checks[t](this,e);this.stateId=e.data}setBlend(e){this._updateCheck(js._checkBlendMode,e),this.gl[e?"enable":"disable"](this.gl.BLEND)}setOffset(e){this._updateCheck(js._checkPolygonOffset,e),this.gl[e?"enable":"disable"](this.gl.POLYGON_OFFSET_FILL)}setDepthTest(e){this.gl[e?"enable":"disable"](this.gl.DEPTH_TEST)}setDepthMask(e){this.gl.depthMask(e)}setCullFace(e){this._cullFace=e,this.gl[e?"enable":"disable"](this.gl.CULL_FACE),this._cullFace&&this._frontFaceDirty&&this.setFrontFace(this._frontFace)}setFrontFace(e){this._frontFace=e,this._frontFaceDirty=!1;const t=this._invertFrontFace?!e:e;this._glFrontFace!==t&&(this._glFrontFace=t,this.gl.frontFace(this.gl[t?"CW":"CCW"]))}setBlendMode(e){if(this.blendModesMap[e]||(e="normal"),e===this.blendMode)return;this.blendMode=e;const t=this.blendModesMap[e],r=this.gl;t.length===2?r.blendFunc(t[0],t[1]):r.blendFuncSeparate(t[0],t[1],t[2],t[3]),t.length===6?(this._blendEq=!0,r.blendEquationSeparate(t[4],t[5])):this._blendEq&&(this._blendEq=!1,r.blendEquationSeparate(r.FUNC_ADD,r.FUNC_ADD))}setPolygonOffset(e,t){this.gl.polygonOffset(e,t)}resetState(){this._glFrontFace=!1,this._frontFace=!1,this._cullFace=!1,this._frontFaceDirty=!1,this._invertFrontFace=!1,this.gl.frontFace(this.gl.CCW),this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,!1),this.forceState(this.defaultState),this._blendEq=!0,this.blendMode="",this.setBlendMode("normal")}_updateCheck(e,t){const r=this.checks.indexOf(e);t&&r===-1?this.checks.push(e):!t&&r!==-1&&this.checks.splice(r,1)}static _checkBlendMode(e,t){e.setBlendMode(t.blendMode)}static _checkPolygonOffset(e,t){e.setPolygonOffset(1,t.polygonOffset)}destroy(){this.gl=null,this.checks.length=0}};Ah.extension={type:[x.WebGLSystem],name:"state"};let Gp=Ah;class Ip{constructor(e){this.target=hh.TEXTURE_2D,this.texture=e,this.width=-1,this.height=-1,this.type=D.UNSIGNED_BYTE,this.internalFormat=Xs.RGBA,this.format=Xs.RGBA,this.samplerType=0}}const Fp={id:"buffer",upload(s,e,t){e.width===s.width||e.height===s.height?t.texSubImage2D(t.TEXTURE_2D,0,0,0,s.width,s.height,e.format,e.type,s.resource):t.texImage2D(e.target,0,e.internalFormat,s.width,s.height,0,e.format,e.type,s.resource),e.width=s.width,e.height=s.height}},Up={"bc1-rgba-unorm":!0,"bc1-rgba-unorm-srgb":!0,"bc2-rgba-unorm":!0,"bc2-rgba-unorm-srgb":!0,"bc3-rgba-unorm":!0,"bc3-rgba-unorm-srgb":!0,"bc4-r-unorm":!0,"bc4-r-snorm":!0,"bc5-rg-unorm":!0,"bc5-rg-snorm":!0,"bc6h-rgb-ufloat":!0,"bc6h-rgb-float":!0,"bc7-rgba-unorm":!0,"bc7-rgba-unorm-srgb":!0,"etc2-rgb8unorm":!0,"etc2-rgb8unorm-srgb":!0,"etc2-rgb8a1unorm":!0,"etc2-rgb8a1unorm-srgb":!0,"etc2-rgba8unorm":!0,"etc2-rgba8unorm-srgb":!0,"eac-r11unorm":!0,"eac-r11snorm":!0,"eac-rg11unorm":!0,"eac-rg11snorm":!0,"astc-4x4-unorm":!0,"astc-4x4-unorm-srgb":!0,"astc-5x4-unorm":!0,"astc-5x4-unorm-srgb":!0,"astc-5x5-unorm":!0,"astc-5x5-unorm-srgb":!0,"astc-6x5-unorm":!0,"astc-6x5-unorm-srgb":!0,"astc-6x6-unorm":!0,"astc-6x6-unorm-srgb":!0,"astc-8x5-unorm":!0,"astc-8x5-unorm-srgb":!0,"astc-8x6-unorm":!0,"astc-8x6-unorm-srgb":!0,"astc-8x8-unorm":!0,"astc-8x8-unorm-srgb":!0,"astc-10x5-unorm":!0,"astc-10x5-unorm-srgb":!0,"astc-10x6-unorm":!0,"astc-10x6-unorm-srgb":!0,"astc-10x8-unorm":!0,"astc-10x8-unorm-srgb":!0,"astc-10x10-unorm":!0,"astc-10x10-unorm-srgb":!0,"astc-12x10-unorm":!0,"astc-12x10-unorm-srgb":!0,"astc-12x12-unorm":!0,"astc-12x12-unorm-srgb":!0},Dp={id:"compressed",upload(s,e,t){t.pixelStorei(t.UNPACK_ALIGNMENT,4);let r=s.pixelWidth,i=s.pixelHeight;const n=!!Up[s.format];for(let o=0;o<s.resource.length;o++){const a=s.resource[o];n?t.compressedTexImage2D(t.TEXTURE_2D,o,e.internalFormat,r,i,0,a):t.texImage2D(t.TEXTURE_2D,o,e.internalFormat,r,i,0,e.format,e.type,a),r=Math.max(r>>1,1),i=Math.max(i>>1,1)}}},Bh={id:"image",upload(s,e,t,r){const i=e.width,n=e.height,o=s.pixelWidth,a=s.pixelHeight,h=s.resourceWidth,l=s.resourceHeight;h<o||l<a?((i!==o||n!==a)&&t.texImage2D(e.target,0,e.internalFormat,o,a,0,e.format,e.type,null),r===2?t.texSubImage2D(t.TEXTURE_2D,0,0,0,h,l,e.format,e.type,s.resource):t.texSubImage2D(t.TEXTURE_2D,0,0,0,e.format,e.type,s.resource)):i===o&&n===a?t.texSubImage2D(t.TEXTURE_2D,0,0,0,e.format,e.type,s.resource):r===2?t.texImage2D(e.target,0,e.internalFormat,o,a,0,e.format,e.type,s.resource):t.texImage2D(e.target,0,e.internalFormat,e.format,e.type,s.resource),e.width=o,e.height=a}},Op={id:"video",upload(s,e,t,r){if(!s.isValid){t.texImage2D(e.target,0,e.internalFormat,1,1,0,e.format,e.type,null);return}Bh.upload(s,e,t,r)}},po={linear:9729,nearest:9728},Lp={linear:{linear:9987,nearest:9985},nearest:{linear:9986,nearest:9984}},bs={"clamp-to-edge":33071,repeat:10497,"mirror-repeat":33648},Np={never:512,less:513,equal:514,"less-equal":515,greater:516,"not-equal":517,"greater-equal":518,always:519};function mo(s,e,t,r,i,n,o,a){const h=n;if(!a||s.addressModeU!=="repeat"||s.addressModeV!=="repeat"||s.addressModeW!=="repeat"){const l=bs[o?"clamp-to-edge":s.addressModeU],c=bs[o?"clamp-to-edge":s.addressModeV],u=bs[o?"clamp-to-edge":s.addressModeW];e[i](h,e.TEXTURE_WRAP_S,l),e[i](h,e.TEXTURE_WRAP_T,c),e.TEXTURE_WRAP_R&&e[i](h,e.TEXTURE_WRAP_R,u)}if((!a||s.magFilter!=="linear")&&e[i](h,e.TEXTURE_MAG_FILTER,po[s.magFilter]),t){if(!a||s.mipmapFilter!=="linear"){const l=Lp[s.minFilter][s.mipmapFilter];e[i](h,e.TEXTURE_MIN_FILTER,l)}}else e[i](h,e.TEXTURE_MIN_FILTER,po[s.minFilter]);if(r&&s.maxAnisotropy>1){const l=Math.min(s.maxAnisotropy,e.getParameter(r.MAX_TEXTURE_MAX_ANISOTROPY_EXT));e[i](h,r.TEXTURE_MAX_ANISOTROPY_EXT,l)}s.compare&&e[i](h,e.TEXTURE_COMPARE_FUNC,Np[s.compare])}function Hp(s){return{r8unorm:s.RED,r8snorm:s.RED,r8uint:s.RED,r8sint:s.RED,r16uint:s.RED,r16sint:s.RED,r16float:s.RED,rg8unorm:s.RG,rg8snorm:s.RG,rg8uint:s.RG,rg8sint:s.RG,r32uint:s.RED,r32sint:s.RED,r32float:s.RED,rg16uint:s.RG,rg16sint:s.RG,rg16float:s.RG,rgba8unorm:s.RGBA,"rgba8unorm-srgb":s.RGBA,rgba8snorm:s.RGBA,rgba8uint:s.RGBA,rgba8sint:s.RGBA,bgra8unorm:s.RGBA,"bgra8unorm-srgb":s.RGBA,rgb9e5ufloat:s.RGB,rgb10a2unorm:s.RGBA,rg11b10ufloat:s.RGB,rg32uint:s.RG,rg32sint:s.RG,rg32float:s.RG,rgba16uint:s.RGBA,rgba16sint:s.RGBA,rgba16float:s.RGBA,rgba32uint:s.RGBA,rgba32sint:s.RGBA,rgba32float:s.RGBA,stencil8:s.STENCIL_INDEX8,depth16unorm:s.DEPTH_COMPONENT,depth24plus:s.DEPTH_COMPONENT,"depth24plus-stencil8":s.DEPTH_STENCIL,depth32float:s.DEPTH_COMPONENT,"depth32float-stencil8":s.DEPTH_STENCIL}}function zp(s,e){let t={},r=s.RGBA;return s instanceof $.get().getWebGLRenderingContext()?e.srgb&&(t={"rgba8unorm-srgb":e.srgb.SRGB8_ALPHA8_EXT,"bgra8unorm-srgb":e.srgb.SRGB8_ALPHA8_EXT}):(t={"rgba8unorm-srgb":s.SRGB8_ALPHA8,"bgra8unorm-srgb":s.SRGB8_ALPHA8},r=s.RGBA8),{r8unorm:s.R8,r8snorm:s.R8_SNORM,r8uint:s.R8UI,r8sint:s.R8I,r16uint:s.R16UI,r16sint:s.R16I,r16float:s.R16F,rg8unorm:s.RG8,rg8snorm:s.RG8_SNORM,rg8uint:s.RG8UI,rg8sint:s.RG8I,r32uint:s.R32UI,r32sint:s.R32I,r32float:s.R32F,rg16uint:s.RG16UI,rg16sint:s.RG16I,rg16float:s.RG16F,rgba8unorm:s.RGBA,...t,rgba8snorm:s.RGBA8_SNORM,rgba8uint:s.RGBA8UI,rgba8sint:s.RGBA8I,bgra8unorm:r,rgb9e5ufloat:s.RGB9_E5,rgb10a2unorm:s.RGB10_A2,rg11b10ufloat:s.R11F_G11F_B10F,rg32uint:s.RG32UI,rg32sint:s.RG32I,rg32float:s.RG32F,rgba16uint:s.RGBA16UI,rgba16sint:s.RGBA16I,rgba16float:s.RGBA16F,rgba32uint:s.RGBA32UI,rgba32sint:s.RGBA32I,rgba32float:s.RGBA32F,stencil8:s.STENCIL_INDEX8,depth16unorm:s.DEPTH_COMPONENT16,depth24plus:s.DEPTH_COMPONENT24,"depth24plus-stencil8":s.DEPTH24_STENCIL8,depth32float:s.DEPTH_COMPONENT32F,"depth32float-stencil8":s.DEPTH32F_STENCIL8,...e.s3tc?{"bc1-rgba-unorm":e.s3tc.COMPRESSED_RGBA_S3TC_DXT1_EXT,"bc2-rgba-unorm":e.s3tc.COMPRESSED_RGBA_S3TC_DXT3_EXT,"bc3-rgba-unorm":e.s3tc.COMPRESSED_RGBA_S3TC_DXT5_EXT}:{},...e.s3tc_sRGB?{"bc1-rgba-unorm-srgb":e.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,"bc2-rgba-unorm-srgb":e.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,"bc3-rgba-unorm-srgb":e.s3tc_sRGB.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT}:{},...e.rgtc?{"bc4-r-unorm":e.rgtc.COMPRESSED_RED_RGTC1_EXT,"bc4-r-snorm":e.rgtc.COMPRESSED_SIGNED_RED_RGTC1_EXT,"bc5-rg-unorm":e.rgtc.COMPRESSED_RED_GREEN_RGTC2_EXT,"bc5-rg-snorm":e.rgtc.COMPRESSED_SIGNED_RED_GREEN_RGTC2_EXT}:{},...e.bptc?{"bc6h-rgb-float":e.bptc.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT,"bc6h-rgb-ufloat":e.bptc.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT,"bc7-rgba-unorm":e.bptc.COMPRESSED_RGBA_BPTC_UNORM_EXT,"bc7-rgba-unorm-srgb":e.bptc.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT}:{},...e.etc?{"etc2-rgb8unorm":e.etc.COMPRESSED_RGB8_ETC2,"etc2-rgb8unorm-srgb":e.etc.COMPRESSED_SRGB8_ETC2,"etc2-rgb8a1unorm":e.etc.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2,"etc2-rgb8a1unorm-srgb":e.etc.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2,"etc2-rgba8unorm":e.etc.COMPRESSED_RGBA8_ETC2_EAC,"etc2-rgba8unorm-srgb":e.etc.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC,"eac-r11unorm":e.etc.COMPRESSED_R11_EAC,"eac-rg11unorm":e.etc.COMPRESSED_SIGNED_RG11_EAC}:{},...e.astc?{"astc-4x4-unorm":e.astc.COMPRESSED_RGBA_ASTC_4x4_KHR,"astc-4x4-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_4x4_KHR,"astc-5x4-unorm":e.astc.COMPRESSED_RGBA_ASTC_5x4_KHR,"astc-5x4-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x4_KHR,"astc-5x5-unorm":e.astc.COMPRESSED_RGBA_ASTC_5x5_KHR,"astc-5x5-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_5x5_KHR,"astc-6x5-unorm":e.astc.COMPRESSED_RGBA_ASTC_6x5_KHR,"astc-6x5-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x5_KHR,"astc-6x6-unorm":e.astc.COMPRESSED_RGBA_ASTC_6x6_KHR,"astc-6x6-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_6x6_KHR,"astc-8x5-unorm":e.astc.COMPRESSED_RGBA_ASTC_8x5_KHR,"astc-8x5-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x5_KHR,"astc-8x6-unorm":e.astc.COMPRESSED_RGBA_ASTC_8x6_KHR,"astc-8x6-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x6_KHR,"astc-8x8-unorm":e.astc.COMPRESSED_RGBA_ASTC_8x8_KHR,"astc-8x8-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_8x8_KHR,"astc-10x5-unorm":e.astc.COMPRESSED_RGBA_ASTC_10x5_KHR,"astc-10x5-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x5_KHR,"astc-10x6-unorm":e.astc.COMPRESSED_RGBA_ASTC_10x6_KHR,"astc-10x6-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x6_KHR,"astc-10x8-unorm":e.astc.COMPRESSED_RGBA_ASTC_10x8_KHR,"astc-10x8-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x8_KHR,"astc-10x10-unorm":e.astc.COMPRESSED_RGBA_ASTC_10x10_KHR,"astc-10x10-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_10x10_KHR,"astc-12x10-unorm":e.astc.COMPRESSED_RGBA_ASTC_12x10_KHR,"astc-12x10-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x10_KHR,"astc-12x12-unorm":e.astc.COMPRESSED_RGBA_ASTC_12x12_KHR,"astc-12x12-unorm-srgb":e.astc.COMPRESSED_SRGB8_ALPHA8_ASTC_12x12_KHR}:{}}}function Wp(s){return{r8unorm:s.UNSIGNED_BYTE,r8snorm:s.BYTE,r8uint:s.UNSIGNED_BYTE,r8sint:s.BYTE,r16uint:s.UNSIGNED_SHORT,r16sint:s.SHORT,r16float:s.HALF_FLOAT,rg8unorm:s.UNSIGNED_BYTE,rg8snorm:s.BYTE,rg8uint:s.UNSIGNED_BYTE,rg8sint:s.BYTE,r32uint:s.UNSIGNED_INT,r32sint:s.INT,r32float:s.FLOAT,rg16uint:s.UNSIGNED_SHORT,rg16sint:s.SHORT,rg16float:s.HALF_FLOAT,rgba8unorm:s.UNSIGNED_BYTE,"rgba8unorm-srgb":s.UNSIGNED_BYTE,rgba8snorm:s.BYTE,rgba8uint:s.UNSIGNED_BYTE,rgba8sint:s.BYTE,bgra8unorm:s.UNSIGNED_BYTE,"bgra8unorm-srgb":s.UNSIGNED_BYTE,rgb9e5ufloat:s.UNSIGNED_INT_5_9_9_9_REV,rgb10a2unorm:s.UNSIGNED_INT_2_10_10_10_REV,rg11b10ufloat:s.UNSIGNED_INT_10F_11F_11F_REV,rg32uint:s.UNSIGNED_INT,rg32sint:s.INT,rg32float:s.FLOAT,rgba16uint:s.UNSIGNED_SHORT,rgba16sint:s.SHORT,rgba16float:s.HALF_FLOAT,rgba32uint:s.UNSIGNED_INT,rgba32sint:s.INT,rgba32float:s.FLOAT,stencil8:s.UNSIGNED_BYTE,depth16unorm:s.UNSIGNED_SHORT,depth24plus:s.UNSIGNED_INT,"depth24plus-stencil8":s.UNSIGNED_INT_24_8,depth32float:s.FLOAT,"depth32float-stencil8":s.FLOAT_32_UNSIGNED_INT_24_8_REV}}const Vp=4;class Rh{constructor(e){this.managedTextures=[],this._glTextures=Object.create(null),this._glSamplers=Object.create(null),this._boundTextures=[],this._activeTextureLocation=-1,this._boundSamplers=Object.create(null),this._uploads={image:Bh,buffer:Fp,video:Op,compressed:Dp},this._premultiplyAlpha=!1,this._useSeparateSamplers=!1,this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_glTextures"),this._renderer.renderableGC.addManagedHash(this,"_glSamplers")}contextChange(e){this._gl=e,this._mapFormatToInternalFormat||(this._mapFormatToInternalFormat=zp(e,this._renderer.context.extensions),this._mapFormatToType=Wp(e),this._mapFormatToFormat=Hp(e)),this._glTextures=Object.create(null),this._glSamplers=Object.create(null),this._boundSamplers=Object.create(null),this._premultiplyAlpha=!1;for(let t=0;t<16;t++)this.bind(A.EMPTY,t)}initSource(e){this.bind(e)}bind(e,t=0){const r=e.source;e?(this.bindSource(r,t),this._useSeparateSamplers&&this._bindSampler(r.style,t)):(this.bindSource(null,t),this._useSeparateSamplers&&this._bindSampler(null,t))}bindSource(e,t=0){const r=this._gl;if(e._touched=this._renderer.textureGC.count,this._boundTextures[t]!==e){this._boundTextures[t]=e,this._activateLocation(t),e||(e=A.EMPTY.source);const i=this.getGlSource(e);r.bindTexture(i.target,i.texture)}}_bindSampler(e,t=0){const r=this._gl;if(!e){this._boundSamplers[t]=null,r.bindSampler(t,null);return}const i=this._getGlSampler(e);this._boundSamplers[t]!==i&&(this._boundSamplers[t]=i,r.bindSampler(t,i))}unbind(e){const t=e.source,r=this._boundTextures,i=this._gl;for(let n=0;n<r.length;n++)if(r[n]===t){this._activateLocation(n);const o=this.getGlSource(t);i.bindTexture(o.target,null),r[n]=null}}_activateLocation(e){this._activeTextureLocation!==e&&(this._activeTextureLocation=e,this._gl.activeTexture(this._gl.TEXTURE0+e))}_initSource(e){const t=this._gl,r=new Ip(t.createTexture());if(r.type=this._mapFormatToType[e.format],r.internalFormat=this._mapFormatToInternalFormat[e.format],r.format=this._mapFormatToFormat[e.format],e.autoGenerateMipmaps&&(this._renderer.context.supports.nonPowOf2mipmaps||e.isPowerOfTwo)){const i=Math.max(e.width,e.height);e.mipLevelCount=Math.floor(Math.log2(i))+1}return this._glTextures[e.uid]=r,this.managedTextures.includes(e)||(e.on("update",this.onSourceUpdate,this),e.on("resize",this.onSourceUpdate,this),e.on("styleChange",this.onStyleChange,this),e.on("destroy",this.onSourceDestroy,this),e.on("unload",this.onSourceUnload,this),e.on("updateMipmaps",this.onUpdateMipmaps,this),this.managedTextures.push(e)),this.onSourceUpdate(e),this.updateStyle(e,!1),r}onStyleChange(e){this.updateStyle(e,!1)}updateStyle(e,t){const r=this._gl,i=this.getGlSource(e);r.bindTexture(r.TEXTURE_2D,i.texture),this._boundTextures[this._activeTextureLocation]=e,mo(e.style,r,e.mipLevelCount>1,this._renderer.context.extensions.anisotropicFiltering,"texParameteri",r.TEXTURE_2D,!this._renderer.context.supports.nonPowOf2wrapping&&!e.isPowerOfTwo,t)}onSourceUnload(e){const t=this._glTextures[e.uid];t&&(this.unbind(e),this._glTextures[e.uid]=null,this._gl.deleteTexture(t.texture))}onSourceUpdate(e){const t=this._gl,r=this.getGlSource(e);t.bindTexture(t.TEXTURE_2D,r.texture),this._boundTextures[this._activeTextureLocation]=e;const i=e.alphaMode==="premultiply-alpha-on-upload";this._premultiplyAlpha!==i&&(this._premultiplyAlpha=i,t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,i)),this._uploads[e.uploadMethodId]?this._uploads[e.uploadMethodId].upload(e,r,t,this._renderer.context.webGLVersion):t.texImage2D(t.TEXTURE_2D,0,t.RGBA,e.pixelWidth,e.pixelHeight,0,t.RGBA,t.UNSIGNED_BYTE,null),e.autoGenerateMipmaps&&e.mipLevelCount>1&&this.onUpdateMipmaps(e,!1)}onUpdateMipmaps(e,t=!0){t&&this.bindSource(e,0);const r=this.getGlSource(e);this._gl.generateMipmap(r.target)}onSourceDestroy(e){e.off("destroy",this.onSourceDestroy,this),e.off("update",this.onSourceUpdate,this),e.off("resize",this.onSourceUpdate,this),e.off("unload",this.onSourceUnload,this),e.off("styleChange",this.onStyleChange,this),e.off("updateMipmaps",this.onUpdateMipmaps,this),this.managedTextures.splice(this.managedTextures.indexOf(e),1),this.onSourceUnload(e)}_initSampler(e){const t=this._gl,r=this._gl.createSampler();return this._glSamplers[e._resourceId]=r,mo(e,t,this._boundTextures[this._activeTextureLocation].mipLevelCount>1,this._renderer.context.extensions.anisotropicFiltering,"samplerParameteri",r,!1,!0),this._glSamplers[e._resourceId]}_getGlSampler(e){return this._glSamplers[e._resourceId]||this._initSampler(e)}getGlSource(e){return this._glTextures[e.uid]||this._initSource(e)}generateCanvas(e){const{pixels:t,width:r,height:i}=this.getPixels(e),n=$.get().createCanvas();n.width=r,n.height=i;const o=n.getContext("2d");if(o){const a=o.createImageData(r,i);a.data.set(t),o.putImageData(a,0,0)}return n}getPixels(e){const t=e.source.resolution,r=e.frame,i=Math.max(Math.round(r.width*t),1),n=Math.max(Math.round(r.height*t),1),o=new Uint8Array(Vp*i*n),a=this._renderer,h=a.renderTarget.getRenderTarget(e),l=a.renderTarget.getGpuRenderTarget(h),c=a.gl;return c.bindFramebuffer(c.FRAMEBUFFER,l.resolveTargetFramebuffer),c.readPixels(Math.round(r.x*t),Math.round(r.y*t),i,n,c.RGBA,c.UNSIGNED_BYTE,o),{pixels:new Uint8ClampedArray(o.buffer),width:i,height:n}}destroy(){this.managedTextures.slice().forEach(e=>this.onSourceDestroy(e)),this.managedTextures=null,this._renderer=null}resetState(){this._activeTextureLocation=-1,this._boundTextures.fill(A.EMPTY.source),this._boundSamplers=Object.create(null);const e=this._gl;this._premultiplyAlpha=!1,e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this._premultiplyAlpha)}}Rh.extension={type:[x.WebGLSystem],name:"texture"};class kh{contextChange(e){const t=new ne({uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uTransformMatrix:{value:new B,type:"mat3x3<f32>"},uRound:{value:0,type:"f32"}}),r=e.limits.maxBatchableTextures,i=jt({name:"graphics",bits:[ui,fi(r),wi,Kt]});this.shader=new pe({glProgram:i,resources:{localUniforms:t,batchSamplers:pi(r)}})}execute(e,t){const r=t.context,i=r.customShader||this.shader,n=e.renderer,o=n.graphicsContext,{batcher:a,instructions:h}=o.getContextRenderData(r);i.groups[0]=n.globalUniforms.bindGroup,n.state.set(e.state),n.shader.bind(i),n.geometry.bind(a.geometry,i.glProgram);const l=h.instructions;for(let c=0;c<h.instructionSize;c++){const u=l[c];if(u.size){for(let f=0;f<u.textures.count;f++)n.texture.bind(u.textures.textures[f],f);n.geometry.draw(u.topology,u.size,u.start)}}}destroy(){this.shader.destroy(!0),this.shader=null}}kh.extension={type:[x.WebGLPipesAdaptor],name:"graphics"};class Gh{init(){const e=jt({name:"mesh",bits:[wi,Kf,Kt]});this._shader=new pe({glProgram:e,resources:{uTexture:A.EMPTY.source,textureUniforms:{uTextureMatrix:{type:"mat3x3<f32>",value:new B}}}})}execute(e,t){const r=e.renderer;let i=t._shader;if(i){if(!i.glProgram){F("Mesh shader has no glProgram",t.shader);return}}else{i=this._shader;const n=t.texture,o=n.source;i.resources.uTexture=o,i.resources.uSampler=o.style,i.resources.textureUniforms.uniforms.uTextureMatrix=n.textureMatrix.mapCoord}i.groups[100]=r.globalUniforms.bindGroup,i.groups[101]=e.localUniformsBindGroup,r.encoder.draw({geometry:t._geometry,shader:i,state:t.state})}destroy(){this._shader.destroy(!0),this._shader=null}}Gh.extension={type:[x.WebGLPipesAdaptor],name:"mesh"};class Ih{constructor(e){this._renderer=e}updateRenderable(){}destroyRenderable(){}validateRenderable(){return!1}addRenderable(e,t){this._renderer.renderPipes.batch.break(t),t.add(e)}execute(e){e.isRenderable&&e.render(this._renderer)}destroy(){this._renderer=null}}Ih.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"customRender"};class Or{constructor(){this.batcherName="default",this.topology="triangle-list",this.attributeSize=4,this.indexSize=6,this.packAsQuad=!0,this.roundPixels=0,this._attributeStart=0,this._batcher=null,this._batch=null}get blendMode(){return this.renderable.groupBlendMode}get color(){return this.renderable.groupColorAlpha}reset(){this.renderable=null,this.texture=null,this._batcher=null,this._batch=null,this.bounds=null}destroy(){}}function qs(s,e){const t=s.instructionSet,r=t.instructions;for(let i=0;i<t.instructionSize;i++){const n=r[i];e[n.renderPipeId].execute(n)}}const $p=new B;class Fh{constructor(e){this._renderer=e}addRenderGroup(e,t){e.isCachedAsTexture?this._addRenderableCacheAsTexture(e,t):this._addRenderableDirect(e,t)}execute(e){e.isRenderable&&(e.isCachedAsTexture?this._executeCacheAsTexture(e):this._executeDirect(e))}destroy(){this._renderer=null}_addRenderableDirect(e,t){this._renderer.renderPipes.batch.break(t),e._batchableRenderGroup&&(ee.return(e._batchableRenderGroup),e._batchableRenderGroup=null),t.add(e)}_addRenderableCacheAsTexture(e,t){const r=e._batchableRenderGroup??(e._batchableRenderGroup=ee.get(Or));r.renderable=e.root,r.transform=e.root.relativeGroupTransform,r.texture=e.texture,r.bounds=e._textureBounds,t.add(e),this._renderer.renderPipes.batch.addToBatch(r,t)}_executeCacheAsTexture(e){if(e.textureNeedsUpdate){e.textureNeedsUpdate=!1;const t=$p.identity().translate(-e._textureBounds.x,-e._textureBounds.y);this._renderer.renderTarget.push(e.texture,!0,null,e.texture.frame),this._renderer.globalUniforms.push({worldTransformMatrix:t,worldColor:4294967295}),qs(e,this._renderer.renderPipes),this._renderer.renderTarget.finishRenderPass(),this._renderer.renderTarget.pop(),this._renderer.globalUniforms.pop()}e._batchableRenderGroup._batcher.updateElement(e._batchableRenderGroup),e._batchableRenderGroup._batcher.geometry.buffers[0].update()}_executeDirect(e){this._renderer.globalUniforms.push({worldTransformMatrix:e.inverseParentTextureTransform,worldColor:e.worldColorAlpha}),qs(e,this._renderer.renderPipes),this._renderer.globalUniforms.pop()}}Fh.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"renderGroup"};function Ks(s,e){e||(e=0);for(let t=e;t<s.length&&s[t];t++)s[t]=null}const Xp=new xe,go=Rt|Er|si;function Uh(s,e=!1){Yp(s);const t=s.childrenToUpdate,r=s.updateTick++;for(const i in t){const n=Number(i),o=t[i],a=o.list,h=o.index;for(let l=0;l<h;l++){const c=a[l];c.parentRenderGroup===s&&c.relativeRenderGroupDepth===n&&Dh(c,r,0)}Ks(a,h),o.index=0}if(e)for(let i=0;i<s.renderGroupChildren.length;i++)Uh(s.renderGroupChildren[i],e)}function Yp(s){const e=s.root;let t;if(s.renderGroupParent){const r=s.renderGroupParent;s.worldTransform.appendFrom(e.relativeGroupTransform,r.worldTransform),s.worldColor=Cr(e.groupColor,r.worldColor),t=e.groupAlpha*r.worldAlpha}else s.worldTransform.copyFrom(e.localTransform),s.worldColor=e.localColor,t=e.localAlpha;t=t<0?0:t>1?1:t,s.worldAlpha=t,s.worldColorAlpha=s.worldColor+((t*255|0)<<24)}function Dh(s,e,t){if(e===s.updateTick)return;s.updateTick=e,s.didChange=!1;const r=s.localTransform;s.updateLocalTransform();const i=s.parent;if(i&&!i.renderGroup?(t|=s._updateFlags,s.relativeGroupTransform.appendFrom(r,i.relativeGroupTransform),t&go&&_o(s,i,t)):(t=s._updateFlags,s.relativeGroupTransform.copyFrom(r),t&go&&_o(s,Xp,t)),!s.renderGroup){const n=s.children,o=n.length;for(let l=0;l<o;l++)Dh(n[l],e,t);const a=s.parentRenderGroup,h=s;h.renderPipeId&&!a.structureDidChange&&a.updateRenderable(h)}}function _o(s,e,t){if(t&Er){s.groupColor=Cr(s.localColor,e.groupColor);let r=s.localAlpha*e.groupAlpha;r=r<0?0:r>1?1:r,s.groupAlpha=r,s.groupColorAlpha=s.groupColor+((r*255|0)<<24)}t&si&&(s.groupBlendMode=s.localBlendMode==="inherit"?e.groupBlendMode:s.localBlendMode),t&Rt&&(s.globalDisplayStatus=s.localDisplayStatus&e.globalDisplayStatus),s._updateFlags=0}function jp(s,e){const{list:t,index:r}=s.childrenRenderablesToUpdate;let i=!1;for(let n=0;n<r;n++){const o=t[n];if(i=e[o.renderPipeId].validateRenderable(o),i)break}return s.structureDidChange=i,i}const qp=new B;class Oh{constructor(e){this._renderer=e}render({container:e,transform:t}){const r=e.parent,i=e.renderGroup.renderGroupParent;e.parent=null,e.renderGroup.renderGroupParent=null;const n=this._renderer;let o=qp;t&&(o=o.copyFrom(e.renderGroup.localTransform),e.renderGroup.localTransform.copyFrom(t));const a=n.renderPipes;this._updateCachedRenderGroups(e.renderGroup,null),this._updateRenderGroups(e.renderGroup),n.globalUniforms.start({worldTransformMatrix:t?e.renderGroup.localTransform:e.renderGroup.worldTransform,worldColor:e.renderGroup.worldColorAlpha}),qs(e.renderGroup,a),a.uniformBatch&&a.uniformBatch.renderEnd(),t&&e.renderGroup.localTransform.copyFrom(o),e.parent=r,e.renderGroup.renderGroupParent=i}destroy(){this._renderer=null}_updateCachedRenderGroups(e,t){if(e.isCachedAsTexture){if(!e.updateCacheTexture)return;t=e}e._parentCacheAsTextureRenderGroup=t;for(let r=e.renderGroupChildren.length-1;r>=0;r--)this._updateCachedRenderGroups(e.renderGroupChildren[r],t);if(e.invalidateMatrices(),e.isCachedAsTexture){if(e.textureNeedsUpdate){const r=e.root.getLocalBounds();r.ceil();const i=e.texture;e.texture&&te.returnTexture(e.texture);const n=this._renderer,o=e.textureOptions.resolution||n.view.resolution,a=e.textureOptions.antialias??n.view.antialias;e.texture=te.getOptimalTexture(r.width,r.height,o,a),e._textureBounds||(e._textureBounds=new se),e._textureBounds.copyFrom(r),i!==e.texture&&e.renderGroupParent&&(e.renderGroupParent.structureDidChange=!0)}}else e.texture&&(te.returnTexture(e.texture),e.texture=null)}_updateRenderGroups(e){const t=this._renderer,r=t.renderPipes;if(e.runOnRender(t),e.instructionSet.renderPipes=r,e.structureDidChange?Ks(e.childrenRenderablesToUpdate.list,0):jp(e,r),Uh(e),e.structureDidChange?(e.structureDidChange=!1,this._buildInstructions(e,t)):this._updateRenderables(e),e.childrenRenderablesToUpdate.index=0,t.renderPipes.batch.upload(e.instructionSet),!(e.isCachedAsTexture&&!e.textureNeedsUpdate))for(let i=0;i<e.renderGroupChildren.length;i++)this._updateRenderGroups(e.renderGroupChildren[i])}_updateRenderables(e){const{list:t,index:r}=e.childrenRenderablesToUpdate;for(let i=0;i<r;i++){const n=t[i];n.didViewUpdate&&e.updateRenderable(n)}Ks(t,r)}_buildInstructions(e,t){const r=e.root,i=e.instructionSet;i.reset();const n=t.renderPipes?t:t.batch.renderer,o=n.renderPipes;o.batch.buildStart(i),o.blendMode.buildStart(),o.colorMask.buildStart(),r.sortableChildren&&r.sortChildren(),r.collectRenderablesWithEffects(i,n,null),o.batch.buildEnd(i),o.blendMode.buildEnd(i)}}Oh.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"renderGroup"};class Lh{constructor(e){this._renderer=e}addRenderable(e,t){const r=this._getGpuSprite(e);e.didViewUpdate&&this._updateBatchableSprite(e,r),this._renderer.renderPipes.batch.addToBatch(r,t)}updateRenderable(e){const t=this._getGpuSprite(e);e.didViewUpdate&&this._updateBatchableSprite(e,t),t._batcher.updateElement(t)}validateRenderable(e){const t=this._getGpuSprite(e);return!t._batcher.checkAndUpdateTexture(t,e._texture)}_updateBatchableSprite(e,t){t.bounds=e.visualBounds,t.texture=e._texture}_getGpuSprite(e){return e._gpuData[this._renderer.uid]||this._initGPUSprite(e)}_initGPUSprite(e){const t=new Or;return t.renderable=e,t.transform=e.groupTransform,t.texture=e._texture,t.bounds=e.visualBounds,t.roundPixels=this._renderer._roundPixels|e._roundPixels,e._gpuData[this._renderer.uid]=t,t}destroy(){this._renderer=null}}Lh.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"sprite"};const Mi=class Nh{constructor(){this.clearBeforeRender=!0,this._backgroundColor=new L(0),this.color=this._backgroundColor,this.alpha=1}init(e){e={...Nh.defaultOptions,...e},this.clearBeforeRender=e.clearBeforeRender,this.color=e.background||e.backgroundColor||this._backgroundColor,this.alpha=e.backgroundAlpha,this._backgroundColor.setAlpha(e.backgroundAlpha)}get color(){return this._backgroundColor}set color(e){this._backgroundColor.setValue(e)}get alpha(){return this._backgroundColor.alpha}set alpha(e){this._backgroundColor.setAlpha(e)}get colorRgba(){return this._backgroundColor.toArray()}destroy(){}};Mi.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"background",priority:0};Mi.defaultOptions={backgroundAlpha:1,backgroundColor:0,clearBeforeRender:!0};let Kp=Mi;const Ft={};q.handle(x.BlendMode,s=>{if(!s.name)throw new Error("BlendMode extension must have a name property");Ft[s.name]=s.ref},s=>{delete Ft[s.name]});class Hh{constructor(e){this._isAdvanced=!1,this._filterHash=Object.create(null),this._renderer=e,this._renderer.runners.prerender.add(this)}prerender(){this._activeBlendMode="normal",this._isAdvanced=!1}setBlendMode(e,t,r){if(this._activeBlendMode===t){this._isAdvanced&&this._renderableList.push(e);return}this._activeBlendMode=t,this._isAdvanced&&this._endAdvancedBlendMode(r),this._isAdvanced=!!Ft[t],this._isAdvanced&&(this._beginAdvancedBlendMode(r),this._renderableList.push(e))}_beginAdvancedBlendMode(e){this._renderer.renderPipes.batch.break(e);const t=this._activeBlendMode;if(!Ft[t]){F(`Unable to assign BlendMode: '${t}'. You may want to include: import 'pixi.js/advanced-blend-modes'`);return}let r=this._filterHash[t];r||(r=this._filterHash[t]=new Pr,r.filters=[new Ft[t]]);const i={renderPipeId:"filter",action:"pushFilter",renderables:[],filterEffect:r,canBundle:!1};this._renderableList=i.renderables,e.add(i)}_endAdvancedBlendMode(e){this._renderableList=null,this._renderer.renderPipes.batch.break(e),e.add({renderPipeId:"filter",action:"popFilter",canBundle:!1})}buildStart(){this._isAdvanced=!1}buildEnd(e){this._isAdvanced&&this._endAdvancedBlendMode(e)}destroy(){this._renderer=null,this._renderableList=null;for(const e in this._filterHash)this._filterHash[e].destroy();this._filterHash=null}}Hh.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"blendMode"};const vs={png:"image/png",jpg:"image/jpeg",webp:"image/webp"},Ai=class zh{constructor(e){this._renderer=e}_normalizeOptions(e,t={}){return e instanceof xe||e instanceof A?{target:e,...t}:{...t,...e}}async image(e){const t=new Image;return t.src=await this.base64(e),t}async base64(e){e=this._normalizeOptions(e,zh.defaultImageOptions);const{format:t,quality:r}=e,i=this.canvas(e);if(i.toBlob!==void 0)return new Promise((n,o)=>{i.toBlob(a=>{if(!a){o(new Error("ICanvas.toBlob failed!"));return}const h=new FileReader;h.onload=()=>n(h.result),h.onerror=o,h.readAsDataURL(a)},vs[t],r)});if(i.toDataURL!==void 0)return i.toDataURL(vs[t],r);if(i.convertToBlob!==void 0){const n=await i.convertToBlob({type:vs[t],quality:r});return new Promise((o,a)=>{const h=new FileReader;h.onload=()=>o(h.result),h.onerror=a,h.readAsDataURL(n)})}throw new Error("Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, or ICanvas.convertToBlob to be implemented")}canvas(e){e=this._normalizeOptions(e);const t=e.target,r=this._renderer;if(t instanceof A)return r.texture.generateCanvas(t);const i=r.textureGenerator.generateTexture(e),n=r.texture.generateCanvas(i);return i.destroy(!0),n}pixels(e){e=this._normalizeOptions(e);const t=e.target,r=this._renderer,i=t instanceof A?t:r.textureGenerator.generateTexture(e),n=r.texture.getPixels(i);return t instanceof xe&&i.destroy(!0),n}texture(e){return e=this._normalizeOptions(e),e.target instanceof A?e.target:this._renderer.textureGenerator.generateTexture(e)}download(e){e=this._normalizeOptions(e);const t=this.canvas(e),r=document.createElement("a");r.download=e.filename??"image.png",r.href=t.toDataURL("image/png"),document.body.appendChild(r),r.click(),document.body.removeChild(r)}log(e){const t=e.width??200;e=this._normalizeOptions(e);const r=this.canvas(e),i=r.toDataURL();console.log(`[Pixi Texture] ${r.width}px ${r.height}px`);const n=["font-size: 1px;",`padding: ${t}px 300px;`,`background: url(${i}) no-repeat;`,"background-size: contain;"].join(" ");console.log("%c ",n)}destroy(){this._renderer=null}};Ai.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"extract"};Ai.defaultImageOptions={format:"png",quality:1};let Zp=Ai;class Bi extends A{static create(e){return new Bi({source:new Q(e)})}resize(e,t,r){return this.source.resize(e,t,r),this}}const Qp=new N,Jp=new se,em=[0,0,0,0];class Wh{constructor(e){this._renderer=e}generateTexture(e){e instanceof xe&&(e={target:e,frame:void 0,textureSourceOptions:{},resolution:void 0});const t=e.resolution||this._renderer.resolution,r=e.antialias||this._renderer.view.antialias,i=e.target;let n=e.clearColor;n?n=Array.isArray(n)&&n.length===4?n:L.shared.setValue(n).toArray():n=em;const o=e.frame?.copyTo(Qp)||ri(i,Jp).rectangle;o.width=Math.max(o.width,1/t)|0,o.height=Math.max(o.height,1/t)|0;const a=Bi.create({...e.textureSourceOptions,width:o.width,height:o.height,resolution:t,antialias:r}),h=B.shared.translate(-o.x,-o.y);return this._renderer.render({container:i,transform:h,target:a,clearColor:n}),a.source.updateMipmaps(),a}destroy(){this._renderer=null}}Wh.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"textureGenerator"};function Zt(s,e,t){const r=(s>>24&255)/255;e[t++]=(s&255)/255*r,e[t++]=(s>>8&255)/255*r,e[t++]=(s>>16&255)/255*r,e[t++]=r}class Vh{constructor(e){this._stackIndex=0,this._globalUniformDataStack=[],this._uniformsPool=[],this._activeUniforms=[],this._bindGroupPool=[],this._activeBindGroups=[],this._renderer=e}reset(){this._stackIndex=0;for(let e=0;e<this._activeUniforms.length;e++)this._uniformsPool.push(this._activeUniforms[e]);for(let e=0;e<this._activeBindGroups.length;e++)this._bindGroupPool.push(this._activeBindGroups[e]);this._activeUniforms.length=0,this._activeBindGroups.length=0}start(e){this.reset(),this.push(e)}bind({size:e,projectionMatrix:t,worldTransformMatrix:r,worldColor:i,offset:n}){const o=this._renderer.renderTarget.renderTarget,a=this._stackIndex?this._globalUniformDataStack[this._stackIndex-1]:{worldTransformMatrix:new B,worldColor:4294967295,offset:new H},h={projectionMatrix:t||this._renderer.renderTarget.projectionMatrix,resolution:e||o.size,worldTransformMatrix:r||a.worldTransformMatrix,worldColor:i||a.worldColor,offset:n||a.offset,bindGroup:null},l=this._uniformsPool.pop()||this._createUniforms();this._activeUniforms.push(l);const c=l.uniforms;c.uProjectionMatrix=h.projectionMatrix,c.uResolution=h.resolution,c.uWorldTransformMatrix.copyFrom(h.worldTransformMatrix),c.uWorldTransformMatrix.tx-=h.offset.x,c.uWorldTransformMatrix.ty-=h.offset.y,Zt(h.worldColor,c.uWorldColorAlpha,0),l.update();let u;this._renderer.renderPipes.uniformBatch?u=this._renderer.renderPipes.uniformBatch.getUniformBindGroup(l,!1):(u=this._bindGroupPool.pop()||new Re,this._activeBindGroups.push(u),u.setResource(l,0)),h.bindGroup=u,this._currentGlobalUniformData=h}push(e){this.bind(e),this._globalUniformDataStack[this._stackIndex++]=this._currentGlobalUniformData}pop(){this._currentGlobalUniformData=this._globalUniformDataStack[--this._stackIndex-1],this._renderer.type===ye.WEBGL&&this._currentGlobalUniformData.bindGroup.resources[0].update()}get bindGroup(){return this._currentGlobalUniformData.bindGroup}get globalUniformData(){return this._currentGlobalUniformData}get uniformGroup(){return this._currentGlobalUniformData.bindGroup.resources[0]}_createUniforms(){return new ne({uProjectionMatrix:{value:new B,type:"mat3x3<f32>"},uWorldTransformMatrix:{value:new B,type:"mat3x3<f32>"},uWorldColorAlpha:{value:new Float32Array(4),type:"vec4<f32>"},uResolution:{value:[0,0],type:"vec2<f32>"}},{isStatic:!0})}destroy(){this._renderer=null}}Vh.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"globalUniforms"};let tm=1;class $h{constructor(){this._tasks=[],this._offset=0}init(){we.system.add(this._update,this)}repeat(e,t,r=!0){const i=tm++;let n=0;return r&&(this._offset+=1e3,n=this._offset),this._tasks.push({func:e,duration:t,start:performance.now(),offset:n,last:performance.now(),repeat:!0,id:i}),i}cancel(e){for(let t=0;t<this._tasks.length;t++)if(this._tasks[t].id===e){this._tasks.splice(t,1);return}}_update(){const e=performance.now();for(let t=0;t<this._tasks.length;t++){const r=this._tasks[t];if(e-r.offset-r.last>=r.duration){const i=e-r.start;r.func(i),r.last=e}}}destroy(){we.system.remove(this._update,this),this._tasks.length=0}}$h.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"scheduler",priority:0};let xo=!1;function rm(s){if(!xo){if($.get().getNavigator().userAgent.toLowerCase().indexOf("chrome")>-1){const e=[`%c  %c  %c  %c  %c PixiJS %c v${Mr} (${s}) http://www.pixijs.com/

`,"background: #E72264; padding:5px 0;","background: #6CA2EA; padding:5px 0;","background: #B5D33D; padding:5px 0;","background: #FED23F; padding:5px 0;","color: #FFFFFF; background: #E72264; padding:5px 0;","color: #E72264; background: #FFFFFF; padding:5px 0;"];globalThis.console.log(...e)}else globalThis.console&&globalThis.console.log(`PixiJS ${Mr} - ${s} - http://www.pixijs.com/`);xo=!0}}class Ri{constructor(e){this._renderer=e}init(e){if(e.hello){let t=this._renderer.name;this._renderer.type===ye.WEBGL&&(t+=` ${this._renderer.context.webGLVersion}`),rm(t)}}}Ri.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"hello",priority:-2};Ri.defaultOptions={hello:!1};function sm(s){let e=!1;for(const r in s)if(s[r]==null){e=!0;break}if(!e)return s;const t=Object.create(null);for(const r in s){const i=s[r];i&&(t[r]=i)}return t}function im(s){let e=0;for(let t=0;t<s.length;t++)s[t]==null?e++:s[t-e]=s[t];return s.length-=e,s}let nm=0;const ki=class Xh{constructor(e){this._managedRenderables=[],this._managedHashes=[],this._managedArrays=[],this._renderer=e}init(e){e={...Xh.defaultOptions,...e},this.maxUnusedTime=e.renderableGCMaxUnusedTime,this._frequency=e.renderableGCFrequency,this.enabled=e.renderableGCActive}get enabled(){return!!this._handler}set enabled(e){this.enabled!==e&&(e?(this._handler=this._renderer.scheduler.repeat(()=>this.run(),this._frequency,!1),this._hashHandler=this._renderer.scheduler.repeat(()=>{for(const t of this._managedHashes)t.context[t.hash]=sm(t.context[t.hash])},this._frequency),this._arrayHandler=this._renderer.scheduler.repeat(()=>{for(const t of this._managedArrays)im(t.context[t.hash])},this._frequency)):(this._renderer.scheduler.cancel(this._handler),this._renderer.scheduler.cancel(this._hashHandler),this._renderer.scheduler.cancel(this._arrayHandler)))}addManagedHash(e,t){this._managedHashes.push({context:e,hash:t})}addManagedArray(e,t){this._managedArrays.push({context:e,hash:t})}prerender({container:e}){this._now=performance.now(),e.renderGroup.gcTick=nm++,this._updateInstructionGCTick(e.renderGroup,e.renderGroup.gcTick)}addRenderable(e){this.enabled&&(e._lastUsed===-1&&(this._managedRenderables.push(e),e.once("destroyed",this._removeRenderable,this)),e._lastUsed=this._now)}run(){const e=this._now,t=this._managedRenderables,r=this._renderer.renderPipes;let i=0;for(let n=0;n<t.length;n++){const o=t[n];if(o===null){i++;continue}const a=o.renderGroup??o.parentRenderGroup,h=a?.instructionSet?.gcTick??-1;if((a?.gcTick??0)===h&&(o._lastUsed=e),e-o._lastUsed>this.maxUnusedTime){if(!o.destroyed){const l=r;a&&(a.structureDidChange=!0),l[o.renderPipeId].destroyRenderable(o)}o._lastUsed=-1,i++,o.off("destroyed",this._removeRenderable,this)}else t[n-i]=o}t.length-=i}destroy(){this.enabled=!1,this._renderer=null,this._managedRenderables.length=0,this._managedHashes.length=0,this._managedArrays.length=0}_removeRenderable(e){const t=this._managedRenderables.indexOf(e);t>=0&&(e.off("destroyed",this._removeRenderable,this),this._managedRenderables[t]=null)}_updateInstructionGCTick(e,t){e.instructionSet.gcTick=t;for(const r of e.renderGroupChildren)this._updateInstructionGCTick(r,t)}};ki.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"renderableGC",priority:0};ki.defaultOptions={renderableGCActive:!0,renderableGCMaxUnusedTime:6e4,renderableGCFrequency:3e4};let om=ki;const Gi=class Yh{constructor(e){this._renderer=e,this.count=0,this.checkCount=0}init(e){e={...Yh.defaultOptions,...e},this.checkCountMax=e.textureGCCheckCountMax,this.maxIdle=e.textureGCAMaxIdle??e.textureGCMaxIdle,this.active=e.textureGCActive}postrender(){this._renderer.renderingToScreen&&(this.count++,this.active&&(this.checkCount++,this.checkCount>this.checkCountMax&&(this.checkCount=0,this.run())))}run(){const e=this._renderer.texture.managedTextures;for(let t=0;t<e.length;t++){const r=e[t];r.autoGarbageCollect&&r.resource&&r._touched>-1&&this.count-r._touched>this.maxIdle&&(r._touched=-1,r.unload())}}destroy(){this._renderer=null}};Gi.extension={type:[x.WebGLSystem,x.WebGPUSystem],name:"textureGC"};Gi.defaultOptions={textureGCActive:!0,textureGCAMaxIdle:null,textureGCMaxIdle:60*60,textureGCCheckCountMax:600};let am=Gi;const Ii=class jh{get autoDensity(){return this.texture.source.autoDensity}set autoDensity(e){this.texture.source.autoDensity=e}get resolution(){return this.texture.source._resolution}set resolution(e){this.texture.source.resize(this.texture.source.width,this.texture.source.height,e)}init(e){e={...jh.defaultOptions,...e},e.view&&(I(O,"ViewSystem.view has been renamed to ViewSystem.canvas"),e.canvas=e.view),this.screen=new N(0,0,e.width,e.height),this.canvas=e.canvas||$.get().createCanvas(),this.antialias=!!e.antialias,this.texture=bh(this.canvas,e),this.renderTarget=new Ys({colorTextures:[this.texture],depth:!!e.depth,isRoot:!0}),this.texture.source.transparent=e.backgroundAlpha<1,this.resolution=e.resolution}resize(e,t,r){this.texture.source.resize(e,t,r),this.screen.width=this.texture.frame.width,this.screen.height=this.texture.frame.height}destroy(e=!1){(typeof e=="boolean"?e:!!e?.removeView)&&this.canvas.parentNode&&this.canvas.parentNode.removeChild(this.canvas)}};Ii.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"view",priority:0};Ii.defaultOptions={width:800,height:600,autoDensity:!1,antialias:!1};let hm=Ii;const qh=[Kp,Vh,Ri,hm,Oh,am,Wh,Zp,Sa,om,$h],Kh=[Hh,th,Lh,Fh,rh,ih,sh,Ih],lm=[...qh,yh,np,ep,fh,nh,Rh,wh,lh,Mh,Eh,dh,Gp,ph,uh],cm=[...Kh],um=[Qa,Gh,kh],Zh=[],Qh=[],Jh=[];q.handleByNamedList(x.WebGLSystem,Zh);q.handleByNamedList(x.WebGLPipes,Qh);q.handleByNamedList(x.WebGLPipesAdaptor,Jh);q.add(...lm,...cm,...um);class dm extends Fr{constructor(){const e={name:"webgl",type:ye.WEBGL,systems:Zh,renderPipes:Qh,renderPipeAdaptors:Jh};super(e)}}const fm=Object.freeze(Object.defineProperty({__proto__:null,WebGLRenderer:dm},Symbol.toStringTag,{value:"Module"}));class el{constructor(e){this._hash=Object.create(null),this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_hash")}contextChange(e){this._gpu=e}getBindGroup(e,t,r){return e._updateKey(),this._hash[e._key]||this._createBindGroup(e,t,r)}_createBindGroup(e,t,r){const i=this._gpu.device,n=t.layout[r],o=[],a=this._renderer;for(const c in n){const u=e.resources[c]??e.resources[n[c]];let f;if(u._resourceType==="uniformGroup"){const d=u;a.ubo.updateUniformGroup(d);const p=d.buffer;f={buffer:a.buffer.getGPUBuffer(p),offset:0,size:p.descriptor.size}}else if(u._resourceType==="buffer"){const d=u;f={buffer:a.buffer.getGPUBuffer(d),offset:0,size:d.descriptor.size}}else if(u._resourceType==="bufferResource"){const d=u;f={buffer:a.buffer.getGPUBuffer(d.buffer),offset:d.offset,size:d.size}}else if(u._resourceType==="textureSampler"){const d=u;f=a.texture.getGpuSampler(d)}else if(u._resourceType==="textureSource"){const d=u;f=a.texture.getGpuSource(d).createView({})}o.push({binding:n[c],resource:f})}const h=a.shader.getProgramData(t).bindGroups[r],l=i.createBindGroup({layout:h,entries:o});return this._hash[e._key]=l,l}destroy(){for(const e of Object.keys(this._hash))this._hash[e]=null;this._hash=null,this._renderer=null}}el.extension={type:[x.WebGPUSystem],name:"bindGroup"};class tl{constructor(e){this._gpuBuffers=Object.create(null),this._managedBuffers=[],e.renderableGC.addManagedHash(this,"_gpuBuffers")}contextChange(e){this._gpu=e}getGPUBuffer(e){return this._gpuBuffers[e.uid]||this.createGPUBuffer(e)}updateBuffer(e){const t=this._gpuBuffers[e.uid]||this.createGPUBuffer(e),r=e.data;return e._updateID&&r&&(e._updateID=0,this._gpu.device.queue.writeBuffer(t,0,r.buffer,0,(e._updateSize||r.byteLength)+3&-4)),t}destroyAll(){for(const e in this._gpuBuffers)this._gpuBuffers[e].destroy();this._gpuBuffers={}}createGPUBuffer(e){this._gpuBuffers[e.uid]||(e.on("update",this.updateBuffer,this),e.on("change",this.onBufferChange,this),e.on("destroy",this.onBufferDestroy,this),this._managedBuffers.push(e));const t=this._gpu.device.createBuffer(e.descriptor);return e._updateID=0,e.data&&(Ns(e.data.buffer,t.getMappedRange()),t.unmap()),this._gpuBuffers[e.uid]=t,t}onBufferChange(e){this._gpuBuffers[e.uid].destroy(),e._updateID=0,this._gpuBuffers[e.uid]=this.createGPUBuffer(e)}onBufferDestroy(e){this._managedBuffers.splice(this._managedBuffers.indexOf(e),1),this._destroyBuffer(e)}destroy(){this._managedBuffers.forEach(e=>this._destroyBuffer(e)),this._managedBuffers=null,this._gpuBuffers=null}_destroyBuffer(e){this._gpuBuffers[e.uid].destroy(),e.off("update",this.updateBuffer,this),e.off("change",this.onBufferChange,this),e.off("destroy",this.onBufferDestroy,this),this._gpuBuffers[e.uid]=null}}tl.extension={type:[x.WebGPUSystem],name:"buffer"};class pm{constructor({minUniformOffsetAlignment:e}){this._minUniformOffsetAlignment=256,this.byteIndex=0,this._minUniformOffsetAlignment=e,this.data=new Float32Array(65535)}clear(){this.byteIndex=0}addEmptyGroup(e){if(e>this._minUniformOffsetAlignment/4)throw new Error(`UniformBufferBatch: array is too large: ${e*4}`);const t=this.byteIndex;let r=t+e*4;if(r=Math.ceil(r/this._minUniformOffsetAlignment)*this._minUniformOffsetAlignment,r>this.data.length*4)throw new Error("UniformBufferBatch: ubo batch got too big");return this.byteIndex=r,t}addGroup(e){const t=this.addEmptyGroup(e.length);for(let r=0;r<e.length;r++)this.data[t/4+r]=e[r];return t}destroy(){this.data=null}}class rl{constructor(e){this._colorMaskCache=15,this._renderer=e}setMask(e){this._colorMaskCache!==e&&(this._colorMaskCache=e,this._renderer.pipeline.setColorMask(e))}destroy(){this._renderer=null,this._colorMaskCache=null}}rl.extension={type:[x.WebGPUSystem],name:"colorMask"};class Fi{constructor(e){this._renderer=e}async init(e){return this._initPromise?this._initPromise:(this._initPromise=this._createDeviceAndAdaptor(e).then(t=>{this.gpu=t,this._renderer.runners.contextChange.emit(this.gpu)}),this._initPromise)}contextChange(e){this._renderer.gpu=e}async _createDeviceAndAdaptor(e){const t=await $.get().getNavigator().gpu.requestAdapter({powerPreference:e.powerPreference,forceFallbackAdapter:e.forceFallbackAdapter}),r=["texture-compression-bc","texture-compression-astc","texture-compression-etc2"].filter(n=>t.features.has(n)),i=await t.requestDevice({requiredFeatures:r});return{adapter:t,device:i}}destroy(){this.gpu=null,this._renderer=null}}Fi.extension={type:[x.WebGPUSystem],name:"device"};Fi.defaultOptions={powerPreference:void 0,forceFallbackAdapter:!1};class sl{constructor(e){this._boundBindGroup=Object.create(null),this._boundVertexBuffer=Object.create(null),this._renderer=e}renderStart(){this.commandFinished=new Promise(e=>{this._resolveCommandFinished=e}),this.commandEncoder=this._renderer.gpu.device.createCommandEncoder()}beginRenderPass(e){this.endRenderPass(),this._clearCache(),this.renderPassEncoder=this.commandEncoder.beginRenderPass(e.descriptor)}endRenderPass(){this.renderPassEncoder&&this.renderPassEncoder.end(),this.renderPassEncoder=null}setViewport(e){this.renderPassEncoder.setViewport(e.x,e.y,e.width,e.height,0,1)}setPipelineFromGeometryProgramAndState(e,t,r,i){const n=this._renderer.pipeline.getPipeline(e,t,r,i);this.setPipeline(n)}setPipeline(e){this._boundPipeline!==e&&(this._boundPipeline=e,this.renderPassEncoder.setPipeline(e))}_setVertexBuffer(e,t){this._boundVertexBuffer[e]!==t&&(this._boundVertexBuffer[e]=t,this.renderPassEncoder.setVertexBuffer(e,this._renderer.buffer.updateBuffer(t)))}_setIndexBuffer(e){if(this._boundIndexBuffer===e)return;this._boundIndexBuffer=e;const t=e.data.BYTES_PER_ELEMENT===2?"uint16":"uint32";this.renderPassEncoder.setIndexBuffer(this._renderer.buffer.updateBuffer(e),t)}resetBindGroup(e){this._boundBindGroup[e]=null}setBindGroup(e,t,r){if(this._boundBindGroup[e]===t)return;this._boundBindGroup[e]=t,t._touch(this._renderer.textureGC.count);const i=this._renderer.bindGroup.getBindGroup(t,r,e);this.renderPassEncoder.setBindGroup(e,i)}setGeometry(e,t){const r=this._renderer.pipeline.getBufferNamesToBind(e,t);for(const i in r)this._setVertexBuffer(i,e.attributes[r[i]].buffer);e.indexBuffer&&this._setIndexBuffer(e.indexBuffer)}_setShaderBindGroups(e,t){for(const r in e.groups){const i=e.groups[r];t||this._syncBindGroup(i),this.setBindGroup(r,i,e.gpuProgram)}}_syncBindGroup(e){for(const t in e.resources){const r=e.resources[t];r.isUniformGroup&&this._renderer.ubo.updateUniformGroup(r)}}draw(e){const{geometry:t,shader:r,state:i,topology:n,size:o,start:a,instanceCount:h,skipSync:l}=e;this.setPipelineFromGeometryProgramAndState(t,r.gpuProgram,i,n),this.setGeometry(t,r.gpuProgram),this._setShaderBindGroups(r,l),t.indexBuffer?this.renderPassEncoder.drawIndexed(o||t.indexBuffer.data.length,h??t.instanceCount,a||0):this.renderPassEncoder.draw(o||t.getSize(),h??t.instanceCount,a||0)}finishRenderPass(){this.renderPassEncoder&&(this.renderPassEncoder.end(),this.renderPassEncoder=null)}postrender(){this.finishRenderPass(),this._gpu.device.queue.submit([this.commandEncoder.finish()]),this._resolveCommandFinished(),this.commandEncoder=null}restoreRenderPass(){const e=this._renderer.renderTarget.adaptor.getDescriptor(this._renderer.renderTarget.renderTarget,!1,[0,0,0,1]);this.renderPassEncoder=this.commandEncoder.beginRenderPass(e);const t=this._boundPipeline,r={...this._boundVertexBuffer},i=this._boundIndexBuffer,n={...this._boundBindGroup};this._clearCache();const o=this._renderer.renderTarget.viewport;this.renderPassEncoder.setViewport(o.x,o.y,o.width,o.height,0,1),this.setPipeline(t);for(const a in r)this._setVertexBuffer(a,r[a]);for(const a in n)this.setBindGroup(a,n[a],null);this._setIndexBuffer(i)}_clearCache(){for(let e=0;e<16;e++)this._boundBindGroup[e]=null,this._boundVertexBuffer[e]=null;this._boundIndexBuffer=null,this._boundPipeline=null}destroy(){this._renderer=null,this._gpu=null,this._boundBindGroup=null,this._boundVertexBuffer=null,this._boundIndexBuffer=null,this._boundPipeline=null}contextChange(e){this._gpu=e}}sl.extension={type:[x.WebGPUSystem],name:"encoder",priority:1};class il{constructor(e){this._renderer=e}contextChange(){this.maxTextures=this._renderer.device.gpu.device.limits.maxSampledTexturesPerShaderStage,this.maxBatchableTextures=this.maxTextures}destroy(){}}il.extension={type:[x.WebGPUSystem],name:"limits"};class nl{constructor(e){this._renderTargetStencilState=Object.create(null),this._renderer=e,e.renderTarget.onRenderTargetChange.add(this)}onRenderTargetChange(e){let t=this._renderTargetStencilState[e.uid];t||(t=this._renderTargetStencilState[e.uid]={stencilMode:Z.DISABLED,stencilReference:0}),this._activeRenderTarget=e,this.setStencilMode(t.stencilMode,t.stencilReference)}setStencilMode(e,t){const r=this._renderTargetStencilState[this._activeRenderTarget.uid];r.stencilMode=e,r.stencilReference=t;const i=this._renderer;i.pipeline.setStencilMode(e),i.encoder.renderPassEncoder.setStencilReference(t)}destroy(){this._renderer.renderTarget.onRenderTargetChange.remove(this),this._renderer=null,this._activeRenderTarget=null,this._renderTargetStencilState=null}}nl.extension={type:[x.WebGPUSystem],name:"stencil"};const Tr={i32:{align:4,size:4},u32:{align:4,size:4},f32:{align:4,size:4},f16:{align:2,size:2},"vec2<i32>":{align:8,size:8},"vec2<u32>":{align:8,size:8},"vec2<f32>":{align:8,size:8},"vec2<f16>":{align:4,size:4},"vec3<i32>":{align:16,size:12},"vec3<u32>":{align:16,size:12},"vec3<f32>":{align:16,size:12},"vec3<f16>":{align:8,size:6},"vec4<i32>":{align:16,size:16},"vec4<u32>":{align:16,size:16},"vec4<f32>":{align:16,size:16},"vec4<f16>":{align:8,size:8},"mat2x2<f32>":{align:8,size:16},"mat2x2<f16>":{align:4,size:8},"mat3x2<f32>":{align:8,size:24},"mat3x2<f16>":{align:4,size:12},"mat4x2<f32>":{align:8,size:32},"mat4x2<f16>":{align:4,size:16},"mat2x3<f32>":{align:16,size:32},"mat2x3<f16>":{align:8,size:16},"mat3x3<f32>":{align:16,size:48},"mat3x3<f16>":{align:8,size:24},"mat4x3<f32>":{align:16,size:64},"mat4x3<f16>":{align:8,size:32},"mat2x4<f32>":{align:16,size:32},"mat2x4<f16>":{align:8,size:16},"mat3x4<f32>":{align:16,size:48},"mat3x4<f16>":{align:8,size:24},"mat4x4<f32>":{align:16,size:64},"mat4x4<f16>":{align:8,size:32}};function mm(s){const e=s.map(r=>({data:r,offset:0,size:0}));let t=0;for(let r=0;r<e.length;r++){const i=e[r];let n=Tr[i.data.type].size;const o=Tr[i.data.type].align;if(!Tr[i.data.type])throw new Error(`[Pixi.js] WebGPU UniformBuffer: Unknown type ${i.data.type}`);i.data.size>1&&(n=Math.max(n,o)*i.data.size),t=Math.ceil(t/o)*o,i.size=n,i.offset=t,t+=n}return t=Math.ceil(t/16)*16,{uboElements:e,size:t}}function gm(s,e){const{size:t,align:r}=Tr[s.data.type],i=(r-t)/4,n=s.data.type.indexOf("i32")>=0?"dataInt32":"data";return`
         v = uv.${s.data.name};
         ${e!==0?`offset += ${e};`:""}

         arrayOffset = offset;

         t = 0;

         for(var i=0; i < ${s.data.size*(t/4)}; i++)
         {
             for(var j = 0; j < ${t/4}; j++)
             {
                 ${n}[arrayOffset++] = v[t++];
             }
             ${i!==0?`arrayOffset += ${i};`:""}
         }
     `}function _m(s){return _h(s,"uboWgsl",gm,hp)}class ol extends mh{constructor(){super({createUboElements:mm,generateUboSync:_m})}}ol.extension={type:[x.WebGPUSystem],name:"ubo"};const Ie=128;class al{constructor(e){this._bindGroupHash=Object.create(null),this._buffers=[],this._bindGroups=[],this._bufferResources=[],this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_bindGroupHash"),this._batchBuffer=new pm({minUniformOffsetAlignment:Ie});const t=256/Ie;for(let r=0;r<t;r++){let i=U.UNIFORM|U.COPY_DST;r===0&&(i|=U.COPY_SRC),this._buffers.push(new ue({data:this._batchBuffer.data,usage:i}))}}renderEnd(){this._uploadBindGroups(),this._resetBindGroups()}_resetBindGroups(){for(const e in this._bindGroupHash)this._bindGroupHash[e]=null;this._batchBuffer.clear()}getUniformBindGroup(e,t){if(!t&&this._bindGroupHash[e.uid])return this._bindGroupHash[e.uid];this._renderer.ubo.ensureUniformGroup(e);const r=e.buffer.data,i=this._batchBuffer.addEmptyGroup(r.length);return this._renderer.ubo.syncUniformGroup(e,this._batchBuffer.data,i/4),this._bindGroupHash[e.uid]=this._getBindGroup(i/Ie),this._bindGroupHash[e.uid]}getUboResource(e){this._renderer.ubo.updateUniformGroup(e);const t=e.buffer.data,r=this._batchBuffer.addGroup(t);return this._getBufferResource(r/Ie)}getArrayBindGroup(e){const t=this._batchBuffer.addGroup(e);return this._getBindGroup(t/Ie)}getArrayBufferResource(e){const r=this._batchBuffer.addGroup(e)/Ie;return this._getBufferResource(r)}_getBufferResource(e){if(!this._bufferResources[e]){const t=this._buffers[e%2];this._bufferResources[e]=new Ei({buffer:t,offset:(e/2|0)*256,size:Ie})}return this._bufferResources[e]}_getBindGroup(e){if(!this._bindGroups[e]){const t=new Re({0:this._getBufferResource(e)});this._bindGroups[e]=t}return this._bindGroups[e]}_uploadBindGroups(){const e=this._renderer.buffer,t=this._buffers[0];t.update(this._batchBuffer.byteIndex),e.updateBuffer(t);const r=this._renderer.gpu.device.createCommandEncoder();for(let i=1;i<this._buffers.length;i++){const n=this._buffers[i];r.copyBufferToBuffer(e.getGPUBuffer(t),Ie,e.getGPUBuffer(n),0,this._batchBuffer.byteIndex)}this._renderer.gpu.device.queue.submit([r.finish()])}destroy(){for(let e=0;e<this._bindGroups.length;e++)this._bindGroups[e].destroy();this._bindGroups=null,this._bindGroupHash=null;for(let e=0;e<this._buffers.length;e++)this._buffers[e].destroy();this._buffers=null;for(let e=0;e<this._bufferResources.length;e++)this._bufferResources[e].destroy();this._bufferResources=null,this._batchBuffer.destroy(),this._bindGroupHash=null,this._renderer=null}}al.extension={type:[x.WebGPUPipes],name:"uniformBatch"};const xm={"point-list":0,"line-list":1,"line-strip":2,"triangle-list":3,"triangle-strip":4};function ym(s,e,t,r,i){return s<<24|e<<16|t<<10|r<<5|i}function bm(s,e,t,r){return t<<6|s<<3|r<<1|e}class hl{constructor(e){this._moduleCache=Object.create(null),this._bufferLayoutsCache=Object.create(null),this._bindingNamesCache=Object.create(null),this._pipeCache=Object.create(null),this._pipeStateCaches=Object.create(null),this._colorMask=15,this._multisampleCount=1,this._renderer=e}contextChange(e){this._gpu=e,this.setStencilMode(Z.DISABLED),this._updatePipeHash()}setMultisampleCount(e){this._multisampleCount!==e&&(this._multisampleCount=e,this._updatePipeHash())}setRenderTarget(e){this._multisampleCount=e.msaaSamples,this._depthStencilAttachment=e.descriptor.depthStencilAttachment?1:0,this._updatePipeHash()}setColorMask(e){this._colorMask!==e&&(this._colorMask=e,this._updatePipeHash())}setStencilMode(e){this._stencilMode!==e&&(this._stencilMode=e,this._stencilState=Ne[e],this._updatePipeHash())}setPipeline(e,t,r,i){const n=this.getPipeline(e,t,r);i.setPipeline(n)}getPipeline(e,t,r,i){e._layoutKey||(ah(e,t.attributeData),this._generateBufferKey(e)),i||(i=e.topology);const n=ym(e._layoutKey,t._layoutKey,r.data,r._blendModeId,xm[i]);return this._pipeCache[n]?this._pipeCache[n]:(this._pipeCache[n]=this._createPipeline(e,t,r,i),this._pipeCache[n])}_createPipeline(e,t,r,i){const n=this._gpu.device,o=this._createVertexBufferLayouts(e,t),a=this._renderer.state.getColorTargets(r);a[0].writeMask=this._stencilMode===Z.RENDERING_MASK_ADD?0:this._colorMask;const h=this._renderer.shader.getProgramData(t).pipeline,l={vertex:{module:this._getModule(t.vertex.source),entryPoint:t.vertex.entryPoint,buffers:o},fragment:{module:this._getModule(t.fragment.source),entryPoint:t.fragment.entryPoint,targets:a},primitive:{topology:i,cullMode:r.cullMode},layout:h,multisample:{count:this._multisampleCount},label:"PIXI Pipeline"};return this._depthStencilAttachment&&(l.depthStencil={...this._stencilState,format:"depth24plus-stencil8",depthWriteEnabled:r.depthTest,depthCompare:r.depthTest?"less":"always"}),n.createRenderPipeline(l)}_getModule(e){return this._moduleCache[e]||this._createModule(e)}_createModule(e){const t=this._gpu.device;return this._moduleCache[e]=t.createShaderModule({code:e}),this._moduleCache[e]}_generateBufferKey(e){const t=[];let r=0;const i=Object.keys(e.attributes).sort();for(let o=0;o<i.length;o++){const a=e.attributes[i[o]];t[r++]=a.offset,t[r++]=a.format,t[r++]=a.stride,t[r++]=a.instance}const n=t.join("|");return e._layoutKey=Lt(n,"geometry"),e._layoutKey}_generateAttributeLocationsKey(e){const t=[];let r=0;const i=Object.keys(e.attributeData).sort();for(let o=0;o<i.length;o++){const a=e.attributeData[i[o]];t[r++]=a.location}const n=t.join("|");return e._attributeLocationsKey=Lt(n,"programAttributes"),e._attributeLocationsKey}getBufferNamesToBind(e,t){const r=e._layoutKey<<16|t._attributeLocationsKey;if(this._bindingNamesCache[r])return this._bindingNamesCache[r];const i=this._createVertexBufferLayouts(e,t),n=Object.create(null),o=t.attributeData;for(let a=0;a<i.length;a++){const l=Object.values(i[a].attributes)[0].shaderLocation;for(const c in o)if(o[c].location===l){n[a]=c;break}}return this._bindingNamesCache[r]=n,n}_createVertexBufferLayouts(e,t){t._attributeLocationsKey||this._generateAttributeLocationsKey(t);const r=e._layoutKey<<16|t._attributeLocationsKey;if(this._bufferLayoutsCache[r])return this._bufferLayoutsCache[r];const i=[];return e.buffers.forEach(n=>{const o={arrayStride:0,stepMode:"vertex",attributes:[]},a=o.attributes;for(const h in t.attributeData){const l=e.attributes[h];(l.divisor??1)!==1&&F(`Attribute ${h} has an invalid divisor value of '${l.divisor}'. WebGPU only supports a divisor value of 1`),l.buffer===n&&(o.arrayStride=l.stride,o.stepMode=l.instance?"instance":"vertex",a.push({shaderLocation:t.attributeData[h].location,offset:l.offset,format:l.format}))}a.length&&i.push(o)}),this._bufferLayoutsCache[r]=i,i}_updatePipeHash(){const e=bm(this._stencilMode,this._multisampleCount,this._colorMask,this._depthStencilAttachment);this._pipeStateCaches[e]||(this._pipeStateCaches[e]=Object.create(null)),this._pipeCache=this._pipeStateCaches[e]}destroy(){this._renderer=null,this._bufferLayoutsCache=null}}hl.extension={type:[x.WebGPUSystem],name:"pipeline"};class vm{constructor(){this.contexts=[],this.msaaTextures=[],this.msaaSamples=1}}class Tm{init(e,t){this._renderer=e,this._renderTargetSystem=t}copyToTexture(e,t,r,i,n){const o=this._renderer,a=this._getGpuColorTexture(e),h=o.texture.getGpuSource(t.source);return o.encoder.commandEncoder.copyTextureToTexture({texture:a,origin:r},{texture:h,origin:n},i),t}startRenderPass(e,t=!0,r,i){const o=this._renderTargetSystem.getGpuRenderTarget(e),a=this.getDescriptor(e,t,r);o.descriptor=a,this._renderer.pipeline.setRenderTarget(o),this._renderer.encoder.beginRenderPass(o),this._renderer.encoder.setViewport(i)}finishRenderPass(){this._renderer.encoder.endRenderPass()}_getGpuColorTexture(e){const t=this._renderTargetSystem.getGpuRenderTarget(e);return t.contexts[0]?t.contexts[0].getCurrentTexture():this._renderer.texture.getGpuSource(e.colorTextures[0].source)}getDescriptor(e,t,r){typeof t=="boolean"&&(t=t?ce.ALL:ce.NONE);const i=this._renderTargetSystem,n=i.getGpuRenderTarget(e),o=e.colorTextures.map((l,c)=>{const u=n.contexts[c];let f,d;u?f=u.getCurrentTexture().createView():f=this._renderer.texture.getGpuSource(l).createView({mipLevelCount:1}),n.msaaTextures[c]&&(d=f,f=this._renderer.texture.getTextureView(n.msaaTextures[c]));const p=t&ce.COLOR?"clear":"load";return r??(r=i.defaultClearColor),{view:f,resolveTarget:d,clearValue:r,storeOp:"store",loadOp:p}});let a;if((e.stencil||e.depth)&&!e.depthStencilTexture&&(e.ensureDepthStencilTexture(),e.depthStencilTexture.source.sampleCount=n.msaa?4:1),e.depthStencilTexture){const l=t&ce.STENCIL?"clear":"load",c=t&ce.DEPTH?"clear":"load";a={view:this._renderer.texture.getGpuSource(e.depthStencilTexture.source).createView(),stencilStoreOp:"store",stencilLoadOp:l,depthClearValue:1,depthLoadOp:c,depthStoreOp:"store"}}return{colorAttachments:o,depthStencilAttachment:a}}clear(e,t=!0,r,i){if(!t)return;const{gpu:n,encoder:o}=this._renderer,a=n.device;if(o.commandEncoder===null){const l=a.createCommandEncoder(),c=this.getDescriptor(e,t,r),u=l.beginRenderPass(c);u.setViewport(i.x,i.y,i.width,i.height,0,1),u.end();const f=l.finish();a.queue.submit([f])}else this.startRenderPass(e,t,r,i)}initGpuRenderTarget(e){e.isRoot=!0;const t=new vm;return e.colorTextures.forEach((r,i)=>{if(r instanceof Be){const n=r.resource.getContext("webgpu"),o=r.transparent?"premultiplied":"opaque";try{n.configure({device:this._renderer.gpu.device,usage:GPUTextureUsage.TEXTURE_BINDING|GPUTextureUsage.COPY_DST|GPUTextureUsage.RENDER_ATTACHMENT|GPUTextureUsage.COPY_SRC,format:"bgra8unorm",alphaMode:o})}catch(a){console.error(a)}t.contexts[i]=n}if(t.msaa=r.source.antialias,r.source.antialias){const n=new Q({width:0,height:0,sampleCount:4});t.msaaTextures[i]=n}}),t.msaa&&(t.msaaSamples=4,e.depthStencilTexture&&(e.depthStencilTexture.source.sampleCount=4)),t}destroyGpuRenderTarget(e){e.contexts.forEach(t=>{t.unconfigure()}),e.msaaTextures.forEach(t=>{t.destroy()}),e.msaaTextures.length=0,e.contexts.length=0}ensureDepthStencilTexture(e){const t=this._renderTargetSystem.getGpuRenderTarget(e);e.depthStencilTexture&&t.msaa&&(e.depthStencilTexture.source.sampleCount=4)}resizeGpuRenderTarget(e){const t=this._renderTargetSystem.getGpuRenderTarget(e);t.width=e.width,t.height=e.height,t.msaa&&e.colorTextures.forEach((r,i)=>{t.msaaTextures[i]?.resize(r.source.width,r.source.height,r.source._resolution)})}}class ll extends Sh{constructor(e){super(e),this.adaptor=new Tm,this.adaptor.init(e,this)}}ll.extension={type:[x.WebGPUSystem],name:"renderTarget"};class cl{constructor(){this._gpuProgramData=Object.create(null)}contextChange(e){this._gpu=e}getProgramData(e){return this._gpuProgramData[e._layoutKey]||this._createGPUProgramData(e)}_createGPUProgramData(e){const t=this._gpu.device,r=e.gpuLayout.map(n=>t.createBindGroupLayout({entries:n})),i={bindGroupLayouts:r};return this._gpuProgramData[e._layoutKey]={bindGroups:r,pipeline:t.createPipelineLayout(i)},this._gpuProgramData[e._layoutKey]}destroy(){this._gpu=null,this._gpuProgramData=null}}cl.extension={type:[x.WebGPUSystem],name:"shader"};const ae={};ae.normal={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"}};ae.add={alpha:{srcFactor:"src-alpha",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"one",dstFactor:"one",operation:"add"}};ae.multiply={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"dst",dstFactor:"one-minus-src-alpha",operation:"add"}};ae.screen={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"one",dstFactor:"one-minus-src",operation:"add"}};ae.overlay={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"one",dstFactor:"one-minus-src",operation:"add"}};ae.none={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"zero",dstFactor:"zero",operation:"add"}};ae["normal-npm"]={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"src-alpha",dstFactor:"one-minus-src-alpha",operation:"add"}};ae["add-npm"]={alpha:{srcFactor:"one",dstFactor:"one",operation:"add"},color:{srcFactor:"src-alpha",dstFactor:"one",operation:"add"}};ae["screen-npm"]={alpha:{srcFactor:"one",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"src-alpha",dstFactor:"one-minus-src",operation:"add"}};ae.erase={alpha:{srcFactor:"zero",dstFactor:"one-minus-src-alpha",operation:"add"},color:{srcFactor:"zero",dstFactor:"one-minus-src",operation:"add"}};ae.min={alpha:{srcFactor:"one",dstFactor:"one",operation:"min"},color:{srcFactor:"one",dstFactor:"one",operation:"min"}};ae.max={alpha:{srcFactor:"one",dstFactor:"one",operation:"max"},color:{srcFactor:"one",dstFactor:"one",operation:"max"}};class ul{constructor(){this.defaultState=new be,this.defaultState.blend=!0}contextChange(e){this.gpu=e}getColorTargets(e){return[{format:"bgra8unorm",writeMask:0,blend:ae[e.blendMode]||ae.normal}]}destroy(){this.gpu=null}}ul.extension={type:[x.WebGPUSystem],name:"state"};const Sm={type:"image",upload(s,e,t){const r=s.resource,i=(s.pixelWidth|0)*(s.pixelHeight|0),n=r.byteLength/i;t.device.queue.writeTexture({texture:e},r,{offset:0,rowsPerImage:s.pixelHeight,bytesPerRow:s.pixelHeight*n},{width:s.pixelWidth,height:s.pixelHeight,depthOrArrayLayers:1})}},dl={"bc1-rgba-unorm":{blockBytes:8,blockWidth:4,blockHeight:4},"bc2-rgba-unorm":{blockBytes:16,blockWidth:4,blockHeight:4},"bc3-rgba-unorm":{blockBytes:16,blockWidth:4,blockHeight:4},"bc7-rgba-unorm":{blockBytes:16,blockWidth:4,blockHeight:4},"etc1-rgb-unorm":{blockBytes:8,blockWidth:4,blockHeight:4},"etc2-rgba8unorm":{blockBytes:16,blockWidth:4,blockHeight:4},"astc-4x4-unorm":{blockBytes:16,blockWidth:4,blockHeight:4}},wm={blockBytes:4,blockWidth:1,blockHeight:1},Pm={type:"compressed",upload(s,e,t){let r=s.pixelWidth,i=s.pixelHeight;const n=dl[s.format]||wm;for(let o=0;o<s.resource.length;o++){const a=s.resource[o],h=Math.ceil(r/n.blockWidth)*n.blockBytes;t.device.queue.writeTexture({texture:e,mipLevel:o},a,{offset:0,bytesPerRow:h},{width:Math.ceil(r/n.blockWidth)*n.blockWidth,height:Math.ceil(i/n.blockHeight)*n.blockHeight,depthOrArrayLayers:1}),r=Math.max(r>>1,1),i=Math.max(i>>1,1)}}},fl={type:"image",upload(s,e,t){const r=s.resource;if(!r)return;if(globalThis.HTMLImageElement&&r instanceof HTMLImageElement){const a=$.get().createCanvas(r.width,r.height);a.getContext("2d").drawImage(r,0,0,r.width,r.height),s.resource=a,F("ImageSource: Image element passed, converting to canvas and replacing resource.")}const i=Math.min(e.width,s.resourceWidth||s.pixelWidth),n=Math.min(e.height,s.resourceHeight||s.pixelHeight),o=s.alphaMode==="premultiply-alpha-on-upload";t.device.queue.copyExternalImageToTexture({source:r},{texture:e,premultipliedAlpha:o},{width:i,height:n})}},Cm={type:"video",upload(s,e,t){fl.upload(s,e,t)}};class Em{constructor(e){this.device=e,this.sampler=e.createSampler({minFilter:"linear"}),this.pipelines={}}_getMipmapPipeline(e){let t=this.pipelines[e];return t||(this.mipmapShaderModule||(this.mipmapShaderModule=this.device.createShaderModule({code:`
                        var<private> pos : array<vec2<f32>, 3> = array<vec2<f32>, 3>(
                        vec2<f32>(-1.0, -1.0), vec2<f32>(-1.0, 3.0), vec2<f32>(3.0, -1.0));

                        struct VertexOutput {
                        @builtin(position) position : vec4<f32>,
                        @location(0) texCoord : vec2<f32>,
                        };

                        @vertex
                        fn vertexMain(@builtin(vertex_index) vertexIndex : u32) -> VertexOutput {
                        var output : VertexOutput;
                        output.texCoord = pos[vertexIndex] * vec2<f32>(0.5, -0.5) + vec2<f32>(0.5);
                        output.position = vec4<f32>(pos[vertexIndex], 0.0, 1.0);
                        return output;
                        }

                        @group(0) @binding(0) var imgSampler : sampler;
                        @group(0) @binding(1) var img : texture_2d<f32>;

                        @fragment
                        fn fragmentMain(@location(0) texCoord : vec2<f32>) -> @location(0) vec4<f32> {
                        return textureSample(img, imgSampler, texCoord);
                        }
                    `})),t=this.device.createRenderPipeline({layout:"auto",vertex:{module:this.mipmapShaderModule,entryPoint:"vertexMain"},fragment:{module:this.mipmapShaderModule,entryPoint:"fragmentMain",targets:[{format:e}]}}),this.pipelines[e]=t),t}generateMipmap(e){const t=this._getMipmapPipeline(e.format);if(e.dimension==="3d"||e.dimension==="1d")throw new Error("Generating mipmaps for non-2d textures is currently unsupported!");let r=e;const i=e.depthOrArrayLayers||1,n=e.usage&GPUTextureUsage.RENDER_ATTACHMENT;if(!n){const h={size:{width:Math.ceil(e.width/2),height:Math.ceil(e.height/2),depthOrArrayLayers:i},format:e.format,usage:GPUTextureUsage.TEXTURE_BINDING|GPUTextureUsage.COPY_SRC|GPUTextureUsage.RENDER_ATTACHMENT,mipLevelCount:e.mipLevelCount-1};r=this.device.createTexture(h)}const o=this.device.createCommandEncoder({}),a=t.getBindGroupLayout(0);for(let h=0;h<i;++h){let l=e.createView({baseMipLevel:0,mipLevelCount:1,dimension:"2d",baseArrayLayer:h,arrayLayerCount:1}),c=n?1:0;for(let u=1;u<e.mipLevelCount;++u){const f=r.createView({baseMipLevel:c++,mipLevelCount:1,dimension:"2d",baseArrayLayer:h,arrayLayerCount:1}),d=o.beginRenderPass({colorAttachments:[{view:f,storeOp:"store",loadOp:"clear",clearValue:{r:0,g:0,b:0,a:0}}]}),p=this.device.createBindGroup({layout:a,entries:[{binding:0,resource:this.sampler},{binding:1,resource:l}]});d.setPipeline(t),d.setBindGroup(0,p),d.draw(3,1,0,0),d.end(),l=f}}if(!n){const h={width:Math.ceil(e.width/2),height:Math.ceil(e.height/2),depthOrArrayLayers:i};for(let l=1;l<e.mipLevelCount;++l)o.copyTextureToTexture({texture:r,mipLevel:l-1},{texture:e,mipLevel:l},h),h.width=Math.ceil(h.width/2),h.height=Math.ceil(h.height/2)}return this.device.queue.submit([o.finish()]),n||r.destroy(),e}}class pl{constructor(e){this.managedTextures=[],this._gpuSources=Object.create(null),this._gpuSamplers=Object.create(null),this._bindGroupHash=Object.create(null),this._textureViewHash=Object.create(null),this._uploads={image:fl,buffer:Sm,video:Cm,compressed:Pm},this._renderer=e,e.renderableGC.addManagedHash(this,"_gpuSources"),e.renderableGC.addManagedHash(this,"_gpuSamplers"),e.renderableGC.addManagedHash(this,"_bindGroupHash"),e.renderableGC.addManagedHash(this,"_textureViewHash")}contextChange(e){this._gpu=e}initSource(e){if(e.autoGenerateMipmaps){const h=Math.max(e.pixelWidth,e.pixelHeight);e.mipLevelCount=Math.floor(Math.log2(h))+1}let t=GPUTextureUsage.TEXTURE_BINDING|GPUTextureUsage.COPY_DST;e.uploadMethodId!=="compressed"&&(t|=GPUTextureUsage.RENDER_ATTACHMENT,t|=GPUTextureUsage.COPY_SRC);const r=dl[e.format]||{blockWidth:1,blockHeight:1},i=Math.ceil(e.pixelWidth/r.blockWidth)*r.blockWidth,n=Math.ceil(e.pixelHeight/r.blockHeight)*r.blockHeight,o={label:e.label,size:{width:i,height:n},format:e.format,sampleCount:e.sampleCount,mipLevelCount:e.mipLevelCount,dimension:e.dimension,usage:t},a=this._gpu.device.createTexture(o);return this._gpuSources[e.uid]=a,this.managedTextures.includes(e)||(e.on("update",this.onSourceUpdate,this),e.on("resize",this.onSourceResize,this),e.on("destroy",this.onSourceDestroy,this),e.on("unload",this.onSourceUnload,this),e.on("updateMipmaps",this.onUpdateMipmaps,this),this.managedTextures.push(e)),this.onSourceUpdate(e),a}onSourceUpdate(e){const t=this.getGpuSource(e);t&&(this._uploads[e.uploadMethodId]&&this._uploads[e.uploadMethodId].upload(e,t,this._gpu),e.autoGenerateMipmaps&&e.mipLevelCount>1&&this.onUpdateMipmaps(e))}onSourceUnload(e){const t=this._gpuSources[e.uid];t&&(this._gpuSources[e.uid]=null,t.destroy())}onUpdateMipmaps(e){this._mipmapGenerator||(this._mipmapGenerator=new Em(this._gpu.device));const t=this.getGpuSource(e);this._mipmapGenerator.generateMipmap(t)}onSourceDestroy(e){e.off("update",this.onSourceUpdate,this),e.off("unload",this.onSourceUnload,this),e.off("destroy",this.onSourceDestroy,this),e.off("resize",this.onSourceResize,this),e.off("updateMipmaps",this.onUpdateMipmaps,this),this.managedTextures.splice(this.managedTextures.indexOf(e),1),this.onSourceUnload(e)}onSourceResize(e){const t=this._gpuSources[e.uid];t?(t.width!==e.pixelWidth||t.height!==e.pixelHeight)&&(this._textureViewHash[e.uid]=null,this._bindGroupHash[e.uid]=null,this.onSourceUnload(e),this.initSource(e)):this.initSource(e)}_initSampler(e){return this._gpuSamplers[e._resourceId]=this._gpu.device.createSampler(e),this._gpuSamplers[e._resourceId]}getGpuSampler(e){return this._gpuSamplers[e._resourceId]||this._initSampler(e)}getGpuSource(e){return this._gpuSources[e.uid]||this.initSource(e)}getTextureBindGroup(e){return this._bindGroupHash[e.uid]??this._createTextureBindGroup(e)}_createTextureBindGroup(e){const t=e.source;return this._bindGroupHash[e.uid]=new Re({0:t,1:t.style,2:new ne({uTextureMatrix:{type:"mat3x3<f32>",value:e.textureMatrix.mapCoord}})}),this._bindGroupHash[e.uid]}getTextureView(e){const t=e.source;return this._textureViewHash[t.uid]??this._createTextureView(t)}_createTextureView(e){return this._textureViewHash[e.uid]=this.getGpuSource(e).createView(),this._textureViewHash[e.uid]}generateCanvas(e){const t=this._renderer,r=t.gpu.device.createCommandEncoder(),i=$.get().createCanvas();i.width=e.source.pixelWidth,i.height=e.source.pixelHeight;const n=i.getContext("webgpu");return n.configure({device:t.gpu.device,usage:GPUTextureUsage.COPY_DST|GPUTextureUsage.COPY_SRC,format:$.get().getNavigator().gpu.getPreferredCanvasFormat(),alphaMode:"premultiplied"}),r.copyTextureToTexture({texture:t.texture.getGpuSource(e.source),origin:{x:0,y:0}},{texture:n.getCurrentTexture()},{width:i.width,height:i.height}),t.gpu.device.queue.submit([r.finish()]),i}getPixels(e){const t=this.generateCanvas(e),r=Le.getOptimalCanvasAndContext(t.width,t.height),i=r.context;i.drawImage(t,0,0);const{width:n,height:o}=t,a=i.getImageData(0,0,n,o),h=new Uint8ClampedArray(a.data.buffer);return Le.returnCanvasAndContext(r),{pixels:h,width:n,height:o}}destroy(){this.managedTextures.slice().forEach(e=>this.onSourceDestroy(e)),this.managedTextures=null;for(const e of Object.keys(this._bindGroupHash)){const t=Number(e);this._bindGroupHash[t]?.destroy(),this._bindGroupHash[t]=null}this._gpu=null,this._mipmapGenerator=null,this._gpuSources=null,this._bindGroupHash=null,this._textureViewHash=null,this._gpuSamplers=null}}pl.extension={type:[x.WebGPUSystem],name:"texture"};class ml{constructor(){this._maxTextures=0}contextChange(e){const t=new ne({uTransformMatrix:{value:new B,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}});this._maxTextures=e.limits.maxBatchableTextures;const r=Yt({name:"graphics",bits:[ci,di(this._maxTextures),jf,qt]});this.shader=new pe({gpuProgram:r,resources:{localUniforms:t}})}execute(e,t){const r=t.context,i=r.customShader||this.shader,n=e.renderer,o=n.graphicsContext,{batcher:a,instructions:h}=o.getContextRenderData(r),l=n.encoder;l.setGeometry(a.geometry,i.gpuProgram);const c=n.globalUniforms.bindGroup;l.setBindGroup(0,c,i.gpuProgram);const u=n.renderPipes.uniformBatch.getUniformBindGroup(i.resources.localUniforms,!0);l.setBindGroup(2,u,i.gpuProgram);const f=h.instructions;let d=null;for(let p=0;p<h.instructionSize;p++){const g=f[p];if(g.topology!==d&&(d=g.topology,l.setPipelineFromGeometryProgramAndState(a.geometry,i.gpuProgram,e.state,g.topology)),i.groups[1]=g.bindGroup,!g.gpuBindGroup){const m=g.textures;g.bindGroup=li(m.textures,m.count,this._maxTextures),g.gpuBindGroup=n.bindGroup.getBindGroup(g.bindGroup,i.gpuProgram,1)}l.setBindGroup(1,g.bindGroup,i.gpuProgram),l.renderPassEncoder.drawIndexed(g.size,1,g.start)}}destroy(){this.shader.destroy(!0),this.shader=null}}ml.extension={type:[x.WebGPUPipesAdaptor],name:"graphics"};class gl{init(){const e=Yt({name:"mesh",bits:[It,qf,qt]});this._shader=new pe({gpuProgram:e,resources:{uTexture:A.EMPTY._source,uSampler:A.EMPTY._source.style,textureUniforms:{uTextureMatrix:{type:"mat3x3<f32>",value:new B}}}})}execute(e,t){const r=e.renderer;let i=t._shader;if(!i)i=this._shader,i.groups[2]=r.texture.getTextureBindGroup(t.texture);else if(!i.gpuProgram){F("Mesh shader has no gpuProgram",t.shader);return}const n=i.gpuProgram;if(n.autoAssignGlobalUniforms&&(i.groups[0]=r.globalUniforms.bindGroup),n.autoAssignLocalUniforms){const o=e.localUniforms;i.groups[1]=r.renderPipes.uniformBatch.getUniformBindGroup(o,!0)}r.encoder.draw({geometry:t._geometry,shader:i,state:t.state})}destroy(){this._shader.destroy(!0),this._shader=null}}gl.extension={type:[x.WebGPUPipesAdaptor],name:"mesh"};const Mm=[...qh,ol,sl,Fi,il,tl,pl,ll,cl,ul,hl,rl,nl,el],Am=[...Kh,al],Bm=[Ja,gl,ml],_l=[],xl=[],yl=[];q.handleByNamedList(x.WebGPUSystem,_l);q.handleByNamedList(x.WebGPUPipes,xl);q.handleByNamedList(x.WebGPUPipesAdaptor,yl);q.add(...Mm,...Am,...Bm);class Rm extends Fr{constructor(){const e={name:"webgpu",type:ye.WEBGPU,systems:_l,renderPipes:xl,renderPipeAdaptors:yl};super(e)}}const km=Object.freeze(Object.defineProperty({__proto__:null,WebGPURenderer:Rm},Symbol.toStringTag,{value:"Module"}));class Gm{constructor(){this.batches=[],this.batched=!1}destroy(){this.batches.forEach(e=>{ee.return(e)}),this.batches.length=0}}class Im{constructor(e,t){this.state=be.for2d(),this.renderer=e,this._adaptor=t,this.renderer.runners.contextChange.add(this)}contextChange(){this._adaptor.contextChange(this.renderer)}validateRenderable(e){const t=e.context,r=!!e._gpuData,i=this.renderer.graphicsContext.updateGpuContext(t);return!!(i.isBatchable||r!==i.isBatchable)}addRenderable(e,t){const r=this.renderer.graphicsContext.updateGpuContext(e.context);e.didViewUpdate&&this._rebuild(e),r.isBatchable?this._addToBatcher(e,t):(this.renderer.renderPipes.batch.break(t),t.add(e))}updateRenderable(e){const r=this._getGpuDataForRenderable(e).batches;for(let i=0;i<r.length;i++){const n=r[i];n._batcher.updateElement(n)}}execute(e){if(!e.isRenderable)return;const t=this.renderer,r=e.context;if(!t.graphicsContext.getGpuContext(r).batches.length)return;const n=r.customShader||this._adaptor.shader;this.state.blendMode=e.groupBlendMode;const o=n.resources.localUniforms.uniforms;o.uTransformMatrix=e.groupTransform,o.uRound=t._roundPixels|e._roundPixels,Zt(e.groupColorAlpha,o.uColor,0),this._adaptor.execute(this,e)}_rebuild(e){const t=this._getGpuDataForRenderable(e),r=this.renderer.graphicsContext.updateGpuContext(e.context);t.destroy(),r.isBatchable&&this._updateBatchesForRenderable(e,t)}_addToBatcher(e,t){const r=this.renderer.renderPipes.batch,i=this._getGpuDataForRenderable(e).batches;for(let n=0;n<i.length;n++){const o=i[n];r.addToBatch(o,t)}}_getGpuDataForRenderable(e){return e._gpuData[this.renderer.uid]||this._initGpuDataForRenderable(e)}_initGpuDataForRenderable(e){const t=new Gm;return e._gpuData[this.renderer.uid]=t,t}_updateBatchesForRenderable(e,t){const r=e.context,i=this.renderer.graphicsContext.getGpuContext(r),n=this.renderer._roundPixels|e._roundPixels;t.batches=i.batches.map(o=>{const a=ee.get(gi);return o.copyTo(a),a.renderable=e,a.roundPixels=n,a})}destroy(){this.renderer=null,this._adaptor.destroy(),this._adaptor=null,this.state=null}}Im.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"graphics"};const bl=class vl extends vi{constructor(...e){super({});let t=e[0]??{};typeof t=="number"&&(I(O,"PlaneGeometry constructor changed please use { width, height, verticesX, verticesY } instead"),t={width:t,height:e[1],verticesX:e[2],verticesY:e[3]}),this.build(t)}build(e){e={...vl.defaultOptions,...e},this.verticesX=this.verticesX??e.verticesX,this.verticesY=this.verticesY??e.verticesY,this.width=this.width??e.width,this.height=this.height??e.height;const t=this.verticesX*this.verticesY,r=[],i=[],n=[],o=this.verticesX-1,a=this.verticesY-1,h=this.width/o,l=this.height/a;for(let u=0;u<t;u++){const f=u%this.verticesX,d=u/this.verticesX|0;r.push(f*h,d*l),i.push(f/o,d/a)}const c=o*a;for(let u=0;u<c;u++){const f=u%o,d=u/o|0,p=d*this.verticesX+f,g=d*this.verticesX+f+1,m=(d+1)*this.verticesX+f,_=(d+1)*this.verticesX+f+1;n.push(p,g,m,g,_,m)}this.buffers[0].data=new Float32Array(r),this.buffers[1].data=new Float32Array(i),this.indexBuffer.data=new Uint32Array(n),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()}};bl.defaultOptions={width:100,height:100,verticesX:10,verticesY:10};let Fm=bl;class Ui{constructor(){this.batcherName="default",this.packAsQuad=!1,this.indexOffset=0,this.attributeOffset=0,this.roundPixels=0,this._batcher=null,this._batch=null,this._textureMatrixUpdateId=-1,this._uvUpdateId=-1}get blendMode(){return this.renderable.groupBlendMode}get topology(){return this._topology||this.geometry.topology}set topology(e){this._topology=e}reset(){this.renderable=null,this.texture=null,this._batcher=null,this._batch=null,this.geometry=null,this._uvUpdateId=-1,this._textureMatrixUpdateId=-1}setTexture(e){this.texture!==e&&(this.texture=e,this._textureMatrixUpdateId=-1)}get uvs(){const t=this.geometry.getBuffer("aUV"),r=t.data;let i=r;const n=this.texture.textureMatrix;return n.isSimple||(i=this._transformedUvs,(this._textureMatrixUpdateId!==n._updateID||this._uvUpdateId!==t._updateID)&&((!i||i.length<r.length)&&(i=this._transformedUvs=new Float32Array(r.length)),this._textureMatrixUpdateId=n._updateID,this._uvUpdateId=t._updateID,n.multiplyUvs(r,i))),i}get positions(){return this.geometry.positions}get indices(){return this.geometry.indices}get color(){return this.renderable.groupColorAlpha}get groupTransform(){return this.renderable.groupTransform}get attributeSize(){return this.geometry.positions.length/2}get indexSize(){return this.geometry.indices.length}}class yo{destroy(){}}class Um{constructor(e,t){this.localUniforms=new ne({uTransformMatrix:{value:new B,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),this.localUniformsBindGroup=new Re({0:this.localUniforms}),this.renderer=e,this._adaptor=t,this._adaptor.init()}validateRenderable(e){const t=this._getMeshData(e),r=t.batched,i=e.batched;if(t.batched=i,r!==i)return!0;if(i){const n=e._geometry;if(n.indices.length!==t.indexSize||n.positions.length!==t.vertexSize)return t.indexSize=n.indices.length,t.vertexSize=n.positions.length,!0;const o=this._getBatchableMesh(e);return o.texture.uid!==e._texture.uid&&(o._textureMatrixUpdateId=-1),!o._batcher.checkAndUpdateTexture(o,e._texture)}return!1}addRenderable(e,t){const r=this.renderer.renderPipes.batch,{batched:i}=this._getMeshData(e);if(i){const n=this._getBatchableMesh(e);n.setTexture(e._texture),n.geometry=e._geometry,r.addToBatch(n,t)}else r.break(t),t.add(e)}updateRenderable(e){if(e.batched){const t=this._getBatchableMesh(e);t.setTexture(e._texture),t.geometry=e._geometry,t._batcher.updateElement(t)}}execute(e){if(!e.isRenderable)return;e.state.blendMode=Wt(e.groupBlendMode,e.texture._source);const t=this.localUniforms;t.uniforms.uTransformMatrix=e.groupTransform,t.uniforms.uRound=this.renderer._roundPixels|e._roundPixels,t.update(),Zt(e.groupColorAlpha,t.uniforms.uColor,0),this._adaptor.execute(this,e)}_getMeshData(e){var t,r;return(t=e._gpuData)[r=this.renderer.uid]||(t[r]=new yo),e._gpuData[this.renderer.uid].meshData||this._initMeshData(e)}_initMeshData(e){return e._gpuData[this.renderer.uid].meshData={batched:e.batched,indexSize:e._geometry.indices?.length,vertexSize:e._geometry.positions?.length},e._gpuData[this.renderer.uid].meshData}_getBatchableMesh(e){var t,r;return(t=e._gpuData)[r=this.renderer.uid]||(t[r]=new yo),e._gpuData[this.renderer.uid].batchableMesh||this._initBatchableMesh(e)}_initBatchableMesh(e){const t=new Ui;return t.renderable=e,t.setTexture(e._texture),t.transform=e.groupTransform,t.roundPixels=this.renderer._roundPixels|e._roundPixels,e._gpuData[this.renderer.uid].batchableMesh=t,t}destroy(){this.localUniforms=null,this.localUniformsBindGroup=null,this._adaptor.destroy(),this._adaptor=null,this.renderer=null}}Um.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"mesh"};class Dm{execute(e,t){const r=e.state,i=e.renderer,n=t.shader||e.defaultShader;n.resources.uTexture=t.texture._source,n.resources.uniforms=e.localUniforms;const o=i.gl,a=e.getBuffers(t);i.shader.bind(n),i.state.set(r),i.geometry.bind(a.geometry,n.glProgram);const l=a.geometry.indexBuffer.data.BYTES_PER_ELEMENT===2?o.UNSIGNED_SHORT:o.UNSIGNED_INT;o.drawElements(o.TRIANGLES,t.particleChildren.length*6,l,0)}}class Om{execute(e,t){const r=e.renderer,i=t.shader||e.defaultShader;i.groups[0]=r.renderPipes.uniformBatch.getUniformBindGroup(e.localUniforms,!0),i.groups[1]=r.texture.getTextureBindGroup(t.texture);const n=e.state,o=e.getBuffers(t);r.encoder.draw({geometry:o.geometry,shader:t.shader||e.defaultShader,state:n,size:t.particleChildren.length*6})}}function bo(s,e=null){const t=s*6;if(t>65535?e||(e=new Uint32Array(t)):e||(e=new Uint16Array(t)),e.length!==t)throw new Error(`Out buffer length is incorrect, got ${e.length} and expected ${t}`);for(let r=0,i=0;r<t;r+=6,i+=4)e[r+0]=i+0,e[r+1]=i+1,e[r+2]=i+2,e[r+3]=i+0,e[r+4]=i+2,e[r+5]=i+3;return e}function Lm(s){return{dynamicUpdate:vo(s,!0),staticUpdate:vo(s,!1)}}function vo(s,e){const t=[];t.push(`

        var index = 0;

        for (let i = 0; i < ps.length; ++i)
        {
            const p = ps[i];

            `);let r=0;for(const n in s){const o=s[n];if(e!==o.dynamic)continue;t.push(`offset = index + ${r}`),t.push(o.code);const a=De(o.format);r+=a.stride/4}t.push(`
            index += stride * 4;
        }
    `),t.unshift(`
        var stride = ${r};
    `);const i=t.join(`
`);return new Function("ps","f32v","u32v",i)}class Nm{constructor(e){this._size=0,this._generateParticleUpdateCache={};const t=this._size=e.size??1e3,r=e.properties;let i=0,n=0;for(const c in r){const u=r[c],f=De(u.format);u.dynamic?n+=f.stride:i+=f.stride}this._dynamicStride=n/4,this._staticStride=i/4,this.staticAttributeBuffer=new at(t*4*i),this.dynamicAttributeBuffer=new at(t*4*n),this.indexBuffer=bo(t);const o=new Xt;let a=0,h=0;this._staticBuffer=new ue({data:new Float32Array(1),label:"static-particle-buffer",shrinkToFit:!1,usage:U.VERTEX|U.COPY_DST}),this._dynamicBuffer=new ue({data:new Float32Array(1),label:"dynamic-particle-buffer",shrinkToFit:!1,usage:U.VERTEX|U.COPY_DST});for(const c in r){const u=r[c],f=De(u.format);u.dynamic?(o.addAttribute(u.attributeName,{buffer:this._dynamicBuffer,stride:this._dynamicStride*4,offset:a*4,format:u.format}),a+=f.size):(o.addAttribute(u.attributeName,{buffer:this._staticBuffer,stride:this._staticStride*4,offset:h*4,format:u.format}),h+=f.size)}o.addIndex(this.indexBuffer);const l=this.getParticleUpdate(r);this._dynamicUpload=l.dynamicUpdate,this._staticUpload=l.staticUpdate,this.geometry=o}getParticleUpdate(e){const t=Hm(e);return this._generateParticleUpdateCache[t]?this._generateParticleUpdateCache[t]:(this._generateParticleUpdateCache[t]=this.generateParticleUpdate(e),this._generateParticleUpdateCache[t])}generateParticleUpdate(e){return Lm(e)}update(e,t){e.length>this._size&&(t=!0,this._size=Math.max(e.length,this._size*1.5|0),this.staticAttributeBuffer=new at(this._size*this._staticStride*4*4),this.dynamicAttributeBuffer=new at(this._size*this._dynamicStride*4*4),this.indexBuffer=bo(this._size),this.geometry.indexBuffer.setDataWithSize(this.indexBuffer,this.indexBuffer.byteLength,!0));const r=this.dynamicAttributeBuffer;if(this._dynamicUpload(e,r.float32View,r.uint32View),this._dynamicBuffer.setDataWithSize(this.dynamicAttributeBuffer.float32View,e.length*this._dynamicStride*4,!0),t){const i=this.staticAttributeBuffer;this._staticUpload(e,i.float32View,i.uint32View),this._staticBuffer.setDataWithSize(i.float32View,e.length*this._staticStride*4,!0)}}destroy(){this._staticBuffer.destroy(),this._dynamicBuffer.destroy(),this.geometry.destroy()}}function Hm(s){const e=[];for(const t in s){const r=s[t];e.push(t,r.code,r.dynamic?"d":"s")}return e.join("_")}var zm=`varying vec2 vUV;
varying vec4 vColor;

uniform sampler2D uTexture;

void main(void){
    vec4 color = texture2D(uTexture, vUV) * vColor;
    gl_FragColor = color;
}`,Wm=`attribute vec2 aVertex;
attribute vec2 aUV;
attribute vec4 aColor;

attribute vec2 aPosition;
attribute float aRotation;

uniform mat3 uTranslationMatrix;
uniform float uRound;
uniform vec2 uResolution;
uniform vec4 uColor;

varying vec2 vUV;
varying vec4 vColor;

vec2 roundPixels(vec2 position, vec2 targetSize)
{       
    return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;
}

void main(void){
    float cosRotation = cos(aRotation);
    float sinRotation = sin(aRotation);
    float x = aVertex.x * cosRotation - aVertex.y * sinRotation;
    float y = aVertex.x * sinRotation + aVertex.y * cosRotation;

    vec2 v = vec2(x, y);
    v = v + aPosition;

    gl_Position = vec4((uTranslationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    if(uRound == 1.0)
    {
        gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
    }

    vUV = aUV;
    vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uColor;
}
`,To=`
struct ParticleUniforms {
  uProjectionMatrix:mat3x3<f32>,
  uColor:vec4<f32>,
  uResolution:vec2<f32>,
  uRoundPixels:f32,
};

@group(0) @binding(0) var<uniform> uniforms: ParticleUniforms;

@group(1) @binding(0) var uTexture: texture_2d<f32>;
@group(1) @binding(1) var uSampler : sampler;

struct VSOutput {
    @builtin(position) position: vec4<f32>,
    @location(0) uv : vec2<f32>,
    @location(1) color : vec4<f32>,
  };
@vertex
fn mainVertex(
  @location(0) aVertex: vec2<f32>,
  @location(1) aPosition: vec2<f32>,
  @location(2) aUV: vec2<f32>,
  @location(3) aColor: vec4<f32>,
  @location(4) aRotation: f32,
) -> VSOutput {
  
   let v = vec2(
       aVertex.x * cos(aRotation) - aVertex.y * sin(aRotation),
       aVertex.x * sin(aRotation) + aVertex.y * cos(aRotation)
   ) + aPosition;

   let position = vec4((uniforms.uProjectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    let vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uniforms.uColor;

  return VSOutput(
   position,
   aUV,
   vColor,
  );
}

@fragment
fn mainFragment(
  @location(0) uv: vec2<f32>,
  @location(1) color: vec4<f32>,
  @builtin(position) position: vec4<f32>,
) -> @location(0) vec4<f32> {

    var sample = textureSample(uTexture, uSampler, uv) * color;
   
    return sample;
}`;class Vm extends pe{constructor(){const e=pt.from({vertex:Wm,fragment:zm}),t=tt.from({fragment:{source:To,entryPoint:"mainFragment"},vertex:{source:To,entryPoint:"mainVertex"}});super({glProgram:e,gpuProgram:t,resources:{uTexture:A.WHITE.source,uSampler:new Je({}),uniforms:{uTranslationMatrix:{value:new B,type:"mat3x3<f32>"},uColor:{value:new L(16777215),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}}})}}class Tl{constructor(e,t){this.state=be.for2d(),this.localUniforms=new ne({uTranslationMatrix:{value:new B,type:"mat3x3<f32>"},uColor:{value:new Float32Array(4),type:"vec4<f32>"},uRound:{value:1,type:"f32"},uResolution:{value:[0,0],type:"vec2<f32>"}}),this.renderer=e,this.adaptor=t,this.defaultShader=new Vm,this.state=be.for2d()}validateRenderable(e){return!1}addRenderable(e,t){this.renderer.renderPipes.batch.break(t),t.add(e)}getBuffers(e){return e._gpuData[this.renderer.uid]||this._initBuffer(e)}_initBuffer(e){return e._gpuData[this.renderer.uid]=new Nm({size:e.particleChildren.length,properties:e._properties}),e._gpuData[this.renderer.uid]}updateRenderable(e){}execute(e){const t=e.particleChildren;if(t.length===0)return;const r=this.renderer,i=this.getBuffers(e);e.texture||(e.texture=t[0].texture);const n=this.state;i.update(t,e._childrenDirty),e._childrenDirty=!1,n.blendMode=Wt(e.blendMode,e.texture._source);const o=this.localUniforms.uniforms,a=o.uTranslationMatrix;e.worldTransform.copyTo(a),a.prepend(r.globalUniforms.globalUniformData.projectionMatrix),o.uResolution=r.globalUniforms.globalUniformData.resolution,o.uRound=r._roundPixels|e._roundPixels,Zt(e.groupColorAlpha,o.uColor,0),this.adaptor.execute(this,e)}destroy(){this.defaultShader&&(this.defaultShader.destroy(),this.defaultShader=null)}}class $m extends Tl{constructor(e){super(e,new Dm)}}$m.extension={type:[x.WebGLPipes],name:"particle"};class Xm extends Tl{constructor(e){super(e,new Om)}}Xm.extension={type:[x.WebGPUPipes],name:"particle"};const Sl=class wl extends Fm{constructor(e={}){e={...wl.defaultOptions,...e},super({width:e.width,height:e.height,verticesX:4,verticesY:4}),this.update(e)}update(e){this.width=e.width??this.width,this.height=e.height??this.height,this._originalWidth=e.originalWidth??this._originalWidth,this._originalHeight=e.originalHeight??this._originalHeight,this._leftWidth=e.leftWidth??this._leftWidth,this._rightWidth=e.rightWidth??this._rightWidth,this._topHeight=e.topHeight??this._topHeight,this._bottomHeight=e.bottomHeight??this._bottomHeight,this._anchorX=e.anchor?.x,this._anchorY=e.anchor?.y,this.updateUvs(),this.updatePositions()}updatePositions(){const e=this.positions,{width:t,height:r,_leftWidth:i,_rightWidth:n,_topHeight:o,_bottomHeight:a,_anchorX:h,_anchorY:l}=this,c=i+n,u=t>c?1:t/c,f=o+a,d=r>f?1:r/f,p=Math.min(u,d),g=h*t,m=l*r;e[0]=e[8]=e[16]=e[24]=-g,e[2]=e[10]=e[18]=e[26]=i*p-g,e[4]=e[12]=e[20]=e[28]=t-n*p-g,e[6]=e[14]=e[22]=e[30]=t-g,e[1]=e[3]=e[5]=e[7]=-m,e[9]=e[11]=e[13]=e[15]=o*p-m,e[17]=e[19]=e[21]=e[23]=r-a*p-m,e[25]=e[27]=e[29]=e[31]=r-m,this.getBuffer("aPosition").update()}updateUvs(){const e=this.uvs;e[0]=e[8]=e[16]=e[24]=0,e[1]=e[3]=e[5]=e[7]=0,e[6]=e[14]=e[22]=e[30]=1,e[25]=e[27]=e[29]=e[31]=1;const t=1/this._originalWidth,r=1/this._originalHeight;e[2]=e[10]=e[18]=e[26]=t*this._leftWidth,e[9]=e[11]=e[13]=e[15]=r*this._topHeight,e[4]=e[12]=e[20]=e[28]=1-t*this._rightWidth,e[17]=e[19]=e[21]=e[23]=1-r*this._bottomHeight,this.getBuffer("aUV").update()}};Sl.defaultOptions={width:100,height:100,leftWidth:10,topHeight:10,rightWidth:10,bottomHeight:10,originalWidth:100,originalHeight:100};let Ym=Sl;class jm extends Ui{constructor(){super(),this.geometry=new Ym}destroy(){this.geometry.destroy()}}class qm{constructor(e){this._renderer=e}addRenderable(e,t){const r=this._getGpuSprite(e);e.didViewUpdate&&this._updateBatchableSprite(e,r),this._renderer.renderPipes.batch.addToBatch(r,t)}updateRenderable(e){const t=this._getGpuSprite(e);e.didViewUpdate&&this._updateBatchableSprite(e,t),t._batcher.updateElement(t)}validateRenderable(e){const t=this._getGpuSprite(e);return!t._batcher.checkAndUpdateTexture(t,e._texture)}_updateBatchableSprite(e,t){t.geometry.update(e),t.setTexture(e._texture)}_getGpuSprite(e){return e._gpuData[this._renderer.uid]||this._initGPUSprite(e)}_initGPUSprite(e){const t=e._gpuData[this._renderer.uid]=new jm,r=t;return r.renderable=e,r.transform=e.groupTransform,r.texture=e._texture,r.roundPixels=this._renderer._roundPixels|e._roundPixels,e.didViewUpdate||this._updateBatchableSprite(e,r),t}destroy(){this._renderer=null}}qm.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"nineSliceSprite"};const Km={name:"tiling-bit",vertex:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`
            uv = (tilingUniforms.uTextureTransform * vec3(uv, 1.0)).xy;

            position = (position - tilingUniforms.uSizeAnchor.zw) * tilingUniforms.uSizeAnchor.xy;
        `},fragment:{header:`
            struct TilingUniforms {
                uMapCoord:mat3x3<f32>,
                uClampFrame:vec4<f32>,
                uClampOffset:vec2<f32>,
                uTextureTransform:mat3x3<f32>,
                uSizeAnchor:vec4<f32>
            };

            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;
            @group(2) @binding(1) var uTexture: texture_2d<f32>;
            @group(2) @binding(2) var uSampler: sampler;
        `,main:`

            var coord = vUV + ceil(tilingUniforms.uClampOffset - vUV);
            coord = (tilingUniforms.uMapCoord * vec3(coord, 1.0)).xy;
            var unclamped = coord;
            coord = clamp(coord, tilingUniforms.uClampFrame.xy, tilingUniforms.uClampFrame.zw);

            var bias = 0.;

            if(unclamped.x == coord.x && unclamped.y == coord.y)
            {
                bias = -32.;
            }

            outColor = textureSampleBias(uTexture, uSampler, coord, bias);
        `}},Zm={name:"tiling-bit",vertex:{header:`
            uniform mat3 uTextureTransform;
            uniform vec4 uSizeAnchor;

        `,main:`
            uv = (uTextureTransform * vec3(aUV, 1.0)).xy;

            position = (position - uSizeAnchor.zw) * uSizeAnchor.xy;
        `},fragment:{header:`
            uniform sampler2D uTexture;
            uniform mat3 uMapCoord;
            uniform vec4 uClampFrame;
            uniform vec2 uClampOffset;
        `,main:`

        vec2 coord = vUV + ceil(uClampOffset - vUV);
        coord = (uMapCoord * vec3(coord, 1.0)).xy;
        vec2 unclamped = coord;
        coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

        outColor = texture(uTexture, coord, unclamped == coord ? 0.0 : -32.0);// lod-bias very negative to force lod 0

        `}};let Ts,Ss;class Qm extends pe{constructor(){Ts??(Ts=Yt({name:"tiling-sprite-shader",bits:[It,Km,qt]})),Ss??(Ss=jt({name:"tiling-sprite-shader",bits:[wi,Zm,Kt]}));const e=new ne({uMapCoord:{value:new B,type:"mat3x3<f32>"},uClampFrame:{value:new Float32Array([0,0,1,1]),type:"vec4<f32>"},uClampOffset:{value:new Float32Array([0,0]),type:"vec2<f32>"},uTextureTransform:{value:new B,type:"mat3x3<f32>"},uSizeAnchor:{value:new Float32Array([100,100,.5,.5]),type:"vec4<f32>"}});super({glProgram:Ss,gpuProgram:Ts,resources:{localUniforms:new ne({uTransformMatrix:{value:new B,type:"mat3x3<f32>"},uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uRound:{value:0,type:"f32"}}),tilingUniforms:e,uTexture:A.EMPTY.source,uSampler:A.EMPTY.source.style}})}updateUniforms(e,t,r,i,n,o){const a=this.resources.tilingUniforms,h=o.width,l=o.height,c=o.textureMatrix,u=a.uniforms.uTextureTransform;u.set(r.a*h/e,r.b*h/t,r.c*l/e,r.d*l/t,r.tx/e,r.ty/t),u.invert(),a.uniforms.uMapCoord=c.mapCoord,a.uniforms.uClampFrame=c.uClampFrame,a.uniforms.uClampOffset=c.uClampOffset,a.uniforms.uTextureTransform=u,a.uniforms.uSizeAnchor[0]=e,a.uniforms.uSizeAnchor[1]=t,a.uniforms.uSizeAnchor[2]=i,a.uniforms.uSizeAnchor[3]=n,o&&(this.resources.uTexture=o.source,this.resources.uSampler=o.source.style)}}class Jm extends vi{constructor(){super({positions:new Float32Array([0,0,1,0,1,1,0,1]),uvs:new Float32Array([0,0,1,0,1,1,0,1]),indices:new Uint32Array([0,1,2,0,2,3])})}}function eg(s,e){const t=s.anchor.x,r=s.anchor.y;e[0]=-t*s.width,e[1]=-r*s.height,e[2]=(1-t)*s.width,e[3]=-r*s.height,e[4]=(1-t)*s.width,e[5]=(1-r)*s.height,e[6]=-t*s.width,e[7]=(1-r)*s.height}function tg(s,e,t,r){let i=0;const n=s.length/e,o=r.a,a=r.b,h=r.c,l=r.d,c=r.tx,u=r.ty;for(t*=e;i<n;){const f=s[t],d=s[t+1];s[t]=o*f+h*d+c,s[t+1]=a*f+l*d+u,t+=e,i++}}function rg(s,e){const t=s.texture,r=t.frame.width,i=t.frame.height;let n=0,o=0;s.applyAnchorToTexture&&(n=s.anchor.x,o=s.anchor.y),e[0]=e[6]=-n,e[2]=e[4]=1-n,e[1]=e[3]=-o,e[5]=e[7]=1-o;const a=B.shared;a.copyFrom(s._tileTransform.matrix),a.tx/=s.width,a.ty/=s.height,a.invert(),a.scale(s.width/r,s.height/i),tg(e,2,0,a)}const Sr=new Jm;class sg{constructor(){this.canBatch=!0,this.geometry=new vi({indices:Sr.indices.slice(),positions:Sr.positions.slice(),uvs:Sr.uvs.slice()})}destroy(){this.geometry.destroy(),this.shader?.destroy()}}class ig{constructor(e){this._state=be.default2d,this._renderer=e}validateRenderable(e){const t=this._getTilingSpriteData(e),r=t.canBatch;this._updateCanBatch(e);const i=t.canBatch;if(i&&i===r){const{batchableMesh:n}=t;return!n._batcher.checkAndUpdateTexture(n,e.texture)}return r!==i}addRenderable(e,t){const r=this._renderer.renderPipes.batch;this._updateCanBatch(e);const i=this._getTilingSpriteData(e),{geometry:n,canBatch:o}=i;if(o){i.batchableMesh||(i.batchableMesh=new Ui);const a=i.batchableMesh;e.didViewUpdate&&(this._updateBatchableMesh(e),a.geometry=n,a.renderable=e,a.transform=e.groupTransform,a.setTexture(e._texture)),a.roundPixels=this._renderer._roundPixels|e._roundPixels,r.addToBatch(a,t)}else r.break(t),i.shader||(i.shader=new Qm),this.updateRenderable(e),t.add(e)}execute(e){const{shader:t}=this._getTilingSpriteData(e);t.groups[0]=this._renderer.globalUniforms.bindGroup;const r=t.resources.localUniforms.uniforms;r.uTransformMatrix=e.groupTransform,r.uRound=this._renderer._roundPixels|e._roundPixels,Zt(e.groupColorAlpha,r.uColor,0),this._state.blendMode=Wt(e.groupBlendMode,e.texture._source),this._renderer.encoder.draw({geometry:Sr,shader:t,state:this._state})}updateRenderable(e){const t=this._getTilingSpriteData(e),{canBatch:r}=t;if(r){const{batchableMesh:i}=t;e.didViewUpdate&&this._updateBatchableMesh(e),i._batcher.updateElement(i)}else if(e.didViewUpdate){const{shader:i}=t;i.updateUniforms(e.width,e.height,e._tileTransform.matrix,e.anchor.x,e.anchor.y,e.texture)}}_getTilingSpriteData(e){return e._gpuData[this._renderer.uid]||this._initTilingSpriteData(e)}_initTilingSpriteData(e){const t=new sg;return t.renderable=e,e._gpuData[this._renderer.uid]=t,t}_updateBatchableMesh(e){const t=this._getTilingSpriteData(e),{geometry:r}=t,i=e.texture.source.style;i.addressMode!=="repeat"&&(i.addressMode="repeat",i.update()),rg(e,r.uvs),eg(e,r.positions)}destroy(){this._renderer=null}_updateCanBatch(e){const t=this._getTilingSpriteData(e),r=e.texture;let i=!0;return this._renderer.type===ye.WEBGL&&(i=this._renderer.context.supports.nonPowOf2wrapping),t.canBatch=r.textureMatrix.isSimple&&(i||r.source.isPowerOfTwo),t.canBatch}}ig.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"tilingSprite"};const ng={name:"local-uniform-msdf-bit",vertex:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32,
                uRound:f32,
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
        `,main:`
            vColor *= localUniforms.uColor;
            modelMatrix *= localUniforms.uTransformMatrix;
        `,end:`
            if(localUniforms.uRound == 1)
            {
                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);
            }
        `},fragment:{header:`
            struct LocalUniforms {
                uColor:vec4<f32>,
                uTransformMatrix:mat3x3<f32>,
                uDistance: f32
            }

            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;
         `,main:`
            outColor = vec4<f32>(calculateMSDFAlpha(outColor, localUniforms.uColor, localUniforms.uDistance));
        `}},og={name:"local-uniform-msdf-bit",vertex:{header:`
            uniform mat3 uTransformMatrix;
            uniform vec4 uColor;
            uniform float uRound;
        `,main:`
            vColor *= uColor;
            modelMatrix *= uTransformMatrix;
        `,end:`
            if(uRound == 1.)
            {
                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);
            }
        `},fragment:{header:`
            uniform float uDistance;
         `,main:`
            outColor = vec4(calculateMSDFAlpha(outColor, vColor, uDistance));
        `}},ag={name:"msdf-bit",fragment:{header:`
            fn calculateMSDFAlpha(msdfColor:vec4<f32>, shapeColor:vec4<f32>, distance:f32) -> f32 {

                // MSDF
                var median = msdfColor.r + msdfColor.g + msdfColor.b -
                    min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                    max(msdfColor.r, max(msdfColor.g, msdfColor.b));

                // SDF
                median = min(median, msdfColor.a);

                var screenPxDistance = distance * (median - 0.5);
                var alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);
                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                var luma: f32 = dot(shapeColor.rgb, vec3<f32>(0.299, 0.587, 0.114));
                var gamma: f32 = mix(1.0, 1.0 / 2.2, luma);
                var coverage: f32 = pow(shapeColor.a * alpha, gamma);

                return coverage;

            }
        `}},hg={name:"msdf-bit",fragment:{header:`
            float calculateMSDFAlpha(vec4 msdfColor, vec4 shapeColor, float distance) {

                // MSDF
                float median = msdfColor.r + msdfColor.g + msdfColor.b -
                                min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -
                                max(msdfColor.r, max(msdfColor.g, msdfColor.b));

                // SDF
                median = min(median, msdfColor.a);

                float screenPxDistance = distance * (median - 0.5);
                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);

                if (median < 0.01) {
                    alpha = 0.0;
                } else if (median > 0.99) {
                    alpha = 1.0;
                }

                // Gamma correction for coverage-like alpha
                float luma = dot(shapeColor.rgb, vec3(0.299, 0.587, 0.114));
                float gamma = mix(1.0, 1.0 / 2.2, luma);
                float coverage = pow(shapeColor.a * alpha, gamma);

                return coverage;
            }
        `}};let ws,Ps;class lg extends pe{constructor(e){const t=new ne({uColor:{value:new Float32Array([1,1,1,1]),type:"vec4<f32>"},uTransformMatrix:{value:new B,type:"mat3x3<f32>"},uDistance:{value:4,type:"f32"},uRound:{value:0,type:"f32"}});ws??(ws=Yt({name:"sdf-shader",bits:[ci,di(e),ng,ag,qt]})),Ps??(Ps=jt({name:"sdf-shader",bits:[ui,fi(e),og,hg,Kt]})),super({glProgram:Ps,gpuProgram:ws,resources:{localUniforms:t,batchSamplers:pi(e)}})}}class cg extends Gr{destroy(){this.context.customShader&&this.context.customShader.destroy(),super.destroy()}}class ug{constructor(e){this._renderer=e,this._renderer.renderableGC.addManagedHash(this,"_gpuBitmapText")}validateRenderable(e){const t=this._getGpuBitmapText(e);return e._didTextUpdate&&(e._didTextUpdate=!1,this._updateContext(e,t)),this._renderer.renderPipes.graphics.validateRenderable(t)}addRenderable(e,t){const r=this._getGpuBitmapText(e);So(e,r),e._didTextUpdate&&(e._didTextUpdate=!1,this._updateContext(e,r)),this._renderer.renderPipes.graphics.addRenderable(r,t),r.context.customShader&&this._updateDistanceField(e)}updateRenderable(e){const t=this._getGpuBitmapText(e);So(e,t),this._renderer.renderPipes.graphics.updateRenderable(t),t.context.customShader&&this._updateDistanceField(e)}_updateContext(e,t){const{context:r}=t,i=Pf.getFont(e.text,e._style);r.clear(),i.distanceField.type!=="none"&&(r.customShader||(r.customShader=new lg(this._renderer.limits.maxBatchableTextures)));const n=Se.graphemeSegmenter(e.text),o=e._style;let a=i.baseLineOffset;const h=Xa(n,o,i,!0);let l=0;const c=o.padding,u=h.scale;let f=h.width,d=h.height+h.offsetY;o._stroke&&(f+=o._stroke.width/u,d+=o._stroke.width/u),r.translate(-e._anchor._x*f-c,-e._anchor._y*d-c).scale(u,u);const p=i.applyFillAsTint?o._fill.color:16777215;for(let g=0;g<h.lines.length;g++){const m=h.lines[g];for(let _=0;_<m.charPositions.length;_++){const y=n[l++],b=i.chars[y];b?.texture&&r.texture(b.texture,p||"black",Math.round(m.charPositions[_]+b.xOffset),Math.round(a+b.yOffset))}a+=i.lineHeight}}_getGpuBitmapText(e){return e._gpuData[this._renderer.uid]||this.initGpuText(e)}initGpuText(e){const t=new cg;return e._gpuData[this._renderer.uid]=t,this._updateContext(e,t),t}_updateDistanceField(e){const t=this._getGpuBitmapText(e).context,r=e._style.fontFamily,i=re.get(`${r}-bitmap`),{a:n,b:o,c:a,d:h}=e.groupTransform,l=Math.sqrt(n*n+o*o),c=Math.sqrt(a*a+h*h),u=(Math.abs(l)+Math.abs(c))/2,f=i.baseRenderedFontSize/e._style.fontSize,d=u*i.distanceField.range*(1/f);t.customShader.resources.localUniforms.uniforms.uDistance=d}destroy(){this._renderer=null}}ug.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"bitmapText"};function So(s,e){e.groupTransform=s.groupTransform,e.groupColorAlpha=s.groupColorAlpha,e.groupColor=s.groupColor,e.groupBlendMode=s.groupBlendMode,e.globalDisplayStatus=s.globalDisplayStatus,e.groupTransform=s.groupTransform,e.localDisplayStatus=s.localDisplayStatus,e.groupAlpha=s.groupAlpha,e._roundPixels=s._roundPixels}class dg extends Or{constructor(e){super(),this.generatingTexture=!1,this._renderer=e,e.runners.resolutionChange.add(this)}resolutionChange(){const e=this.renderable;e._autoResolution&&e.onViewUpdate()}destroy(){this._renderer.htmlText.returnTexturePromise(this.texturePromise),this.texturePromise=null,this._renderer=null}}function Zs(s,e){const{texture:t,bounds:r}=s;Fo(r,e._anchor,t);const i=e._style._getFinalPadding();r.minX-=i,r.minY-=i,r.maxX-=i,r.maxY-=i}class fg{constructor(e){this._renderer=e}validateRenderable(e){return e._didTextUpdate}addRenderable(e,t){const r=this._getGpuText(e);e._didTextUpdate&&(this._updateGpuText(e).catch(i=>{console.error(i)}),e._didTextUpdate=!1,Zs(r,e)),this._renderer.renderPipes.batch.addToBatch(r,t)}updateRenderable(e){const t=this._getGpuText(e);t._batcher.updateElement(t)}async _updateGpuText(e){e._didTextUpdate=!1;const t=this._getGpuText(e);if(t.generatingTexture)return;t.texturePromise&&(this._renderer.htmlText.returnTexturePromise(t.texturePromise),t.texturePromise=null),t.generatingTexture=!0,e._resolution=e._autoResolution?this._renderer.resolution:e.resolution;const r=this._renderer.htmlText.getTexturePromise(e);t.texturePromise=r,t.texture=await r;const i=e.renderGroup||e.parentRenderGroup;i&&(i.structureDidChange=!0),t.generatingTexture=!1,Zs(t,e)}_getGpuText(e){return e._gpuData[this._renderer.uid]||this.initGpuText(e)}initGpuText(e){const t=new dg(this._renderer);return t.renderable=e,t.transform=e.groupTransform,t.texture=A.EMPTY,t.bounds={minX:0,maxX:1,minY:0,maxY:0},t.roundPixels=this._renderer._roundPixels|e._roundPixels,e._resolution=e._autoResolution?this._renderer.resolution:e.resolution,e._gpuData[this._renderer.uid]=t,t}destroy(){this._renderer=null}}fg.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"htmlText"};function pg(){const{userAgent:s}=$.get().getNavigator();return/^((?!chrome|android).)*safari/i.test(s)}const mg=new se;function Pl(s,e,t,r){const i=mg;i.minX=0,i.minY=0,i.maxX=s.width/r|0,i.maxY=s.height/r|0;const n=te.getOptimalTexture(i.width,i.height,r,!1);return n.source.uploadMethodId="image",n.source.resource=s,n.source.alphaMode="premultiply-alpha-on-upload",n.frame.width=e/r,n.frame.height=t/r,n.source.emit("update",n.source),n.updateUvs(),n}function gg(s,e){const t=e.fontFamily,r=[],i={},n=/font-family:([^;"\s]+)/g,o=s.match(n);function a(h){i[h]||(r.push(h),i[h]=!0)}if(Array.isArray(t))for(let h=0;h<t.length;h++)a(t[h]);else a(t);o&&o.forEach(h=>{const l=h.split(":")[1].trim();a(l)});for(const h in e.tagStyles){const l=e.tagStyles[h].fontFamily;a(l)}return r}async function _g(s){const t=await(await $.get().fetch(s)).blob(),r=new FileReader;return await new Promise((n,o)=>{r.onloadend=()=>n(r.result),r.onerror=o,r.readAsDataURL(t)})}async function wo(s,e){const t=await _g(e);return`@font-face {
        font-family: "${s.fontFamily}";
        src: url('${t}');
        font-weight: ${s.fontWeight};
        font-style: ${s.fontStyle};
    }`}const yr=new Map;async function xg(s,e,t){const r=s.filter(i=>re.has(`${i}-and-url`)).map((i,n)=>{if(!yr.has(i)){const{url:o}=re.get(`${i}-and-url`);n===0?yr.set(i,wo({fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:i},o)):yr.set(i,wo({fontWeight:t.fontWeight,fontStyle:t.fontStyle,fontFamily:i},o))}return yr.get(i)});return(await Promise.all(r)).join(`
`)}function yg(s,e,t,r,i){const{domElement:n,styleElement:o,svgRoot:a}=i;n.innerHTML=`<style>${e.cssStyle}</style><div style='padding:0;'>${s}</div>`,n.setAttribute("style",`transform: scale(${t});transform-origin: top left; display: inline-block`),o.textContent=r;const{width:h,height:l}=i.image;return a.setAttribute("width",h.toString()),a.setAttribute("height",l.toString()),new XMLSerializer().serializeToString(a)}function bg(s,e){const t=Le.getOptimalCanvasAndContext(s.width,s.height,e),{context:r}=t;return r.clearRect(0,0,s.width,s.height),r.drawImage(s,0,0),t}function vg(s,e,t){return new Promise(async r=>{t&&await new Promise(i=>setTimeout(i,100)),s.onload=()=>{r()},s.src=`data:image/svg+xml;charset=utf8,${encodeURIComponent(e)}`,s.crossOrigin="anonymous"})}class Tg{constructor(e){this._renderer=e,this._createCanvas=e.type===ye.WEBGPU}getTexture(e){return this.getTexturePromise(e)}getTexturePromise(e){return this._buildTexturePromise(e)}async _buildTexturePromise(e){const{text:t,style:r,resolution:i,textureStyle:n}=e,o=ee.get(Za),a=gg(t,r),h=await xg(a,r,Ti.defaultTextStyle),l=Yf(t,r,h,o),c=Math.ceil(Math.ceil(Math.max(1,l.width)+r.padding*2)*i),u=Math.ceil(Math.ceil(Math.max(1,l.height)+r.padding*2)*i),f=o.image,d=2;f.width=(c|0)+d,f.height=(u|0)+d;const p=yg(t,r,i,h,o);await vg(f,p,pg()&&a.length>0);const g=f;let m;this._createCanvas&&(m=bg(f,i));const _=Pl(m?m.canvas:g,f.width-d,f.height-d,i);return n&&(_.source.style=n),this._createCanvas&&(this._renderer.texture.initSource(_.source),Le.returnCanvasAndContext(m)),ee.return(o),_}returnTexturePromise(e){e.then(t=>{this._cleanUp(t)}).catch(()=>{F("HTMLTextSystem: Failed to clean texture")})}_cleanUp(e){te.returnTexture(e,!0),e.source.resource=null,e.source.uploadMethodId="unknown"}destroy(){this._renderer=null}}Tg.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"htmlText"};class Sg extends Or{constructor(e){super(),this._renderer=e,e.runners.resolutionChange.add(this)}resolutionChange(){const e=this.renderable;e._autoResolution&&e.onViewUpdate()}destroy(){this._renderer.canvasText.returnTexture(this.texture),this._renderer=null}}class wg{constructor(e){this._renderer=e}validateRenderable(e){return e._didTextUpdate}addRenderable(e,t){const r=this._getGpuText(e);e._didTextUpdate&&(this._updateGpuText(e),e._didTextUpdate=!1),this._renderer.renderPipes.batch.addToBatch(r,t)}updateRenderable(e){const t=this._getGpuText(e);t._batcher.updateElement(t)}_updateGpuText(e){const t=this._getGpuText(e);t.texture&&this._renderer.canvasText.returnTexture(t.texture),e._resolution=e._autoResolution?this._renderer.resolution:e.resolution,t.texture=t.texture=this._renderer.canvasText.getTexture(e),Zs(t,e)}_getGpuText(e){return e._gpuData[this._renderer.uid]||this.initGpuText(e)}initGpuText(e){const t=new Sg(this._renderer);return t.renderable=e,t.transform=e.groupTransform,t.bounds={minX:0,maxX:1,minY:0,maxY:0},t.roundPixels=this._renderer._roundPixels|e._roundPixels,e._gpuData[this._renderer.uid]=t,t}destroy(){this._renderer=null}}wg.extension={type:[x.WebGLPipes,x.WebGPUPipes,x.CanvasPipes],name:"text"};class Pg{constructor(e){this._renderer=e}getTexture(e,t,r,i){typeof e=="string"&&(I("8.0.0","CanvasTextSystem.getTexture: Use object TextOptions instead of separate arguments"),e={text:e,style:r,resolution:t}),e.style instanceof Oe||(e.style=new Oe(e.style)),e.textureStyle instanceof Je||(e.textureStyle=new Je(e.textureStyle)),typeof e.text!="string"&&(e.text=e.text.toString());const{text:n,style:o,textureStyle:a}=e,h=e.resolution??this._renderer.resolution,{frame:l,canvasAndContext:c}=xs.getCanvasAndContext({text:n,style:o,resolution:h}),u=Pl(c.canvas,l.width,l.height,h);if(a&&(u.source.style=a),o.trim&&(l.pad(o.padding),u.frame.copyFrom(l),u.updateUvs()),o.filters){const f=this._applyFilters(u,o.filters);return this.returnTexture(u),xs.returnCanvasAndContext(c),f}return this._renderer.texture.initSource(u._source),xs.returnCanvasAndContext(c),u}returnTexture(e){const t=e.source;t.resource=null,t.uploadMethodId="unknown",t.alphaMode="no-premultiply-alpha",te.returnTexture(e,!0)}renderTextToCanvas(){I("8.10.0","CanvasTextSystem.renderTextToCanvas: no longer supported, use CanvasTextSystem.getTexture instead")}_applyFilters(e,t){const r=this._renderer.renderTarget.renderTarget,i=this._renderer.filter.generateFilteredTexture({texture:e,filters:t});return this._renderer.renderTarget.bind(r,!1),i}destroy(){this._renderer=null}}Pg.extension={type:[x.WebGLSystem,x.WebGPUSystem,x.CanvasSystem],name:"canvasText"};q.add(Rl,kl);export{Mg as A,ug as B,xe as C,Cf as D,Gf as E,Bg as F,Gr as G,Tg as H,Um as M,qm as N,Eu as R,Mu as T,Ag as a,Cu as b,Eg as c,Im as d,q as e,Oa as f,$m as g,Xm as h,Pg as i,wg as j,fg as k,ig as l,Of as m,If as n};
//# sourceMappingURL=pixi-DRyRoF6D.js.map
