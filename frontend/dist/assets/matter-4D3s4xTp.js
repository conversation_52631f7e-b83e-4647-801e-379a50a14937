import{c as Y}from"./pixi-DRyRoF6D.js";var ie={exports:{}};/*!
 * matter-js 0.19.0 by @liabru
 * http://brm.io/matter-js/
 * License MIT
 * 
 * The MIT License (MIT)
 * 
 * Copyright (c) <PERSON> and contributors.
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */(function(ae,ue){(function(D,g){ae.exports=g()})(Y,function(){return function(B){var D={};function g(e){if(D[e])return D[e].exports;var f=D[e]={i:e,l:!1,exports:{}};return B[e].call(f.exports,f,f.exports,g),f.l=!0,f.exports}return g.m=B,g.c=D,g.d=function(e,f,s){g.o(e,f)||Object.defineProperty(e,f,{enumerable:!0,get:s})},g.r=function(e){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},g.t=function(e,f){if(f&1&&(e=g(e)),f&8||f&4&&typeof e=="object"&&e&&e.__esModule)return e;var s=Object.create(null);if(g.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),f&2&&typeof e!="string")for(var r in e)g.d(s,r,function(c){return e[c]}.bind(null,r));return s},g.n=function(e){var f=e&&e.__esModule?function(){return e.default}:function(){return e};return g.d(f,"a",f),f},g.o=function(e,f){return Object.prototype.hasOwnProperty.call(e,f)},g.p="",g(g.s=20)}([function(B,D){var g={};B.exports=g,function(){g._baseDelta=1e3/60,g._nextId=0,g._seed=0,g._nowStartTime=+new Date,g._warnedOnce={},g._decomp=null,g.extend=function(f,s){var r,c;typeof s=="boolean"?(r=2,c=s):(r=1,c=!0);for(var o=r;o<arguments.length;o++){var u=arguments[o];if(u)for(var a in u)c&&u[a]&&u[a].constructor===Object&&(!f[a]||f[a].constructor===Object)?(f[a]=f[a]||{},g.extend(f[a],c,u[a])):f[a]=u[a]}return f},g.clone=function(f,s){return g.extend({},s,f)},g.keys=function(f){if(Object.keys)return Object.keys(f);var s=[];for(var r in f)s.push(r);return s},g.values=function(f){var s=[];if(Object.keys){for(var r=Object.keys(f),c=0;c<r.length;c++)s.push(f[r[c]]);return s}for(var o in f)s.push(f[o]);return s},g.get=function(f,s,r,c){s=s.split(".").slice(r,c);for(var o=0;o<s.length;o+=1)f=f[s[o]];return f},g.set=function(f,s,r,c,o){var u=s.split(".").slice(c,o);return g.get(f,s,0,-1)[u[u.length-1]]=r,r},g.shuffle=function(f){for(var s=f.length-1;s>0;s--){var r=Math.floor(g.random()*(s+1)),c=f[s];f[s]=f[r],f[r]=c}return f},g.choose=function(f){return f[Math.floor(g.random()*f.length)]},g.isElement=function(f){return typeof HTMLElement<"u"?f instanceof HTMLElement:!!(f&&f.nodeType&&f.nodeName)},g.isArray=function(f){return Object.prototype.toString.call(f)==="[object Array]"},g.isFunction=function(f){return typeof f=="function"},g.isPlainObject=function(f){return typeof f=="object"&&f.constructor===Object},g.isString=function(f){return toString.call(f)==="[object String]"},g.clamp=function(f,s,r){return f<s?s:f>r?r:f},g.sign=function(f){return f<0?-1:1},g.now=function(){if(typeof window<"u"&&window.performance){if(window.performance.now)return window.performance.now();if(window.performance.webkitNow)return window.performance.webkitNow()}return Date.now?Date.now():new Date-g._nowStartTime},g.random=function(f,s){return f=typeof f<"u"?f:0,s=typeof s<"u"?s:1,f+e()*(s-f)};var e=function(){return g._seed=(g._seed*9301+49297)%233280,g._seed/233280};g.colorToNumber=function(f){return f=f.replace("#",""),f.length==3&&(f=f.charAt(0)+f.charAt(0)+f.charAt(1)+f.charAt(1)+f.charAt(2)+f.charAt(2)),parseInt(f,16)},g.logLevel=1,g.log=function(){console&&g.logLevel>0&&g.logLevel<=3&&console.log.apply(console,["matter-js:"].concat(Array.prototype.slice.call(arguments)))},g.info=function(){console&&g.logLevel>0&&g.logLevel<=2&&console.info.apply(console,["matter-js:"].concat(Array.prototype.slice.call(arguments)))},g.warn=function(){console&&g.logLevel>0&&g.logLevel<=3&&console.warn.apply(console,["matter-js:"].concat(Array.prototype.slice.call(arguments)))},g.warnOnce=function(){var f=Array.prototype.slice.call(arguments).join(" ");g._warnedOnce[f]||(g.warn(f),g._warnedOnce[f]=!0)},g.deprecated=function(f,s,r){f[s]=g.chain(function(){g.warnOnce("🔅 deprecated 🔅",r)},f[s])},g.nextId=function(){return g._nextId++},g.indexOf=function(f,s){if(f.indexOf)return f.indexOf(s);for(var r=0;r<f.length;r++)if(f[r]===s)return r;return-1},g.map=function(f,s){if(f.map)return f.map(s);for(var r=[],c=0;c<f.length;c+=1)r.push(s(f[c]));return r},g.topologicalSort=function(f){var s=[],r=[],c=[];for(var o in f)!r[o]&&!c[o]&&g._topologicalSort(o,r,c,f,s);return s},g._topologicalSort=function(f,s,r,c,o){var u=c[f]||[];r[f]=!0;for(var a=0;a<u.length;a+=1){var t=u[a];r[t]||s[t]||g._topologicalSort(t,s,r,c,o)}r[f]=!1,s[f]=!0,o.push(f)},g.chain=function(){for(var f=[],s=0;s<arguments.length;s+=1){var r=arguments[s];r._chained?f.push.apply(f,r._chained):f.push(r)}var c=function(){for(var o,u=new Array(arguments.length),a=0,t=arguments.length;a<t;a++)u[a]=arguments[a];for(a=0;a<f.length;a+=1){var n=f[a].apply(o,u);typeof n<"u"&&(o=n)}return o};return c._chained=f,c},g.chainPathBefore=function(f,s,r){return g.set(f,s,g.chain(r,g.get(f,s)))},g.chainPathAfter=function(f,s,r){return g.set(f,s,g.chain(g.get(f,s),r))},g.setDecomp=function(f){g._decomp=f},g.getDecomp=function(){var f=g._decomp;try{!f&&typeof window<"u"&&(f=window.decomp),!f&&typeof Y<"u"&&(f=Y.decomp)}catch{f=null}return f}}()},function(B,D){var g={};B.exports=g,function(){g.create=function(e){var f={min:{x:0,y:0},max:{x:0,y:0}};return e&&g.update(f,e),f},g.update=function(e,f,s){e.min.x=1/0,e.max.x=-1/0,e.min.y=1/0,e.max.y=-1/0;for(var r=0;r<f.length;r++){var c=f[r];c.x>e.max.x&&(e.max.x=c.x),c.x<e.min.x&&(e.min.x=c.x),c.y>e.max.y&&(e.max.y=c.y),c.y<e.min.y&&(e.min.y=c.y)}s&&(s.x>0?e.max.x+=s.x:e.min.x+=s.x,s.y>0?e.max.y+=s.y:e.min.y+=s.y)},g.contains=function(e,f){return f.x>=e.min.x&&f.x<=e.max.x&&f.y>=e.min.y&&f.y<=e.max.y},g.overlaps=function(e,f){return e.min.x<=f.max.x&&e.max.x>=f.min.x&&e.max.y>=f.min.y&&e.min.y<=f.max.y},g.translate=function(e,f){e.min.x+=f.x,e.max.x+=f.x,e.min.y+=f.y,e.max.y+=f.y},g.shift=function(e,f){var s=e.max.x-e.min.x,r=e.max.y-e.min.y;e.min.x=f.x,e.max.x=f.x+s,e.min.y=f.y,e.max.y=f.y+r}}()},function(B,D){var g={};B.exports=g,function(){g.create=function(e,f){return{x:e||0,y:f||0}},g.clone=function(e){return{x:e.x,y:e.y}},g.magnitude=function(e){return Math.sqrt(e.x*e.x+e.y*e.y)},g.magnitudeSquared=function(e){return e.x*e.x+e.y*e.y},g.rotate=function(e,f,s){var r=Math.cos(f),c=Math.sin(f);s||(s={});var o=e.x*r-e.y*c;return s.y=e.x*c+e.y*r,s.x=o,s},g.rotateAbout=function(e,f,s,r){var c=Math.cos(f),o=Math.sin(f);r||(r={});var u=s.x+((e.x-s.x)*c-(e.y-s.y)*o);return r.y=s.y+((e.x-s.x)*o+(e.y-s.y)*c),r.x=u,r},g.normalise=function(e){var f=g.magnitude(e);return f===0?{x:0,y:0}:{x:e.x/f,y:e.y/f}},g.dot=function(e,f){return e.x*f.x+e.y*f.y},g.cross=function(e,f){return e.x*f.y-e.y*f.x},g.cross3=function(e,f,s){return(f.x-e.x)*(s.y-e.y)-(f.y-e.y)*(s.x-e.x)},g.add=function(e,f,s){return s||(s={}),s.x=e.x+f.x,s.y=e.y+f.y,s},g.sub=function(e,f,s){return s||(s={}),s.x=e.x-f.x,s.y=e.y-f.y,s},g.mult=function(e,f){return{x:e.x*f,y:e.y*f}},g.div=function(e,f){return{x:e.x/f,y:e.y/f}},g.perp=function(e,f){return f=f===!0?-1:1,{x:f*-e.y,y:f*e.x}},g.neg=function(e){return{x:-e.x,y:-e.y}},g.angle=function(e,f){return Math.atan2(f.y-e.y,f.x-e.x)},g._temp=[g.create(),g.create(),g.create(),g.create(),g.create(),g.create()]}()},function(B,D,g){var e={};B.exports=e;var f=g(2),s=g(0);(function(){e.create=function(r,c){for(var o=[],u=0;u<r.length;u++){var a=r[u],t={x:a.x,y:a.y,index:u,body:c,isInternal:!1};o.push(t)}return o},e.fromPath=function(r,c){var o=/L?\s*([-\d.e]+)[\s,]*([-\d.e]+)*/ig,u=[];return r.replace(o,function(a,t,n){u.push({x:parseFloat(t),y:parseFloat(n)})}),e.create(u,c)},e.centre=function(r){for(var c=e.area(r,!0),o={x:0,y:0},u,a,t,n=0;n<r.length;n++)t=(n+1)%r.length,u=f.cross(r[n],r[t]),a=f.mult(f.add(r[n],r[t]),u),o=f.add(o,a);return f.div(o,6*c)},e.mean=function(r){for(var c={x:0,y:0},o=0;o<r.length;o++)c.x+=r[o].x,c.y+=r[o].y;return f.div(c,r.length)},e.area=function(r,c){for(var o=0,u=r.length-1,a=0;a<r.length;a++)o+=(r[u].x-r[a].x)*(r[u].y+r[a].y),u=a;return c?o/2:Math.abs(o)/2},e.inertia=function(r,c){for(var o=0,u=0,a=r,t,n,i=0;i<a.length;i++)n=(i+1)%a.length,t=Math.abs(f.cross(a[n],a[i])),o+=t*(f.dot(a[n],a[n])+f.dot(a[n],a[i])+f.dot(a[i],a[i])),u+=t;return c/6*(o/u)},e.translate=function(r,c,o){o=typeof o<"u"?o:1;var u=r.length,a=c.x*o,t=c.y*o,n;for(n=0;n<u;n++)r[n].x+=a,r[n].y+=t;return r},e.rotate=function(r,c,o){if(c!==0){var u=Math.cos(c),a=Math.sin(c),t=o.x,n=o.y,i=r.length,l,x,y,P;for(P=0;P<i;P++)l=r[P],x=l.x-t,y=l.y-n,l.x=t+(x*u-y*a),l.y=n+(x*a+y*u);return r}},e.contains=function(r,c){for(var o=c.x,u=c.y,a=r.length,t=r[a-1],n,i=0;i<a;i++){if(n=r[i],(o-t.x)*(n.y-t.y)+(u-t.y)*(t.x-n.x)>0)return!1;t=n}return!0},e.scale=function(r,c,o,u){if(c===1&&o===1)return r;u=u||e.centre(r);for(var a,t,n=0;n<r.length;n++)a=r[n],t=f.sub(a,u),r[n].x=u.x+t.x*c,r[n].y=u.y+t.y*o;return r},e.chamfer=function(r,c,o,u,a){typeof c=="number"?c=[c]:c=c||[8],o=typeof o<"u"?o:-1,u=u||2,a=a||14;for(var t=[],n=0;n<r.length;n++){var i=r[n-1>=0?n-1:r.length-1],l=r[n],x=r[(n+1)%r.length],y=c[n<c.length?n:c.length-1];if(y===0){t.push(l);continue}var P=f.normalise({x:l.y-i.y,y:i.x-l.x}),C=f.normalise({x:x.y-l.y,y:l.x-x.x}),v=Math.sqrt(2*Math.pow(y,2)),p=f.mult(s.clone(P),y),m=f.normalise(f.mult(f.add(P,C),.5)),h=f.sub(l,f.mult(m,v)),S=o;o===-1&&(S=Math.pow(y,.32)*1.75),S=s.clamp(S,u,a),S%2===1&&(S+=1);for(var d=Math.acos(f.dot(P,C)),M=d/S,w=0;w<S;w++)t.push(f.add(f.rotate(p,M*w),h))}return t},e.clockwiseSort=function(r){var c=e.mean(r);return r.sort(function(o,u){return f.angle(c,o)-f.angle(c,u)}),r},e.isConvex=function(r){var c=0,o=r.length,u,a,t,n;if(o<3)return null;for(u=0;u<o;u++)if(a=(u+1)%o,t=(u+2)%o,n=(r[a].x-r[u].x)*(r[t].y-r[a].y),n-=(r[a].y-r[u].y)*(r[t].x-r[a].x),n<0?c|=1:n>0&&(c|=2),c===3)return!1;return c!==0?!0:null},e.hull=function(r){var c=[],o=[],u,a;for(r=r.slice(0),r.sort(function(t,n){var i=t.x-n.x;return i!==0?i:t.y-n.y}),a=0;a<r.length;a+=1){for(u=r[a];o.length>=2&&f.cross3(o[o.length-2],o[o.length-1],u)<=0;)o.pop();o.push(u)}for(a=r.length-1;a>=0;a-=1){for(u=r[a];c.length>=2&&f.cross3(c[c.length-2],c[c.length-1],u)<=0;)c.pop();c.push(u)}return c.pop(),o.pop(),c.concat(o)}})()},function(B,D,g){var e={};B.exports=e;var f=g(3),s=g(2),r=g(7),c=g(0),o=g(1),u=g(11);(function(){e._timeCorrection=!0,e._inertiaScale=4,e._nextCollidingGroupId=1,e._nextNonCollidingGroupId=-1,e._nextCategory=1,e._baseDelta=1e3/60,e.create=function(t){var n={id:c.nextId(),type:"body",label:"Body",parts:[],plugin:{},angle:0,vertices:f.fromPath("L 0 0 L 40 0 L 40 40 L 0 40"),position:{x:0,y:0},force:{x:0,y:0},torque:0,positionImpulse:{x:0,y:0},constraintImpulse:{x:0,y:0,angle:0},totalContacts:0,speed:0,angularSpeed:0,velocity:{x:0,y:0},angularVelocity:0,isSensor:!1,isStatic:!1,isSleeping:!1,motion:0,sleepThreshold:60,density:.001,restitution:0,friction:.1,frictionStatic:.5,frictionAir:.01,collisionFilter:{category:1,mask:4294967295,group:0},slop:.05,timeScale:1,render:{visible:!0,opacity:1,strokeStyle:null,fillStyle:null,lineWidth:null,sprite:{xScale:1,yScale:1,xOffset:0,yOffset:0}},events:null,bounds:null,chamfer:null,circleRadius:0,positionPrev:null,anglePrev:0,parent:null,axes:null,area:0,mass:0,inertia:0,deltaTime:16.666666666666668,_original:null},i=c.extend(n,t);return a(i,t),i},e.nextGroup=function(t){return t?e._nextNonCollidingGroupId--:e._nextCollidingGroupId++},e.nextCategory=function(){return e._nextCategory=e._nextCategory<<1,e._nextCategory};var a=function(t,n){n=n||{},e.set(t,{bounds:t.bounds||o.create(t.vertices),positionPrev:t.positionPrev||s.clone(t.position),anglePrev:t.anglePrev||t.angle,vertices:t.vertices,parts:t.parts||[t],isStatic:t.isStatic,isSleeping:t.isSleeping,parent:t.parent||t}),f.rotate(t.vertices,t.angle,t.position),u.rotate(t.axes,t.angle),o.update(t.bounds,t.vertices,t.velocity),e.set(t,{axes:n.axes||t.axes,area:n.area||t.area,mass:n.mass||t.mass,inertia:n.inertia||t.inertia});var i=t.isStatic?"#14151f":c.choose(["#f19648","#f5d259","#f55a3c","#063e7b","#ececd1"]),l=t.isStatic?"#555":"#ccc",x=t.isStatic&&t.render.fillStyle===null?1:0;t.render.fillStyle=t.render.fillStyle||i,t.render.strokeStyle=t.render.strokeStyle||l,t.render.lineWidth=t.render.lineWidth||x,t.render.sprite.xOffset+=-(t.bounds.min.x-t.position.x)/(t.bounds.max.x-t.bounds.min.x),t.render.sprite.yOffset+=-(t.bounds.min.y-t.position.y)/(t.bounds.max.y-t.bounds.min.y)};e.set=function(t,n,i){var l;typeof n=="string"&&(l=n,n={},n[l]=i);for(l in n)if(Object.prototype.hasOwnProperty.call(n,l))switch(i=n[l],l){case"isStatic":e.setStatic(t,i);break;case"isSleeping":r.set(t,i);break;case"mass":e.setMass(t,i);break;case"density":e.setDensity(t,i);break;case"inertia":e.setInertia(t,i);break;case"vertices":e.setVertices(t,i);break;case"position":e.setPosition(t,i);break;case"angle":e.setAngle(t,i);break;case"velocity":e.setVelocity(t,i);break;case"angularVelocity":e.setAngularVelocity(t,i);break;case"speed":e.setSpeed(t,i);break;case"angularSpeed":e.setAngularSpeed(t,i);break;case"parts":e.setParts(t,i);break;case"centre":e.setCentre(t,i);break;default:t[l]=i}},e.setStatic=function(t,n){for(var i=0;i<t.parts.length;i++){var l=t.parts[i];l.isStatic=n,n?(l._original={restitution:l.restitution,friction:l.friction,mass:l.mass,inertia:l.inertia,density:l.density,inverseMass:l.inverseMass,inverseInertia:l.inverseInertia},l.restitution=0,l.friction=1,l.mass=l.inertia=l.density=1/0,l.inverseMass=l.inverseInertia=0,l.positionPrev.x=l.position.x,l.positionPrev.y=l.position.y,l.anglePrev=l.angle,l.angularVelocity=0,l.speed=0,l.angularSpeed=0,l.motion=0):l._original&&(l.restitution=l._original.restitution,l.friction=l._original.friction,l.mass=l._original.mass,l.inertia=l._original.inertia,l.density=l._original.density,l.inverseMass=l._original.inverseMass,l.inverseInertia=l._original.inverseInertia,l._original=null)}},e.setMass=function(t,n){var i=t.inertia/(t.mass/6);t.inertia=i*(n/6),t.inverseInertia=1/t.inertia,t.mass=n,t.inverseMass=1/t.mass,t.density=t.mass/t.area},e.setDensity=function(t,n){e.setMass(t,n*t.area),t.density=n},e.setInertia=function(t,n){t.inertia=n,t.inverseInertia=1/t.inertia},e.setVertices=function(t,n){n[0].body===t?t.vertices=n:t.vertices=f.create(n,t),t.axes=u.fromVertices(t.vertices),t.area=f.area(t.vertices),e.setMass(t,t.density*t.area);var i=f.centre(t.vertices);f.translate(t.vertices,i,-1),e.setInertia(t,e._inertiaScale*f.inertia(t.vertices,t.mass)),f.translate(t.vertices,t.position),o.update(t.bounds,t.vertices,t.velocity)},e.setParts=function(t,n,i){var l;for(n=n.slice(0),t.parts.length=0,t.parts.push(t),t.parent=t,l=0;l<n.length;l++){var x=n[l];x!==t&&(x.parent=t,t.parts.push(x))}if(t.parts.length!==1){if(i=typeof i<"u"?i:!0,i){var y=[];for(l=0;l<n.length;l++)y=y.concat(n[l].vertices);f.clockwiseSort(y);var P=f.hull(y),C=f.centre(P);e.setVertices(t,P),f.translate(t.vertices,C)}var v=e._totalProperties(t);t.area=v.area,t.parent=t,t.position.x=v.centre.x,t.position.y=v.centre.y,t.positionPrev.x=v.centre.x,t.positionPrev.y=v.centre.y,e.setMass(t,v.mass),e.setInertia(t,v.inertia),e.setPosition(t,v.centre)}},e.setCentre=function(t,n,i){i?(t.positionPrev.x+=n.x,t.positionPrev.y+=n.y,t.position.x+=n.x,t.position.y+=n.y):(t.positionPrev.x=n.x-(t.position.x-t.positionPrev.x),t.positionPrev.y=n.y-(t.position.y-t.positionPrev.y),t.position.x=n.x,t.position.y=n.y)},e.setPosition=function(t,n,i){var l=s.sub(n,t.position);i?(t.positionPrev.x=t.position.x,t.positionPrev.y=t.position.y,t.velocity.x=l.x,t.velocity.y=l.y,t.speed=s.magnitude(l)):(t.positionPrev.x+=l.x,t.positionPrev.y+=l.y);for(var x=0;x<t.parts.length;x++){var y=t.parts[x];y.position.x+=l.x,y.position.y+=l.y,f.translate(y.vertices,l),o.update(y.bounds,y.vertices,t.velocity)}},e.setAngle=function(t,n,i){var l=n-t.angle;i?(t.anglePrev=t.angle,t.angularVelocity=l,t.angularSpeed=Math.abs(l)):t.anglePrev+=l;for(var x=0;x<t.parts.length;x++){var y=t.parts[x];y.angle+=l,f.rotate(y.vertices,l,t.position),u.rotate(y.axes,l),o.update(y.bounds,y.vertices,t.velocity),x>0&&s.rotateAbout(y.position,l,t.position,y.position)}},e.setVelocity=function(t,n){var i=t.deltaTime/e._baseDelta;t.positionPrev.x=t.position.x-n.x*i,t.positionPrev.y=t.position.y-n.y*i,t.velocity.x=(t.position.x-t.positionPrev.x)/i,t.velocity.y=(t.position.y-t.positionPrev.y)/i,t.speed=s.magnitude(t.velocity)},e.getVelocity=function(t){var n=e._baseDelta/t.deltaTime;return{x:(t.position.x-t.positionPrev.x)*n,y:(t.position.y-t.positionPrev.y)*n}},e.getSpeed=function(t){return s.magnitude(e.getVelocity(t))},e.setSpeed=function(t,n){e.setVelocity(t,s.mult(s.normalise(e.getVelocity(t)),n))},e.setAngularVelocity=function(t,n){var i=t.deltaTime/e._baseDelta;t.anglePrev=t.angle-n*i,t.angularVelocity=(t.angle-t.anglePrev)/i,t.angularSpeed=Math.abs(t.angularVelocity)},e.getAngularVelocity=function(t){return(t.angle-t.anglePrev)*e._baseDelta/t.deltaTime},e.getAngularSpeed=function(t){return Math.abs(e.getAngularVelocity(t))},e.setAngularSpeed=function(t,n){e.setAngularVelocity(t,c.sign(e.getAngularVelocity(t))*n)},e.translate=function(t,n,i){e.setPosition(t,s.add(t.position,n),i)},e.rotate=function(t,n,i,l){if(!i)e.setAngle(t,t.angle+n,l);else{var x=Math.cos(n),y=Math.sin(n),P=t.position.x-i.x,C=t.position.y-i.y;e.setPosition(t,{x:i.x+(P*x-C*y),y:i.y+(P*y+C*x)},l),e.setAngle(t,t.angle+n,l)}},e.scale=function(t,n,i,l){var x=0,y=0;l=l||t.position;for(var P=0;P<t.parts.length;P++){var C=t.parts[P];f.scale(C.vertices,n,i,l),C.axes=u.fromVertices(C.vertices),C.area=f.area(C.vertices),e.setMass(C,t.density*C.area),f.translate(C.vertices,{x:-C.position.x,y:-C.position.y}),e.setInertia(C,e._inertiaScale*f.inertia(C.vertices,C.mass)),f.translate(C.vertices,{x:C.position.x,y:C.position.y}),P>0&&(x+=C.area,y+=C.inertia),C.position.x=l.x+(C.position.x-l.x)*n,C.position.y=l.y+(C.position.y-l.y)*i,o.update(C.bounds,C.vertices,t.velocity)}t.parts.length>1&&(t.area=x,t.isStatic||(e.setMass(t,t.density*x),e.setInertia(t,y))),t.circleRadius&&(n===i?t.circleRadius*=n:t.circleRadius=null)},e.update=function(t,n){n=(typeof n<"u"?n:1e3/60)*t.timeScale;var i=n*n,l=e._timeCorrection?n/(t.deltaTime||n):1,x=1-t.frictionAir*(n/c._baseDelta),y=(t.position.x-t.positionPrev.x)*l,P=(t.position.y-t.positionPrev.y)*l;t.velocity.x=y*x+t.force.x/t.mass*i,t.velocity.y=P*x+t.force.y/t.mass*i,t.positionPrev.x=t.position.x,t.positionPrev.y=t.position.y,t.position.x+=t.velocity.x,t.position.y+=t.velocity.y,t.deltaTime=n,t.angularVelocity=(t.angle-t.anglePrev)*x*l+t.torque/t.inertia*i,t.anglePrev=t.angle,t.angle+=t.angularVelocity;for(var C=0;C<t.parts.length;C++){var v=t.parts[C];f.translate(v.vertices,t.velocity),C>0&&(v.position.x+=t.velocity.x,v.position.y+=t.velocity.y),t.angularVelocity!==0&&(f.rotate(v.vertices,t.angularVelocity,t.position),u.rotate(v.axes,t.angularVelocity),C>0&&s.rotateAbout(v.position,t.angularVelocity,t.position,v.position)),o.update(v.bounds,v.vertices,t.velocity)}},e.updateVelocities=function(t){var n=e._baseDelta/t.deltaTime,i=t.velocity;i.x=(t.position.x-t.positionPrev.x)*n,i.y=(t.position.y-t.positionPrev.y)*n,t.speed=Math.sqrt(i.x*i.x+i.y*i.y),t.angularVelocity=(t.angle-t.anglePrev)*n,t.angularSpeed=Math.abs(t.angularVelocity)},e.applyForce=function(t,n,i){var l={x:n.x-t.position.x,y:n.y-t.position.y};t.force.x+=i.x,t.force.y+=i.y,t.torque+=l.x*i.y-l.y*i.x},e._totalProperties=function(t){for(var n={mass:0,area:0,inertia:0,centre:{x:0,y:0}},i=t.parts.length===1?0:1;i<t.parts.length;i++){var l=t.parts[i],x=l.mass!==1/0?l.mass:1;n.mass+=x,n.area+=l.area,n.inertia+=l.inertia,n.centre=s.add(n.centre,s.mult(l.position,x))}return n.centre=s.div(n.centre,n.mass),n}})()},function(B,D,g){var e={};B.exports=e;var f=g(0);(function(){e.on=function(s,r,c){for(var o=r.split(" "),u,a=0;a<o.length;a++)u=o[a],s.events=s.events||{},s.events[u]=s.events[u]||[],s.events[u].push(c);return c},e.off=function(s,r,c){if(!r){s.events={};return}typeof r=="function"&&(c=r,r=f.keys(s.events).join(" "));for(var o=r.split(" "),u=0;u<o.length;u++){var a=s.events[o[u]],t=[];if(c&&a)for(var n=0;n<a.length;n++)a[n]!==c&&t.push(a[n]);s.events[o[u]]=t}},e.trigger=function(s,r,c){var o,u,a,t,n=s.events;if(n&&f.keys(n).length>0){c||(c={}),o=r.split(" ");for(var i=0;i<o.length;i++)if(u=o[i],a=n[u],a){t=f.clone(c,!1),t.name=u,t.source=s;for(var l=0;l<a.length;l++)a[l].apply(s,[t])}}}})()},function(B,D,g){var e={};B.exports=e;var f=g(5),s=g(0),r=g(1),c=g(4);(function(){e.create=function(o){return s.extend({id:s.nextId(),type:"composite",parent:null,isModified:!1,bodies:[],constraints:[],composites:[],label:"Composite",plugin:{},cache:{allBodies:null,allConstraints:null,allComposites:null}},o)},e.setModified=function(o,u,a,t){if(o.isModified=u,u&&o.cache&&(o.cache.allBodies=null,o.cache.allConstraints=null,o.cache.allComposites=null),a&&o.parent&&e.setModified(o.parent,u,a,t),t)for(var n=0;n<o.composites.length;n++){var i=o.composites[n];e.setModified(i,u,a,t)}},e.add=function(o,u){var a=[].concat(u);f.trigger(o,"beforeAdd",{object:u});for(var t=0;t<a.length;t++){var n=a[t];switch(n.type){case"body":if(n.parent!==n){s.warn("Composite.add: skipped adding a compound body part (you must add its parent instead)");break}e.addBody(o,n);break;case"constraint":e.addConstraint(o,n);break;case"composite":e.addComposite(o,n);break;case"mouseConstraint":e.addConstraint(o,n.constraint);break}}return f.trigger(o,"afterAdd",{object:u}),o},e.remove=function(o,u,a){var t=[].concat(u);f.trigger(o,"beforeRemove",{object:u});for(var n=0;n<t.length;n++){var i=t[n];switch(i.type){case"body":e.removeBody(o,i,a);break;case"constraint":e.removeConstraint(o,i,a);break;case"composite":e.removeComposite(o,i,a);break;case"mouseConstraint":e.removeConstraint(o,i.constraint);break}}return f.trigger(o,"afterRemove",{object:u}),o},e.addComposite=function(o,u){return o.composites.push(u),u.parent=o,e.setModified(o,!0,!0,!1),o},e.removeComposite=function(o,u,a){var t=s.indexOf(o.composites,u);if(t!==-1&&e.removeCompositeAt(o,t),a)for(var n=0;n<o.composites.length;n++)e.removeComposite(o.composites[n],u,!0);return o},e.removeCompositeAt=function(o,u){return o.composites.splice(u,1),e.setModified(o,!0,!0,!1),o},e.addBody=function(o,u){return o.bodies.push(u),e.setModified(o,!0,!0,!1),o},e.removeBody=function(o,u,a){var t=s.indexOf(o.bodies,u);if(t!==-1&&e.removeBodyAt(o,t),a)for(var n=0;n<o.composites.length;n++)e.removeBody(o.composites[n],u,!0);return o},e.removeBodyAt=function(o,u){return o.bodies.splice(u,1),e.setModified(o,!0,!0,!1),o},e.addConstraint=function(o,u){return o.constraints.push(u),e.setModified(o,!0,!0,!1),o},e.removeConstraint=function(o,u,a){var t=s.indexOf(o.constraints,u);if(t!==-1&&e.removeConstraintAt(o,t),a)for(var n=0;n<o.composites.length;n++)e.removeConstraint(o.composites[n],u,!0);return o},e.removeConstraintAt=function(o,u){return o.constraints.splice(u,1),e.setModified(o,!0,!0,!1),o},e.clear=function(o,u,a){if(a)for(var t=0;t<o.composites.length;t++)e.clear(o.composites[t],u,!0);return u?o.bodies=o.bodies.filter(function(n){return n.isStatic}):o.bodies.length=0,o.constraints.length=0,o.composites.length=0,e.setModified(o,!0,!0,!1),o},e.allBodies=function(o){if(o.cache&&o.cache.allBodies)return o.cache.allBodies;for(var u=[].concat(o.bodies),a=0;a<o.composites.length;a++)u=u.concat(e.allBodies(o.composites[a]));return o.cache&&(o.cache.allBodies=u),u},e.allConstraints=function(o){if(o.cache&&o.cache.allConstraints)return o.cache.allConstraints;for(var u=[].concat(o.constraints),a=0;a<o.composites.length;a++)u=u.concat(e.allConstraints(o.composites[a]));return o.cache&&(o.cache.allConstraints=u),u},e.allComposites=function(o){if(o.cache&&o.cache.allComposites)return o.cache.allComposites;for(var u=[].concat(o.composites),a=0;a<o.composites.length;a++)u=u.concat(e.allComposites(o.composites[a]));return o.cache&&(o.cache.allComposites=u),u},e.get=function(o,u,a){var t,n;switch(a){case"body":t=e.allBodies(o);break;case"constraint":t=e.allConstraints(o);break;case"composite":t=e.allComposites(o).concat(o);break}return t?(n=t.filter(function(i){return i.id.toString()===u.toString()}),n.length===0?null:n[0]):null},e.move=function(o,u,a){return e.remove(o,u),e.add(a,u),o},e.rebase=function(o){for(var u=e.allBodies(o).concat(e.allConstraints(o)).concat(e.allComposites(o)),a=0;a<u.length;a++)u[a].id=s.nextId();return o},e.translate=function(o,u,a){for(var t=a?e.allBodies(o):o.bodies,n=0;n<t.length;n++)c.translate(t[n],u);return o},e.rotate=function(o,u,a,t){for(var n=Math.cos(u),i=Math.sin(u),l=t?e.allBodies(o):o.bodies,x=0;x<l.length;x++){var y=l[x],P=y.position.x-a.x,C=y.position.y-a.y;c.setPosition(y,{x:a.x+(P*n-C*i),y:a.y+(P*i+C*n)}),c.rotate(y,u)}return o},e.scale=function(o,u,a,t,n){for(var i=n?e.allBodies(o):o.bodies,l=0;l<i.length;l++){var x=i[l],y=x.position.x-t.x,P=x.position.y-t.y;c.setPosition(x,{x:t.x+y*u,y:t.y+P*a}),c.scale(x,u,a)}return o},e.bounds=function(o){for(var u=e.allBodies(o),a=[],t=0;t<u.length;t+=1){var n=u[t];a.push(n.bounds.min,n.bounds.max)}return r.create(a)}})()},function(B,D,g){var e={};B.exports=e;var f=g(4),s=g(5),r=g(0);(function(){e._motionWakeThreshold=.18,e._motionSleepThreshold=.08,e._minBias=.9,e.update=function(c,o){for(var u=o/r._baseDelta,a=e._motionSleepThreshold,t=0;t<c.length;t++){var n=c[t],i=f.getSpeed(n),l=f.getAngularSpeed(n),x=i*i+l*l;if(n.force.x!==0||n.force.y!==0){e.set(n,!1);continue}var y=Math.min(n.motion,x),P=Math.max(n.motion,x);n.motion=e._minBias*y+(1-e._minBias)*P,n.sleepThreshold>0&&n.motion<a?(n.sleepCounter+=1,n.sleepCounter>=n.sleepThreshold/u&&e.set(n,!0)):n.sleepCounter>0&&(n.sleepCounter-=1)}},e.afterCollisions=function(c){for(var o=e._motionSleepThreshold,u=0;u<c.length;u++){var a=c[u];if(a.isActive){var t=a.collision,n=t.bodyA.parent,i=t.bodyB.parent;if(!(n.isSleeping&&i.isSleeping||n.isStatic||i.isStatic)&&(n.isSleeping||i.isSleeping)){var l=n.isSleeping&&!n.isStatic?n:i,x=l===n?i:n;!l.isStatic&&x.motion>o&&e.set(l,!1)}}}},e.set=function(c,o){var u=c.isSleeping;o?(c.isSleeping=!0,c.sleepCounter=c.sleepThreshold,c.positionImpulse.x=0,c.positionImpulse.y=0,c.positionPrev.x=c.position.x,c.positionPrev.y=c.position.y,c.anglePrev=c.angle,c.speed=0,c.angularSpeed=0,c.motion=0,u||s.trigger(c,"sleepStart")):(c.isSleeping=!1,c.sleepCounter=0,u&&s.trigger(c,"sleepEnd"))}})()},function(B,D,g){var e={};B.exports=e;var f=g(3),s=g(9);(function(){var r=[],c={overlap:0,axis:null},o={overlap:0,axis:null};e.create=function(u,a){return{pair:null,collided:!1,bodyA:u,bodyB:a,parentA:u.parent,parentB:a.parent,depth:0,normal:{x:0,y:0},tangent:{x:0,y:0},penetration:{x:0,y:0},supports:[]}},e.collides=function(u,a,t){if(e._overlapAxes(c,u.vertices,a.vertices,u.axes),c.overlap<=0||(e._overlapAxes(o,a.vertices,u.vertices,a.axes),o.overlap<=0))return null;var n=t&&t.table[s.id(u,a)],i;n?i=n.collision:(i=e.create(u,a),i.collided=!0,i.bodyA=u.id<a.id?u:a,i.bodyB=u.id<a.id?a:u,i.parentA=i.bodyA.parent,i.parentB=i.bodyB.parent),u=i.bodyA,a=i.bodyB;var l;c.overlap<o.overlap?l=c:l=o;var x=i.normal,y=i.supports,P=l.axis,C=P.x,v=P.y;C*(a.position.x-u.position.x)+v*(a.position.y-u.position.y)<0?(x.x=C,x.y=v):(x.x=-C,x.y=-v),i.tangent.x=-x.y,i.tangent.y=x.x,i.depth=l.overlap,i.penetration.x=x.x*i.depth,i.penetration.y=x.y*i.depth;var p=e._findSupports(u,a,x,1),m=0;if(f.contains(u.vertices,p[0])&&(y[m++]=p[0]),f.contains(u.vertices,p[1])&&(y[m++]=p[1]),m<2){var h=e._findSupports(a,u,x,-1);f.contains(a.vertices,h[0])&&(y[m++]=h[0]),m<2&&f.contains(a.vertices,h[1])&&(y[m++]=h[1])}return m===0&&(y[m++]=p[0]),y.length=m,i},e._overlapAxes=function(u,a,t,n){var i=a.length,l=t.length,x=a[0].x,y=a[0].y,P=t[0].x,C=t[0].y,v=n.length,p=Number.MAX_VALUE,m=0,h,S,d,M,w,A;for(w=0;w<v;w++){var T=n[w],I=T.x,L=T.y,E=x*I+y*L,R=P*I+C*L,F=E,O=R;for(A=1;A<i;A+=1)M=a[A].x*I+a[A].y*L,M>F?F=M:M<E&&(E=M);for(A=1;A<l;A+=1)M=t[A].x*I+t[A].y*L,M>O?O=M:M<R&&(R=M);if(S=F-R,d=O-E,h=S<d?S:d,h<p&&(p=h,m=w,h<=0))break}u.axis=n[m],u.overlap=p},e._projectToAxis=function(u,a,t){for(var n=a[0].x*t.x+a[0].y*t.y,i=n,l=1;l<a.length;l+=1){var x=a[l].x*t.x+a[l].y*t.y;x>i?i=x:x<n&&(n=x)}u.min=n,u.max=i},e._findSupports=function(u,a,t,n){var i=a.vertices,l=i.length,x=u.position.x,y=u.position.y,P=t.x*n,C=t.y*n,v=Number.MAX_VALUE,p,m,h,S,d;for(d=0;d<l;d+=1)m=i[d],S=P*(x-m.x)+C*(y-m.y),S<v&&(v=S,p=m);return h=i[(l+p.index-1)%l],v=P*(x-h.x)+C*(y-h.y),m=i[(p.index+1)%l],P*(x-m.x)+C*(y-m.y)<v?(r[0]=p,r[1]=m,r):(r[0]=p,r[1]=h,r)}})()},function(B,D,g){var e={};B.exports=e;var f=g(16);(function(){e.create=function(s,r){var c=s.bodyA,o=s.bodyB,u={id:e.id(c,o),bodyA:c,bodyB:o,collision:s,contacts:[],activeContacts:[],separation:0,isActive:!0,confirmedActive:!0,isSensor:c.isSensor||o.isSensor,timeCreated:r,timeUpdated:r,inverseMass:0,friction:0,frictionStatic:0,restitution:0,slop:0};return e.update(u,s,r),u},e.update=function(s,r,c){var o=s.contacts,u=r.supports,a=s.activeContacts,t=r.parentA,n=r.parentB,i=t.vertices.length;s.isActive=!0,s.timeUpdated=c,s.collision=r,s.separation=r.depth,s.inverseMass=t.inverseMass+n.inverseMass,s.friction=t.friction<n.friction?t.friction:n.friction,s.frictionStatic=t.frictionStatic>n.frictionStatic?t.frictionStatic:n.frictionStatic,s.restitution=t.restitution>n.restitution?t.restitution:n.restitution,s.slop=t.slop>n.slop?t.slop:n.slop,r.pair=s,a.length=0;for(var l=0;l<u.length;l++){var x=u[l],y=x.body===t?x.index:i+x.index,P=o[y];P?a.push(P):a.push(o[y]=f.create(x))}},e.setActive=function(s,r,c){r?(s.isActive=!0,s.timeUpdated=c):(s.isActive=!1,s.activeContacts.length=0)},e.id=function(s,r){return s.id<r.id?"A"+s.id+"B"+r.id:"A"+r.id+"B"+s.id}})()},function(B,D,g){var e={};B.exports=e;var f=g(3),s=g(2),r=g(7),c=g(1),o=g(11),u=g(0);(function(){e._warming=.4,e._torqueDampen=1,e._minLength=1e-6,e.create=function(a){var t=a;t.bodyA&&!t.pointA&&(t.pointA={x:0,y:0}),t.bodyB&&!t.pointB&&(t.pointB={x:0,y:0});var n=t.bodyA?s.add(t.bodyA.position,t.pointA):t.pointA,i=t.bodyB?s.add(t.bodyB.position,t.pointB):t.pointB,l=s.magnitude(s.sub(n,i));t.length=typeof t.length<"u"?t.length:l,t.id=t.id||u.nextId(),t.label=t.label||"Constraint",t.type="constraint",t.stiffness=t.stiffness||(t.length>0?1:.7),t.damping=t.damping||0,t.angularStiffness=t.angularStiffness||0,t.angleA=t.bodyA?t.bodyA.angle:t.angleA,t.angleB=t.bodyB?t.bodyB.angle:t.angleB,t.plugin={};var x={visible:!0,lineWidth:2,strokeStyle:"#ffffff",type:"line",anchors:!0};return t.length===0&&t.stiffness>.1?(x.type="pin",x.anchors=!1):t.stiffness<.9&&(x.type="spring"),t.render=u.extend(x,t.render),t},e.preSolveAll=function(a){for(var t=0;t<a.length;t+=1){var n=a[t],i=n.constraintImpulse;n.isStatic||i.x===0&&i.y===0&&i.angle===0||(n.position.x+=i.x,n.position.y+=i.y,n.angle+=i.angle)}},e.solveAll=function(a,t){for(var n=u.clamp(t/u._baseDelta,0,1),i=0;i<a.length;i+=1){var l=a[i],x=!l.bodyA||l.bodyA&&l.bodyA.isStatic,y=!l.bodyB||l.bodyB&&l.bodyB.isStatic;(x||y)&&e.solve(a[i],n)}for(i=0;i<a.length;i+=1)l=a[i],x=!l.bodyA||l.bodyA&&l.bodyA.isStatic,y=!l.bodyB||l.bodyB&&l.bodyB.isStatic,!x&&!y&&e.solve(a[i],n)},e.solve=function(a,t){var n=a.bodyA,i=a.bodyB,l=a.pointA,x=a.pointB;if(!(!n&&!i)){n&&!n.isStatic&&(s.rotate(l,n.angle-a.angleA,l),a.angleA=n.angle),i&&!i.isStatic&&(s.rotate(x,i.angle-a.angleB,x),a.angleB=i.angle);var y=l,P=x;if(n&&(y=s.add(n.position,l)),i&&(P=s.add(i.position,x)),!(!y||!P)){var C=s.sub(y,P),v=s.magnitude(C);v<e._minLength&&(v=e._minLength);var p=(v-a.length)/v,m=a.stiffness>=1||a.length===0,h=m?a.stiffness*t:a.stiffness*t*t,S=a.damping*t,d=s.mult(C,p*h),M=(n?n.inverseMass:0)+(i?i.inverseMass:0),w=(n?n.inverseInertia:0)+(i?i.inverseInertia:0),A=M+w,T,I,L,E,R;if(S>0){var F=s.create();L=s.div(C,v),R=s.sub(i&&s.sub(i.position,i.positionPrev)||F,n&&s.sub(n.position,n.positionPrev)||F),E=s.dot(L,R)}n&&!n.isStatic&&(I=n.inverseMass/M,n.constraintImpulse.x-=d.x*I,n.constraintImpulse.y-=d.y*I,n.position.x-=d.x*I,n.position.y-=d.y*I,S>0&&(n.positionPrev.x-=S*L.x*E*I,n.positionPrev.y-=S*L.y*E*I),T=s.cross(l,d)/A*e._torqueDampen*n.inverseInertia*(1-a.angularStiffness),n.constraintImpulse.angle-=T,n.angle-=T),i&&!i.isStatic&&(I=i.inverseMass/M,i.constraintImpulse.x+=d.x*I,i.constraintImpulse.y+=d.y*I,i.position.x+=d.x*I,i.position.y+=d.y*I,S>0&&(i.positionPrev.x+=S*L.x*E*I,i.positionPrev.y+=S*L.y*E*I),T=s.cross(x,d)/A*e._torqueDampen*i.inverseInertia*(1-a.angularStiffness),i.constraintImpulse.angle+=T,i.angle+=T)}}},e.postSolveAll=function(a){for(var t=0;t<a.length;t++){var n=a[t],i=n.constraintImpulse;if(!(n.isStatic||i.x===0&&i.y===0&&i.angle===0)){r.set(n,!1);for(var l=0;l<n.parts.length;l++){var x=n.parts[l];f.translate(x.vertices,i),l>0&&(x.position.x+=i.x,x.position.y+=i.y),i.angle!==0&&(f.rotate(x.vertices,i.angle,n.position),o.rotate(x.axes,i.angle),l>0&&s.rotateAbout(x.position,i.angle,n.position,x.position)),c.update(x.bounds,x.vertices,n.velocity)}i.angle*=e._warming,i.x*=e._warming,i.y*=e._warming}}},e.pointAWorld=function(a){return{x:(a.bodyA?a.bodyA.position.x:0)+(a.pointA?a.pointA.x:0),y:(a.bodyA?a.bodyA.position.y:0)+(a.pointA?a.pointA.y:0)}},e.pointBWorld=function(a){return{x:(a.bodyB?a.bodyB.position.x:0)+(a.pointB?a.pointB.x:0),y:(a.bodyB?a.bodyB.position.y:0)+(a.pointB?a.pointB.y:0)}}})()},function(B,D,g){var e={};B.exports=e;var f=g(2),s=g(0);(function(){e.fromVertices=function(r){for(var c={},o=0;o<r.length;o++){var u=(o+1)%r.length,a=f.normalise({x:r[u].y-r[o].y,y:r[o].x-r[u].x}),t=a.y===0?1/0:a.x/a.y;t=t.toFixed(3).toString(),c[t]=a}return s.values(c)},e.rotate=function(r,c){if(c!==0)for(var o=Math.cos(c),u=Math.sin(c),a=0;a<r.length;a++){var t=r[a],n;n=t.x*o-t.y*u,t.y=t.x*u+t.y*o,t.x=n}}})()},function(B,D,g){var e={};B.exports=e;var f=g(3),s=g(0),r=g(4),c=g(1),o=g(2);(function(){e.rectangle=function(u,a,t,n,i){i=i||{};var l={label:"Rectangle Body",position:{x:u,y:a},vertices:f.fromPath("L 0 0 L "+t+" 0 L "+t+" "+n+" L 0 "+n)};if(i.chamfer){var x=i.chamfer;l.vertices=f.chamfer(l.vertices,x.radius,x.quality,x.qualityMin,x.qualityMax),delete i.chamfer}return r.create(s.extend({},l,i))},e.trapezoid=function(u,a,t,n,i,l){l=l||{},i*=.5;var x=(1-i*2)*t,y=t*i,P=y+x,C=P+y,v;i<.5?v="L 0 0 L "+y+" "+-n+" L "+P+" "+-n+" L "+C+" 0":v="L 0 0 L "+P+" "+-n+" L "+C+" 0";var p={label:"Trapezoid Body",position:{x:u,y:a},vertices:f.fromPath(v)};if(l.chamfer){var m=l.chamfer;p.vertices=f.chamfer(p.vertices,m.radius,m.quality,m.qualityMin,m.qualityMax),delete l.chamfer}return r.create(s.extend({},p,l))},e.circle=function(u,a,t,n,i){n=n||{};var l={label:"Circle Body",circleRadius:t};i=i||25;var x=Math.ceil(Math.max(10,Math.min(i,t)));return x%2===1&&(x+=1),e.polygon(u,a,x,t,s.extend({},l,n))},e.polygon=function(u,a,t,n,i){if(i=i||{},t<3)return e.circle(u,a,n,i);for(var l=2*Math.PI/t,x="",y=l*.5,P=0;P<t;P+=1){var C=y+P*l,v=Math.cos(C)*n,p=Math.sin(C)*n;x+="L "+v.toFixed(3)+" "+p.toFixed(3)+" "}var m={label:"Polygon Body",position:{x:u,y:a},vertices:f.fromPath(x)};if(i.chamfer){var h=i.chamfer;m.vertices=f.chamfer(m.vertices,h.radius,h.quality,h.qualityMin,h.qualityMax),delete i.chamfer}return r.create(s.extend({},m,i))},e.fromVertices=function(u,a,t,n,i,l,x,y){var P=s.getDecomp(),C,v,p,m,h,S,d,M,w,A,T;for(C=!!(P&&P.quickDecomp),n=n||{},p=[],i=typeof i<"u"?i:!1,l=typeof l<"u"?l:.01,x=typeof x<"u"?x:10,y=typeof y<"u"?y:.01,s.isArray(t[0])||(t=[t]),A=0;A<t.length;A+=1)if(S=t[A],m=f.isConvex(S),h=!m,h&&!C&&s.warnOnce("Bodies.fromVertices: Install the 'poly-decomp' library and use Common.setDecomp or provide 'decomp' as a global to decompose concave vertices."),m||!C)m?S=f.clockwiseSort(S):S=f.hull(S),p.push({position:{x:u,y:a},vertices:S});else{var I=S.map(function(H){return[H.x,H.y]});P.makeCCW(I),l!==!1&&P.removeCollinearPoints(I,l),y!==!1&&P.removeDuplicatePoints&&P.removeDuplicatePoints(I,y);var L=P.quickDecomp(I);for(d=0;d<L.length;d++){var E=L[d],R=E.map(function(H){return{x:H[0],y:H[1]}});x>0&&f.area(R)<x||p.push({position:f.centre(R),vertices:R})}}for(d=0;d<p.length;d++)p[d]=r.create(s.extend(p[d],n));if(i){var F=5;for(d=0;d<p.length;d++){var O=p[d];for(M=d+1;M<p.length;M++){var G=p[M];if(c.overlaps(O.bounds,G.bounds)){var V=O.vertices,W=G.vertices;for(w=0;w<O.vertices.length;w++)for(T=0;T<G.vertices.length;T++){var k=o.magnitudeSquared(o.sub(V[(w+1)%V.length],W[T])),z=o.magnitudeSquared(o.sub(V[w],W[(T+1)%W.length]));k<F&&z<F&&(V[w].isInternal=!0,W[T].isInternal=!0)}}}}}return p.length>1?(v=r.create(s.extend({parts:p.slice(0)},n)),r.setPosition(v,{x:u,y:a}),v):p[0]}})()},function(B,D,g){var e={};B.exports=e;var f=g(0),s=g(8);(function(){e.create=function(r){var c={bodies:[],pairs:null};return f.extend(c,r)},e.setBodies=function(r,c){r.bodies=c.slice(0)},e.clear=function(r){r.bodies=[]},e.collisions=function(r){var c=[],o=r.pairs,u=r.bodies,a=u.length,t=e.canCollide,n=s.collides,i,l;for(u.sort(e._compareBoundsX),i=0;i<a;i++){var x=u[i],y=x.bounds,P=x.bounds.max.x,C=x.bounds.max.y,v=x.bounds.min.y,p=x.isStatic||x.isSleeping,m=x.parts.length,h=m===1;for(l=i+1;l<a;l++){var S=u[l],d=S.bounds;if(d.min.x>P)break;if(!(C<d.min.y||v>d.max.y)&&!(p&&(S.isStatic||S.isSleeping))&&t(x.collisionFilter,S.collisionFilter)){var M=S.parts.length;if(h&&M===1){var w=n(x,S,o);w&&c.push(w)}else for(var A=m>1?1:0,T=M>1?1:0,I=A;I<m;I++)for(var L=x.parts[I],y=L.bounds,E=T;E<M;E++){var R=S.parts[E],d=R.bounds;if(!(y.min.x>d.max.x||y.max.x<d.min.x||y.max.y<d.min.y||y.min.y>d.max.y)){var w=n(L,R,o);w&&c.push(w)}}}}}return c},e.canCollide=function(r,c){return r.group===c.group&&r.group!==0?r.group>0:(r.mask&c.category)!==0&&(c.mask&r.category)!==0},e._compareBoundsX=function(r,c){return r.bounds.min.x-c.bounds.min.x}})()},function(B,D,g){var e={};B.exports=e;var f=g(0);(function(){e.create=function(s){var r={};return s||f.log("Mouse.create: element was undefined, defaulting to document.body","warn"),r.element=s||document.body,r.absolute={x:0,y:0},r.position={x:0,y:0},r.mousedownPosition={x:0,y:0},r.mouseupPosition={x:0,y:0},r.offset={x:0,y:0},r.scale={x:1,y:1},r.wheelDelta=0,r.button=-1,r.pixelRatio=parseInt(r.element.getAttribute("data-pixel-ratio"),10)||1,r.sourceEvents={mousemove:null,mousedown:null,mouseup:null,mousewheel:null},r.mousemove=function(c){var o=e._getRelativeMousePosition(c,r.element,r.pixelRatio),u=c.changedTouches;u&&(r.button=0,c.preventDefault()),r.absolute.x=o.x,r.absolute.y=o.y,r.position.x=r.absolute.x*r.scale.x+r.offset.x,r.position.y=r.absolute.y*r.scale.y+r.offset.y,r.sourceEvents.mousemove=c},r.mousedown=function(c){var o=e._getRelativeMousePosition(c,r.element,r.pixelRatio),u=c.changedTouches;u?(r.button=0,c.preventDefault()):r.button=c.button,r.absolute.x=o.x,r.absolute.y=o.y,r.position.x=r.absolute.x*r.scale.x+r.offset.x,r.position.y=r.absolute.y*r.scale.y+r.offset.y,r.mousedownPosition.x=r.position.x,r.mousedownPosition.y=r.position.y,r.sourceEvents.mousedown=c},r.mouseup=function(c){var o=e._getRelativeMousePosition(c,r.element,r.pixelRatio),u=c.changedTouches;u&&c.preventDefault(),r.button=-1,r.absolute.x=o.x,r.absolute.y=o.y,r.position.x=r.absolute.x*r.scale.x+r.offset.x,r.position.y=r.absolute.y*r.scale.y+r.offset.y,r.mouseupPosition.x=r.position.x,r.mouseupPosition.y=r.position.y,r.sourceEvents.mouseup=c},r.mousewheel=function(c){r.wheelDelta=Math.max(-1,Math.min(1,c.wheelDelta||-c.detail)),c.preventDefault()},e.setElement(r,r.element),r},e.setElement=function(s,r){s.element=r,r.addEventListener("mousemove",s.mousemove),r.addEventListener("mousedown",s.mousedown),r.addEventListener("mouseup",s.mouseup),r.addEventListener("mousewheel",s.mousewheel),r.addEventListener("DOMMouseScroll",s.mousewheel),r.addEventListener("touchmove",s.mousemove),r.addEventListener("touchstart",s.mousedown),r.addEventListener("touchend",s.mouseup)},e.clearSourceEvents=function(s){s.sourceEvents.mousemove=null,s.sourceEvents.mousedown=null,s.sourceEvents.mouseup=null,s.sourceEvents.mousewheel=null,s.wheelDelta=0},e.setOffset=function(s,r){s.offset.x=r.x,s.offset.y=r.y,s.position.x=s.absolute.x*s.scale.x+s.offset.x,s.position.y=s.absolute.y*s.scale.y+s.offset.y},e.setScale=function(s,r){s.scale.x=r.x,s.scale.y=r.y,s.position.x=s.absolute.x*s.scale.x+s.offset.x,s.position.y=s.absolute.y*s.scale.y+s.offset.y},e._getRelativeMousePosition=function(s,r,c){var o=r.getBoundingClientRect(),u=document.documentElement||document.body.parentNode||document.body,a=window.pageXOffset!==void 0?window.pageXOffset:u.scrollLeft,t=window.pageYOffset!==void 0?window.pageYOffset:u.scrollTop,n=s.changedTouches,i,l;return n?(i=n[0].pageX-o.left-a,l=n[0].pageY-o.top-t):(i=s.pageX-o.left-a,l=s.pageY-o.top-t),{x:i/(r.clientWidth/(r.width||r.clientWidth)*c),y:l/(r.clientHeight/(r.height||r.clientHeight)*c)}}})()},function(B,D,g){var e={};B.exports=e;var f=g(0);(function(){e._registry={},e.register=function(s){if(e.isPlugin(s)||f.warn("Plugin.register:",e.toString(s),"does not implement all required fields."),s.name in e._registry){var r=e._registry[s.name],c=e.versionParse(s.version).number,o=e.versionParse(r.version).number;c>o?(f.warn("Plugin.register:",e.toString(r),"was upgraded to",e.toString(s)),e._registry[s.name]=s):c<o?f.warn("Plugin.register:",e.toString(r),"can not be downgraded to",e.toString(s)):s!==r&&f.warn("Plugin.register:",e.toString(s),"is already registered to different plugin object")}else e._registry[s.name]=s;return s},e.resolve=function(s){return e._registry[e.dependencyParse(s).name]},e.toString=function(s){return typeof s=="string"?s:(s.name||"anonymous")+"@"+(s.version||s.range||"0.0.0")},e.isPlugin=function(s){return s&&s.name&&s.version&&s.install},e.isUsed=function(s,r){return s.used.indexOf(r)>-1},e.isFor=function(s,r){var c=s.for&&e.dependencyParse(s.for);return!s.for||r.name===c.name&&e.versionSatisfies(r.version,c.range)},e.use=function(s,r){if(s.uses=(s.uses||[]).concat(r||[]),s.uses.length===0){f.warn("Plugin.use:",e.toString(s),"does not specify any dependencies to install.");return}for(var c=e.dependencies(s),o=f.topologicalSort(c),u=[],a=0;a<o.length;a+=1)if(o[a]!==s.name){var t=e.resolve(o[a]);if(!t){u.push("❌ "+o[a]);continue}e.isUsed(s,t.name)||(e.isFor(t,s)||(f.warn("Plugin.use:",e.toString(t),"is for",t.for,"but installed on",e.toString(s)+"."),t._warned=!0),t.install?t.install(s):(f.warn("Plugin.use:",e.toString(t),"does not specify an install function."),t._warned=!0),t._warned?(u.push("🔶 "+e.toString(t)),delete t._warned):u.push("✅ "+e.toString(t)),s.used.push(t.name))}u.length>0&&f.info(u.join("  "))},e.dependencies=function(s,r){var c=e.dependencyParse(s),o=c.name;if(r=r||{},!(o in r)){s=e.resolve(s)||s,r[o]=f.map(s.uses||[],function(a){e.isPlugin(a)&&e.register(a);var t=e.dependencyParse(a),n=e.resolve(a);return n&&!e.versionSatisfies(n.version,t.range)?(f.warn("Plugin.dependencies:",e.toString(n),"does not satisfy",e.toString(t),"used by",e.toString(c)+"."),n._warned=!0,s._warned=!0):n||(f.warn("Plugin.dependencies:",e.toString(a),"used by",e.toString(c),"could not be resolved."),s._warned=!0),t.name});for(var u=0;u<r[o].length;u+=1)e.dependencies(r[o][u],r);return r}},e.dependencyParse=function(s){if(f.isString(s)){var r=/^[\w-]+(@(\*|[\^~]?\d+\.\d+\.\d+(-[0-9A-Za-z-+]+)?))?$/;return r.test(s)||f.warn("Plugin.dependencyParse:",s,"is not a valid dependency string."),{name:s.split("@")[0],range:s.split("@")[1]||"*"}}return{name:s.name,range:s.range||s.version}},e.versionParse=function(s){var r=/^(\*)|(\^|~|>=|>)?\s*((\d+)\.(\d+)\.(\d+))(-[0-9A-Za-z-+]+)?$/;r.test(s)||f.warn("Plugin.versionParse:",s,"is not a valid version or range.");var c=r.exec(s),o=Number(c[4]),u=Number(c[5]),a=Number(c[6]);return{isRange:!!(c[1]||c[2]),version:c[3],range:s,operator:c[1]||c[2]||"",major:o,minor:u,patch:a,parts:[o,u,a],prerelease:c[7],number:o*1e8+u*1e4+a}},e.versionSatisfies=function(s,r){r=r||"*";var c=e.versionParse(r),o=e.versionParse(s);if(c.isRange){if(c.operator==="*"||s==="*")return!0;if(c.operator===">")return o.number>c.number;if(c.operator===">=")return o.number>=c.number;if(c.operator==="~")return o.major===c.major&&o.minor===c.minor&&o.patch>=c.patch;if(c.operator==="^")return c.major>0?o.major===c.major&&o.number>=c.number:c.minor>0?o.minor===c.minor&&o.patch>=c.patch:o.patch===c.patch}return s===r||s==="*"}})()},function(B,D){var g={};B.exports=g,function(){g.create=function(e){return{vertex:e,normalImpulse:0,tangentImpulse:0}}}()},function(B,D,g){var e={};B.exports=e;var f=g(7),s=g(18),r=g(13),c=g(19),o=g(5),u=g(6),a=g(10),t=g(0),n=g(4);(function(){e.create=function(i){i=i||{};var l={positionIterations:6,velocityIterations:4,constraintIterations:2,enableSleeping:!1,events:[],plugin:{},gravity:{x:0,y:1,scale:.001},timing:{timestamp:0,timeScale:1,lastDelta:0,lastElapsed:0}},x=t.extend(l,i);return x.world=i.world||u.create({label:"World"}),x.pairs=i.pairs||c.create(),x.detector=i.detector||r.create(),x.grid={buckets:[]},x.world.gravity=x.gravity,x.broadphase=x.grid,x.metrics={},x},e.update=function(i,l){var x=t.now(),y=i.world,P=i.detector,C=i.pairs,v=i.timing,p=v.timestamp,m;l=typeof l<"u"?l:t._baseDelta,l*=v.timeScale,v.timestamp+=l,v.lastDelta=l;var h={timestamp:v.timestamp,delta:l};o.trigger(i,"beforeUpdate",h);var S=u.allBodies(y),d=u.allConstraints(y);for(y.isModified&&(r.setBodies(P,S),u.setModified(y,!1,!1,!0)),i.enableSleeping&&f.update(S,l),e._bodiesApplyGravity(S,i.gravity),l>0&&e._bodiesUpdate(S,l),a.preSolveAll(S),m=0;m<i.constraintIterations;m++)a.solveAll(d,l);a.postSolveAll(S),P.pairs=i.pairs;var M=r.collisions(P);c.update(C,M,p),i.enableSleeping&&f.afterCollisions(C.list),C.collisionStart.length>0&&o.trigger(i,"collisionStart",{pairs:C.collisionStart});var w=t.clamp(20/i.positionIterations,0,1);for(s.preSolvePosition(C.list),m=0;m<i.positionIterations;m++)s.solvePosition(C.list,l,w);for(s.postSolvePosition(S),a.preSolveAll(S),m=0;m<i.constraintIterations;m++)a.solveAll(d,l);for(a.postSolveAll(S),s.preSolveVelocity(C.list),m=0;m<i.velocityIterations;m++)s.solveVelocity(C.list,l);return e._bodiesUpdateVelocities(S),C.collisionActive.length>0&&o.trigger(i,"collisionActive",{pairs:C.collisionActive}),C.collisionEnd.length>0&&o.trigger(i,"collisionEnd",{pairs:C.collisionEnd}),e._bodiesClearForces(S),o.trigger(i,"afterUpdate",h),i.timing.lastElapsed=t.now()-x,i},e.merge=function(i,l){if(t.extend(i,l),l.world){i.world=l.world,e.clear(i);for(var x=u.allBodies(i.world),y=0;y<x.length;y++){var P=x[y];f.set(P,!1),P.id=t.nextId()}}},e.clear=function(i){c.clear(i.pairs),r.clear(i.detector)},e._bodiesClearForces=function(i){for(var l=i.length,x=0;x<l;x++){var y=i[x];y.force.x=0,y.force.y=0,y.torque=0}},e._bodiesApplyGravity=function(i,l){var x=typeof l.scale<"u"?l.scale:.001,y=i.length;if(!(l.x===0&&l.y===0||x===0))for(var P=0;P<y;P++){var C=i[P];C.isStatic||C.isSleeping||(C.force.y+=C.mass*l.y*x,C.force.x+=C.mass*l.x*x)}},e._bodiesUpdate=function(i,l){for(var x=i.length,y=0;y<x;y++){var P=i[y];P.isStatic||P.isSleeping||n.update(P,l)}},e._bodiesUpdateVelocities=function(i){for(var l=i.length,x=0;x<l;x++)n.updateVelocities(i[x])}})()},function(B,D,g){var e={};B.exports=e;var f=g(3),s=g(0),r=g(1);(function(){e._restingThresh=2,e._restingThreshTangent=Math.sqrt(6),e._positionDampen=.9,e._positionWarming=.8,e._frictionNormalMultiplier=5,e._frictionMaxStatic=Number.MAX_VALUE,e.preSolvePosition=function(c){var o,u,a,t=c.length;for(o=0;o<t;o++)u=c[o],u.isActive&&(a=u.activeContacts.length,u.collision.parentA.totalContacts+=a,u.collision.parentB.totalContacts+=a)},e.solvePosition=function(c,o,u){var a,t,n,i,l,x,y,P,C=e._positionDampen*(u||1),v=s.clamp(o/s._baseDelta,0,1),p=c.length;for(a=0;a<p;a++)t=c[a],!(!t.isActive||t.isSensor)&&(n=t.collision,i=n.parentA,l=n.parentB,x=n.normal,t.separation=x.x*(l.positionImpulse.x+n.penetration.x-i.positionImpulse.x)+x.y*(l.positionImpulse.y+n.penetration.y-i.positionImpulse.y));for(a=0;a<p;a++)t=c[a],!(!t.isActive||t.isSensor)&&(n=t.collision,i=n.parentA,l=n.parentB,x=n.normal,P=t.separation-t.slop*v,(i.isStatic||l.isStatic)&&(P*=2),i.isStatic||i.isSleeping||(y=C/i.totalContacts,i.positionImpulse.x+=x.x*P*y,i.positionImpulse.y+=x.y*P*y),l.isStatic||l.isSleeping||(y=C/l.totalContacts,l.positionImpulse.x-=x.x*P*y,l.positionImpulse.y-=x.y*P*y))},e.postSolvePosition=function(c){for(var o=e._positionWarming,u=c.length,a=f.translate,t=r.update,n=0;n<u;n++){var i=c[n],l=i.positionImpulse,x=l.x,y=l.y,P=i.velocity;if(i.totalContacts=0,x!==0||y!==0){for(var C=0;C<i.parts.length;C++){var v=i.parts[C];a(v.vertices,l),t(v.bounds,v.vertices,P),v.position.x+=x,v.position.y+=y}i.positionPrev.x+=x,i.positionPrev.y+=y,x*P.x+y*P.y<0?(l.x=0,l.y=0):(l.x*=o,l.y*=o)}}},e.preSolveVelocity=function(c){var o=c.length,u,a;for(u=0;u<o;u++){var t=c[u];if(!(!t.isActive||t.isSensor)){var n=t.activeContacts,i=n.length,l=t.collision,x=l.parentA,y=l.parentB,P=l.normal,C=l.tangent;for(a=0;a<i;a++){var v=n[a],p=v.vertex,m=v.normalImpulse,h=v.tangentImpulse;if(m!==0||h!==0){var S=P.x*m+C.x*h,d=P.y*m+C.y*h;x.isStatic||x.isSleeping||(x.positionPrev.x+=S*x.inverseMass,x.positionPrev.y+=d*x.inverseMass,x.anglePrev+=x.inverseInertia*((p.x-x.position.x)*d-(p.y-x.position.y)*S)),y.isStatic||y.isSleeping||(y.positionPrev.x-=S*y.inverseMass,y.positionPrev.y-=d*y.inverseMass,y.anglePrev-=y.inverseInertia*((p.x-y.position.x)*d-(p.y-y.position.y)*S))}}}}},e.solveVelocity=function(c,o){var u=o/s._baseDelta,a=u*u,t=a*u,n=-e._restingThresh*u,i=e._restingThreshTangent,l=e._frictionNormalMultiplier*u,x=e._frictionMaxStatic,y=c.length,P,C,v,p;for(v=0;v<y;v++){var m=c[v];if(!(!m.isActive||m.isSensor)){var h=m.collision,S=h.parentA,d=h.parentB,M=S.velocity,w=d.velocity,A=h.normal.x,T=h.normal.y,I=h.tangent.x,L=h.tangent.y,E=m.activeContacts,R=E.length,F=1/R,O=S.inverseMass+d.inverseMass,G=m.friction*m.frictionStatic*l;for(M.x=S.position.x-S.positionPrev.x,M.y=S.position.y-S.positionPrev.y,w.x=d.position.x-d.positionPrev.x,w.y=d.position.y-d.positionPrev.y,S.angularVelocity=S.angle-S.anglePrev,d.angularVelocity=d.angle-d.anglePrev,p=0;p<R;p++){var V=E[p],W=V.vertex,k=W.x-S.position.x,z=W.y-S.position.y,H=W.x-d.position.x,N=W.y-d.position.y,U=M.x-z*S.angularVelocity,re=M.y+k*S.angularVelocity,se=w.x-N*d.angularVelocity,oe=w.y+H*d.angularVelocity,j=U-se,q=re-oe,J=A*j+T*q,Q=I*j+L*q,b=m.separation+J,X=Math.min(b,1);X=b<0?0:X;var _=X*G;Q<-_||Q>_?(C=Q>0?Q:-Q,P=m.friction*(Q>0?1:-1)*t,P<-C?P=-C:P>C&&(P=C)):(P=Q,C=x);var ee=k*T-z*A,te=H*T-N*A,ne=F/(O+S.inverseInertia*ee*ee+d.inverseInertia*te*te),Z=(1+m.restitution)*J*ne;if(P*=ne,J<n)V.normalImpulse=0;else{var le=V.normalImpulse;V.normalImpulse+=Z,V.normalImpulse>0&&(V.normalImpulse=0),Z=V.normalImpulse-le}if(Q<-i||Q>i)V.tangentImpulse=0;else{var fe=V.tangentImpulse;V.tangentImpulse+=P,V.tangentImpulse<-C&&(V.tangentImpulse=-C),V.tangentImpulse>C&&(V.tangentImpulse=C),P=V.tangentImpulse-fe}var $=A*Z+I*P,K=T*Z+L*P;S.isStatic||S.isSleeping||(S.positionPrev.x+=$*S.inverseMass,S.positionPrev.y+=K*S.inverseMass,S.anglePrev+=(k*K-z*$)*S.inverseInertia),d.isStatic||d.isSleeping||(d.positionPrev.x-=$*d.inverseMass,d.positionPrev.y-=K*d.inverseMass,d.anglePrev-=(H*K-N*$)*d.inverseInertia)}}}}})()},function(B,D,g){var e={};B.exports=e;var f=g(9),s=g(0);(function(){e.create=function(r){return s.extend({table:{},list:[],collisionStart:[],collisionActive:[],collisionEnd:[]},r)},e.update=function(r,c,o){var u=r.list,a=u.length,t=r.table,n=c.length,i=r.collisionStart,l=r.collisionEnd,x=r.collisionActive,y,P,C,v;for(i.length=0,l.length=0,x.length=0,v=0;v<a;v++)u[v].confirmedActive=!1;for(v=0;v<n;v++)y=c[v],C=y.pair,C?(C.isActive?x.push(C):i.push(C),f.update(C,y,o),C.confirmedActive=!0):(C=f.create(y,o),t[C.id]=C,i.push(C),u.push(C));var p=[];for(a=u.length,v=0;v<a;v++)C=u[v],C.confirmedActive||(f.setActive(C,!1,o),l.push(C),!C.collision.bodyA.isSleeping&&!C.collision.bodyB.isSleeping&&p.push(v));for(v=0;v<p.length;v++)P=p[v]-v,C=u[P],u.splice(P,1),delete t[C.id]},e.clear=function(r){return r.table={},r.list.length=0,r.collisionStart.length=0,r.collisionActive.length=0,r.collisionEnd.length=0,r}})()},function(B,D,g){var e=B.exports=g(21);e.Axes=g(11),e.Bodies=g(12),e.Body=g(4),e.Bounds=g(1),e.Collision=g(8),e.Common=g(0),e.Composite=g(6),e.Composites=g(22),e.Constraint=g(10),e.Contact=g(16),e.Detector=g(13),e.Engine=g(17),e.Events=g(5),e.Grid=g(23),e.Mouse=g(14),e.MouseConstraint=g(24),e.Pair=g(9),e.Pairs=g(19),e.Plugin=g(15),e.Query=g(25),e.Render=g(26),e.Resolver=g(18),e.Runner=g(27),e.SAT=g(28),e.Sleeping=g(7),e.Svg=g(29),e.Vector=g(2),e.Vertices=g(3),e.World=g(30),e.Engine.run=e.Runner.run,e.Common.deprecated(e.Engine,"run","Engine.run ➤ use Matter.Runner.run(engine) instead")},function(B,D,g){var e={};B.exports=e;var f=g(15),s=g(0);(function(){e.name="matter-js",e.version="0.19.0",e.uses=[],e.used=[],e.use=function(){f.use(e,Array.prototype.slice.call(arguments))},e.before=function(r,c){return r=r.replace(/^Matter./,""),s.chainPathBefore(e,r,c)},e.after=function(r,c){return r=r.replace(/^Matter./,""),s.chainPathAfter(e,r,c)}})()},function(B,D,g){var e={};B.exports=e;var f=g(6),s=g(10),r=g(0),c=g(4),o=g(12),u=r.deprecated;(function(){e.stack=function(a,t,n,i,l,x,y){for(var P=f.create({label:"Stack"}),C=a,v=t,p,m=0,h=0;h<i;h++){for(var S=0,d=0;d<n;d++){var M=y(C,v,d,h,p,m);if(M){var w=M.bounds.max.y-M.bounds.min.y,A=M.bounds.max.x-M.bounds.min.x;w>S&&(S=w),c.translate(M,{x:A*.5,y:w*.5}),C=M.bounds.max.x+l,f.addBody(P,M),p=M,m+=1}else C+=l}v+=S+x,C=a}return P},e.chain=function(a,t,n,i,l,x){for(var y=a.bodies,P=1;P<y.length;P++){var C=y[P-1],v=y[P],p=C.bounds.max.y-C.bounds.min.y,m=C.bounds.max.x-C.bounds.min.x,h=v.bounds.max.y-v.bounds.min.y,S=v.bounds.max.x-v.bounds.min.x,d={bodyA:C,pointA:{x:m*t,y:p*n},bodyB:v,pointB:{x:S*i,y:h*l}},M=r.extend(d,x);f.addConstraint(a,s.create(M))}return a.label+=" Chain",a},e.mesh=function(a,t,n,i,l){var x=a.bodies,y,P,C,v,p;for(y=0;y<n;y++){for(P=1;P<t;P++)C=x[P-1+y*t],v=x[P+y*t],f.addConstraint(a,s.create(r.extend({bodyA:C,bodyB:v},l)));if(y>0)for(P=0;P<t;P++)C=x[P+(y-1)*t],v=x[P+y*t],f.addConstraint(a,s.create(r.extend({bodyA:C,bodyB:v},l))),i&&P>0&&(p=x[P-1+(y-1)*t],f.addConstraint(a,s.create(r.extend({bodyA:p,bodyB:v},l)))),i&&P<t-1&&(p=x[P+1+(y-1)*t],f.addConstraint(a,s.create(r.extend({bodyA:p,bodyB:v},l))))}return a.label+=" Mesh",a},e.pyramid=function(a,t,n,i,l,x,y){return e.stack(a,t,n,i,l,x,function(P,C,v,p,m,h){var S=Math.min(i,Math.ceil(n/2)),d=m?m.bounds.max.x-m.bounds.min.x:0;if(!(p>S)){p=S-p;var M=p,w=n-1-p;if(!(v<M||v>w)){h===1&&c.translate(m,{x:(v+(n%2===1?1:-1))*d,y:0});var A=m?v*d:0;return y(a+A+v*l,C,v,p,m,h)}}})},e.newtonsCradle=function(a,t,n,i,l){for(var x=f.create({label:"Newtons Cradle"}),y=0;y<n;y++){var P=1.9,C=o.circle(a+y*(i*P),t+l,i,{inertia:1/0,restitution:1,friction:0,frictionAir:1e-4,slop:1}),v=s.create({pointA:{x:a+y*(i*P),y:t},bodyB:C});f.addBody(x,C),f.addConstraint(x,v)}return x},u(e,"newtonsCradle","Composites.newtonsCradle ➤ moved to newtonsCradle example"),e.car=function(a,t,n,i,l){var x=c.nextGroup(!0),y=20,P=-n*.5+y,C=n*.5-y,v=0,p=f.create({label:"Car"}),m=o.rectangle(a,t,n,i,{collisionFilter:{group:x},chamfer:{radius:i*.5},density:2e-4}),h=o.circle(a+P,t+v,l,{collisionFilter:{group:x},friction:.8}),S=o.circle(a+C,t+v,l,{collisionFilter:{group:x},friction:.8}),d=s.create({bodyB:m,pointB:{x:P,y:v},bodyA:h,stiffness:1,length:0}),M=s.create({bodyB:m,pointB:{x:C,y:v},bodyA:S,stiffness:1,length:0});return f.addBody(p,m),f.addBody(p,h),f.addBody(p,S),f.addConstraint(p,d),f.addConstraint(p,M),p},u(e,"car","Composites.car ➤ moved to car example"),e.softBody=function(a,t,n,i,l,x,y,P,C,v){C=r.extend({inertia:1/0},C),v=r.extend({stiffness:.2,render:{type:"line",anchors:!1}},v);var p=e.stack(a,t,n,i,l,x,function(m,h){return o.circle(m,h,P,C)});return e.mesh(p,n,i,y,v),p.label="Soft Body",p},u(e,"softBody","Composites.softBody ➤ moved to softBody and cloth examples")})()},function(B,D,g){var e={};B.exports=e;var f=g(9),s=g(0),r=s.deprecated;(function(){e.create=function(c){var o={buckets:{},pairs:{},pairsList:[],bucketWidth:48,bucketHeight:48};return s.extend(o,c)},e.update=function(c,o,u,a){var t,n,i,l=u.world,x=c.buckets,y,P,C=!1;for(t=0;t<o.length;t++){var v=o[t];if(!(v.isSleeping&&!a)&&!(l.bounds&&(v.bounds.max.x<l.bounds.min.x||v.bounds.min.x>l.bounds.max.x||v.bounds.max.y<l.bounds.min.y||v.bounds.min.y>l.bounds.max.y))){var p=e._getRegion(c,v);if(!v.region||p.id!==v.region.id||a){(!v.region||a)&&(v.region=p);var m=e._regionUnion(p,v.region);for(n=m.startCol;n<=m.endCol;n++)for(i=m.startRow;i<=m.endRow;i++){P=e._getBucketId(n,i),y=x[P];var h=n>=p.startCol&&n<=p.endCol&&i>=p.startRow&&i<=p.endRow,S=n>=v.region.startCol&&n<=v.region.endCol&&i>=v.region.startRow&&i<=v.region.endRow;!h&&S&&S&&y&&e._bucketRemoveBody(c,y,v),(v.region===p||h&&!S||a)&&(y||(y=e._createBucket(x,P)),e._bucketAddBody(c,y,v))}v.region=p,C=!0}}}C&&(c.pairsList=e._createActivePairsList(c))},r(e,"update","Grid.update ➤ replaced by Matter.Detector"),e.clear=function(c){c.buckets={},c.pairs={},c.pairsList=[]},r(e,"clear","Grid.clear ➤ replaced by Matter.Detector"),e._regionUnion=function(c,o){var u=Math.min(c.startCol,o.startCol),a=Math.max(c.endCol,o.endCol),t=Math.min(c.startRow,o.startRow),n=Math.max(c.endRow,o.endRow);return e._createRegion(u,a,t,n)},e._getRegion=function(c,o){var u=o.bounds,a=Math.floor(u.min.x/c.bucketWidth),t=Math.floor(u.max.x/c.bucketWidth),n=Math.floor(u.min.y/c.bucketHeight),i=Math.floor(u.max.y/c.bucketHeight);return e._createRegion(a,t,n,i)},e._createRegion=function(c,o,u,a){return{id:c+","+o+","+u+","+a,startCol:c,endCol:o,startRow:u,endRow:a}},e._getBucketId=function(c,o){return"C"+c+"R"+o},e._createBucket=function(c,o){var u=c[o]=[];return u},e._bucketAddBody=function(c,o,u){var a=c.pairs,t=f.id,n=o.length,i;for(i=0;i<n;i++){var l=o[i];if(!(u.id===l.id||u.isStatic&&l.isStatic)){var x=t(u,l),y=a[x];y?y[2]+=1:a[x]=[u,l,1]}}o.push(u)},e._bucketRemoveBody=function(c,o,u){var a=c.pairs,t=f.id,n;o.splice(s.indexOf(o,u),1);var i=o.length;for(n=0;n<i;n++){var l=a[t(u,o[n])];l&&(l[2]-=1)}},e._createActivePairsList=function(c){var o,u=c.pairs,a=s.keys(u),t=a.length,n=[],i;for(i=0;i<t;i++)o=u[a[i]],o[2]>0?n.push(o):delete u[a[i]];return n}})()},function(B,D,g){var e={};B.exports=e;var f=g(3),s=g(7),r=g(14),c=g(5),o=g(13),u=g(10),a=g(6),t=g(0),n=g(1);(function(){e.create=function(i,l){var x=(i?i.mouse:null)||(l?l.mouse:null);x||(i&&i.render&&i.render.canvas?x=r.create(i.render.canvas):l&&l.element?x=r.create(l.element):(x=r.create(),t.warn("MouseConstraint.create: options.mouse was undefined, options.element was undefined, may not function as expected")));var y=u.create({label:"Mouse Constraint",pointA:x.position,pointB:{x:0,y:0},length:.01,stiffness:.1,angularStiffness:1,render:{strokeStyle:"#90EE90",lineWidth:3}}),P={type:"mouseConstraint",mouse:x,element:null,body:null,constraint:y,collisionFilter:{category:1,mask:4294967295,group:0}},C=t.extend(P,l);return c.on(i,"beforeUpdate",function(){var v=a.allBodies(i.world);e.update(C,v),e._triggerEvents(C)}),C},e.update=function(i,l){var x=i.mouse,y=i.constraint,P=i.body;if(x.button===0){if(y.bodyB)s.set(y.bodyB,!1),y.pointA=x.position;else for(var C=0;C<l.length;C++)if(P=l[C],n.contains(P.bounds,x.position)&&o.canCollide(P.collisionFilter,i.collisionFilter))for(var v=P.parts.length>1?1:0;v<P.parts.length;v++){var p=P.parts[v];if(f.contains(p.vertices,x.position)){y.pointA=x.position,y.bodyB=i.body=P,y.pointB={x:x.position.x-P.position.x,y:x.position.y-P.position.y},y.angleB=P.angle,s.set(P,!1),c.trigger(i,"startdrag",{mouse:x,body:P});break}}}else y.bodyB=i.body=null,y.pointB=null,P&&c.trigger(i,"enddrag",{mouse:x,body:P})},e._triggerEvents=function(i){var l=i.mouse,x=l.sourceEvents;x.mousemove&&c.trigger(i,"mousemove",{mouse:l}),x.mousedown&&c.trigger(i,"mousedown",{mouse:l}),x.mouseup&&c.trigger(i,"mouseup",{mouse:l}),r.clearSourceEvents(l)}})()},function(B,D,g){var e={};B.exports=e;var f=g(2),s=g(8),r=g(1),c=g(12),o=g(3);(function(){e.collides=function(u,a){for(var t=[],n=a.length,i=u.bounds,l=s.collides,x=r.overlaps,y=0;y<n;y++){var P=a[y],C=P.parts.length,v=C===1?0:1;if(x(P.bounds,i))for(var p=v;p<C;p++){var m=P.parts[p];if(x(m.bounds,i)){var h=l(m,u);if(h){t.push(h);break}}}}return t},e.ray=function(u,a,t,n){n=n||1e-100;for(var i=f.angle(a,t),l=f.magnitude(f.sub(a,t)),x=(t.x+a.x)*.5,y=(t.y+a.y)*.5,P=c.rectangle(x,y,l,n,{angle:i}),C=e.collides(P,u),v=0;v<C.length;v+=1){var p=C[v];p.body=p.bodyB=p.bodyA}return C},e.region=function(u,a,t){for(var n=[],i=0;i<u.length;i++){var l=u[i],x=r.overlaps(l.bounds,a);(x&&!t||!x&&t)&&n.push(l)}return n},e.point=function(u,a){for(var t=[],n=0;n<u.length;n++){var i=u[n];if(r.contains(i.bounds,a))for(var l=i.parts.length===1?0:1;l<i.parts.length;l++){var x=i.parts[l];if(r.contains(x.bounds,a)&&o.contains(x.vertices,a)){t.push(i);break}}}return t}})()},function(B,D,g){var e={};B.exports=e;var f=g(4),s=g(0),r=g(6),c=g(1),o=g(5),u=g(2),a=g(14);(function(){var t,n;typeof window<"u"&&(t=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(v){window.setTimeout(function(){v(s.now())},1e3/60)},n=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.webkitCancelAnimationFrame||window.msCancelAnimationFrame),e._goodFps=30,e._goodDelta=1e3/60,e.create=function(v){var p={engine:null,element:null,canvas:null,mouse:null,frameRequestId:null,timing:{historySize:60,delta:0,deltaHistory:[],lastTime:0,lastTimestamp:0,lastElapsed:0,timestampElapsed:0,timestampElapsedHistory:[],engineDeltaHistory:[],engineElapsedHistory:[],elapsedHistory:[]},options:{width:800,height:600,pixelRatio:1,background:"#14151f",wireframeBackground:"#14151f",hasBounds:!!v.bounds,enabled:!0,wireframes:!0,showSleeping:!0,showDebug:!1,showStats:!1,showPerformance:!1,showBounds:!1,showVelocity:!1,showCollisions:!1,showSeparations:!1,showAxes:!1,showPositions:!1,showAngleIndicator:!1,showIds:!1,showVertexNumbers:!1,showConvexHulls:!1,showInternalEdges:!1,showMousePosition:!1}},m=s.extend(p,v);return m.canvas&&(m.canvas.width=m.options.width||m.canvas.width,m.canvas.height=m.options.height||m.canvas.height),m.mouse=v.mouse,m.engine=v.engine,m.canvas=m.canvas||x(m.options.width,m.options.height),m.context=m.canvas.getContext("2d"),m.textures={},m.bounds=m.bounds||{min:{x:0,y:0},max:{x:m.canvas.width,y:m.canvas.height}},m.controller=e,m.options.showBroadphase=!1,m.options.pixelRatio!==1&&e.setPixelRatio(m,m.options.pixelRatio),s.isElement(m.element)&&m.element.appendChild(m.canvas),m},e.run=function(v){(function p(m){v.frameRequestId=t(p),i(v,m),e.world(v,m),(v.options.showStats||v.options.showDebug)&&e.stats(v,v.context,m),(v.options.showPerformance||v.options.showDebug)&&e.performance(v,v.context,m)})()},e.stop=function(v){n(v.frameRequestId)},e.setPixelRatio=function(v,p){var m=v.options,h=v.canvas;p==="auto"&&(p=y(h)),m.pixelRatio=p,h.setAttribute("data-pixel-ratio",p),h.width=m.width*p,h.height=m.height*p,h.style.width=m.width+"px",h.style.height=m.height+"px"},e.lookAt=function(v,p,m,h){h=typeof h<"u"?h:!0,p=s.isArray(p)?p:[p],m=m||{x:0,y:0};for(var S={min:{x:1/0,y:1/0},max:{x:-1/0,y:-1/0}},d=0;d<p.length;d+=1){var M=p[d],w=M.bounds?M.bounds.min:M.min||M.position||M,A=M.bounds?M.bounds.max:M.max||M.position||M;w&&A&&(w.x<S.min.x&&(S.min.x=w.x),A.x>S.max.x&&(S.max.x=A.x),w.y<S.min.y&&(S.min.y=w.y),A.y>S.max.y&&(S.max.y=A.y))}var T=S.max.x-S.min.x+2*m.x,I=S.max.y-S.min.y+2*m.y,L=v.canvas.height,E=v.canvas.width,R=E/L,F=T/I,O=1,G=1;F>R?G=F/R:O=R/F,v.options.hasBounds=!0,v.bounds.min.x=S.min.x,v.bounds.max.x=S.min.x+T*O,v.bounds.min.y=S.min.y,v.bounds.max.y=S.min.y+I*G,h&&(v.bounds.min.x+=T*.5-T*O*.5,v.bounds.max.x+=T*.5-T*O*.5,v.bounds.min.y+=I*.5-I*G*.5,v.bounds.max.y+=I*.5-I*G*.5),v.bounds.min.x-=m.x,v.bounds.max.x-=m.x,v.bounds.min.y-=m.y,v.bounds.max.y-=m.y,v.mouse&&(a.setScale(v.mouse,{x:(v.bounds.max.x-v.bounds.min.x)/v.canvas.width,y:(v.bounds.max.y-v.bounds.min.y)/v.canvas.height}),a.setOffset(v.mouse,v.bounds.min))},e.startViewTransform=function(v){var p=v.bounds.max.x-v.bounds.min.x,m=v.bounds.max.y-v.bounds.min.y,h=p/v.options.width,S=m/v.options.height;v.context.setTransform(v.options.pixelRatio/h,0,0,v.options.pixelRatio/S,0,0),v.context.translate(-v.bounds.min.x,-v.bounds.min.y)},e.endViewTransform=function(v){v.context.setTransform(v.options.pixelRatio,0,0,v.options.pixelRatio,0,0)},e.world=function(v,p){var m=s.now(),h=v.engine,S=h.world,d=v.canvas,M=v.context,w=v.options,A=v.timing,T=r.allBodies(S),I=r.allConstraints(S),L=w.wireframes?w.wireframeBackground:w.background,E=[],R=[],F,O={timestamp:h.timing.timestamp};if(o.trigger(v,"beforeRender",O),v.currentBackground!==L&&C(v,L),M.globalCompositeOperation="source-in",M.fillStyle="transparent",M.fillRect(0,0,d.width,d.height),M.globalCompositeOperation="source-over",w.hasBounds){for(F=0;F<T.length;F++){var G=T[F];c.overlaps(G.bounds,v.bounds)&&E.push(G)}for(F=0;F<I.length;F++){var V=I[F],W=V.bodyA,k=V.bodyB,z=V.pointA,H=V.pointB;W&&(z=u.add(W.position,V.pointA)),k&&(H=u.add(k.position,V.pointB)),!(!z||!H)&&(c.contains(v.bounds,z)||c.contains(v.bounds,H))&&R.push(V)}e.startViewTransform(v),v.mouse&&(a.setScale(v.mouse,{x:(v.bounds.max.x-v.bounds.min.x)/v.options.width,y:(v.bounds.max.y-v.bounds.min.y)/v.options.height}),a.setOffset(v.mouse,v.bounds.min))}else R=I,E=T,v.options.pixelRatio!==1&&v.context.setTransform(v.options.pixelRatio,0,0,v.options.pixelRatio,0,0);!w.wireframes||h.enableSleeping&&w.showSleeping?e.bodies(v,E,M):(w.showConvexHulls&&e.bodyConvexHulls(v,E,M),e.bodyWireframes(v,E,M)),w.showBounds&&e.bodyBounds(v,E,M),(w.showAxes||w.showAngleIndicator)&&e.bodyAxes(v,E,M),w.showPositions&&e.bodyPositions(v,E,M),w.showVelocity&&e.bodyVelocity(v,E,M),w.showIds&&e.bodyIds(v,E,M),w.showSeparations&&e.separations(v,h.pairs.list,M),w.showCollisions&&e.collisions(v,h.pairs.list,M),w.showVertexNumbers&&e.vertexNumbers(v,E,M),w.showMousePosition&&e.mousePosition(v,v.mouse,M),e.constraints(R,M),w.hasBounds&&e.endViewTransform(v),o.trigger(v,"afterRender",O),A.lastElapsed=s.now()-m},e.stats=function(v,p,m){for(var h=v.engine,S=h.world,d=r.allBodies(S),M=0,w=55,A=44,T=0,I=0,L=0;L<d.length;L+=1)M+=d[L].parts.length;var E={Part:M,Body:d.length,Cons:r.allConstraints(S).length,Comp:r.allComposites(S).length,Pair:h.pairs.list.length};p.fillStyle="#0e0f19",p.fillRect(T,I,w*5.5,A),p.font="12px Arial",p.textBaseline="top",p.textAlign="right";for(var R in E){var F=E[R];p.fillStyle="#aaa",p.fillText(R,T+w,I+8),p.fillStyle="#eee",p.fillText(F,T+w,I+26),T+=w}},e.performance=function(v,p){var m=v.engine,h=v.timing,S=h.deltaHistory,d=h.elapsedHistory,M=h.timestampElapsedHistory,w=h.engineDeltaHistory,A=h.engineElapsedHistory,T=m.timing.lastDelta,I=l(S),L=l(d),E=l(w),R=l(A),F=l(M),O=F/I||0,G=1e3/I||0,V=4,W=12,k=60,z=34,H=10,N=69;p.fillStyle="#0e0f19",p.fillRect(0,50,W*4+k*5+22,z),e.status(p,H,N,k,V,S.length,Math.round(G)+" fps",G/e._goodFps,function(U){return S[U]/I-1}),e.status(p,H+W+k,N,k,V,w.length,T.toFixed(2)+" dt",e._goodDelta/T,function(U){return w[U]/E-1}),e.status(p,H+(W+k)*2,N,k,V,A.length,R.toFixed(2)+" ut",1-R/e._goodFps,function(U){return A[U]/R-1}),e.status(p,H+(W+k)*3,N,k,V,d.length,L.toFixed(2)+" rt",1-L/e._goodFps,function(U){return d[U]/L-1}),e.status(p,H+(W+k)*4,N,k,V,M.length,O.toFixed(2)+" x",O*O*O,function(U){return(M[U]/S[U]/O||0)-1})},e.status=function(v,p,m,h,S,d,M,w,A){v.strokeStyle="#888",v.fillStyle="#444",v.lineWidth=1,v.fillRect(p,m+7,h,1),v.beginPath(),v.moveTo(p,m+7-S*s.clamp(.4*A(0),-2,2));for(var T=0;T<h;T+=1)v.lineTo(p+T,m+7-(T<d?S*s.clamp(.4*A(T),-2,2):0));v.stroke(),v.fillStyle="hsl("+s.clamp(25+95*w,0,120)+",100%,60%)",v.fillRect(p,m-7,4,4),v.font="12px Arial",v.textBaseline="middle",v.textAlign="right",v.fillStyle="#eee",v.fillText(M,p+h,m-5)},e.constraints=function(v,p){for(var m=p,h=0;h<v.length;h++){var S=v[h];if(!(!S.render.visible||!S.pointA||!S.pointB)){var d=S.bodyA,M=S.bodyB,w,A;if(d?w=u.add(d.position,S.pointA):w=S.pointA,S.render.type==="pin")m.beginPath(),m.arc(w.x,w.y,3,0,2*Math.PI),m.closePath();else{if(M?A=u.add(M.position,S.pointB):A=S.pointB,m.beginPath(),m.moveTo(w.x,w.y),S.render.type==="spring")for(var T=u.sub(A,w),I=u.perp(u.normalise(T)),L=Math.ceil(s.clamp(S.length/5,12,20)),E,R=1;R<L;R+=1)E=R%2===0?1:-1,m.lineTo(w.x+T.x*(R/L)+I.x*E*4,w.y+T.y*(R/L)+I.y*E*4);m.lineTo(A.x,A.y)}S.render.lineWidth&&(m.lineWidth=S.render.lineWidth,m.strokeStyle=S.render.strokeStyle,m.stroke()),S.render.anchors&&(m.fillStyle=S.render.strokeStyle,m.beginPath(),m.arc(w.x,w.y,3,0,2*Math.PI),m.arc(A.x,A.y,3,0,2*Math.PI),m.closePath(),m.fill())}}},e.bodies=function(v,p,m){var h=m;v.engine;var S=v.options,d=S.showInternalEdges||!S.wireframes,M,w,A,T;for(A=0;A<p.length;A++)if(M=p[A],!!M.render.visible){for(T=M.parts.length>1?1:0;T<M.parts.length;T++)if(w=M.parts[T],!!w.render.visible){if(S.showSleeping&&M.isSleeping?h.globalAlpha=.5*w.render.opacity:w.render.opacity!==1&&(h.globalAlpha=w.render.opacity),w.render.sprite&&w.render.sprite.texture&&!S.wireframes){var I=w.render.sprite,L=P(v,I.texture);h.translate(w.position.x,w.position.y),h.rotate(w.angle),h.drawImage(L,L.width*-I.xOffset*I.xScale,L.height*-I.yOffset*I.yScale,L.width*I.xScale,L.height*I.yScale),h.rotate(-w.angle),h.translate(-w.position.x,-w.position.y)}else{if(w.circleRadius)h.beginPath(),h.arc(w.position.x,w.position.y,w.circleRadius,0,2*Math.PI);else{h.beginPath(),h.moveTo(w.vertices[0].x,w.vertices[0].y);for(var E=1;E<w.vertices.length;E++)!w.vertices[E-1].isInternal||d?h.lineTo(w.vertices[E].x,w.vertices[E].y):h.moveTo(w.vertices[E].x,w.vertices[E].y),w.vertices[E].isInternal&&!d&&h.moveTo(w.vertices[(E+1)%w.vertices.length].x,w.vertices[(E+1)%w.vertices.length].y);h.lineTo(w.vertices[0].x,w.vertices[0].y),h.closePath()}S.wireframes?(h.lineWidth=1,h.strokeStyle="#bbb",h.stroke()):(h.fillStyle=w.render.fillStyle,w.render.lineWidth&&(h.lineWidth=w.render.lineWidth,h.strokeStyle=w.render.strokeStyle,h.stroke()),h.fill())}h.globalAlpha=1}}},e.bodyWireframes=function(v,p,m){var h=m,S=v.options.showInternalEdges,d,M,w,A,T;for(h.beginPath(),w=0;w<p.length;w++)if(d=p[w],!!d.render.visible)for(T=d.parts.length>1?1:0;T<d.parts.length;T++){for(M=d.parts[T],h.moveTo(M.vertices[0].x,M.vertices[0].y),A=1;A<M.vertices.length;A++)!M.vertices[A-1].isInternal||S?h.lineTo(M.vertices[A].x,M.vertices[A].y):h.moveTo(M.vertices[A].x,M.vertices[A].y),M.vertices[A].isInternal&&!S&&h.moveTo(M.vertices[(A+1)%M.vertices.length].x,M.vertices[(A+1)%M.vertices.length].y);h.lineTo(M.vertices[0].x,M.vertices[0].y)}h.lineWidth=1,h.strokeStyle="#bbb",h.stroke()},e.bodyConvexHulls=function(v,p,m){var h=m,S,d,M;for(h.beginPath(),d=0;d<p.length;d++)if(S=p[d],!(!S.render.visible||S.parts.length===1)){for(h.moveTo(S.vertices[0].x,S.vertices[0].y),M=1;M<S.vertices.length;M++)h.lineTo(S.vertices[M].x,S.vertices[M].y);h.lineTo(S.vertices[0].x,S.vertices[0].y)}h.lineWidth=1,h.strokeStyle="rgba(255,255,255,0.2)",h.stroke()},e.vertexNumbers=function(v,p,m){var h=m,S,d,M;for(S=0;S<p.length;S++){var w=p[S].parts;for(M=w.length>1?1:0;M<w.length;M++){var A=w[M];for(d=0;d<A.vertices.length;d++)h.fillStyle="rgba(255,255,255,0.2)",h.fillText(S+"_"+d,A.position.x+(A.vertices[d].x-A.position.x)*.8,A.position.y+(A.vertices[d].y-A.position.y)*.8)}}},e.mousePosition=function(v,p,m){var h=m;h.fillStyle="rgba(255,255,255,0.8)",h.fillText(p.position.x+"  "+p.position.y,p.position.x+5,p.position.y-5)},e.bodyBounds=function(v,p,m){var h=m;v.engine;var S=v.options;h.beginPath();for(var d=0;d<p.length;d++){var M=p[d];if(M.render.visible)for(var w=p[d].parts,A=w.length>1?1:0;A<w.length;A++){var T=w[A];h.rect(T.bounds.min.x,T.bounds.min.y,T.bounds.max.x-T.bounds.min.x,T.bounds.max.y-T.bounds.min.y)}}S.wireframes?h.strokeStyle="rgba(255,255,255,0.08)":h.strokeStyle="rgba(0,0,0,0.1)",h.lineWidth=1,h.stroke()},e.bodyAxes=function(v,p,m){var h=m;v.engine;var S=v.options,d,M,w,A;for(h.beginPath(),M=0;M<p.length;M++){var T=p[M],I=T.parts;if(T.render.visible)if(S.showAxes)for(w=I.length>1?1:0;w<I.length;w++)for(d=I[w],A=0;A<d.axes.length;A++){var L=d.axes[A];h.moveTo(d.position.x,d.position.y),h.lineTo(d.position.x+L.x*20,d.position.y+L.y*20)}else for(w=I.length>1?1:0;w<I.length;w++)for(d=I[w],A=0;A<d.axes.length;A++)h.moveTo(d.position.x,d.position.y),h.lineTo((d.vertices[0].x+d.vertices[d.vertices.length-1].x)/2,(d.vertices[0].y+d.vertices[d.vertices.length-1].y)/2)}S.wireframes?(h.strokeStyle="indianred",h.lineWidth=1):(h.strokeStyle="rgba(255, 255, 255, 0.4)",h.globalCompositeOperation="overlay",h.lineWidth=2),h.stroke(),h.globalCompositeOperation="source-over"},e.bodyPositions=function(v,p,m){var h=m;v.engine;var S=v.options,d,M,w,A;for(h.beginPath(),w=0;w<p.length;w++)if(d=p[w],!!d.render.visible)for(A=0;A<d.parts.length;A++)M=d.parts[A],h.arc(M.position.x,M.position.y,3,0,2*Math.PI,!1),h.closePath();for(S.wireframes?h.fillStyle="indianred":h.fillStyle="rgba(0,0,0,0.5)",h.fill(),h.beginPath(),w=0;w<p.length;w++)d=p[w],d.render.visible&&(h.arc(d.positionPrev.x,d.positionPrev.y,2,0,2*Math.PI,!1),h.closePath());h.fillStyle="rgba(255,165,0,0.8)",h.fill()},e.bodyVelocity=function(v,p,m){var h=m;h.beginPath();for(var S=0;S<p.length;S++){var d=p[S];if(d.render.visible){var M=f.getVelocity(d);h.moveTo(d.position.x,d.position.y),h.lineTo(d.position.x+M.x,d.position.y+M.y)}}h.lineWidth=3,h.strokeStyle="cornflowerblue",h.stroke()},e.bodyIds=function(v,p,m){var h=m,S,d;for(S=0;S<p.length;S++)if(p[S].render.visible){var M=p[S].parts;for(d=M.length>1?1:0;d<M.length;d++){var w=M[d];h.font="12px Arial",h.fillStyle="rgba(255,255,255,0.5)",h.fillText(w.id,w.position.x+10,w.position.y-10)}}},e.collisions=function(v,p,m){var h=m,S=v.options,d,M,w,A;for(h.beginPath(),w=0;w<p.length;w++)if(d=p[w],!!d.isActive)for(M=d.collision,A=0;A<d.activeContacts.length;A++){var T=d.activeContacts[A],I=T.vertex;h.rect(I.x-1.5,I.y-1.5,3.5,3.5)}for(S.wireframes?h.fillStyle="rgba(255,255,255,0.7)":h.fillStyle="orange",h.fill(),h.beginPath(),w=0;w<p.length;w++)if(d=p[w],!!d.isActive&&(M=d.collision,d.activeContacts.length>0)){var L=d.activeContacts[0].vertex.x,E=d.activeContacts[0].vertex.y;d.activeContacts.length===2&&(L=(d.activeContacts[0].vertex.x+d.activeContacts[1].vertex.x)/2,E=(d.activeContacts[0].vertex.y+d.activeContacts[1].vertex.y)/2),M.bodyB===M.supports[0].body||M.bodyA.isStatic===!0?h.moveTo(L-M.normal.x*8,E-M.normal.y*8):h.moveTo(L+M.normal.x*8,E+M.normal.y*8),h.lineTo(L,E)}S.wireframes?h.strokeStyle="rgba(255,165,0,0.7)":h.strokeStyle="orange",h.lineWidth=1,h.stroke()},e.separations=function(v,p,m){var h=m,S=v.options,d,M,w,A,T;for(h.beginPath(),T=0;T<p.length;T++)if(d=p[T],!!d.isActive){M=d.collision,w=M.bodyA,A=M.bodyB;var I=1;!A.isStatic&&!w.isStatic&&(I=.5),A.isStatic&&(I=0),h.moveTo(A.position.x,A.position.y),h.lineTo(A.position.x-M.penetration.x*I,A.position.y-M.penetration.y*I),I=1,!A.isStatic&&!w.isStatic&&(I=.5),w.isStatic&&(I=0),h.moveTo(w.position.x,w.position.y),h.lineTo(w.position.x+M.penetration.x*I,w.position.y+M.penetration.y*I)}S.wireframes?h.strokeStyle="rgba(255,165,0,0.5)":h.strokeStyle="orange",h.stroke()},e.inspector=function(v,p){v.engine;var m=v.selected,h=v.render,S=h.options,d;if(S.hasBounds){var M=h.bounds.max.x-h.bounds.min.x,w=h.bounds.max.y-h.bounds.min.y,A=M/h.options.width,T=w/h.options.height;p.scale(1/A,1/T),p.translate(-h.bounds.min.x,-h.bounds.min.y)}for(var I=0;I<m.length;I++){var L=m[I].data;switch(p.translate(.5,.5),p.lineWidth=1,p.strokeStyle="rgba(255,165,0,0.9)",p.setLineDash([1,2]),L.type){case"body":d=L.bounds,p.beginPath(),p.rect(Math.floor(d.min.x-3),Math.floor(d.min.y-3),Math.floor(d.max.x-d.min.x+6),Math.floor(d.max.y-d.min.y+6)),p.closePath(),p.stroke();break;case"constraint":var E=L.pointA;L.bodyA&&(E=L.pointB),p.beginPath(),p.arc(E.x,E.y,10,0,2*Math.PI),p.closePath(),p.stroke();break}p.setLineDash([]),p.translate(-.5,-.5)}v.selectStart!==null&&(p.translate(.5,.5),p.lineWidth=1,p.strokeStyle="rgba(255,165,0,0.6)",p.fillStyle="rgba(255,165,0,0.1)",d=v.selectBounds,p.beginPath(),p.rect(Math.floor(d.min.x),Math.floor(d.min.y),Math.floor(d.max.x-d.min.x),Math.floor(d.max.y-d.min.y)),p.closePath(),p.stroke(),p.fill(),p.translate(-.5,-.5)),S.hasBounds&&p.setTransform(1,0,0,1,0,0)};var i=function(v,p){var m=v.engine,h=v.timing,S=h.historySize,d=m.timing.timestamp;h.delta=p-h.lastTime||e._goodDelta,h.lastTime=p,h.timestampElapsed=d-h.lastTimestamp||0,h.lastTimestamp=d,h.deltaHistory.unshift(h.delta),h.deltaHistory.length=Math.min(h.deltaHistory.length,S),h.engineDeltaHistory.unshift(m.timing.lastDelta),h.engineDeltaHistory.length=Math.min(h.engineDeltaHistory.length,S),h.timestampElapsedHistory.unshift(h.timestampElapsed),h.timestampElapsedHistory.length=Math.min(h.timestampElapsedHistory.length,S),h.engineElapsedHistory.unshift(m.timing.lastElapsed),h.engineElapsedHistory.length=Math.min(h.engineElapsedHistory.length,S),h.elapsedHistory.unshift(h.lastElapsed),h.elapsedHistory.length=Math.min(h.elapsedHistory.length,S)},l=function(v){for(var p=0,m=0;m<v.length;m+=1)p+=v[m];return p/v.length||0},x=function(v,p){var m=document.createElement("canvas");return m.width=v,m.height=p,m.oncontextmenu=function(){return!1},m.onselectstart=function(){return!1},m},y=function(v){var p=v.getContext("2d"),m=window.devicePixelRatio||1,h=p.webkitBackingStorePixelRatio||p.mozBackingStorePixelRatio||p.msBackingStorePixelRatio||p.oBackingStorePixelRatio||p.backingStorePixelRatio||1;return m/h},P=function(v,p){var m=v.textures[p];return m||(m=v.textures[p]=new Image,m.src=p,m)},C=function(v,p){var m=p;/(jpg|gif|png)$/.test(p)&&(m="url("+p+")"),v.canvas.style.background=m,v.canvas.style.backgroundSize="contain",v.currentBackground=p}})()},function(B,D,g){var e={};B.exports=e;var f=g(5),s=g(17),r=g(0);(function(){var c,o;if(typeof window<"u"&&(c=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame,o=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.webkitCancelAnimationFrame||window.msCancelAnimationFrame),!c){var u;c=function(a){u=setTimeout(function(){a(r.now())},1e3/60)},o=function(){clearTimeout(u)}}e.create=function(a){var t={fps:60,deltaSampleSize:60,counterTimestamp:0,frameCounter:0,deltaHistory:[],timePrev:null,frameRequestId:null,isFixed:!1,enabled:!0},n=r.extend(t,a);return n.delta=n.delta||1e3/n.fps,n.deltaMin=n.deltaMin||1e3/n.fps,n.deltaMax=n.deltaMax||1e3/(n.fps*.5),n.fps=1e3/n.delta,n},e.run=function(a,t){return typeof a.positionIterations<"u"&&(t=a,a=e.create()),function n(i){a.frameRequestId=c(n),i&&a.enabled&&e.tick(a,t,i)}(),a},e.tick=function(a,t,n){var i=t.timing,l;a.isFixed?l=a.delta:(l=n-a.timePrev||a.delta,a.timePrev=n,a.deltaHistory.push(l),a.deltaHistory=a.deltaHistory.slice(-a.deltaSampleSize),l=Math.min.apply(null,a.deltaHistory),l=l<a.deltaMin?a.deltaMin:l,l=l>a.deltaMax?a.deltaMax:l,a.delta=l);var x={timestamp:i.timestamp};f.trigger(a,"beforeTick",x),a.frameCounter+=1,n-a.counterTimestamp>=1e3&&(a.fps=a.frameCounter*((n-a.counterTimestamp)/1e3),a.counterTimestamp=n,a.frameCounter=0),f.trigger(a,"tick",x),f.trigger(a,"beforeUpdate",x),s.update(t,l),f.trigger(a,"afterUpdate",x),f.trigger(a,"afterTick",x)},e.stop=function(a){o(a.frameRequestId)},e.start=function(a,t){e.run(a,t)}})()},function(B,D,g){var e={};B.exports=e;var f=g(8),s=g(0),r=s.deprecated;(function(){e.collides=function(c,o){return f.collides(c,o)},r(e,"collides","SAT.collides ➤ replaced by Collision.collides")})()},function(B,D,g){var e={};B.exports=e,g(1);var f=g(0);(function(){e.pathToVertices=function(s,r){typeof window<"u"&&!("SVGPathSeg"in window)&&f.warn("Svg.pathToVertices: SVGPathSeg not defined, a polyfill is required.");var c,o,u,a,t,n,i,l,x,y,P=[],C,v,p=0,m=0,h=0;r=r||15;var S=function(M,w,A){var T=A%2===1&&A>1;if(!x||M!=x.x||w!=x.y){x&&T?(C=x.x,v=x.y):(C=0,v=0);var I={x:C+M,y:v+w};(T||!x)&&(x=I),P.push(I),m=C+M,h=v+w}},d=function(M){var w=M.pathSegTypeAsLetter.toUpperCase();if(w!=="Z"){switch(w){case"M":case"L":case"T":case"C":case"S":case"Q":m=M.x,h=M.y;break;case"H":m=M.x;break;case"V":h=M.y;break}S(m,h,M.pathSegType)}};for(e._svgPathToAbsolute(s),u=s.getTotalLength(),n=[],c=0;c<s.pathSegList.numberOfItems;c+=1)n.push(s.pathSegList.getItem(c));for(i=n.concat();p<u;){if(y=s.getPathSegAtLength(p),t=n[y],t!=l){for(;i.length&&i[0]!=t;)d(i.shift());l=t}switch(t.pathSegTypeAsLetter.toUpperCase()){case"C":case"T":case"S":case"Q":case"A":a=s.getPointAtLength(p),S(a.x,a.y,0);break}p+=r}for(c=0,o=i.length;c<o;++c)d(i[c]);return P},e._svgPathToAbsolute=function(s){for(var r,c,o,u,a,t,n=s.pathSegList,i=0,l=0,x=n.numberOfItems,y=0;y<x;++y){var P=n.getItem(y),C=P.pathSegTypeAsLetter;if(/[MLHVCSQTA]/.test(C))"x"in P&&(i=P.x),"y"in P&&(l=P.y);else switch("x1"in P&&(o=i+P.x1),"x2"in P&&(a=i+P.x2),"y1"in P&&(u=l+P.y1),"y2"in P&&(t=l+P.y2),"x"in P&&(i+=P.x),"y"in P&&(l+=P.y),C){case"m":n.replaceItem(s.createSVGPathSegMovetoAbs(i,l),y);break;case"l":n.replaceItem(s.createSVGPathSegLinetoAbs(i,l),y);break;case"h":n.replaceItem(s.createSVGPathSegLinetoHorizontalAbs(i),y);break;case"v":n.replaceItem(s.createSVGPathSegLinetoVerticalAbs(l),y);break;case"c":n.replaceItem(s.createSVGPathSegCurvetoCubicAbs(i,l,o,u,a,t),y);break;case"s":n.replaceItem(s.createSVGPathSegCurvetoCubicSmoothAbs(i,l,a,t),y);break;case"q":n.replaceItem(s.createSVGPathSegCurvetoQuadraticAbs(i,l,o,u),y);break;case"t":n.replaceItem(s.createSVGPathSegCurvetoQuadraticSmoothAbs(i,l),y);break;case"a":n.replaceItem(s.createSVGPathSegArcAbs(i,l,P.r1,P.r2,P.angle,P.largeArcFlag,P.sweepFlag),y);break;case"z":case"Z":i=r,l=c;break}(C=="M"||C=="m")&&(r=i,c=l)}}})()},function(B,D,g){var e={};B.exports=e;var f=g(6);g(0),function(){e.create=f.create,e.add=f.add,e.remove=f.remove,e.clear=f.clear,e.addComposite=f.addComposite,e.addBody=f.addBody,e.addConstraint=f.addConstraint}()}])})})(ie);var ve=ie.exports;export{ve as m};
//# sourceMappingURL=matter-4D3s4xTp.js.map
