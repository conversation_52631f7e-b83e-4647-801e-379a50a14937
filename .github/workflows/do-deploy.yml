name: Deploy to DigitalOcean App Platform

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy:
    name: Deploy to DigitalOcean
    runs-on: ubuntu-latest
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install doctl
      uses: digitalocean/action-doctl@v2
      with:
        token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

    - name: Validate App Spec
      run: |
        echo "Validating DigitalOcean App Platform specification..."
        doctl apps spec validate .do/app.yaml

    - name: Deploy to DigitalOcean App Platform
      run: |
        # Check if app exists
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}' || echo "")
        
        if [ -z "$APP_ID" ]; then
          echo "Creating new app..."
          doctl apps create --spec .do/app.yaml --wait
        else
          echo "Updating existing app with ID: $APP_ID"
          doctl apps update $APP_ID --spec .do/app.yaml --wait
        fi

    - name: Get App Info
      run: |
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')
        echo "App ID: $APP_ID"
        doctl apps get $APP_ID

    - name: Run Database Migrations
      run: |
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')
        echo "Running database migrations..."
        doctl apps run $APP_ID --component web -- python manage.py migrate --noinput

    - name: Create Superuser (if needed)
      run: |
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')
        echo "Creating superuser if needed..."
        doctl apps run $APP_ID --component web -- python -c "
        import django; django.setup()
        from django.contrib.auth import get_user_model
        User = get_user_model()
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', '${{ secrets.ADMIN_PASSWORD }}')
            print('Superuser created')
        else:
            print('Superuser already exists')
        "

    - name: Deployment Summary
      run: |
        APP_ID=$(doctl apps list --format ID,Name --no-header | grep "goali-app" | awk '{print $1}')
        APP_URL=$(doctl apps get $APP_ID --format LiveURL --no-header)
        echo "🚀 Deployment completed successfully!"
        echo "📱 App URL: $APP_URL"
        echo "🔧 Admin URL: $APP_URL/admin/"
        echo "💚 Health Check: $APP_URL/health/"